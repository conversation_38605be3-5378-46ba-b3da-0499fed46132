#!/bin/bash

# 生产环境部署脚本
# 确保所有安全检查和优化都已完成

set -e  # 遇到错误立即退出

echo "🚀 开始生产环境部署..."

# 1. 环境检查
echo "📋 检查环境配置..."
if [ ! -f ".env.production" ]; then
    echo "❌ 错误: .env.production 文件不存在"
    echo "请复制 .env.production.example 并配置实际值"
    exit 1
fi

# 检查必要的环境变量
required_vars=(
    "DATABASE_URL"
    "NEXT_PUBLIC_SUPABASE_URL"
    "NEXT_PUBLIC_SUPABASE_ANON_KEY"
    "SUPABASE_SERVICE_ROLE_KEY"
)

for var in "${required_vars[@]}"; do
    if ! grep -q "^${var}=" .env.production; then
        echo "❌ 错误: 缺少必要的环境变量 ${var}"
        exit 1
    fi
done

echo "✅ 环境配置检查通过"

# 2. 依赖检查和安装
echo "📦 安装生产依赖..."
npm ci --only=production

# 3. 类型检查
echo "🔍 执行类型检查..."
npm run type-check

# 4. 代码质量检查
echo "🧹 执行代码质量检查..."
npm run lint

# 5. 安全扫描
echo "🔒 执行安全扫描..."
npm audit --audit-level=high

# 6. 数据库迁移
echo "🗄️ 执行数据库迁移..."
npx prisma migrate deploy
npx prisma generate

# 7. 构建应用
echo "🏗️ 构建生产版本..."
npm run build

# 8. 安全配置验证
echo "🛡️ 验证安全配置..."

# 检查审计日志表是否存在
echo "检查审计日志表..."
npx prisma db execute --stdin <<< "SELECT COUNT(*) FROM \"AuditLog\" LIMIT 1;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 审计日志表存在"
else
    echo "❌ 审计日志表不存在，请检查数据库迁移"
    exit 1
fi

# 9. 性能优化检查
echo "⚡ 性能优化检查..."

# 检查构建产物大小
build_size=$(du -sh .next | cut -f1)
echo "构建产物大小: $build_size"

# 检查是否启用了压缩
if grep -q "compress: true" next.config.js; then
    echo "✅ 启用了压缩"
else
    echo "⚠️ 建议启用压缩以提高性能"
fi

# 10. 安全头部检查
echo "🔐 安全头部配置检查..."
if grep -q "X-Content-Type-Options" lib/security/utils.ts; then
    echo "✅ 安全头部配置正确"
else
    echo "❌ 缺少安全头部配置"
    exit 1
fi

# 11. 启动应用（可选）
if [ "$1" = "--start" ]; then
    echo "🚀 启动生产服务器..."
    npm start
else
    echo "✅ 部署准备完成！"
    echo ""
    echo "要启动生产服务器，请运行:"
    echo "npm start"
    echo ""
    echo "或者使用 PM2:"
    echo "pm2 start ecosystem.config.js"
fi

echo ""
echo "🎉 生产环境部署完成！"
echo ""
echo "📊 部署后检查清单:"
echo "□ 访问应用并测试主要功能"
echo "□ 检查 /api/security/status 安全状态"
echo "□ 监控错误日志和性能指标"
echo "□ 验证审计日志记录正常"
echo "□ 测试速率限制功能"
echo "□ 确认所有安全头部正确设置"
