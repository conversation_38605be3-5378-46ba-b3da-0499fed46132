{"name": "journal_nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "check": "npm run lint && npm run format:check", "fix": "npm run lint:fix && npm run format", "postinstall": "fumadocs-mdx && prisma generate", "knip": "knip"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/commands": "^6.8.1", "@codemirror/history": "^0.19.2", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lang-python": "^6.2.0", "@codemirror/lang-yaml": "^6.1.2", "@codemirror/language": "^6.11.0", "@codemirror/language-data": "^6.5.1", "@codemirror/search": "^6.5.10", "@dimforge/rapier3d-compat": "^0.17.3", "@next/mdx": "^15.3.2", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-tooltip": "^1.2.6", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/storage-js": "^2.7.3", "@supabase/supabase-js": "^2.49.4", "@types/mdx": "^2.0.13", "@types/minimist": "^1.2.5", "@uiw/codemirror-theme-basic": "^4.23.12", "@uiw/codemirror-theme-github": "^4.23.12", "@uiw/codemirror-theme-quietlight": "^4.23.12", "@uiw/codemirror-theme-solarized": "^4.23.12", "@uiw/codemirror-theme-vscode": "^4.23.12", "@uiw/codemirror-theme-xcode": "^4.23.12", "@uiw/codemirror-themes": "^4.23.12", "@uiw/react-codemirror": "^4.23.12", "@uiw/react-markdown-preview": "^5.1.3", "@uiw/react-md-editor": "^4.0.5", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "@xterm/addon-canvas": "^0.7.0", "@xterm/addon-clipboard": "^0.1.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-image": "^0.8.0", "@xterm/addon-search": "^0.15.0", "@xterm/addon-unicode11": "^0.8.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/addon-webgl": "^0.18.0", "@xterm/xterm": "^5.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "emoji-picker-react": "^4.12.2", "eslint-plugin-tailwindcss": "^4.0.0-alpha.0", "file-saver": "^2.0.5", "framer-motion": "^12.9.2", "fumadocs-core": "^15.2.12", "fumadocs-mdx": "^11.6.2", "fumadocs-ui": "^15.2.15", "geist": "^1.4.2", "glob": "^11.0.2", "gray-matter": "^4.0.3", "gsap": "^3.13.0", "hast-util-to-text": "^4.0.2", "idb-keyval": "^6.2.1", "js-yaml": "^4.1.0", "jsdom": "^26.1.0", "jszip": "^3.10.1", "lucide-react": "^0.501.0", "mdast": "^0.26.2", "mime": "^4.0.7", "minimist": "^1.2.8", "nanoid": "^5.1.5", "next": "15.3.1", "next-mdx-remote": "^5.0.0", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "openai": "^5.1.0", "postcss": "^8.5.3", "qrcode": "^1.5.4", "qs": "^6.14.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.1", "react-rnd": "^10.5.2", "rehype-citation": "^2.3.1", "rehype-highlight": "^7.0.2", "rehype-mathjax": "^7.1.0", "rehype-raw": "^7.0.0", "rehype-stringify": "^10.0.1", "remark-captions": "^2.2.4", "remark-frontmatter": "^5.0.0", "remark-gemoji": "^8.0.0", "remark-gfm": "^4.0.1", "remark-heading-id": "^1.0.1", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "sonner": "^2.0.5", "sugar-high": "^0.9.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "three": "^0.176.0", "together-ai": "^0.16.0", "tw-animate-css": "^1.2.9", "unified": "^11.0.5", "unist-util-inspect": "^8.1.0", "unist-util-visit": "^5.0.0", "xterm-readline": "^1.1.2", "yaml": "^2.7.1", "zod": "^3.24.3", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.27.1", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.7", "@eslint/js": "^9.26.0", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.16", "@types/js-yaml": "^4.0.9", "@types/jsdom": "^21.1.7", "@types/node": "^20.17.32", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.18", "@types/react": "^19", "@types/react-dom": "^19", "@types/remark-heading-id": "^1.0.0", "@types/three": "^0.176.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "babel-loader": "^10.0.0", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.1.0", "knip": "^5.55.1", "prettier": "^3.5.3", "prisma": "^6.8.2", "ts-node": "^10.9.1", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "uglify-js": "^3.19.3", "workbox-build": "^7.3.0"}, "overrides": {"minimatch": "^3.1.2"}}