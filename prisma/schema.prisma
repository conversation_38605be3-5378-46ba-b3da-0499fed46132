generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

/// *
///  * ──────────────────────────────── Ⅰ. 用户域 ────────────────────────────────
model User {
  id                     String                  @id @default(cuid())
  name                   String?
  email                  String                  @unique
  emailVerified          DateTime?
  password               String?
  image                  String?
  avatarColor            String?                 @default("#6366f1") // 头像颜色，存储Hex color code
  createdAt              DateTime                @default(now())
  /// AI相关字段
  aiReqRest              Int                     @default(100) // 剩余可用次数
  aiReqTotal             Int                     @default(0) // 历史总请求次数
  aiModel                String?                 @default("deepseek-chat") // 指定服务商/模型
  aiLastReqTime          DateTime? // 最近一次请求时间
  TaskApprovers          ApprovalTask[]          @relation("TaskApprover")
  TaskRequesters         ApprovalTask[]          @relation("TaskRequester")
  papers                 Paper[]
  publishComments        PublishComment[]
  reviewAssignments      ReviewAssignment[]
  reviewComments         ReviewComment[]
  roleBindings           RoleBinding[]
  notificationsAsActor   Notification[]          @relation("UserNotificationsAsActor")
  notificationRecipients NotificationRecipient[] @relation("UserNotificationRecipients")
}

/// RBAC 三元组：主体-角色-作用域
model RoleBinding {
  id            String        @id @default(cuid())
  principalId   String
  principalType PrincipalType
  role          Role
  scopeType     ScopeType
  scopeId       String?
  alias         String?
  condition     Json?
  createdAt     DateTime      @default(now())
  principal     User          @relation(fields: [principalId], references: [id], onDelete: Cascade)

  @@unique([principalId, role, scopeType, scopeId])
  @@index([principalId, scopeType, scopeId])
}

/// *
///  * ──────────────────────────────── Ⅲ. 期刊期次 ────────────────────────────────
model Issue {
  id            String    @id @default(cuid())
  name          String
  year          Int?
  volume        Int?
  number        Int?
  description   String?
  slug          String?   @unique
  coverImageUrl String?
  publishedAt   DateTime?
  createdAt     DateTime  @default(now())
  papers        Paper[]

  @@index([publishedAt])
}

model Paper {
  /// *
  ///    * —— 标识 ——
  id                String             @id @default(cuid())
  title             String
  slug              String?            @unique
  doi               String?            @unique
  /// *
  ///    * —— 元数据 ——
  abstract          String
  authors           String[]
  affiliations      String[]
  videoUrl          String?
  paperTags         String[]           @default([])
  category          Category           @default(UNKNOWN)
  type              PaperType          @default(FULL)
  authorCanReply    Boolean            @default(false)
  reviewersCanReply Boolean            @default(false)
  needsAttention    Boolean            @default(true)
  submittedAt       DateTime           @default(now())
  acceptedAt        DateTime?
  publishedAt       DateTime?
  /// *
  ///    * —— 指标 ——
  readNumber        Int                @default(0)
  downloads         Int                @default(0)
  citation          Int                @default(0)
  altmetric         Json?
  /// *
  ///    * —— 关联 ——
  authorId          String
  issueId           String?
  /// *
  ///    * —— 流程字段 ——
  status            PaperStatus        @default(SUBMITTED)
  lastUpdatedAt     DateTime?
  author            User               @relation(fields: [authorId], references: [id], onDelete: Cascade)
  issue             Issue?             @relation(fields: [issueId], references: [id])
  publishComments   PublishComment[]
  reviewAssignments ReviewAssignment[]
  reviewComments    ReviewComment[]

  @@index([status])
  @@index([issueId])
}

/// *
///  * ──────────────────────────────── Ⅴ. 评审分配 ────────────────────────────────
model ReviewAssignment {
  id         String    @id @default(cuid())
  paperId    String
  reviewerId String
  round      Int       @default(1)
  alias      String?
  assignedAt DateTime  @default(now())
  dueAt      DateTime?
  completed  Boolean   @default(false)
  paper      Paper     @relation(fields: [paperId], references: [id], onDelete: Cascade)
  reviewer   User      @relation(fields: [reviewerId], references: [id], onDelete: Cascade)

  @@unique([paperId, reviewerId, round])
  @@index([reviewerId, completed])
  @@index([paperId, round])
}

model PublishComment {
  id          String           @id @default(cuid())
  text        String
  createdAt   DateTime         @default(now())
  userId      String
  paperId     String
  anchorType  AnchorType       @default(NONE)
  height      Float?
  page        Int?
  parentId    String?
  quote       String?
  width       Float?
  x           Float?
  y           Float?
  highlightId String?
  pageIndex   Int?
  pageTop     Float?
  yPosition   Float?
  paper       Paper            @relation(fields: [paperId], references: [id], onDelete: Cascade)
  parent      PublishComment?  @relation("PublishCommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies     PublishComment[] @relation("PublishCommentReplies")
  user        User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([paperId])
  @@index([parentId])
}

/// *
///  * —— 审稿/编辑内部讨论 ——
model ReviewComment {
  id                 String      @id @default(cuid())
  paperId            String
  authorId           String
  roleLabel          String
  body               String
  type               CommentType @default(MESSAGE)
  createdAt          DateTime    @default(now())
  visibleToAuthor    Boolean     @default(false)
  visibleToReviewers Boolean     @default(false)
  author             User        @relation(fields: [authorId], references: [id], onDelete: Cascade)
  paper              Paper       @relation(fields: [paperId], references: [id], onDelete: Cascade)
}

/// *
///  * ApprovalTask —— 任何**需要二次确认**的敏感操作都写成一条任务
///  * ❶ requester 创建 PENDING 任务
///  * ❷ approver 在 UI / API 中把 status 置为 APPROVED / REJECTED
///  * ❸ 业务服务监听更新，执行真正 side-effect
model ApprovalTask {
  id          String     @id @default(cuid())
  /// *
  ///    * —— 关联对象 ——
  ///    * 例如 entityType="PAPER"、action="PUBLISH"
  ///    * 方便未来支持多种实体 / 多种动作
  entityType  String
  entityId    String
  action      String
  /// *
  ///    * —— 人员 & 状态 ——
  requesterId String
  approverId  String?
  status      TaskStatus @default(PENDING)
  /// *
  ///    * —— 时间戳 ——
  createdAt   DateTime   @default(now())
  decidedAt   DateTime?
  /// *
  ///    * —— 备注 / 审批意见 ——
  comment     String?
  approver    User?      @relation("TaskApprover", fields: [approverId], references: [id])
  requester   User       @relation("TaskRequester", fields: [requesterId], references: [id])

  @@index([entityType, entityId])
  @@index([status, action])
  @@index([requesterId])
  @@index([approverId])
}

/// ──────────────────────────────── Ⅶ. 通知系统 ────────────────────────────────
/// 核心事件表：一次业务动作（留言、回复、@mention 等）只记录一行
model Notification {
  id String @id @default(cuid())

  /// —— 行为触发者 ——
  actorId String
  actor   User   @relation("UserNotificationsAsActor", fields: [actorId], references: [id], onDelete: Cascade)

  /// —— 业务上下文 ——
  type      NotificationType
  paperId   String? // 论文场景
  commentId String? // 产生通知的评论
  payload   Json? // 预留：附加数据（如 @mention 列表、变更前后 diff）

  /// —— 冗余信息（反范式，加速列表渲染） ——
  preview    String? // 留言/回复前 N 字
  paperTitle String?
  actorName  String? // 避免跨表 Join

  createdAt DateTime @default(now())

  /// —— 多收件人关系 ——
  recipients NotificationRecipient[]

  @@index([paperId])
  @@index([commentId])
}

/// 收件人状态表：一条通知 × N 个用户，各自维护已读/已归档等状态
model NotificationRecipient {
  id             String @id @default(cuid())
  notificationId String
  userId         String

  /// —— 状态字段 ——
  readAt     DateTime? // NULL = 未读
  archivedAt DateTime? // NULL = 未归档
  muted      Boolean   @default(false) // 预留：个性化静音

  notification Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
  user         User         @relation("UserNotificationRecipients", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([notificationId, userId]) // 保证不重复推送
  @@index([userId, readAt])
}

/// 通知类别：根据业务场景增删
enum NotificationType {
  COMMENT_ON_PAPER // 给论文留言
  REPLY_ON_COMMENT // 回复某人
  MENTION // @某人（未来扩展）
  REVIEW_UPDATE // 审稿更新
  PAPER_STATUS_CHANGE // 论文状态流转（未来扩展）
  SYSTEM // 系统广播
}

/// *
///  * ──────────────────────────────── Ⅱ. RBAC ────────────────────────────────
enum PrincipalType {
  USER
  GROUP
  SERVICE
}

enum ScopeType {
  GLOBAL
  ISSUE
  PAPER
}

enum Role {
  /// *
  ///    * 站级
  ADMIN
  CHIEF_EDITOR
  /// *
  ///    * 期刊级
  ISSUE_EDITOR
  /// *
  ///    * 稿件级
  AUTHOR
  REVIEWER
  PAPER_EDITOR
}

/// *
///  * ──────────────────────────────── Ⅳ. 稿件 ────────────────────────────────
enum Category {
  UNKNOWN
  DESIGN
  HCI
  AI
  SYSTEMS
  THEORY
}

enum PaperType {
  FULL
  GALLERY
  PREPRINT
}

enum PaperStatus {
  SUBMITTED
  FIRST_CONTENT_CHECK
  REVIEWER_NEEDED
  REVIEWER_INVITED
  UNDER_REVIEW
  FIRST_REVIEW_DONE
  WATING_AUTHOR_REPLY
  WATING_REVIEWER_REPLY
  SECOND_REVIEW_DONE
  WAITING_MATERIAL_UPDATE
  REVISION_SUBMITTED
  ACCEPTED
  REJECTED
  PUBLISHED
  ARCHIVED
  IN_PRESS
  RETRACTED
  TASK_WATING_APPROVAL
  WAITING_PAYMENT
  PAYMENT_RECEIVED
}

/// *
///  * ──────────────────────────────── Ⅵ. 讨论区 ────────────────────────────────
enum CommentType {
  MESSAGE
  STATUS_CHANGE
}

/// *
///  * —— 公开留言（读者） ——
enum AnchorType {
  NONE
  TEXT
  RECT
}

/// *
///  * 审批状态
enum TaskStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

/// *
///  * ──────────────────────────────── Ⅶ. 审计日志 ────────────────────────────────
model AuditLog {
  id           String   @id @default(cuid())
  userId       String
  action       String
  resourceType String
  resourceId   String
  reason       String?
  metadata     Json?
  ipAddress    String?
  userAgent    String?
  timestamp    DateTime @default(now())
  severity     String   @default("LOW") // LOW, MEDIUM, HIGH, CRITICAL

  @@index([userId])
  @@index([action])
  @@index([resourceType, resourceId])
  @@index([timestamp])
  @@index([severity])
}
