/* prisma/seed.ts */
import { PrismaClient, Role, ScopeType, Category } from "@prisma/client";
import { createClient } from "@supabase/supabase-js";

const prisma = new PrismaClient();
const PWD_PLAIN = "111";

// 直接使用字符串，避免可能的枚举不匹配问题
const PAPER_STATUS = {
  SUBMITTED: "SUBMITTED",
};

// Supabase Service Role client (for admin user creation)
const sbAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

async function main() {
  /* ---------- 1. 先清空干净（测试库才这样做！） ---------- */
  await prisma.reviewAssignment.deleteMany();
  await prisma.reviewComment.deleteMany();
  await prisma.publishComment.deleteMany();
  await prisma.paper.deleteMany();
  await prisma.issue.deleteMany();
  await prisma.roleBinding.deleteMany();
  await prisma.user.deleteMany();

  /* ---------- 2. 创建 6 个角色用户 ---------- */
  const roles: Role[] = [
    Role.ADMIN,
    Role.CHIEF_EDITOR,
    Role.ISSUE_EDITOR,
    Role.AUTHOR,
    Role.REVIEWER,
    Role.PAPER_EDITOR,
  ];

  const users: Array<{ id: string; name: string; email: string }> = [];

  for (const role of roles) {
    const email = `${role.toLowerCase()}@example.com`;

    // ① 在 Supabase Auth 创建账号
    const { data, error } = await sbAdmin.auth.admin.createUser({
      email,
      password: PWD_PLAIN,
      email_confirm: true,
      user_metadata: { name: role, roles: [{ role, scope: "GLOBAL" }] },
    });
    if (error || !data.user) throw error ?? new Error("createUser failed");

    // ② 在业务库写入 User 行（保持 id 一致）
    const user = await prisma.user.create({
      data: {
        id: data.user.id,
        email,
        name: role,
        password: null, // 已弃用
      },
    });
    users.push({ id: user.id, name: role, email });
  }

  /* ---------- 3. RoleBinding：全局绑定 ---------- */
  await Promise.all(
    users.map((u, i) =>
      prisma.roleBinding.create({
        data: {
          principalId: u.id,
          principalType: "USER",
          role: roles[i],
          scopeType: ScopeType.GLOBAL, // GLOBAL 作用域
        },
      })
    )
  );

  /* ---------- 4. 生成 25 期期刊 Issue ---------- */
  const issues = await Promise.all(
    Array.from({ length: 25 }).map((_, idx) => {
      const year = 2020 + Math.floor(idx / 5);
      const volume = 1 + Math.floor(idx / 5);
      const number = (idx % 5) + 1;
      return prisma.issue.create({
        data: {
          name: `Vol.${volume} No.${number} (${year})`,
          year,
          volume,
          number,
        },
      });
    })
  );

  /* ---------- 5. 随机生成 20 篇 Paper ---------- */
  const authorUser = users.find(u => u.name === "AUTHOR")!;
  const sampleTags = ["HCI", "Design", "AI", "Systems", "Theory"];

  await Promise.all(
    Array.from({ length: 20 }).map((_, i) => {
      const issue = issues[Math.floor(Math.random() * 5)]; // 前 5 期
      return prisma.paper.create({
        data: {
          title: `Sample Paper #${i + 1}`,
          abstract: "This is a sample abstract used for seeding the database.",
          authors: ["Jordan Smith", "Rui Li"],
          affiliations: ["Interaction Design Lab, City University", "Independent Artist"],
          paperTags: [sampleTags[i % sampleTags.length]],
          category: Category.UNKNOWN,
          status: "SUBMITTED" as any, // 强制类型转换
          authorId: authorUser.id,
          issueId: issue.id,
          needsAttention: true,
        },
      });
    })
  );

  console.log("✅ Seed finished.");
}

main()
  .catch(e => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());
