/* types/remark-captions.d.ts
   Minimal TypeScript shim for the `remark-captions` plugin.
   It declares the module and its (optional) options so
   `import remarkCaptions from 'remark-captions'` works with type-checking */

declare module "remark-captions" {
  import { Root } from "mdast";
  import { Plugin } from "unified";

  /** Partial option shape (§ examples in plugin README) */
  interface RemarkCaptionsOptions {
    /** map caption prefixes for external elements, e.g. { table: 'Table:' } */
    external?: Record<string, string>;
    /** map caption prefixes for internal elements, e.g. { image: 'Figure:' } */
    internal?: Record<string, string>;
    /** extra class for generated <figure> */
    figureClassName?: string;
    /** extra class for generated <figcaption> */
    figcaptionClassName?: string;
    /** extra class for the <img> or other media element */
    imageClassName?: string;
  }

  /** The actual plugin exported by `remark-captions` */
  const remarkCaptions: Plugin<[RemarkCaptionsOptions?], Root>;

  export = remarkCaptions; // enables `import remarkCaptions from 'remark-captions'`
  //   ^—— uses CommonJS default export style
}
