/**
 * PM2 生产环境配置
 * 用于管理 Next.js 应用的生产部署
 */

module.exports = {
  apps: [
    {
      name: 'journal-nextjs',
      script: 'npm',
      args: 'start',
      cwd: './',
      instances: 'max', // 使用所有可用的 CPU 核心
      exec_mode: 'cluster',
      
      // 环境配置
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 自动重启配置
      autorestart: true,
      watch: false, // 生产环境不监听文件变化
      max_memory_restart: '1G',
      
      // 进程管理
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // 健康检查
      health_check_grace_period: 3000,
      
      // 环境变量
      env_file: '.env.production',
      
      // 高级配置
      node_args: '--max-old-space-size=2048',
      
      // 监控配置
      monitoring: false, // 如果使用 PM2 Plus，设置为 true
      
      // 集群配置
      listen_timeout: 8000,
      kill_timeout: 5000,
      
      // 错误处理
      ignore_watch: [
        'node_modules',
        '.next',
        'logs',
        '.git'
      ],
      
      // 合并日志
      merge_logs: true,
      
      // 时间戳
      time: true,
    }
  ],

  // 部署配置
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: '**************:your-username/journal-nextjs.git',
      path: '/var/www/journal-nextjs',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'ForwardAgent=yes'
    }
  }
};
