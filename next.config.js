// next.config.js
const { createMDX } = require("fumadocs-mdx/next");
const runtimeCachingDefault = require("next-pwa/cache");
const withPWAFactory = require("next-pwa");

const withMDX = createMDX();

const withPWA = withPWAFactory({
  dest: "public",
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === "development",

  /* 👇 正确的离线回退页面写法 */
  fallbacks: {
    document: "/offline.html", // 离线导航时返回
  },

  /* 精确过滤要预缓存的文件 */
  manifestTransforms: [
    async manifest => ({
      manifest: manifest.filter(entry => {
        const url = entry.url;
        if (url.startsWith("/_next/static")) return true;
        if (url === "/editor" || url.startsWith("/editor/")) return true;
        if (url === "/paged-preview" || url === "/offline.html") return true;
        return false;
      }),
    }),
  ],

  /* 运行时缓存策略 */
  runtimeCaching: [
    {
      urlPattern: /^\/_next\/static\//,
      handler: "CacheFirst",
      options: {
        cacheName: "next-static",
        expiration: { maxEntries: 50, maxAgeSeconds: 60 * 60 * 24 * 365 },
      },
    },
    {
      urlPattern: ({ url }) =>
        url.pathname.startsWith("/editor") || url.pathname.startsWith("/paged-preview"),
      handler: "StaleWhileRevalidate",
      options: { cacheName: "editor-shell" },
    },
    {
      urlPattern: /^https:\/\/rqzfxgvhgunzpnkdazrd\.supabase\.co\/storage\/v1\/object\/public\/.*/i,
      handler: "CacheFirst",
      options: {
        cacheName: "supabase-images",
        expiration: { maxEntries: 100, maxAgeSeconds: 60 * 60 * 24 * 365 },
      },
    },
    /* 缓存 /api/editor/projects/<n>/tree 响应  */
    {
      urlPattern: /\/api\/editor\/projects\/.*\/tree$/i,
      handler: "StaleWhileRevalidate",
      options: {
        cacheName: "tree-json",
        expiration: { maxEntries: 20, maxAgeSeconds: 60 * 60 * 24 },
      },
    },
    /* 其他默认 NetworkFirst（离线兜底用） */
    ...runtimeCachingDefault.filter(c => c.handler === "NetworkFirst"),
  ],
});

/* -------- 基础 Next 配置 -------- */
const baseConfig = {
  reactStrictMode: true,
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "rqzfxgvhgunzpnkdazrd.supabase.co",
        pathname: "/storage/**",
      },
    ],
  },
  pageExtensions: ["js", "jsx", "ts", "tsx", "md", "mdx"],
  // 允许开发时跨域访问资源
  allowedDevOrigins: [
    "http://**************:3000", // 本地局域网访问
    "http://localhost:3000",
  ],
};

/* -------- 导出 -------- */
module.exports = withPWA(withMDX(baseConfig));
