import js from "@eslint/js";
import globals from "globals";
import tseslint from "typescript-eslint";
import pluginReact from "eslint-plugin-react";
import pluginReactHooks from "eslint-plugin-react-hooks";
import pluginImport from "eslint-plugin-import";
import pluginJSXA11y from "eslint-plugin-jsx-a11y";
import plugin<PERSON><PERSON><PERSON> from "eslint-plugin-prettier";
import { flatConfig as next } from "@next/eslint-plugin-next";
import { defineConfig } from "eslint/config";

// 全局忽略配置
const ignores = [
  "node_modules/**",
  ".next/**",
  "build/**",
  "dist/**",
  "public/**",
  ".vercel/**",
  "**/node_modules/**",
  "next-env.d.ts",
  "*.config.js",
  "*.config.mjs",
];

export default defineConfig([
  { ignores },
  next.coreWebVitals,
  js.configs.recommended,
  tseslint.configs.recommended,
  {
    ignores,
    files: ["**/*.{js,jsx,ts,tsx}"],
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
      },
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
        project: "./tsconfig.json",
        ecmaFeatures: { jsx: true },
      },
    },
    plugins: {
      "@typescript-eslint": tseslint.plugin,
      react: pluginReact,
      "react-hooks": pluginReactHooks,
      import: pluginImport,
      "jsx-a11y": pluginJSXA11y,
      prettier: pluginPrettier,
    },
    rules: {
      // 基础
      "no-unused-vars": ["warn", { argsIgnorePattern: "^_", varsIgnorePattern: "^_" }],
      "no-console": ["warn", { allow: ["warn", "error", "info"] }],
      "prefer-const": "warn",
      // TypeScript
      "@typescript-eslint/no-unused-vars": [
        "warn",
        { argsIgnorePattern: "^_", varsIgnorePattern: "^_" },
      ],
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/ban-ts-comment": "warn",
      // React
      "react/jsx-uses-react": "off",
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
      "react/display-name": "off",
      "react-hooks/rules-of-hooks": "error",
      "react-hooks/exhaustive-deps": "warn",
      // import
      "import/order": [
        "warn",
        {
          groups: ["builtin", "external", "internal", "parent", "sibling", "index"],
          "newlines-between": "always",
          alphabetize: { order: "asc", caseInsensitive: true },
        },
      ],
      "import/first": "error",
      "import/no-duplicates": "error",
      // a11y
      "jsx-a11y/alt-text": "warn",
      "jsx-a11y/aria-props": "warn",
      "jsx-a11y/aria-role": "warn",
      "jsx-a11y/role-has-required-aria-props": "warn",
      // Prettier
      "prettier/prettier": "warn",
    },
    settings: {
      react: { version: "detect" },
    },
  },
]);
