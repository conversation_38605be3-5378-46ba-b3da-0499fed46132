# News Images

这个文件夹用于存储新闻文章的封面图片。

## 使用方法

1. **添加图片**：将图片文件放在这个文件夹中
2. **支持格式**：JPG, PNG, GIF, SVG
3. **推荐尺寸**：建议使用方形图片，最小 200x200 像素
4. **文件命名**：使用描述性的文件名，如 `vim-editor.jpg`

## 在 MDX 文件中使用

在你的 MDX 文件的 frontmatter 中添加 `image` 字段：

```yaml
---
title: "你的文章标题"
publishedAt: "2024-01-01"
summary: "文章摘要"
category: "分类"
image: "your-image.jpg"
---
```

## 注意事项

- 如果没有指定图片或图片加载失败，会显示 brand-600 颜色的默认方块
- 图片会自动调整为方形显示
- 支持响应式设计，在不同屏幕尺寸下自动调整
- 图片会在鼠标悬停时有轻微的缩放效果

## 示例

当前文件夹中的示例图片：

- `vim-editor.jpg` - Vim 编辑器主题
- `static-typing.png` - 静态类型主题
- `spaces-tabs.gif` - 代码缩进辩论主题
