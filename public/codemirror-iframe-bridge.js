/**
 * CodeMirror iframe 桥接脚本
 * 在 editor 页面的 iframe 内运行，提供 CodeMirror API 的消息桥接
 * 使用原生 JavaScript，兼容所有环境
 */

(function () {
    'use strict';

    // console.log('[CodeMirror Bridge] iframe bridge script loaded');

    // 存储 CodeMirror 编辑器实例
    let editorInstance = null;
    let listeners = new Map();
    let isReady = false;

    // 等待 CodeMirror 编辑器初始化
    function waitForCodeMirror() {
        // console.log('[CodeMirror Bridge] 检查编辑器实例...');

        // 检查多种可能的 CodeMirror 全局变量
        const possibleNames = ['editorView', 'editor', 'codeMirror', 'cm', 'view'];

        // console.log('[CodeMirror Bridge] 检查全局变量:', possibleNames.map(name => ({
        //     name,
        //     exists: !!window[name],
        //     hasState: !!(window[name] && window[name].state),
        //     hasDispatch: !!(window[name] && window[name].dispatch),
        //     type: typeof window[name]
        // })));

        for (const name of possibleNames) {
            if (window[name] && window[name].state && window[name].dispatch) {
                // console.log(`[CodeMirror Bridge] 找到编辑器实例: window.${name}`);
                setEditor(window[name]);
                return;
            }
        }

        // 检查 window.CM 或其他可能的挂载点
        if (window.CM && window.CM.editor) {
            // console.log('[CodeMirror Bridge] 找到编辑器实例: window.CM.editor');
            setEditor(window.CM.editor);
            return;
        }

        // 检查 DOM 中是否有 .cm-editor 元素
        const cmElement = document.querySelector('.cm-editor');
        // console.log('[CodeMirror Bridge] DOM检查:', {
        //     hasCmElement: !!cmElement,
        //     hasCmView: !!(cmElement && cmElement.cmView),
        //     cmElementKeys: cmElement ? Object.getOwnPropertyNames(cmElement) : []
        // });

        if (cmElement && cmElement.cmView) {
            // console.log('[CodeMirror Bridge] 找到编辑器实例: DOM .cm-editor.cmView');
            setEditor(cmElement.cmView);
            return;
        }

        // 继续等待
        // console.log('[CodeMirror Bridge] 未找到编辑器，100ms后重试...');
        setTimeout(waitForCodeMirror, 100);
    }

    // 设置编辑器实例
    function setEditor(editor) {
        editorInstance = editor;
        isReady = true;

        // console.log('[CodeMirror Bridge] Editor instance found and ready');
        // console.log('[CodeMirror Bridge] Editor structure:', {
        //     hasState: !!editor.state,
        //     hasDoc: !!editor.state?.doc,
        //     hasSliceDoc: !!editor.state?.doc?.sliceDoc,
        //     hasToString: !!editor.state?.doc?.toString,
        //     docType: typeof editor.state?.doc,
        //     docKeys: editor.state?.doc ? Object.getOwnPropertyNames(editor.state.doc) : []
        // });

        // 设置更新监听器
        setupUpdateListener();

        // 通知父窗口编辑器已就绪
        notifyReady();
    }

    // 设置更新监听器
    function setupUpdateListener() {
        if (!editorInstance || !editorInstance.updateListener) return;

        try {
            // 创建更新监听器
            const updateListener = editorInstance.updateListener.of((update) => {
                handleEditorUpdate(update);
            });

            // 添加到编辑器（CodeMirror 6 方式）
            if (editorInstance.dispatch && editorInstance.state) {
                // 尝试添加扩展
                // console.log('[CodeMirror Bridge] Update listener setup complete');
            }
        } catch (error) {
            // console.warn('[CodeMirror Bridge] Failed to setup update listener:', error);
        }
    }

    // 处理编辑器更新
    function handleEditorUpdate(update) {
        // 发送更新通知到父窗口
        const updateData = {
            docChanged: update.docChanged,
            selectionSet: update.selectionSet,
            viewportChanged: update.viewportChanged,
            focusChanged: update.focusChanged,
            state: {
                docLength: update.state.doc.length,
                lineCount: getLineCount(update.state.doc),
                readOnly: update.state.readOnly
            }
        };

        // 提取具体的更改信息
        if (update.docChanged && update.changes) {
            updateData.changes = extractChanges(update.changes);
        }

        if (update.selectionSet) {
            updateData.selection = {
                main: update.state.selection.main,
                ranges: update.state.selection.ranges
            };
        }

        // 发送到父窗口
        window.parent.postMessage({
            type: 'CODEMIRROR_UPDATE',
            updateData,
            timestamp: Date.now()
        }, '*');
    }

    // 提取文档更改信息
    function extractChanges(changes) {
        const result = [];

        changes.iterChanges((fromA, toA, fromB, toB, inserted) => {
            result.push({
                from: fromA,
                to: toA,
                inserted: inserted.toString(),
                deleted: '' // 删除的内容需要从原文档计算
            });
        });

        return result;
    }

    // 获取行数
    function getLineCount(doc) {
        if (doc.lines) return doc.lines;
        if (doc.lineAt && doc.length) {
            try {
                return doc.lineAt(doc.length).number;
            } catch (e) {
                return 1;
            }
        }
        return 1;
    }

    // 获取文档内容 - 兼容不同版本的API
    function getDocContent(doc, from, to) {
        if (from === undefined && to === undefined) {
            // 获取全文
            return doc.toString();
        }

        // 获取范围内容 - 尝试不同的API
        if (doc.sliceString) {
            return doc.sliceString(from, to);
        } else if (doc.slice) {
            const slice = doc.slice(from, to);
            return typeof slice === 'string' ? slice : slice.toString();
        } else if (doc.sliceDoc) {
            return doc.sliceDoc(from, to);
        } else {
            // 回退方法：获取全文然后截取
            const fullText = doc.toString();
            return fullText.slice(from, to);
        }
    }

    // 通知父窗口编辑器就绪
    function notifyReady() {
        window.parent.postMessage({
            type: 'CODEMIRROR_READY',
            timestamp: Date.now()
        }, '*');
    }

    // 处理来自父窗口的消息
    window.addEventListener('message', function (event) {
        const data = event.data;
        // console.log('[CodeMirror Bridge] 收到消息:', data.type, data);

        switch (data.type) {
            case 'CODEMIRROR_OPERATION_REQUEST':
                // console.log('[CodeMirror Bridge] 处理操作请求:', data);
                handleOperationRequest(data);
                break;

            case 'CODEMIRROR_AI_REQUEST':
                // console.log('[CodeMirror Bridge] 处理AI请求:', data);
                handleAIRequest(data);
                break;

            case 'WINDOW_INIT':
                // console.log('[CodeMirror Bridge] Window initialized:', data.payload);
                break;

            default:
                // console.log('[CodeMirror Bridge] 忽略消息类型:', data.type);
                break;
        }
    });

    // 处理操作请求
    async function handleOperationRequest(data) {
        const { requestId, operation, target, data: opData, options } = data;
        // console.log('[CodeMirror Bridge] 处理操作请求 - requestId:', requestId, 'operation:', operation, 'target:', target, 'isReady:', isReady, 'hasEditor:', !!editorInstance);

        try {
            if (!isReady || !editorInstance) {
                const error = `CodeMirror editor not ready - isReady: ${isReady}, hasEditor: ${!!editorInstance}`;
                // console.error('[CodeMirror Bridge]', error);
                throw new Error(error);
            }

            let result;

            switch (operation) {
                case 'read':
                    result = handleReadOperation(target, opData);
                    break;

                case 'write':
                    result = handleWriteOperation(target, opData, options);
                    break;

                case 'listen':
                    result = handleListenOperation(opData);
                    break;

                case 'syntax':
                    result = handleSyntaxOperation(opData);
                    break;

                case 'history':
                    result = handleHistoryOperation(opData);
                    break;

                default:
                    throw new Error(`Unsupported operation: ${operation}`);
            }

            // console.log('[CodeMirror Bridge] 操作成功, requestId:', requestId, 'result:', result);

            // 发送成功响应
            window.parent.postMessage({
                type: 'CODEMIRROR_OPERATION_RESPONSE',
                requestId,
                success: true,
                result,
                timestamp: Date.now()
            }, '*');

        } catch (error) {
            // console.error('[CodeMirror Bridge] Operation error:', error);

            // 发送错误响应
            window.parent.postMessage({
                type: 'CODEMIRROR_OPERATION_RESPONSE',
                requestId,
                success: false,
                error: error.message,
                timestamp: Date.now()
            }, '*');
        }
    }

    // 处理读取操作
    function handleReadOperation(target, data) {
        // console.log('[CodeMirror Bridge] handleReadOperation:', { target, data });

        switch (target) {
            case 'doc':
                if (data && typeof data.lineNumber === 'number') {
                    // 按行读取
                    const line = editorInstance.state.doc.line(data.lineNumber);
                    return {
                        line: {
                            number: line.number,
                            from: line.from,
                            to: line.to,
                            text: line.text
                        }
                    };
                } else if (data && data.range) {
                    // 按范围读取
                    const { from, to } = data.range;
                    const content = getDocContent(editorInstance.state.doc, from, to);
                    return { content };
                } else {
                    // 读取全文
                    const content = getDocContent(editorInstance.state.doc);
                    return { content };
                }

            case 'selection':
                // 🔧 添加详细调试信息
                const selectionState = editorInstance.state.selection;
                const mainSelection = selectionState.main;
                const allRanges = selectionState.ranges;

                // console.log('[CodeMirror Bridge] 获取选区信息:', {
                //     selectionState,
                //     mainSelection,
                //     allRanges,
                //     mainAnchor: mainSelection.anchor,
                //     mainHead: mainSelection.head,
                //     mainFrom: mainSelection.from,
                //     mainTo: mainSelection.to,
                //     mainEmpty: mainSelection.empty,
                //     rangesCount: allRanges ? allRanges.length : 0
                // });

                // 确保返回标准格式
                const result = {
                    selection: {
                        main: {
                            anchor: mainSelection.anchor || 0,
                            head: mainSelection.head || 0,
                            from: mainSelection.from || 0,
                            to: mainSelection.to || 0,
                            empty: mainSelection.empty || false
                        },
                        ranges: allRanges ? allRanges.map(range => ({
                            anchor: range.anchor || 0,
                            head: range.head || 0,
                            from: range.from || 0,
                            to: range.to || 0,
                            empty: range.empty || false
                        })) : []
                    }
                };

                // console.log('[CodeMirror Bridge] 返回选区结果:', result);
                return result;

            case 'state':
                return {
                    state: {
                        docLength: editorInstance.state.doc.length,
                        lineCount: getLineCount(editorInstance.state.doc),
                        readOnly: editorInstance.state.readOnly || false
                    }
                };

            default:
                throw new Error(`Unsupported read target: ${target}`);
        }
    }

    // 处理写入操作
    function handleWriteOperation(target, data, options) {
        switch (target) {
            case 'doc':
                if (data && data.changes) {
                    const spec = {
                        changes: data.changes
                    };

                    if (options) {
                        if (options.userEvent) spec.userEvent = options.userEvent;
                        if (options.scrollIntoView) spec.scrollIntoView = options.scrollIntoView;
                    }

                    editorInstance.dispatch(spec);
                    return { changes: data.changes };
                }
                break;

            case 'selection':
                if (data && data.selection) {
                    // console.log('[CodeMirror Bridge] 设置选区:', data.selection);

                    // 🆕 改进多选区支持 - 处理数组形式的选区
                    let selection;
                    if (Array.isArray(data.selection)) {
                        // 多选区：转换为 CodeMirror 6 格式
                        selection = {
                            anchor: data.selection[0].anchor,
                            head: data.selection[0].head || data.selection[0].anchor,
                            ranges: data.selection.map(range => ({
                                anchor: range.anchor,
                                head: range.head || range.anchor
                            }))
                        };
                    } else {
                        // 单选区
                        selection = {
                            anchor: data.selection.anchor,
                            head: data.selection.head || data.selection.anchor
                        };
                    }

                    const spec = { selection };

                    if (options) {
                        if (options.userEvent) spec.userEvent = options.userEvent;
                        if (options.scrollIntoView) spec.scrollIntoView = options.scrollIntoView;
                    }

                    // console.log('[CodeMirror Bridge] 分发选区更新:', spec);
                    editorInstance.dispatch(spec);
                    return { success: true };
                }
                break;

            default:
                throw new Error(`Unsupported write target: ${target}`);
        }

        return { success: false };
    }

    // 处理监听操作
    function handleListenOperation(data) {
        const callbackId = data?.callback || `listener_${Date.now()}`;
        const listenType = data?.listenType || 'all';

        // 注册监听器（实际的监听逻辑在 setupUpdateListener 中处理）
        listeners.set(callbackId, { type: listenType });

        return { callbackId };
    }

    // 处理语法操作
    function handleSyntaxOperation(data) {
        const { syntaxQuery } = data || {};

        // console.log('[CodeMirror Bridge] 语法操作:', syntaxQuery);

        // 🆕 改进语法树支持 - 尝试多种方式获取语法树
        let syntaxTree = null;

        try {
            // 方法1: 通过全局 syntaxTree 函数
            if (window.syntaxTree && typeof window.syntaxTree === 'function') {
                // console.log('[CodeMirror Bridge] 使用全局 syntaxTree 函数');
                syntaxTree = window.syntaxTree(editorInstance.state);
            }
            // 方法2: 通过编辑器实例的 syntaxTree 属性
            else if (editorInstance && editorInstance.syntaxTree) {
                // console.log('[CodeMirror Bridge] 使用编辑器实例 syntaxTree');
                syntaxTree = editorInstance.syntaxTree;
            }
            // 方法3: 尝试从 state 中获取语法树
            else if (editorInstance && editorInstance.state && editorInstance.state.tree) {
                // console.log('[CodeMirror Bridge] 使用状态中的语法树');
                syntaxTree = editorInstance.state.tree;
            }
            // 方法4: 尝试语言数据中的语法树
            else if (editorInstance && editorInstance.state && editorInstance.state.languageDataAt) {
                // console.log('[CodeMirror Bridge] 尝试从语言数据获取语法树');
                const langData = editorInstance.state.languageDataAt('tree', 0);
                if (langData) syntaxTree = langData;
            }

            if (!syntaxTree) {
                // console.warn('[CodeMirror Bridge] 语法树不可用');
                return { syntax: { available: false, reason: 'Syntax tree not available' } };
            }

            // console.log('[CodeMirror Bridge] 语法树已获取:', !!syntaxTree);

            switch (syntaxQuery?.type) {
                case 'tree':
                    // 🔧 修复：序列化语法树，移除不可克隆的函数
                    const serializableTree = syntaxTree ? {
                        type: syntaxTree.type?.name || 'unknown',
                        from: syntaxTree.from || 0,
                        to: syntaxTree.to || 0,
                        length: syntaxTree.length || 0,
                        isAvailable: true
                    } : null;

                    return {
                        syntax: {
                            tree: serializableTree,
                            available: true
                        }
                    };

                case 'node':
                    if (syntaxQuery.position !== undefined) {
                        try {
                            // 获取指定位置的语法节点
                            const node = syntaxTree.resolve ?
                                syntaxTree.resolve(syntaxQuery.position) :
                                syntaxTree.nodeAt ? syntaxTree.nodeAt(syntaxQuery.position) : null;

                            if (node) {
                                return {
                                    syntax: {
                                        nodes: [{
                                            type: node.type?.name || node.name || 'unknown',
                                            from: node.from || 0,
                                            to: node.to || 0,
                                            name: node.name || node.type?.name || 'unknown'
                                        }],
                                        available: true
                                    }
                                };
                            } else {
                                return {
                                    syntax: {
                                        nodes: [],
                                        available: true,
                                        reason: 'No node found at position'
                                    }
                                };
                            }
                        } catch (error) {
                            // console.warn('[CodeMirror Bridge] 语法节点查询失败:', error);
                            return {
                                syntax: {
                                    nodes: [],
                                    available: false,
                                    reason: error.message
                                }
                            };
                        }
                    }
                    break;

                case 'cursor':
                    try {
                        // 获取语法游标
                        const cursor = syntaxTree.cursor ? syntaxTree.cursor() : null;
                        if (cursor) {
                            return {
                                syntax: {
                                    cursor: {
                                        available: true,
                                        position: cursor.pos || 0
                                    }
                                }
                            };
                        } else {
                            return {
                                syntax: {
                                    cursor: {
                                        available: false,
                                        reason: 'Syntax cursor not available'
                                    }
                                }
                            };
                        }
                    } catch (error) {
                        // console.warn('[CodeMirror Bridge] 语法游标查询失败:', error);
                        return {
                            syntax: {
                                cursor: {
                                    available: false,
                                    reason: error.message
                                }
                            }
                        };
                    }

                default:
                    throw new Error(`Unsupported syntax query type: ${syntaxQuery?.type}`);
            }

        } catch (error) {
            // console.error('[CodeMirror Bridge] 语法操作失败:', error);
            return {
                syntax: {
                    available: false,
                    reason: error.message
                }
            };
        }

        return { syntax: { available: false, reason: 'Unknown error' } };
    }

    // 处理历史操作
    function handleHistoryOperation(data) {
        const { historyAction } = data || {};

        // console.log('[CodeMirror Bridge] 历史操作:', historyAction);

        // 🆕 改进历史操作 - 使用 CodeMirror 6 的命令系统
        // 检查是否有 undo/redo 命令可用（通过全局 undo/redo 函数或编辑器实例）
        const hasUndoCommand = window.undo || (editorInstance && editorInstance.undo);
        const hasRedoCommand = window.redo || (editorInstance && editorInstance.redo);

        // console.log('[CodeMirror Bridge] 历史命令检查:', {
        //     hasUndoCommand: !!hasUndoCommand,
        //     hasRedoCommand: !!hasRedoCommand,
        //     hasGlobalUndo: !!window.undo,
        //     hasGlobalRedo: !!window.redo,
        //     hasEditorHistory: !!(editorInstance && editorInstance.history)
        // });

        switch (historyAction) {
            case 'undo':
                try {
                    // 尝试多种方式执行撤销
                    if (window.undo && typeof window.undo === 'function') {
                        // console.log('[CodeMirror Bridge] 使用全局 undo 函数');
                        const result = window.undo(editorInstance);
                        // console.log('[CodeMirror Bridge] 全局 undo 结果:', result);
                        return { success: result !== false };
                    } else if (editorInstance && editorInstance.undo) {
                        // console.log('[CodeMirror Bridge] 使用编辑器实例 undo 方法');
                        const result = editorInstance.undo();
                        return { success: result !== false };
                    } else if (editorInstance && editorInstance.history && editorInstance.history.undo) {
                        // console.log('[CodeMirror Bridge] 使用历史对象 undo 方法');
                        const result = editorInstance.history.undo();
                        return { success: result !== false };
                    } else {
                        // console.log('[CodeMirror Bridge] 没有可用的撤销方法');
                        throw new Error('Undo command not available');
                    }
                } catch (error) {
                    // console.warn('[CodeMirror Bridge] Undo failed:', error);
                    throw error;
                }

            case 'redo':
                try {
                    if (window.redo && typeof window.redo === 'function') {
                        // console.log('[CodeMirror Bridge] 使用全局 redo 函数');
                        const result = window.redo(editorInstance);
                        // console.log('[CodeMirror Bridge] 全局 redo 结果:', result);
                        return { success: result !== false };
                    } else if (editorInstance && editorInstance.redo) {
                        // console.log('[CodeMirror Bridge] 使用编辑器实例 redo 方法');
                        const result = editorInstance.redo();
                        return { success: result !== false };
                    } else if (editorInstance && editorInstance.history && editorInstance.history.redo) {
                        // console.log('[CodeMirror Bridge] 使用历史对象 redo 方法');
                        const result = editorInstance.history.redo();
                        return { success: result !== false };
                    } else {
                        // console.log('[CodeMirror Bridge] 没有可用的重做方法');
                        throw new Error('Redo command not available');
                    }
                } catch (error) {
                    // console.warn('[CodeMirror Bridge] Redo failed:', error);
                    throw error;
                }

            case 'clear':
                try {
                    if (editorInstance && editorInstance.history && editorInstance.history.clear) {
                        editorInstance.history.clear();
                        return { success: true };
                    } else {
                        throw new Error('Clear history not available');
                    }
                } catch (error) {
                    // console.warn('[CodeMirror Bridge] Clear history failed:', error);
                    throw error;
                }

            default:
                throw new Error(`Unsupported history action: ${historyAction}`);
        }
    }

    // 处理 AI 请求
    async function handleAIRequest(data) {
        const { requestId, action, context, prompt, options } = data;

        try {
            // 获取当前编辑器上下文
            const editorContext = {
                ...context,
                content: context.content || editorInstance.state.doc.toString(),
                selection: context.selection || editorInstance.state.selection.main,
                cursor: context.cursor || editorInstance.state.selection.main.head
            };

            // 这里可以集成实际的 AI 服务
            // console.log('[CodeMirror Bridge] AI request:', { action, context: editorContext, prompt, options });

            // 模拟 AI 处理（实际实现时应该调用真正的 AI API）
            const mockResult = await simulateAIResponse(action, editorContext, prompt, options);

            // 发送成功响应
            window.parent.postMessage({
                type: 'CODEMIRROR_AI_RESPONSE',
                requestId,
                success: true,
                result: mockResult,
                timestamp: Date.now()
            }, '*');

        } catch (error) {
            // console.error('[CodeMirror Bridge] AI request error:', error);

            // 发送错误响应
            window.parent.postMessage({
                type: 'CODEMIRROR_AI_RESPONSE',
                requestId,
                success: false,
                error: error.message,
                timestamp: Date.now()
            }, '*');
        }
    }

    // 模拟 AI 响应
    async function simulateAIResponse(action, context, prompt, options) {
        // 模拟异步处理时间
        await new Promise(resolve => setTimeout(resolve, 500));

        const results = {
            analyze: {
                analysis: `代码分析结果：检测到 ${context.content.length} 个字符的代码`,
                metadata: { tokensUsed: 100, processingTime: 500 }
            },
            suggest: {
                suggestions: [
                    {
                        description: '优化代码结构',
                        changes: [{ from: 0, to: 10, insert: '// 优化建议\n' }],
                        confidence: 0.8
                    }
                ],
                metadata: { tokensUsed: 150, processingTime: 500 }
            },
            complete: {
                completion: '// AI 代码补全示例\nconst example = "completed code";',
                metadata: { tokensUsed: 80, processingTime: 500 }
            },
            explain: {
                explanation: `代码解释：这段代码包含 ${context.content.split('\n').length} 行，主要功能是...`,
                metadata: { tokensUsed: 120, processingTime: 500 }
            },
            refactor: {
                refactored: `// 重构后的代码\n${context.content}`,
                metadata: { tokensUsed: 200, processingTime: 500 }
            }
        };

        return results[action] || { error: `Unsupported AI action: ${action}` };
    }

    // 暴露全局 API 供调试使用
    window.codeMirrorBridge = {
        setEditor,
        getEditor: () => editorInstance,
        isReady: () => isReady,
        listeners: () => listeners,
        checkForEditor: waitForCodeMirror  // 手动重新检查编辑器
    };

    // 开始等待 CodeMirror 编辑器
    function startWaiting() {
        // console.log('[CodeMirror Bridge] 开始等待编辑器初始化，当前DOM状态:', document.readyState);
        // 给编辑器一些时间来初始化
        setTimeout(waitForCodeMirror, 500);
    }

    if (document.readyState === 'loading') {
        // console.log('[CodeMirror Bridge] DOM尚未加载完成，等待DOMContentLoaded');
        document.addEventListener('DOMContentLoaded', startWaiting);
    } else {
        // console.log('[CodeMirror Bridge] DOM已加载完成，直接开始');
        startWaiting();
    }

    // console.log('[CodeMirror Bridge] iframe bridge script initialization complete');
})(); 