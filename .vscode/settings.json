{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[javascriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[markdown]": {"editor.defaultFormatter": "yzhang.markdown-all-in-one"}, "[css]": {"editor.defaultFormatter": "vscode.css-language-features"}, "files.associations": {"*.css": "tailwindcss"}, "tailwindCSS.includeLanguages": {"typescriptreact": "html", "javascriptreact": "html"}, "typescript.tsdk": "node_modules/typescript/lib", "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": true, "**/.next": true}, "search.exclude": {"**/node_modules": true, "**/.next": true, "**/dist": true}}