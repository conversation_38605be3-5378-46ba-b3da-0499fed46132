/**
 * 修复WebSocket升级请求的pattern.split错误
 * 这个模块会向全局添加一个安全补丁，确保类似的错误不会发生
 */

// 备份原始的String.prototype.split方法
const originalSplit = String.prototype.split;

// 创建一个安全的split方法，在非字符串上调用时不会崩溃
function safeSplit(pattern, limit) {
  // 确保this是字符串
  if (typeof this !== "string") {
    console.warn("Warning: 尝试在非字符串上调用split方法", this);
    return [];
  }

  // 使用原始split方法
  return originalSplit.call(this, pattern, limit);
}

/**
 * 应用WebSocket安全补丁
 * 在开发环境中使用，防止pattern.split错误
 */
export function applyWebSocketPatch() {
  if (process.env.NODE_ENV === "development") {
    try {
      // 替换全局String.prototype.split方法
      String.prototype.split = safeSplit;
      console.log("已应用WebSocket安全补丁");
    } catch (error) {
      console.error("应用WebSocket安全补丁失败:", error);
    }
  }
}

/**
 * 监视并拦截WebSocket错误
 * 在发生pattern.split错误时提供更友好的错误处理
 */
export function monitorWebSocketErrors() {
  if (typeof window !== "undefined") {
    const originalOnError = window.onerror;

    window.onerror = function (message, source, lineno, colno, error) {
      // 检测是否为pattern.split错误
      if (
        message &&
        (message.toString().includes("pattern.split") ||
          (error && error.stack && error.stack.includes("pattern.split")))
      ) {
        console.warn("WebSocket错误已被拦截:", message);
        // 阻止错误继续传播
        return true;
      }

      // 否则使用原始的错误处理程序
      if (originalOnError) {
        return originalOnError.call(this, message, source, lineno, colno, error);
      }

      return false;
    };
  }
}

// 默认导出
export default { applyWebSocketPatch, monitorWebSocketErrors };
