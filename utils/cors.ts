import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// 允许的源列表
const allowedOrigins = [
  "http://localhost:3000",
  "http://**************:3000",
  // 生产环境域名，根据实际情况添加
];

// CORS头选项
export const corsHeaders = {
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-CSRF-Token",
  "Access-Control-Allow-Credentials": "true",
  "Access-Control-Max-Age": "86400", // 24小时
};

/**
 * 处理CORS预检请求
 */
export function handleOptions(req: NextRequest) {
  // 获取请求的Origin
  const origin = req.headers.get("origin") || "";
  const isAllowedOrigin = allowedOrigins.includes(origin);

  // 处理预检请求
  if (req.method === "OPTIONS") {
    const headers = {
      ...(isAllowedOrigin && { "Access-Control-Allow-Origin": origin }),
      ...corsHeaders,
    };
    return NextResponse.json({}, { headers, status: 200 });
  }
  return null;
}

/**
 * 向任何响应添加CORS头
 */
export function addCorsHeaders(req: NextRequest, res: NextResponse) {
  const origin = req.headers.get("origin") || "";
  const isAllowedOrigin = allowedOrigins.includes(origin);

  if (isAllowedOrigin) {
    res.headers.set("Access-Control-Allow-Origin", origin);
  }

  // 添加其他CORS头
  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.headers.set(key, value);
  });

  return res;
}

/**
 * 包装API路由处理器，添加CORS支持
 * @param handler API路由处理器函数
 */
export function withCors(handler: (req: NextRequest) => Promise<NextResponse> | NextResponse) {
  return async function (req: NextRequest) {
    // 处理OPTIONS请求
    const optionsResponse = handleOptions(req);
    if (optionsResponse) return optionsResponse;

    // 处理正常请求
    const response = await handler(req);

    // 向响应添加CORS头
    return addCorsHeaders(req, response);
  };
}
