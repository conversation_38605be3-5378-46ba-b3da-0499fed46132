import { Role, ScopeType } from "@prisma/client";
import { SupabaseClient } from "@supabase/supabase-js";

// 角色数据类型定义
export interface UserRole {
  role: Role;
  scope?: ScopeType | string | null;
  scopeType?: ScopeType | string | null;
  scopeId?: string | null;
  alias?: string | null;
}

/**
 * 兼容层辅助函数，便于从旧的getSession()迁移到新的getUser()
 * 这将返回一个与旧API结构相似的对象，但使用新的getUser()API
 */
export async function getSessionCompat(supabase: SupabaseClient) {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  return {
    data: {
      session: user
        ? {
          user: {
            ...user,
            roles: user.user_metadata?.roles || [],
          },
        }
        : null,
    },
  };
}

/**
 * 从用户对象中提取角色信息的辅助函数，并处理可能的不同数据结构
 */
export function getUserRoles(user: any): UserRole[] {
  if (!user) return [];

  // 提取角色数据
  let roleData = user.user_metadata?.roles || user.roles || [];

  // 确保角色始终为数组
  if (!Array.isArray(roleData)) {
    roleData = roleData ? [roleData] : [];
  }

  // 处理不同格式的角色数据
  return roleData.map((r: any): UserRole => {
    // 处理简单字符串角色格式
    if (typeof r === "string") {
      return { role: r as Role, scope: "GLOBAL", scopeType: "GLOBAL" };
    }

    // 处理对象格式
    if (typeof r === "object" && r !== null) {
      // 标准化scope和scopeType字段
      const scope = r.scope || r.scopeType || null;

      return {
        role: r.role as Role,
        scope: scope,
        scopeType: scope,
        scopeId: r.scopeId || null,
        alias: r.alias || null,
      };
    }

    // 未知格式，返回默认值
    return { role: "GUEST" as Role };
  });
}
