import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";

export async function updateSession(request: NextRequest) {
  const supabaseResponse = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // 在 supabaseResponse 中设置 cookie
          supabaseResponse.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: CookieOptions) {
          // 在 supabaseResponse 中移除 cookie
          supabaseResponse.cookies.set({
            name,
            value: "",
            ...options,
          });
        },
      },
    }
  );

  // 重要：避免在 createServerClient 和 supabase.auth.getUser() 之间添加任何逻辑
  // 这样会导致很难调试的用户会话问题
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // 可根据需要执行特定的中间件逻辑
  // 例如:
  // if (!user && !request.nextUrl.pathname.startsWith('/account')) {
  //   const redirectUrl = new URL('/account', request.url);
  //   return NextResponse.redirect(redirectUrl);
  // }

  return supabaseResponse;
}
