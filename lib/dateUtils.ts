/**
 * 统一的日期格式化工具函数
 * 提供ISO标准格式的日期格式化，与review页面保持一致
 */

/**
 * 格式化日期为ISO标准格式："年-月-日 时:分"
 * @param dateInput - 日期输入，可以是Date对象、ISO字符串或null
 * @returns 格式化后的日期字符串，如"2024-12-14 15:30"，无效输入返回"--"
 */
export function formatDateTime(dateInput: string | Date | null): string {
  if (!dateInput) return "--";
  
  try {
    const date = dateInput instanceof Date ? dateInput : new Date(dateInput);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return "--";
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch {
    return "--";
  }
}

/**
 * 格式化日期为仅日期格式："年-月-日"
 * @param dateInput - 日期输入，可以是Date对象、ISO字符串或null
 * @returns 格式化后的日期字符串，如"2024-12-14"，无效输入返回"--"
 */
export function formatDate(dateInput: string | Date | null): string {
  if (!dateInput) return "--";
  
  try {
    const date = dateInput instanceof Date ? dateInput : new Date(dateInput);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return "--";
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch {
    return "--";
  }
}
