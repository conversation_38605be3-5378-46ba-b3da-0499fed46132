/**
 * 主题相关命令集合
 */

import { CommandDefinition, CommandContext, CommandResult } from './registry';
import { globalThemeManager } from '@/lib/terminal/ThemeManager';
import { ThemeName, getThemeNames, getThemeList, isValidThemeName } from '@/lib/terminal/themes';
import { createCommandLink } from '@/lib/terminal/osc8-link-manager';

// 主题命令
const themeCommand: CommandDefinition = {
    name: 'theme',
    description: 'Manage terminal themes',
    category: 'terminal',
    parameters: [
        {
            name: 'action',
            type: 'string',
            description: 'Operation type',
            required: false,
            enum: ['list', 'set', 'preview', 'cancel', 'back', 'current', 'info']
        },
        {
            name: 'name',
            type: 'string',
            description: 'Theme name',
            required: false,
            enum: getThemeNames()
        }
    ],
    examples: [
        'theme',
        'theme action=list',
        'theme action=set name=dark',
        'theme action=preview name=ai',
        'theme action=cancel',
        'theme action=back',
        'theme action=current',
        'theme action=info name=matrix'
    ],
    async execute(args, context): Promise<CommandResult> {
        const { terminal } = context;
        const action = args.action || 'current';

        try {
            switch (action) {
                case 'list':
                    return await handleListThemes(context);

                case 'set':
                    if (!args.name) {
                        return {
                            success: false,
                            error: 'Please specify the theme name, for example: theme action=set name=dark'
                        };
                    }
                    return await handleSetTheme(args.name, context);

                case 'preview':
                    if (!args.name) {
                        return {
                            success: false,
                            error: 'Please specify the theme name to preview, for example: theme action=preview name=ai'
                        };
                    }
                    return await handlePreviewTheme(args.name, context);

                case 'cancel':
                    return await handleCancelPreview(context);

                case 'back':
                    return await handleBackToPrevious(context);

                case 'current':
                    return await handleCurrentTheme(context);

                case 'info':
                    return await handleThemeInfo(args.name, context);

                default:
                    return {
                        success: false,
                        error: `Unknown operation: ${action}`
                    };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Theme operation failed'
            };
        }
    }
};

// 处理列出所有主题
async function handleListThemes(context: CommandContext): Promise<CommandResult> {
    const themes = getThemeList();
    const currentTheme = globalThemeManager.getCurrentThemeName();

    const themeList = themes.map(theme => {
        const isCurrent = theme.name === currentTheme;
        const status = isCurrent ? '(current)' : '';
        const link = createCommandLink(`${theme.name}${status}`, 'theme', { action: 'set', name: theme.name });
        return `  • ${link} - ${theme.description}`;
    }).join('\n');

    const output = `🎨 Available theme list:

${themeList}

Usage instructions:
• Click the theme name to switch
• Or use: theme action=set name=<theme name>
• Preview theme: theme action=preview name=<theme name>
• Cancel preview: theme action=cancel
• Restore previous: theme action=back`;

    return {
        success: true,
        message: output,
        data: { themes, currentTheme },
        terminalOutput: context.source === 'terminal' ? output : undefined,
        shouldUpdateTerminal: context.source === 'terminal'
    };
}

// 处理切换主题
async function handleSetTheme(themeName: string, context: CommandContext): Promise<CommandResult> {
    if (!isValidThemeName(themeName)) {
        return {
            success: false,
            error: `Theme "${themeName}" does not exist. Use theme action=list to view available themes.`
        };
    }

    const oldTheme = globalThemeManager.getCurrentThemeName();
    const success = globalThemeManager.switchTheme(themeName, 'command');

    if (!success) {
        return {
            success: false,
            error: `Switch to theme "${themeName}" failed`
        };
    }

    const output = `✅ Theme switched: ${oldTheme} → ${themeName}`;

    return {
        success: true,
        message: output,
        data: { oldTheme, newTheme: themeName },
        // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
        // terminalOutput: context.source === 'terminal' ? output : undefined,
        shouldUpdateTerminal: false
    };
}

// 处理预览主题
async function handlePreviewTheme(themeName: string, context: CommandContext): Promise<CommandResult> {
    if (!isValidThemeName(themeName)) {
        return {
            success: false,
            error: `Theme "${themeName}" does not exist. Use theme action=list to view available themes.`
        };
    }

    const success = globalThemeManager.previewTheme(themeName);

    if (!success) {
        return {
            success: false,
            error: `Preview theme "${themeName}" failed`
        };
    }

    const cancelLink = createCommandLink('Cancel preview', 'theme', { action: 'cancel' });
    const applyLink = createCommandLink('Apply this theme', 'theme', { action: 'set', name: themeName });

    const output = `🔍 Previewing theme: ${themeName}

${cancelLink} | ${applyLink}`;

    return {
        success: true,
        message: output,
        data: { previewTheme: themeName },
        terminalOutput: context.source === 'terminal' ? output : undefined,
        shouldUpdateTerminal: context.source === 'terminal'
    };
}

// 处理取消预览
async function handleCancelPreview(context: CommandContext): Promise<CommandResult> {
    globalThemeManager.cancelPreview();
    const currentTheme = globalThemeManager.getCurrentThemeName();

    const output = `✅ Canceled preview, restored to theme: ${currentTheme}`;

    return {
        success: true,
        message: output,
        terminalOutput: context.source === 'terminal' ? output : undefined,
        shouldUpdateTerminal: context.source === 'terminal'
    };
}

// 处理返回上一个主题
async function handleBackToPrevious(context: CommandContext): Promise<CommandResult> {
    const success = globalThemeManager.switchToPreviousTheme();

    if (!success) {
        return {
            success: false,
            error: 'No previous theme history'
        };
    }

    const currentTheme = globalThemeManager.getCurrentThemeName();
    const output = `✅ 已恢复到上一个主题: ${currentTheme}`;

    return {
        success: true,
        message: output,
        data: { currentTheme },
        // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
        // terminalOutput: context.source === 'terminal' ? output : undefined,
        shouldUpdateTerminal: false
    };
}

// 处理显示当前主题
async function handleCurrentTheme(context: CommandContext): Promise<CommandResult> {
    const currentTheme = globalThemeManager.getCurrentThemeName();
    const themeInfo = globalThemeManager.getThemeInfo();
    const history = globalThemeManager.getThemeHistory();

    const historyText = history.length > 0
        ? `Recently used: ${history.slice(-3).map(t => t.name).join(' → ')}`
        : 'No history';

    const listLink = createCommandLink('View all themes', 'theme', { action: 'list' });
    const infoLink = createCommandLink('Detailed information', 'theme', { action: 'info', name: currentTheme });

    const output = `🎨 Current theme: ${currentTheme}
📋 Description: ${themeInfo?.description || 'No description'}
📚 ${historyText}

${listLink} | ${infoLink}`;

    return {
        success: true,
        message: output,
        data: { currentTheme, themeInfo, history },
        terminalOutput: context.source === 'terminal' ? output : undefined,
        shouldUpdateTerminal: context.source === 'terminal'
    };
}

// 处理主题详细信息
async function handleThemeInfo(themeName: string | undefined, context: CommandContext): Promise<CommandResult> {
    const targetTheme = themeName || globalThemeManager.getCurrentThemeName();
    const themeInfo = globalThemeManager.getThemeInfo(targetTheme as ThemeName);

    if (!themeInfo) {
        return {
            success: false,
            error: `Theme "${targetTheme}" does not exist`
        };
    }

    const setLink = createCommandLink('Switch to this theme', 'theme', { action: 'set', name: targetTheme });
    const previewLink = createCommandLink('Preview this theme', 'theme', { action: 'preview', name: targetTheme });

    const output = `🎨 Theme information: ${themeInfo.name}

📋 Description: ${themeInfo.description}
🎨 Color configuration:
  • Background color: ${themeInfo.colors.background}
  • Foreground color: ${themeInfo.colors.foreground}
  • Cursor color: ${themeInfo.colors.cursor}
  • Selection background: ${themeInfo.colors.selectionBackground}

${setLink} | ${previewLink}`;

    return {
        success: true,
        message: output,
        data: themeInfo,
        terminalOutput: context.source === 'terminal' ? output : undefined,
        shouldUpdateTerminal: context.source === 'terminal'
    };
}

// 注册主题命令
export function registerThemeCommands() {
    const { commandRegistry } = require('./registry');
    commandRegistry.register(themeCommand);
} 