/**
 * 命令系统初始化中心
 * 注册所有可用命令并导出核心接口
 */

import { commandRegistry, commandExecutor, CommandDefinition, CommandContext, CommandResult } from './registry';
import { registerWindowCommands } from './window-commands';
import { registerTerminalCommands } from './terminal-commands';
import { registerThemeCommands } from './theme-commands';

// 系统命令
const helpCommand: CommandDefinition = {
    name: 'help',
    description: '显示帮助信息',
    category: 'system',
    parameters: [
        {
            name: 'command',
            type: 'string',
            description: '查看特定命令的帮助',
            required: false
        }
    ],
    examples: ['help', 'help command=create_window'],
    async execute(args, context): Promise<CommandResult> {
        if (args.command) {
            const command = commandRegistry.get(args.command);
            if (!command) {
                return {
                    success: false,
                    error: `命令 "${args.command}" 不存在`
                };
            }

            const helpText = `📋 ${command.name} - ${command.description}

🔧 参数:
${command.parameters.map(p =>
                `  • ${p.name} (${p.type})${p.required ? ' *必需*' : ' 可选'}: ${p.description}${p.enum ? `\n    可选值: ${p.enum.join(', ')}` : ''}`
            ).join('\n')}

💡 示例:
${command.examples.map(ex => `  ${ex}`).join('\n')}`;

            return {
                success: true,
                message: helpText,
                data: command,
                // 🔇 保留help命令的详细输出 - 这是有用的帮助信息，不是简单确认
                terminalOutput: context.source === 'terminal' ? helpText : undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        }

        // 显示所有命令
        const allCommands = commandRegistry.getAll();
        const categorized = allCommands.reduce((acc, cmd) => {
            if (!acc[cmd.category]) acc[cmd.category] = [];
            acc[cmd.category].push(cmd);
            return acc;
        }, {} as Record<string, CommandDefinition[]>);

        const helpText = `🎯 可用命令列表:

${Object.entries(categorized).map(([category, commands]) =>
            `📁 ${category.toUpperCase()}:\n${commands.map(cmd => `  • ${cmd.name} - ${cmd.description}`).join('\n')}`
        ).join('\n\n')}

💡 获取特定命令详细信息: help command=<命令名>`;

        return {
            success: true,
            message: helpText,
            data: { categories: categorized, total: allCommands.length },
            terminalOutput: context.source === 'terminal' ? helpText : undefined,
            shouldUpdateTerminal: context.source === 'terminal'
        };
    }
};

// 系统信息命令
const statusCommand: CommandDefinition = {
    name: 'status',
    description: '显示系统状态信息',
    category: 'system',
    parameters: [],
    examples: ['status'],
    async execute(args, context): Promise<CommandResult> {
        const allCommands = commandRegistry.getAll();
        const byCategory = allCommands.reduce((acc, cmd) => {
            acc[cmd.category] = (acc[cmd.category] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const statusInfo = {
            totalCommands: allCommands.length,
            categories: byCategory,
            source: context.source,
            timestamp: new Date().toISOString()
        };

        const statusText = `📊 系统状态:
• 总命令数: ${statusInfo.totalCommands}
• 命令分类: ${Object.entries(byCategory).map(([cat, count]) => `${cat}(${count})`).join(', ')}
• 调用来源: ${context.source}
• 查询时间: ${new Date().toLocaleString()}`;

        return {
            success: true,
            message: statusText,
            data: statusInfo,
            terminalOutput: context.source === 'terminal' ? statusText : undefined,
            shouldUpdateTerminal: context.source === 'terminal'
        };
    }
};

// 初始化所有命令
export function initializeCommands() {
    // 注册系统命令
    commandRegistry.register(helpCommand);
    commandRegistry.register(statusCommand);

    // 注册窗口管理命令
    registerWindowCommands();

    // 注册终端命令
    registerTerminalCommands();

    // 注册主题命令
    registerThemeCommands();

    console.log(`✅ 已注册 ${commandRegistry.getAll().length} 个命令`);
}

// 导出核心接口
export {
    commandRegistry,
    commandExecutor
} from './registry';

export type {
    CommandDefinition,
    CommandContext,
    CommandResult
} from './registry';

// 便捷执行函数
export async function executeCommand(
    commandName: string,
    args: Record<string, any>,
    context: Partial<CommandContext> = {}
): Promise<CommandResult> {
    const fullContext: CommandContext = {
        source: 'api',
        ...context
    };

    return commandExecutor.execute(commandName, args, fullContext);
}

// 获取AI Function Calling Schema
export function getAIFunctionSchema(): any[] {
    return commandRegistry.generateFunctionSchema();
}

// 检查命令是否存在
export function hasCommand(name: string): boolean {
    return commandRegistry.get(name) !== undefined;
}

// 获取命令列表
export function getCommandList(): { name: string; description: string; category: string }[] {
    return commandRegistry.getAll().map(cmd => ({
        name: cmd.name,
        description: cmd.description,
        category: cmd.category
    }));
} 