/**
 * 统一命令注册中心
 * 所有命令在此定义一次，支持terminal和AI两种调用方式
 */

export interface CommandParameter {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'object';
    description: string;
    required?: boolean;
    enum?: string[];
    default?: any;
}

export interface CommandDefinition {
    name: string;
    description: string;
    category: 'system' | 'window' | 'file' | 'ai' | 'utility' | 'terminal' | 'auth' | 'editor';
    parameters: CommandParameter[];
    examples: string[];
    // 执行函数 - 统一接口
    execute: (args: Record<string, any>, context: CommandContext) => Promise<CommandResult>;
}

export interface CommandContext {
    source: 'terminal' | 'ai' | 'api';
    userId?: string;
    sessionId?: string;
    terminal?: any; // terminal instance for terminal calls
    requestId?: string;
}

export interface CommandResult {
    success: boolean;
    message?: string;
    data?: any;
    error?: string;
    // UI相关
    shouldCreateWindow?: boolean;
    windowConfig?: any;
    shouldUpdateTerminal?: boolean;
    terminalOutput?: string;
}

// 命令注册中心
class CommandRegistry {
    private commands = new Map<string, CommandDefinition>();

    register(command: CommandDefinition) {
        this.commands.set(command.name, command);
    }

    get(name: string): CommandDefinition | undefined {
        return this.commands.get(name);
    }

    getAll(): CommandDefinition[] {
        return Array.from(this.commands.values());
    }

    getByCategory(category: string): CommandDefinition[] {
        return Array.from(this.commands.values()).filter(cmd => cmd.category === category);
    }

    // 生成OpenAI Function Calling Schema
    generateFunctionSchema(): any[] {
        return Array.from(this.commands.values()).map(cmd => ({
            type: "function",
            function: {
                name: cmd.name,
                description: cmd.description,
                parameters: {
                    type: "object",
                    properties: cmd.parameters.reduce((props, param) => {
                        props[param.name] = {
                            type: param.type,
                            description: param.description,
                            ...(param.enum ? { enum: param.enum } : {})
                        };
                        return props;
                    }, {} as Record<string, any>),
                    required: cmd.parameters.filter(p => p.required).map(p => p.name)
                }
            }
        }));
    }
}

// 全局注册中心实例
export const commandRegistry = new CommandRegistry();

// 命令执行引擎
export class CommandExecutor {
    async execute(commandName: string, args: Record<string, any>, context: CommandContext): Promise<CommandResult> {
        const command = commandRegistry.get(commandName);

        if (!command) {
            return {
                success: false,
                error: `未知命令: ${commandName}`
            };
        }

        try {
            // 参数验证
            const validationResult = this.validateParameters(command.parameters, args);
            if (!validationResult.valid) {
                return {
                    success: false,
                    error: `参数错误: ${validationResult.error}`
                };
            }

            // 执行命令
            const result = await command.execute(args, context);

            // 后处理：根据context类型进行特殊处理
            return this.postProcess(result, context);
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '命令执行失败'
            };
        }
    }

    private validateParameters(params: CommandParameter[], args: Record<string, any>): { valid: boolean; error?: string } {
        for (const param of params) {
            if (param.required && !(param.name in args)) {
                return { valid: false, error: `缺少必需参数: ${param.name}` };
            }

            if (param.name in args) {
                const value = args[param.name];
                const expectedType = param.type;

                if (expectedType === 'number' && typeof value !== 'number') {
                    if (isNaN(Number(value))) {
                        return { valid: false, error: `参数 ${param.name} 必须是数字` };
                    }
                    args[param.name] = Number(value); // 转换类型
                }

                if (expectedType === 'boolean' && typeof value !== 'boolean') {
                    if (typeof value === 'string') {
                        args[param.name] = value.toLowerCase() === 'true';
                    }
                }

                if (expectedType === 'array' && typeof value === 'string') {
                    try {
                        args[param.name] = JSON.parse(value);
                    } catch (error) {
                        return { valid: false, error: `参数 ${param.name} 必须是有效的JSON数组` };
                    }
                }

                if (expectedType === 'object' && typeof value === 'string') {
                    try {
                        args[param.name] = JSON.parse(value);
                    } catch (error) {
                        return { valid: false, error: `参数 ${param.name} 必须是有效的JSON对象` };
                    }
                }

                if (param.enum && !param.enum.includes(value)) {
                    return { valid: false, error: `参数 ${param.name} 必须是: ${param.enum.join(', ')}` };
                }
            }
        }

        return { valid: true };
    }

    private postProcess(result: CommandResult, context: CommandContext): CommandResult {
        // 根据调用来源进行后处理
        if (context.source === 'terminal' && result.shouldUpdateTerminal) {
            // Terminal特殊处理 - 保留专门的终端输出（如复杂表格、状态信息等）
            if (context.terminal && result.terminalOutput) {
                context.terminal.writeln(result.terminalOutput);
            }
        }

        return result;
    }
}

// 全局执行引擎实例
export const commandExecutor = new CommandExecutor(); 