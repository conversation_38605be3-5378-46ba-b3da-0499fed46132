/**
 * 命令系统初始化
 * 确保所有命令在应用启动时被正确注册
 */

import { initializeCommands } from './index';

// 标记系统是否已初始化
let isInitialized = false;

/**
 * 初始化命令系统（幂等）
 */
export function ensureCommandsInitialized(): void {
    if (isInitialized) {
        return;
    }

    try {
        initializeCommands();
        isInitialized = true;
        console.log('🎯 统一命令系统初始化完成');
    } catch (error) {
        console.error('❌ 命令系统初始化失败:', error);
        throw error;
    }
}

/**
 * 重置初始化状态（用于测试）
 */
export function resetCommandSystemForTesting(): void {
    isInitialized = false;
}

// 自动初始化（如果在浏览器环境中）
if (typeof window !== 'undefined') {
    // 延迟初始化，确保其他模块已加载
    setTimeout(() => {
        try {
            ensureCommandsInitialized();
        } catch (error) {
            console.warn('自动命令初始化失败，将在首次使用时初始化:', error);
        }
    }, 100);
} 