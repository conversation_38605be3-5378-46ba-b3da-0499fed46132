/**
 * 窗口管理命令定义
 */

import { CommandDefinition, CommandContext, CommandResult, commandRegistry } from './registry';
import { globalMessageManager } from '@/lib/floating-window-messages';

// 统一的浮窗服务接口
class WindowService {
    async createWindow(config: any): Promise<{ windowId: string; success: boolean }> {
        // 生成窗口ID
        const windowId = `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // 🔧 先处理默认值，避免 undefined 覆盖
        const cleanConfig = { ...config };

        // 🎯 获取屏幕尺寸用于百分比计算
        let screenWidth = 1920;  // 默认值
        let screenHeight = 1080; // 默认值
        if (typeof window !== 'undefined') {
            screenWidth = window.innerWidth;
            screenHeight = window.innerHeight;
        }

        // 🔢 处理百分比参数 (0.0-1.0 的小数转换为像素值)
        if (cleanConfig.width !== undefined && cleanConfig.width > 0 && cleanConfig.width <= 1) {
            cleanConfig.width = Math.round(screenWidth * cleanConfig.width);
        }
        if (cleanConfig.height !== undefined && cleanConfig.height > 0 && cleanConfig.height <= 1) {
            cleanConfig.height = Math.round(screenHeight * cleanConfig.height);
        }
        if (cleanConfig.x !== undefined && cleanConfig.x >= 0 && cleanConfig.x <= 1) {
            cleanConfig.x = Math.round(screenWidth * cleanConfig.x);
        }
        if (cleanConfig.y !== undefined && cleanConfig.y >= 0 && cleanConfig.y <= 1) {
            cleanConfig.y = Math.round(screenHeight * cleanConfig.y);
        }

        // 只在值为 undefined 时设置默认值
        if (cleanConfig.x === undefined) cleanConfig.x = Math.random() * 200 + 100;
        if (cleanConfig.y === undefined) cleanConfig.y = Math.random() * 200 + 100;
        if (cleanConfig.width === undefined) cleanConfig.width = 400;
        if (cleanConfig.height === undefined) cleanConfig.height = 300;

        // 设置最终配置
        const windowConfig = {
            id: windowId,
            title: cleanConfig.title,
            ...cleanConfig
        };

        // 通过浏览器事件创建窗口
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('ai_create_window', {
                detail: windowConfig
            }));

            // 等待事件处理
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        return { windowId, success: true };
    }



    async listWindows(): Promise<any[]> {
        try {
            return await globalMessageManager.queryAllWindows();
        } catch (error) {
            return [];
        }
    }

    async updateWindow(windowId: string, updates: any): Promise<boolean> {
        try {
            // 🔧 根据更新类型选择不同的操作
            let operation: 'update' | 'move' | 'resize' = 'update';

            // 如果只有位置信息，使用move操作
            if ((updates.x !== undefined || updates.y !== undefined) &&
                updates.width === undefined && updates.height === undefined) {
                operation = 'move';
            }
            // 如果只有尺寸信息，使用resize操作  
            else if ((updates.width !== undefined || updates.height !== undefined) &&
                updates.x === undefined && updates.y === undefined) {
                operation = 'resize';
            }
            // 其他情况使用update操作

            await globalMessageManager.sendWindowOperation(
                windowId,
                operation,
                updates,
                undefined,
                true
            );
            return true;
        } catch (error) {
            console.error(`[WindowService] Update window failed: ${windowId}`, error);
            return false;
        }
    }

    async closeWindow(windowId: string): Promise<boolean> {
        try {
            console.log(`[WindowService] Start closing window: ${windowId}`);

            await globalMessageManager.sendWindowOperation(
                windowId,
                'close',
                undefined,
                undefined,
                true  // 期望响应
            );

            console.log(`[WindowService] Close command sent: ${windowId}`);

            // 等待一段时间确保操作完成
            await new Promise(resolve => setTimeout(resolve, 100));

            console.log(`[WindowService] Window close operation completed: ${windowId}`);
            return true;
        } catch (error) {
            console.error(`[WindowService] Close window failed: ${windowId}`, error);
            return false;
        }
    }
}

const windowService = new WindowService();

// 🔢 通用百分比处理函数
function processPercentageValues(values: any): any {
    // 获取屏幕尺寸
    let screenWidth = 1920;  // 默认值
    let screenHeight = 1080; // 默认值
    if (typeof window !== 'undefined') {
        screenWidth = window.innerWidth;
        screenHeight = window.innerHeight;
    }

    const processed = { ...values };

    // 处理宽度百分比
    if (processed.width !== undefined && processed.width > 0 && processed.width <= 1) {
        processed.width = Math.round(screenWidth * processed.width);
    }
    // 处理高度百分比
    if (processed.height !== undefined && processed.height > 0 && processed.height <= 1) {
        processed.height = Math.round(screenHeight * processed.height);
    }
    // 处理X坐标百分比
    if (processed.x !== undefined && processed.x >= 0 && processed.x <= 1) {
        processed.x = Math.round(screenWidth * processed.x);
    }
    // 处理Y坐标百分比
    if (processed.y !== undefined && processed.y >= 0 && processed.y <= 1) {
        processed.y = Math.round(screenHeight * processed.y);
    }

    return processed;
}

// 窗口创建命令
const createWindowCommand: CommandDefinition = {
    name: 'create_window',
    description: 'Create a new floating window',
    category: 'window',
    parameters: [
        {
            name: 'type',
            type: 'string',
            description: 'Window type',
            required: true,
            enum: ['test', 'img', 'url', 'path']
        },
        {
            name: 'target',
            type: 'string',
            description: 'Target content (URL, image path, etc.)',
            required: false
        },
        {
            name: 'width',
            type: 'number',
            description: 'Window width (pixel value or 0.0-1.0 screen percentage)',
            required: false
        },
        {
            name: 'height',
            type: 'number',
            description: 'Window height (pixel value or 0.0-1.0 screen percentage)',
            required: false
        },
        {
            name: 'x',
            type: 'number',
            description: 'Window X coordinate (pixel value or 0.0-1.0 screen percentage)',
            required: false
        },
        {
            name: 'y',
            type: 'number',
            description: 'Window Y coordinate (pixel value or 0.0-1.0 screen percentage)',
            required: false
        },
        {
            name: 'title',
            type: 'string',
            description: 'Window title',
            required: false
        }
    ],
    examples: [
        'create_window type=test title="Test window"',
        'create_window type=img target="/test.png" width=500',
        'create_window type=url target="https://www.google.com"',
        'create_window type=test width=0.5 height=0.5 x=0.25 y=0.25',
        'create_window type=path target=/docs width=0.8 height=0.6'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            let windowConfig: any;

            switch (args.type) {
                case 'test':
                    windowConfig = {
                        title: args.title || 'Test window',
                        content: 'Hello World! This is a test floating window.\nYou can:\n- Drag the title bar to move the window\n- Drag the corners to resize the window\n- Click the close button to close the window',
                        // 🔧 为测试窗口提供合理的默认值
                        width: args.width,
                        height: args.height,
                        x: args.x,
                        y: args.y
                        // 注意：undefined 值将由 WindowService.createWindow() 处理
                    };
                    break;

                case 'img':
                    if (!args.target) {
                        return {
                            success: false,
                            error: 'Image window needs to specify image path'
                        };
                    }
                    windowConfig = {
                        type: 'image',
                        title: args.title || `Image: ${args.target.split('/').pop() || 'Unknown'}`,
                        imageUrl: args.target,
                        imageAlt: args.title || args.target,
                        width: args.width,
                        height: args.height,
                        x: args.x,
                        y: args.y
                    };
                    break;

                case 'url':
                case 'path':
                    if (!args.target) {
                        return {
                            success: false,
                            error: 'Web page window needs to specify URL or path'
                        };
                    }

                    let finalUrl = args.target;
                    let isExternal = false;

                    // 判断是否为外部链接
                    if (args.target.startsWith('http://') || args.target.startsWith('https://')) {
                        isExternal = true;
                        // 🔄 使用代理模式访问外部链接
                        finalUrl = `/api/proxy?url=${encodeURIComponent(args.target)}`;
                    } else if (args.type === 'path' && !args.target.startsWith('/')) {
                        finalUrl = `/${args.target}`;
                    }

                    // 对于内部路径，确保是完整的内部URL
                    if (!isExternal) {
                        if (!finalUrl.startsWith('/')) {
                            return {
                                success: false,
                                error: 'Please use a valid internal path, such as: /terminal, /editor/myproject, etc.'
                            };
                        }
                        finalUrl = `${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}${finalUrl}`;
                    }

                    windowConfig = {
                        title: args.title || (isExternal ? `${args.target}` : `${args.target}`),
                        url: finalUrl,
                        originalUrl: isExternal ? args.target : undefined, // 保存原始URL用于显示
                        width: args.width,
                        height: args.height,
                        x: args.x,
                        y: args.y
                    };
                    break;

                default:
                    return {
                        success: false,
                        error: `Unsupported window type: ${args.type}`
                    };
            }

            const result = await windowService.createWindow(windowConfig);

            return {
                success: result.success,
                message: `✅ ${windowConfig.title} created`,
                data: {
                    windowId: result.windowId,
                    config: windowConfig
                },
                // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
                // terminalOutput: context.source === 'terminal' ? `✅ ${windowConfig.title} 已创建` : undefined,
                shouldUpdateTerminal: false
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to create window'
            };
        }
    }
};

// 窗口列表命令
const listWindowsCommand: CommandDefinition = {
    name: 'list_windows',
    description: 'List all active floating windows (including workspace dimensions and window position information)',
    category: 'window',
    parameters: [
        {
            name: 'detailed',
            type: 'boolean',
            description: 'Display detailed information',
            required: false,
            default: true
        }
    ],
    examples: ['list_windows', 'list_windows detailed=false'],
    async execute(args, context): Promise<CommandResult> {
        try {
            const windows = await windowService.listWindows();
            const detailed = args.detailed !== false; // 默认为true

            // 获取浏览器窗口尺寸信息
            let viewportInfo = {
                width: 0,
                height: 0,
                innerWidth: 0,
                innerHeight: 0,
                screenWidth: 0,
                screenHeight: 0
            };

            if (typeof window !== 'undefined') {
                viewportInfo = {
                    width: window.outerWidth || 0,
                    height: window.outerHeight || 0,
                    innerWidth: window.innerWidth || 0,
                    innerHeight: window.innerHeight || 0,
                    screenWidth: window.screen?.width || 0,
                    screenHeight: window.screen?.height || 0
                };
            }

            // 构建详细的窗口信息
            const windowsWithDetails = windows.map((w, i) => {
                const x = w.x || 0;
                const y = w.y || 0;
                const width = w.width || 0;
                const height = w.height || 0;
                const zIndex = w.zIndex || 0;

                return {
                    ...w,
                    index: i + 1,
                    position: { x, y },
                    size: { width, height },
                    zIndex,
                    isVisible: w.isVisible !== false,
                    isClosing: w.isClosing === true,
                    // 计算窗口边界
                    bounds: {
                        left: x,
                        top: y,
                        right: x + width,
                        bottom: y + height
                    },
                    // 检查是否在视口内
                    inViewport: {
                        fullyVisible: x >= 0 && y >= 0 &&
                            (x + width) <= viewportInfo.innerWidth &&
                            (y + height) <= viewportInfo.innerHeight,
                        partiallyVisible: (x + width) > 0 && (y + height) > 0 &&
                            x < viewportInfo.innerWidth &&
                            y < viewportInfo.innerHeight
                    }
                };
            });

            // 按zIndex排序（最高的在前）
            const sortedWindows = windowsWithDetails.sort((a, b) => b.zIndex - a.zIndex);

            if (context.source === 'terminal' && detailed) {
                // 构建详细的终端输出
                const workspaceInfo = `\x1b[96m Workspace Information:\x1b[0m
  Browser Window: \x1b[94m${viewportInfo.width} × ${viewportInfo.height}\x1b[0m px (External Size)
  Visible Area: \x1b[94m${viewportInfo.innerWidth} × ${viewportInfo.innerHeight}\x1b[0m px (Internal Size)
  Screen Resolution: \x1b[94m${viewportInfo.screenWidth} × ${viewportInfo.screenHeight}\x1b[0m px`;

                const windowsList = sortedWindows.length > 0 ?
                    sortedWindows.map((w) => {
                        const statusIcon = w.isClosing ? '\x1b[31m🗑️\x1b[0m' :
                            !w.isVisible ? '\x1b[33m👁️\x1b[0m' :
                                w.inViewport.fullyVisible ? '\x1b[32m✅\x1b[0m' :
                                    w.inViewport.partiallyVisible ? '\x1b[33m⚠️\x1b[0m' : '\x1b[31m❌\x1b[0m';

                        const zIndexInfo = w.zIndex > 0 ? ` (z:${w.zIndex})` : '';
                        const visibilityInfo = w.inViewport.fullyVisible ? '\x1b[32mFully Visible\x1b[0m' :
                            w.inViewport.partiallyVisible ? '\x1b[33mPartially Visible\x1b[0m' :
                                '\x1b[31mOut of Viewport\x1b[0m';

                        return `  ${statusIcon} ${w.index}. \x1b[97m${w.title}\x1b[0m${zIndexInfo}
     ID: \x1b[90m${w.id}\x1b[0m
     Position: \x1b[94m(${w.position.x}, ${w.position.y})\x1b[0m | Size: \x1b[94m${w.size.width} × ${w.size.height}\x1b[0m | ${visibilityInfo}`;
                    }).join('\n') : '  \x1b[90mNo active windows\x1b[0m';

                const summary = `\x1b[96m Layout Overview:\x1b[0m
  Active Windows: \x1b[94m${windows.length}\x1b[0m
  Visible Windows: \x1b[32m${sortedWindows.filter(w => w.isVisible && !w.isClosing).length}\x1b[0m
  Fully Visible: \x1b[32m${sortedWindows.filter(w => w.inViewport.fullyVisible).length}\x1b[0m
  Partially Visible: \x1b[33m${sortedWindows.filter(w => w.inViewport.partiallyVisible && !w.inViewport.fullyVisible).length}\x1b[0m
  Out of Viewport: \x1b[31m${sortedWindows.filter(w => !w.inViewport.partiallyVisible).length}\x1b[0m`;

                const terminalOutput = `${workspaceInfo}

\x1b[96m Floating Window List (${windows.length} windows, sorted by zIndex):\x1b[0m
${windowsList}

${summary}

\x1b[90m💡 Status: ✅Fully Visible ⚠️Partially Visible ❌Out of Viewport 👁️Hidden 🗑️Closing\x1b[0m`;

                return {
                    success: true,
                    message: `Found ${windows.length} active windows`,
                    data: {
                        windows: sortedWindows,
                        count: windows.length,
                        viewport: viewportInfo,
                        summary: {
                            total: windows.length,
                            visible: sortedWindows.filter(w => w.isVisible && !w.isClosing).length,
                            fullyVisible: sortedWindows.filter(w => w.inViewport.fullyVisible).length,
                            partiallyVisible: sortedWindows.filter(w => w.inViewport.partiallyVisible && !w.inViewport.fullyVisible).length,
                            outOfViewport: sortedWindows.filter(w => !w.inViewport.partiallyVisible).length
                        }
                    },
                    terminalOutput,
                    shouldUpdateTerminal: true
                };
            } else {
                // 简化输出或非终端调用
                const simpleOutput = context.source === 'terminal' ?
                    `📋 Active Windows (${windows.length} windows):\n${sortedWindows.map((w) => `  ${w.index}. ${w.title} (${w.id}) - ${w.position.x},${w.position.y} | ${w.size.width}×${w.size.height}`).join('\n')}` :
                    undefined;

                return {
                    success: true,
                    message: `Found ${windows.length} active windows`,
                    data: {
                        windows: sortedWindows,
                        count: windows.length,
                        viewport: viewportInfo
                    },
                    terminalOutput: simpleOutput,
                    shouldUpdateTerminal: context.source === 'terminal'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: 'Failed to get window list: ' + (error instanceof Error ? error.message : 'Unknown error')
            };
        }
    }
};

// 窗口移动命令
const moveWindowCommand: CommandDefinition = {
    name: 'move_window',
    description: 'Move the specified window position',
    category: 'window',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Window ID',
            required: true
        },
        {
            name: 'x',
            type: 'number',
            description: 'New X coordinate (pixel value or 0.0-1.0 screen percentage)',
            required: false
        },
        {
            name: 'y',
            type: 'number',
            description: 'New Y coordinate (pixel value or 0.0-1.0 screen percentage)',
            required: false
        },
        {
            name: 'duration',
            type: 'number',
            description: 'Animation duration (milliseconds)',
            required: false
        }
    ],
    examples: [
        'move_window windowId=abc123 x=100 y=200',
        'move_window windowId=abc123 x=100 y=200 duration=500',
        'move_window windowId=abc123 x=0.5 y=0.3',
        'move_window windowId=abc123 x=0.1 y=0.1 duration=300'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const updates: any = {};
            if (args.x !== undefined) updates.x = args.x;
            if (args.y !== undefined) updates.y = args.y;
            if (args.duration !== undefined) updates.duration = args.duration;

            // 🔢 处理百分比值
            const processedUpdates = processPercentageValues(updates);

            const success = await windowService.updateWindow(args.windowId, processedUpdates);

            if (success) {
                return {
                    success: true,
                    message: `✅ Window moved`,
                    data: { windowId: args.windowId, updates },
                    // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
                    // terminalOutput: context.source === 'terminal' ? `✅ 窗口 ${args.windowId} 已移动` : undefined,
                    shouldUpdateTerminal: false
                };
            } else {
                return {
                    success: false,
                    error: 'Failed to move window'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to move window'
            };
        }
    }
};

// 关闭窗口命令
const closeWindowCommand: CommandDefinition = {
    name: 'close_window',
    description: 'Close the specified window',
    category: 'window',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Window ID to close',
            required: true
        }
    ],
    examples: ['close_window windowId=abc123'],
    async execute(args, context): Promise<CommandResult> {
        try {
            const success = await windowService.closeWindow(args.windowId);

            if (success) {
                return {
                    success: true,
                    message: `✅ Window closed`,
                    data: { windowId: args.windowId },
                    // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
                    // terminalOutput: context.source === 'terminal' ? `✅ 窗口 ${args.windowId} 已关闭` : undefined,
                    shouldUpdateTerminal: false
                };
            } else {
                return {
                    success: false,
                    error: 'Failed to close window'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to close window'
            };
        }
    }
};

// 批量窗口创建命令
const createMultipleWindowsCommand: CommandDefinition = {
    name: 'create_multiple_windows',
    description: 'Create multiple floating windows in batch',
    category: 'window',
    parameters: [
        {
            name: 'count',
            type: 'number',
            description: 'Number of windows to create',
            required: true
        },
        {
            name: 'type',
            type: 'string',
            description: 'Window type',
            required: false,
            enum: ['test', 'img', 'url', 'path'],
            default: 'test'
        },
        {
            name: 'title_prefix',
            type: 'string',
            description: 'Window title prefix',
            required: false,
            default: '窗口'
        },
        {
            name: 'spacing',
            type: 'number',
            description: 'Window spacing',
            required: false,
            default: 50
        },
        {
            name: 'start_x',
            type: 'number',
            description: 'Starting X coordinate',
            required: false,
            default: 100
        },
        {
            name: 'start_y',
            type: 'number',
            description: 'Starting Y coordinate',
            required: false,
            default: 100
        }
    ],
    examples: [
        'create_multiple_windows count=3',
        'create_multiple_windows count=5 title_prefix="Data" spacing=100',
        'create_multiple_windows count=3 start_x=200 start_y=150 spacing=80'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const count = Math.min(args.count || 1, 10); // 限制最大10个窗口
            const type = args.type || 'test';
            const titlePrefix = args.title_prefix || 'window ';
            const spacing = args.spacing || 50;
            const startX = args.start_x || 100;
            const startY = args.start_y || 100;

            const createdWindows = [];
            const errors = [];

            for (let i = 0; i < count; i++) {
                try {
                    const windowConfig = {
                        type: type,
                        title: `${titlePrefix}${i + 1}`,
                        x: startX + (i * spacing),
                        y: startY + (i * Math.floor(spacing / 2)),
                        width: 400,
                        height: 300
                    };

                    // 调用基础的窗口创建逻辑
                    const result = await createWindowCommand.execute(windowConfig, context);

                    if (result.success) {
                        createdWindows.push({
                            index: i + 1,
                            windowId: result.data?.windowId,
                            title: windowConfig.title,
                            position: { x: windowConfig.x, y: windowConfig.y }
                        });
                    } else {
                        errors.push(`Window ${i + 1}: ${result.error}`);
                    }

                    // 添加小延迟避免创建过快
                    if (i < count - 1) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                } catch (error) {
                    errors.push(`Window ${i + 1}: ${error instanceof Error ? error.message : 'Failed to create'}`);
                }
            }

            const successCount = createdWindows.length;
            const message = errors.length > 0
                ? `✅ Successfully created ${successCount} windows, ${errors.length} failed`
                : `✅ Successfully created ${successCount} windows`;

            return {
                success: successCount > 0,
                message: message,
                data: {
                    successCount,
                    totalCount: count,
                    createdWindows,
                    errors
                },
                // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
                // terminalOutput: context.source === 'terminal'
                //     ? `${message}\n${createdWindows.map(w => `  • ${w.title} - 位置(${w.position.x}, ${w.position.y})`).join('\n')}`
                //     : undefined,
                shouldUpdateTerminal: false
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to create multiple windows'
            };
        }
    }
};

// 🆕 智能调整当前窗口尺寸命令
const currentWindowResizeCommand: CommandDefinition = {
    name: 'current_window_resize',
    description: 'Smartly adjust the size of the current active window (no need to specify window ID)',
    category: 'window',
    parameters: [
        {
            name: 'width',
            type: 'number',
            description: 'New width (pixel value or 0.0-1.0 screen percentage)',
            required: true
        },
        {
            name: 'height',
            type: 'number',
            description: 'New height (pixel value or 0.0-1.0 screen percentage)',
            required: true
        },
        {
            name: 'duration',
            type: 'number',
            description: 'Animation duration (milliseconds)',
            required: false,
            default: 300
        },
        {
            name: 'easing',
            type: 'string',
            description: 'Easing function',
            required: false,
            enum: ['ease-out', 'ease-in', 'ease-in-out', 'linear'],
            default: 'ease-out'
        }
    ],
    examples: [
        'current_window_resize width=500 height=500',
        'current_window_resize width=800 height=600 duration=400 easing=ease-in-out',
        'current_window_resize width=0.6 height=0.8',
        'current_window_resize width=0.5 height=0.5 duration=500'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            // 1. 获取所有窗口
            const windows = await windowService.listWindows();

            if (windows.length === 0) {
                return {
                    success: false,
                    error: 'No window found to adjust'
                };
            }

            // 2. 智能选择窗口（选择z-index最高的可见窗口）
            const targetWindow = windows
                .filter(w => w.isVisible && !w.isClosing)
                .sort((a, b) => (b.zIndex || 0) - (a.zIndex || 0))[0];

            if (!targetWindow) {
                return {
                    success: false,
                    error: 'No visible active window found'
                };
            }

            // 3. 执行resize操作
            const updates = {
                width: args.width,
                height: args.height,
                duration: args.duration || 300,
                easing: args.easing || 'ease-out'
            };

            // 🔢 处理百分比值
            const processedUpdates = processPercentageValues(updates);

            const success = await windowService.updateWindow(targetWindow.id, processedUpdates);

            if (success) {
                return {
                    success: true,
                    message: `✅ Window "${targetWindow.title}" resized to ${args.width} × ${args.height}`,
                    data: {
                        windowId: targetWindow.id,
                        windowTitle: targetWindow.title,
                        updates
                    },
                    // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
                    // terminalOutput: context.source === 'terminal' ?
                    //     `✅ 窗口 "${targetWindow.title}" 已调整为 ${args.width} × ${args.height}` : undefined,
                    shouldUpdateTerminal: false
                };
            } else {
                return {
                    success: false,
                    error: 'Failed to resize window'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to resize window'
            };
        }
    }
};

// 🆕 智能移动当前窗口命令
const currentWindowMoveCommand: CommandDefinition = {
    name: 'current_window_move',
    description: 'Smartly move the position of the current active window (no need to specify window ID)',
    category: 'window',
    parameters: [
        {
            name: 'x',
            type: 'number',
            description: 'New X coordinate (pixel value or 0.0-1.0 screen percentage)',
            required: false
        },
        {
            name: 'y',
            type: 'number',
            description: 'New Y coordinate (pixel value or 0.0-1.0 screen percentage)',
            required: false
        },
        {
            name: 'duration',
            type: 'number',
            description: 'Animation duration (milliseconds)',
            required: false,
            default: 300
        },
        {
            name: 'easing',
            type: 'string',
            description: 'Easing function',
            required: false,
            enum: ['ease-out', 'ease-in', 'ease-in-out', 'linear'],
            default: 'ease-out'
        }
    ],
    examples: [
        'current_window_move x=100 y=200',
        'current_window_move x=300 duration=500 easing=ease-in-out',
        'current_window_move x=0.5 y=0.2',
        'current_window_move x=0.8 duration=400'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            // 1. 获取所有窗口
            const windows = await windowService.listWindows();

            if (windows.length === 0) {
                return {
                    success: false,
                    error: 'No window found to move'
                };
            }

            // 2. 智能选择窗口（选择z-index最高的可见窗口）
            const targetWindow = windows
                .filter(w => w.isVisible && !w.isClosing)
                .sort((a, b) => (b.zIndex || 0) - (a.zIndex || 0))[0];

            if (!targetWindow) {
                return {
                    success: false,
                    error: 'No visible active window found'
                };
            }

            // 3. 构建更新数据（只更新指定的坐标）
            const updates: any = {
                duration: args.duration || 300,
                easing: args.easing || 'ease-out'
            };

            if (args.x !== undefined) updates.x = args.x;
            if (args.y !== undefined) updates.y = args.y;

            if (args.x === undefined && args.y === undefined) {
                return {
                    success: false,
                    error: 'Please specify at least x or y coordinates'
                };
            }

            // 4. 执行move操作
            // 🔢 处理百分比值
            const processedUpdates = processPercentageValues(updates);

            const success = await windowService.updateWindow(targetWindow.id, processedUpdates);

            if (success) {
                const positionText = [
                    args.x !== undefined ? `x: ${args.x}` : null,
                    args.y !== undefined ? `y: ${args.y}` : null
                ].filter(Boolean).join(', ');

                return {
                    success: true,
                    message: `✅ Window "${targetWindow.title}" moved to (${positionText})`,
                    data: {
                        windowId: targetWindow.id,
                        windowTitle: targetWindow.title,
                        updates
                    },
                    // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
                    // terminalOutput: context.source === 'terminal' ?
                    //     `✅ 窗口 "${targetWindow.title}" 已移动到 (${positionText})` : undefined,
                    shouldUpdateTerminal: false
                };
            } else {
                return {
                    success: false,
                    error: 'Failed to move window'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to move window'
            };
        }
    }
};

// 关闭所有窗口命令
const closeAllWindowsCommand: CommandDefinition = {
    name: 'close_all_windows',
    description: 'Close all active windows',
    category: 'window',
    parameters: [],
    examples: ['close_all_windows'],
    async execute(args, context): Promise<CommandResult> {
        try {
            // 首先获取所有窗口
            const windows = await windowService.listWindows();

            if (windows.length === 0) {
                return {
                    success: true,
                    message: 'No windows to close',
                    data: { closedCount: 0, totalCount: 0 },
                    // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
                    // terminalOutput: context.source === 'terminal' ? '📭 当前没有活动窗口' : undefined,
                    shouldUpdateTerminal: false
                };
            }

            // 逐个关闭所有窗口
            let successCount = 0;
            const errors: string[] = [];

            for (const window of windows) {
                try {
                    const success = await windowService.closeWindow(window.id);
                    if (success) {
                        successCount++;
                    } else {
                        errors.push(`Window ${window.id} closed failed`);
                    }
                } catch (error) {
                    errors.push(`Window ${window.id} closed exception: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }

            const message = `Closed ${successCount}/${windows.length} windows`;
            // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
            // const terminalOutput = context.source === 'terminal' ?
            //     `✅ ${message}${errors.length > 0 ? '\n⚠️ 部分窗口关闭失败:\n' + errors.join('\n') : ''}` :
            //     undefined;

            return {
                success: successCount > 0,
                message: errors.length === 0 ? `✅ ${message}` : `⚠️ ${message}, but ${errors.length} failed`,
                data: {
                    closedCount: successCount,
                    totalCount: windows.length,
                    errors: errors.length > 0 ? errors : undefined
                },
                // terminalOutput,
                shouldUpdateTerminal: false
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to close all windows'
            };
        }
    }
};

// 🆕 DOM查询命令
const domQueryCommand: CommandDefinition = {
    name: 'window_dom_query',
    description: 'Query the attribute value of the DOM element in the floating window',
    category: 'window',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Target window ID',
            required: true
        },
        {
            name: 'selector',
            type: 'string',
            description: 'CSS selector',
            required: true
        },
        {
            name: 'attribute',
            type: 'string',
            description: 'Attribute name to query, default textContent',
            required: false,
            default: 'textContent'
        }
    ],
    examples: [
        'window_dom_query windowId=win-123 selector=".title" attribute=textContent',
        'window_dom_query windowId=win-123 selector="#editor" attribute=value'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, selector, attribute = 'textContent' } = args;

            const result = await globalMessageManager.sendDOMQuery(windowId, selector, attribute);

            return {
                success: true,
                message: `✅ DOM query successful`,
                data: { selector, attribute, result },
                terminalOutput: context.source === 'terminal' ?
                    `🔍 DOM query result:\nSelector: ${selector}\nAttribute: ${attribute}\nContent: ${JSON.stringify(result, null, 2)}` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'DOM query failed'
            };
        }
    }
};

// 🆕 DOM更新命令
const domUpdateCommand: CommandDefinition = {
    name: 'window_dom_update',
    description: 'Update the attribute or content of the DOM element in the floating window',
    category: 'window',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Target window ID',
            required: true
        },
        {
            name: 'selector',
            type: 'string',
            description: 'CSS selector',
            required: true
        },
        {
            name: 'content',
            type: 'string',
            description: 'New content',
            required: true
        },
        {
            name: 'attribute',
            type: 'string',
            description: 'Attribute name to update, default textContent',
            required: false,
            default: 'textContent'
        }
    ],
    examples: [
        'window_dom_update windowId=win-123 selector="#title" content="New title" attribute=textContent',
        'window_dom_update windowId=win-123 selector=".btn" content="Click me" attribute=innerHTML'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, selector, content, attribute = 'textContent' } = args;

            const result = await globalMessageManager.sendDOMUpdate(windowId, selector, content, attribute);

            return {
                success: true,
                message: `✅ DOM update successful`,
                data: { selector, attribute, content, result },
                terminalOutput: context.source === 'terminal' ?
                    `✅ DOM update successful:\nSelector: ${selector}\nAttribute: ${attribute}\nNew content: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'DOM update failed'
            };
        }
    }
};

// 🆕 CodeMirror内容读取命令
const codemirrorReadCommand: CommandDefinition = {
    name: 'codemirror_read_content',
    description: 'Read the content of the CodeMirror editor',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'from',
            type: 'number',
            description: 'Starting position',
            required: false
        },
        {
            name: 'to',
            type: 'number',
            description: 'End position',
            required: false
        }
    ],
    examples: [
        'codemirror_read_content windowId=editor-123',
        'codemirror_read_content windowId=editor-123 from=0 to=100'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, from, to } = args;

            const range = (from !== undefined || to !== undefined) ? { from, to } : undefined;
            const content = await globalMessageManager.readCodeMirrorContent(windowId, range);

            const preview = content.length > 100 ? content.substring(0, 100) + '...' : content;

            return {
                success: true,
                message: `✅ Editor content read successful${range ? ` (${range.from || 0}-${range.to || 'end'})` : ''}: "${preview}"`,
                data: {
                    content: content,
                    text: content, // 兼容性字段
                    length: content.length,
                    range: range,
                    preview: preview
                },
                terminalOutput: context.source === 'terminal' ?
                    `📝 Editor content:\n${content.substring(0, 500)}${content.length > 500 ? '\n...(content too long, truncated)' : ''}` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to read editor content'
            };
        }
    }
};

// 🆕 CodeMirror内容写入命令
const codemirrorWriteCommand: CommandDefinition = {
    name: 'codemirror_write_content',
    description: 'Modify the content of the CodeMirror editor document',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'changes',
            type: 'array',
            description: 'Change array, each element contains from, to, insert fields',
            required: true
        },
        {
            name: 'userEvent',
            type: 'string',
            description: 'User event identifier',
            required: false
        },
        {
            name: 'scrollIntoView',
            type: 'boolean',
            description: 'Whether to scroll to the view',
            required: false
        }
    ],
    examples: [
        'codemirror_write_content windowId=editor-123 changes=[{"from":0,"to":0,"insert":"Hello World"}]',
        'codemirror_write_content windowId=editor-123 changes=[{"from":0,"to":5,"insert":"Hi"}] scrollIntoView=true'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, changes, userEvent, scrollIntoView } = args;

            const options = {
                userEvent,
                scrollIntoView
            };

            const result = await globalMessageManager.writeCodeMirrorContent(windowId, changes, options);

            return {
                success: true,
                message: `✅ Editor content modified successfully`,
                data: { changes, options, result },
                terminalOutput: context.source === 'terminal' ?
                    `✅ Editor content modified (${changes.length} changes)` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to modify editor content'
            };
        }
    }
};

// 🆕 增强的窗口创建命令（支持样式）
const createStyledWindowCommand: CommandDefinition = {
    name: 'create_styled_window',
    description: 'Create a floating window with custom styles',
    category: 'window',
    parameters: [
        {
            name: 'type',
            type: 'string',
            description: 'Window type',
            required: true,
            enum: ['test', 'img', 'url', 'path', 'styled']
        },
        {
            name: 'target',
            type: 'string',
            description: 'Target content (URL, image path, etc.)',
            required: false
        },
        {
            name: 'width',
            type: 'number',
            description: 'Window width',
            required: false
        },
        {
            name: 'height',
            type: 'number',
            description: 'Window height',
            required: false
        },
        {
            name: 'x',
            type: 'number',
            description: 'Window X coordinate',
            required: false
        },
        {
            name: 'y',
            type: 'number',
            description: 'Window Y coordinate',
            required: false
        },
        {
            name: 'title',
            type: 'string',
            description: 'Window title',
            required: false
        },
        {
            name: 'backgroundColor',
            type: 'string',
            description: 'Background color (CSS color value)',
            required: false
        },
        {
            name: 'textColor',
            type: 'string',
            description: 'Text color (CSS color value)',
            required: false
        },
        {
            name: 'borderColor',
            type: 'string',
            description: 'Border color (CSS color value)',
            required: false
        },
        {
            name: 'borderWidth',
            type: 'string',
            description: 'Border width',
            required: false
        },
        {
            name: 'borderRadius',
            type: 'string',
            description: 'Corner radius',
            required: false
        },
        {
            name: 'customCSS',
            type: 'string',
            description: 'Custom CSS styles',
            required: false
        }
    ],
    examples: [
        'create_styled_window type=styled title="Red window" width=500 height=300 backgroundColor=red',
        'create_styled_window type=styled backgroundColor="#ff0000" textColor=white borderRadius=10px',
        'create_styled_window type=test backgroundColor=lightblue borderColor=blue borderWidth=2px'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            let windowConfig: any;

            // 构建样式对象
            const styles: any = {};
            if (args.backgroundColor) styles.backgroundColor = args.backgroundColor;
            if (args.textColor) styles.color = args.textColor;
            if (args.borderColor) styles.borderColor = args.borderColor;
            if (args.borderWidth) styles.borderWidth = args.borderWidth;
            if (args.borderRadius) styles.borderRadius = args.borderRadius;

            // 合并自定义CSS
            if (args.customCSS) {
                try {
                    const customStyles = JSON.parse(args.customCSS);
                    Object.assign(styles, customStyles);
                } catch {
                    // 如果不是JSON，当作CSS字符串处理
                    styles.customCSSText = args.customCSS;
                }
            }

            switch (args.type) {
                case 'styled':
                    windowConfig = {
                        title: args.title || 'Custom styled window',
                        content: `<div style="padding: 20px; height: 100%; display: flex; align-items: center; justify-content: center; font-family: Arial, sans-serif;">
                            <div style="text-align: center;">
                                <h2>Custom styled window</h2>
                                <p>This is a test window with custom styles</p>
                                <p>Background color: ${args.backgroundColor || 'default'}</p>
                                <p>Text color: ${args.textColor || 'default'}</p>
                            </div>
                        </div>`,
                        width: args.width,
                        height: args.height,
                        x: args.x,
                        y: args.y,
                        styles
                    };
                    break;

                case 'test':
                    windowConfig = {
                        title: args.title || 'Test window',
                        content: 'Hello World! This is a test floating window.\nYou can:\n- Drag the title bar to move the window\n- Drag the corners to resize the window\n- Click the close button to close the window',
                        width: args.width,
                        height: args.height,
                        x: args.x,
                        y: args.y,
                        styles
                    };
                    break;

                default:
                    // 对于其他类型，委托给原始的create_window命令，然后应用样式
                    const baseResult = await createWindowCommand.execute(args, context);
                    if (!baseResult.success) {
                        return baseResult;
                    }

                    // 如果有样式，应用到创建的窗口
                    if (Object.keys(styles).length > 0) {
                        try {
                            const windowId = baseResult.data?.windowId;
                            if (windowId) {
                                // 应用样式到窗口
                                await globalMessageManager.sendWindowOperation(windowId, 'update', { styles }, undefined, false);
                            }
                        } catch (error) {
                            console.warn('Failed to apply styles:', error);
                        }
                    }

                    return baseResult;
            }

            const result = await windowService.createWindow(windowConfig);

            return {
                success: result.success,
                message: `✅ ${windowConfig.title} created`,
                data: {
                    windowId: result.windowId,
                    config: windowConfig,
                    styles
                },
                // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
                // terminalOutput: context.source === 'terminal' ?
                //     `✅ ${windowConfig.title} 已创建\n🎨 样式: ${JSON.stringify(styles, null, 2)}` :
                //     undefined,
                shouldUpdateTerminal: false
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to create styled window'
            };
        }
    }
};

// 🆕 CodeMirror按行读取命令  
const codemirrorReadLineCommand: CommandDefinition = {
    name: 'codemirror_read_line',
    description: 'Read the content of the CodeMirror editor line by line',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'lineNumber',
            type: 'number',
            description: 'Line number (starting from 1)',
            required: true
        }
    ],
    examples: [
        'codemirror_read_line windowId=editor-123 lineNumber=1',
        'codemirror_read_line windowId=editor-123 lineNumber=10'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, lineNumber } = args;
            const lineInfo = await globalMessageManager.readCodeMirrorLine(windowId, lineNumber);

            return {
                success: true,
                message: `✅ Read line ${lineNumber} successfully: "${lineInfo.text}"`,
                data: {
                    lineNumber: lineInfo.number,
                    content: lineInfo.text,
                    text: lineInfo.text, // 兼容性字段
                    from: lineInfo.from,
                    to: lineInfo.to,
                    lineInfo: lineInfo // 完整原始数据
                },
                terminalOutput: context.source === 'terminal' ?
                    `📝 Line ${lineNumber} content: "${lineInfo.text}"` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to read line content'
            };
        }
    }
};

// 🆕 CodeMirror获取选区命令
const codemirrorGetSelectionCommand: CommandDefinition = {
    name: 'codemirror_get_selection',
    description: 'Get the selection information of the CodeMirror editor',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        }
    ],
    examples: [
        'codemirror_get_selection windowId=editor-123'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId } = args;
            const selection = await globalMessageManager.getCodeMirrorSelection(windowId);

            // 🔧 添加调试信息
            console.log('[WindowCommands] Received selection data:', {
                raw: selection,
                main: selection.main,
                mainAnchor: selection.main?.anchor,
                mainHead: selection.main?.head,
                mainFrom: (selection.main as any)?.from,
                mainTo: (selection.main as any)?.to,
                mainEmpty: (selection.main as any)?.empty,
                ranges: selection.ranges,
                rangesLength: selection.ranges?.length
            });

            // 检查是否有有效选区
            const hasSelection = selection.main.anchor !== selection.main.head;
            let selectedText = '';
            let analysisMessage = '';

            console.log('[WindowCommands] Selection analysis:', {
                hasSelection,
                anchorEqualsHead: selection.main.anchor === selection.main.head,
                anchor: selection.main.anchor,
                head: selection.main.head
            });

            if (hasSelection) {
                // 获取主选区的文本内容
                const from = Math.min(selection.main.anchor, selection.main.head);
                const to = Math.max(selection.main.anchor, selection.main.head);

                try {
                    selectedText = await globalMessageManager.readCodeMirrorContent(windowId, { from, to });
                    const textLength = selectedText.length;
                    const lineCount = (selectedText.match(/\n/g) || []).length + 1;

                    analysisMessage = `✅ Get selection successfully: ${textLength} characters (${lineCount} lines)`;

                    // 如果有多个选区，也获取它们的内容
                    if (selection.ranges && selection.ranges.length > 1) {
                        analysisMessage += `, ${selection.ranges.length} selections`;
                    }
                } catch (error) {
                    selectedText = '[Failed to read selection content]';
                    analysisMessage = `✅ Get selection position successfully, but failed to read content`;
                }
            } else {
                analysisMessage = `✅ Get selection information successfully: no selection content (cursor position: ${selection.main.anchor})`;
            }

            return {
                success: true,
                message: analysisMessage,
                data: {
                    ...selection,
                    hasSelection,
                    selectedText,
                    textLength: selectedText.length,
                    lineCount: hasSelection ? (selectedText.match(/\n/g) || []).length + 1 : 0,
                    // 兼容性字段
                    content: selectedText,
                    text: selectedText
                },
                terminalOutput: context.source === 'terminal' ?
                    `🎯 Selection information: ${hasSelection ?
                        `Selected "${selectedText.length > 50 ? selectedText.substring(0, 50) + '...' : selectedText}"` :
                        `Cursor position ${selection.main.anchor}`}` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to get selection information'
            };
        }
    }
};

// 🆕 CodeMirror设置选区命令
const codemirrorSetSelectionCommand: CommandDefinition = {
    name: 'codemirror_set_selection',
    description: 'Set the selection of the CodeMirror editor',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'anchor',
            type: 'number',
            description: 'Selection start position',
            required: true
        },
        {
            name: 'head',
            type: 'number',
            description: 'Selection end position',
            required: false
        },
        {
            name: 'scrollIntoView',
            type: 'boolean',
            description: 'Whether to scroll to the view',
            required: false
        }
    ],
    examples: [
        'codemirror_set_selection windowId=editor-123 anchor=0 head=10',
        'codemirror_set_selection windowId=editor-123 anchor=50 scrollIntoView=true'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, anchor, head, scrollIntoView } = args;
            const selection = { anchor, head: head || anchor };
            const options = { scrollIntoView };

            const success = await globalMessageManager.setCodeMirrorSelection(windowId, selection, options);

            return {
                success,
                message: success ? `✅ Set selection successfully` : `❌ Set selection failed`,
                data: { selection, options },
                terminalOutput: context.source === 'terminal' ?
                    `🎯 Selection set: ${anchor}-${head || anchor}` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to set selection'
            };
        }
    }
};

// 🆕 CodeMirror获取状态命令
const codemirrorGetStateCommand: CommandDefinition = {
    name: 'codemirror_get_state',
    description: 'Get the state information of the CodeMirror editor',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        }
    ],
    examples: [
        'codemirror_get_state windowId=editor-123'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId } = args;
            const state = await globalMessageManager.getCodeMirrorState(windowId);

            return {
                success: true,
                message: `✅ Get editor state successfully`,
                data: state,
                terminalOutput: context.source === 'terminal' ?
                    `Editor state: document length=${state.docLength}, line count=${state.lineCount}, read only=${state.readOnly}` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to get editor state'
            };
        }
    }
};

// 🆕 CodeMirror监听更新命令
const codemirrorListenCommand: CommandDefinition = {
    name: 'codemirror_listen',
    description: 'Listen to the update event of the CodeMirror editor',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'listenType',
            type: 'string',
            description: 'Listen type',
            required: false,
            enum: ['doc', 'selection', 'viewport', 'all'],
            default: 'all'
        },
        {
            name: 'callbackId',
            type: 'string',
            description: 'Callback identifier',
            required: false
        }
    ],
    examples: [
        'codemirror_listen windowId=editor-123',
        'codemirror_listen windowId=editor-123 listenType=doc',
        'codemirror_listen windowId=editor-123 listenType=selection callbackId=my-callback'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, listenType = 'all', callbackId } = args;
            const actualCallbackId = await globalMessageManager.listenCodeMirrorUpdates(windowId, listenType, callbackId);

            return {
                success: true,
                message: `✅ Listener set successfully`,
                data: { listenType, callbackId: actualCallbackId },
                terminalOutput: context.source === 'terminal' ?
                    `👂 Listener set: type=${listenType}, callback ID=${actualCallbackId}` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to set listener'
            };
        }
    }
};

// 🆕 CodeMirror添加装饰命令
const codemirrorAddDecorationCommand: CommandDefinition = {
    name: 'codemirror_add_decoration',
    description: 'Add decoration effect to the CodeMirror editor',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'type',
            type: 'string',
            description: 'Decoration type',
            required: true,
            enum: ['mark', 'widget', 'line']
        },
        {
            name: 'from',
            type: 'number',
            description: 'Starting position',
            required: true
        },
        {
            name: 'to',
            type: 'number',
            description: 'End position (mark type required)',
            required: false
        },
        {
            name: 'class',
            type: 'string',
            description: 'CSS class name',
            required: false
        },
        {
            name: 'content',
            type: 'string',
            description: 'Content (widget type required)',
            required: false
        }
    ],
    examples: [
        'codemirror_add_decoration windowId=editor-123 type=mark from=0 to=10 class=highlight',
        'codemirror_add_decoration windowId=editor-123 type=widget from=50 content="Comment"',
        'codemirror_add_decoration windowId=editor-123 type=line from=5 class=error-line'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, type, from, to, class: className, content } = args;
            const decoration = {
                type,
                from,
                to,
                class: className,
                content
            };

            const success = await globalMessageManager.addCodeMirrorDecoration(windowId, decoration);

            return {
                success,
                message: success ? `✅ Decoration added successfully` : `❌ Decoration added failed`,
                data: decoration,
                terminalOutput: context.source === 'terminal' ?
                    `🎨 Decoration added: ${type} (${from}${to ? `-${to}` : ''})` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to add decoration'
            };
        }
    }
};

// 🆕 CodeMirror语法查询命令
const codemirrorQuerySyntaxCommand: CommandDefinition = {
    name: 'codemirror_query_syntax',
    description: 'Query the syntax tree information of the CodeMirror editor',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'type',
            type: 'string',
            description: 'Query type',
            required: true,
            enum: ['tree', 'node', 'cursor']
        },
        {
            name: 'position',
            type: 'number',
            description: 'Query position',
            required: false
        },
        {
            name: 'nodeType',
            type: 'string',
            description: 'Node type',
            required: false
        }
    ],
    examples: [
        'codemirror_query_syntax windowId=editor-123 type=tree',
        'codemirror_query_syntax windowId=editor-123 type=node position=100',
        'codemirror_query_syntax windowId=editor-123 type=cursor position=50'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, type, position, nodeType } = args;
            const syntaxQuery = { type, position, nodeType };
            const syntax = await globalMessageManager.queryCodeMirrorSyntax(windowId, syntaxQuery);

            return {
                success: true,
                message: `✅ Syntax query successful`,
                data: { syntaxQuery, syntax },
                terminalOutput: context.source === 'terminal' ?
                    `🌳 Syntax information: ${type} query${position ? ` (position=${position})` : ''}` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to query syntax'
            };
        }
    }
};

// 🆕 CodeMirror历史操作命令
const codemirrorHistoryCommand: CommandDefinition = {
    name: 'codemirror_history',
    description: 'Execute CodeMirror editor history operations (undo/redo)',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'action',
            type: 'string',
            description: 'History operation type',
            required: true,
            enum: ['undo', 'redo', 'clear']
        }
    ],
    examples: [
        'codemirror_history windowId=editor-123 action=undo',
        'codemirror_history windowId=editor-123 action=redo',
        'codemirror_history windowId=editor-123 action=clear'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, action } = args;
            const success = await globalMessageManager.executeCodeMirrorHistory(windowId, action);

            const actionNames: Record<string, string> = {
                undo: 'Undo',
                redo: 'Redo',
                clear: 'Clear history'
            };

            const actionName = actionNames[action] || action;

            return {
                success,
                message: success ? `✅ ${actionName} successful` : `❌ ${actionName} failed`,
                data: { action, success },
                terminalOutput: context.source === 'terminal' ?
                    `⏮️ ${actionName} operation${success ? ' successful' : ' failed'}` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to execute history operation'
            };
        }
    }
};

// 🆕 CodeMirror AI分析命令
const codemirrorAIAnalyzeCommand: CommandDefinition = {
    name: 'codemirror_ai_analyze',
    description: 'Use AI to analyze the code in the CodeMirror editor',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'action',
            type: 'string',
            description: 'AI operation type',
            required: true,
            enum: ['analyze', 'suggest', 'refactor', 'complete', 'explain']
        },
        {
            name: 'selectionFrom',
            type: 'number',
            description: 'Analysis area start position',
            required: false
        },
        {
            name: 'selectionTo',
            type: 'number',
            description: 'Analysis area end position',
            required: false
        },
        {
            name: 'cursor',
            type: 'number',
            description: 'Cursor position',
            required: false
        },
        {
            name: 'prompt',
            type: 'string',
            description: 'AI prompt',
            required: false
        },
        {
            name: 'language',
            type: 'string',
            description: 'Code language',
            required: false
        },
        {
            name: 'model',
            type: 'string',
            description: 'AI model',
            required: false
        }
    ],
    examples: [
        'codemirror_ai_analyze windowId=editor-123 action=analyze selectionFrom=0 selectionTo=100',
        'codemirror_ai_analyze windowId=editor-123 action=explain cursor=50 language=javascript',
        'codemirror_ai_analyze windowId=editor-123 action=suggest prompt="Optimize this code" selectionFrom=20 selectionTo=80'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, action, selectionFrom, selectionTo, cursor, prompt, language, model } = args;

            const aiContext: any = { language };
            if (selectionFrom !== undefined && selectionTo !== undefined) {
                aiContext.selection = { from: selectionFrom, to: selectionTo };
            }
            if (cursor !== undefined) {
                aiContext.cursor = cursor;
            }

            const options = model ? { model } : undefined;
            const result = await globalMessageManager.sendCodeMirrorAI(windowId, action, aiContext, prompt, options);

            const actionNames: Record<string, string> = {
                analyze: 'Analyze',
                suggest: 'Suggest',
                refactor: 'Refactor',
                complete: 'Complete',
                explain: 'Explain'
            };

            const actionName = actionNames[action] || action;

            return {
                success: true,
                message: `✅ AI${actionName} completed`,
                data: { action, context: aiContext, result },
                terminalOutput: context.source === 'terminal' ?
                    `🤖 AI${actionName} result: ${JSON.stringify(result, null, 2).substring(0, 200)}...` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'AI operation failed'
            };
        }
    }
};

// 🆕 CodeMirror按行范围读取命令
const codemirrorReadLinesCommand: CommandDefinition = {
    name: 'codemirror_read_lines',
    description: 'Read the content of the CodeMirror editor line by line',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'fromLine',
            type: 'number',
            description: 'Starting line number (starting from 1)',
            required: true
        },
        {
            name: 'toLine',
            type: 'number',
            description: 'End line number (inclusive, starting from 1)',
            required: true
        }
    ],
    examples: [
        'codemirror_read_lines windowId=editor-123 fromLine=1 toLine=5',
        'codemirror_read_lines windowId=editor-123 fromLine=10 toLine=15'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, fromLine, toLine } = args;

            if (fromLine < 1 || toLine < 1 || fromLine > toLine) {
                return {
                    success: false,
                    error: `Invalid line number range: ${fromLine}-${toLine}. Line numbers must start from 1 and the starting line cannot be greater than the ending line.`
                };
            }

            // 首先获取编辑器状态以验证行号范围
            const state = await globalMessageManager.getCodeMirrorState(windowId);
            if (fromLine > state.lineCount || toLine > state.lineCount) {
                return {
                    success: false,
                    error: `Line number out of range: document has ${state.lineCount} lines, requested range ${fromLine}-${toLine} lines.`
                };
            }

            // 逐行读取内容
            const lines: Array<{ number: number, text: string, from: number, to: number }> = [];
            for (let lineNum = fromLine; lineNum <= toLine; lineNum++) {
                const lineInfo = await globalMessageManager.readCodeMirrorLine(windowId, lineNum);
                lines.push(lineInfo);
            }

            // 合并所有行的文本内容
            const allText = lines.map(line => line.text).join('\n');
            const totalLines = lines.length;

            return {
                success: true,
                message: `✅ Read line ${fromLine}-${toLine} successfully (total ${totalLines} lines): "${allText.length > 100 ? allText.substring(0, 100) + '...' : allText}"`,
                data: {
                    fromLine,
                    toLine,
                    totalLines,
                    content: allText,
                    text: allText, // 兼容性字段
                    lines: lines, // 详细的行信息
                    charRange: {
                        from: lines[0]?.from || 0,
                        to: lines[lines.length - 1]?.to || 0
                    }
                },
                terminalOutput: context.source === 'terminal' ?
                    `📝 Line ${fromLine}-${toLine} content (${totalLines} lines):\n${allText}` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to read line range content'
            };
        }
    }
};

// 🆕 CodeMirror按行号写入命令
const codemirrorWriteLineCommand: CommandDefinition = {
    name: 'codemirror_write_line',
    description: 'Write content at the specified line number position in the CodeMirror editor',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'lineNumber',
            type: 'number',
            description: 'Target line number (starting from 1)',
            required: true
        },
        {
            name: 'content',
            type: 'string',
            description: 'Content to write',
            required: true
        },
        {
            name: 'mode',
            type: 'string',
            description: 'Write mode: insert_end=append at the end of the line (default), insert_start=insert at the beginning of the line, replace_line=replace the entire line, new_line_after=new line after the current line, new_line_before=new line before the current line',
            required: false,
            enum: ['insert_start', 'insert_end', 'replace_line', 'new_line_after', 'new_line_before'],
            default: 'insert_end'
        },
        {
            name: 'scrollIntoView',
            type: 'boolean',
            description: 'Whether to scroll to the view',
            required: false,
            default: true
        }
    ],
    examples: [
        'codemirror_write_line windowId=editor-123 lineNumber=20 content="hello"  # append at the end of the line (default)',
        'codemirror_write_line windowId=editor-123 lineNumber=5 content="// " mode=insert_start  # insert at the beginning of the line',
        'codemirror_write_line windowId=editor-123 lineNumber=10 content="New line content" mode=new_line_after  # new line after the current line'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { windowId, lineNumber, content, mode = 'insert_end', scrollIntoView = true } = args;

            if (lineNumber < 1) {
                return {
                    success: false,
                    error: `Invalid line number: ${lineNumber}. Line numbers must start from 1.`
                };
            }

            // 获取编辑器状态
            const state = await globalMessageManager.getCodeMirrorState(windowId);

            let targetLine: number;
            let changes: Array<{ from: number; to?: number; insert: string }> = [];

            // 处理超出范围的行号
            if (lineNumber > state.lineCount) {
                if (mode === 'new_line_after' || mode === 'new_line_before') {
                    // 在文档末尾添加新行
                    targetLine = state.lineCount;
                    const lastLineInfo = await globalMessageManager.readCodeMirrorLine(windowId, targetLine);
                    changes.push({
                        from: lastLineInfo.to,
                        insert: `\n${content}`
                    });
                } else {
                    return {
                        success: false,
                        error: `Line number out of range: document has ${state.lineCount} lines, requested line ${lineNumber}. For out-of-range line numbers, only new_line_after or new_line_before modes are supported.`
                    };
                }
            } else {
                // 在现有行操作
                targetLine = lineNumber;
                const lineInfo = await globalMessageManager.readCodeMirrorLine(windowId, targetLine);

                switch (mode) {
                    case 'insert_start':
                        // 在行首插入
                        changes.push({
                            from: lineInfo.from,
                            insert: content
                        });
                        break;

                    case 'insert_end':
                        // 在行末插入
                        changes.push({
                            from: lineInfo.to,
                            insert: content
                        });
                        break;

                    case 'replace_line':
                        // 替换整行
                        changes.push({
                            from: lineInfo.from,
                            to: lineInfo.to,
                            insert: content
                        });
                        break;

                    case 'new_line_after':
                        // 在当前行后添加新行
                        changes.push({
                            from: lineInfo.to,
                            insert: `\n${content}`
                        });
                        break;

                    case 'new_line_before':
                        // 在当前行前添加新行
                        changes.push({
                            from: lineInfo.from,
                            insert: `${content}\n`
                        });
                        break;

                    default:
                        return {
                            success: false,
                            error: `Unsupported write mode: ${mode}`
                        };
                }
            }

            // 执行写入操作
            const options = {
                userEvent: 'insert',
                scrollIntoView
            };

            const result = await globalMessageManager.writeCodeMirrorContent(windowId, changes, options);

            const modeNames: Record<string, string> = {
                insert_start: 'insert at the beginning of the line',
                insert_end: 'insert at the end of the line',
                replace_line: 'replace the entire line',
                new_line_after: 'new line after the current line',
                new_line_before: 'new line before the current line'
            };

            const modeName = modeNames[mode] || mode;
            const actualLine = lineNumber > state.lineCount ? `document end` : `line ${lineNumber}`;

            return {
                success: true,
                message: `✅ Write content at ${actualLine}${modeName} successfully: "${content}"`,
                data: {
                    lineNumber,
                    targetLine,
                    content,
                    mode,
                    modeName,
                    changes,
                    result,
                    originalLineCount: state.lineCount
                },
                terminalOutput: context.source === 'terminal' ?
                    `✅ Write content at ${actualLine}${modeName}: "${content}"` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to write content by line number'
            };
        }
    }
};

// 🆕 CodeMirror精确文本编辑命令
const codemirrorEditTextCommand: CommandDefinition = {
    name: 'codemirror_edit_text',
    description: 'Perform precise text editing operations in the CodeMirror editor',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Editor window ID',
            required: true
        },
        {
            name: 'lineNumber',
            type: 'number',
            description: 'Target line number (starting from 1, optional, if not specified, search the entire document)',
            required: false
        },
        {
            name: 'operation',
            type: 'string',
            description: 'Operation type',
            required: true,
            enum: ['insert_before', 'insert_after', 'replace', 'delete', 'find_replace']
        },
        {
            name: 'target',
            type: 'string',
            description: 'Target text (text to search for)',
            required: true
        },
        {
            name: 'content',
            type: 'string',
            description: 'Content to insert or replace (not required for delete operation)',
            required: false
        },
        {
            name: 'regex',
            type: 'boolean',
            description: 'Whether to use regular expression matching',
            required: false,
            default: false
        },
        {
            name: 'caseSensitive',
            type: 'boolean',
            description: 'Whether to use case-sensitive matching',
            required: false,
            default: true
        },
        {
            name: 'all',
            type: 'boolean',
            description: 'Whether to operate on all matching items (default is only the first one)',
            required: false,
            default: false
        },
        {
            name: 'scrollIntoView',
            type: 'boolean',
            description: 'Whether to scroll to the view',
            required: false,
            default: true
        }
    ],
    examples: [
        'codemirror_edit_text windowId=editor-123 lineNumber=22 operation=insert_after target="project" content="world"  # insert world after project',
        'codemirror_edit_text windowId=editor-123 operation=replace target="hello" content="hi"  # replace hello with hi',
        'codemirror_edit_text windowId=editor-123 lineNumber=5 operation=delete target="old"  # delete old text in the 5th line'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const {
                windowId,
                lineNumber,
                operation,
                target,
                content = '',
                regex = false,
                caseSensitive = true,
                all = false,
                scrollIntoView = true
            } = args;

            // 验证操作类型
            if (!['insert_before', 'insert_after', 'replace', 'delete', 'find_replace'].includes(operation)) {
                return {
                    success: false,
                    error: `Unsupported operation type: ${operation}`
                };
            }

            // 验证必需的参数
            if (['insert_before', 'insert_after', 'replace', 'find_replace'].includes(operation) && !content) {
                return {
                    success: false,
                    error: `Operation ${operation} requires providing content parameter`
                };
            }

            let sourceText: string;
            let searchRange: { from: number, to: number };

            // 获取搜索范围和文本
            if (lineNumber) {
                // 在指定行搜索
                const state = await globalMessageManager.getCodeMirrorState(windowId);
                if (lineNumber < 1 || lineNumber > state.lineCount) {
                    return {
                        success: false,
                        error: `Line number out of range: document has ${state.lineCount} lines, requested line ${lineNumber}.`
                    };
                }

                const lineInfo = await globalMessageManager.readCodeMirrorLine(windowId, lineNumber);
                sourceText = lineInfo.text;
                searchRange = { from: lineInfo.from, to: lineInfo.to };
            } else {
                // 在全文搜索
                const docContentResult: any = await globalMessageManager.readCodeMirrorContent(windowId);
                sourceText = typeof docContentResult === 'string' ? docContentResult : (docContentResult?.content || '');
                searchRange = { from: 0, to: sourceText.length };
            }

            // 构建搜索模式
            let searchPattern: RegExp;
            try {
                if (regex) {
                    const flags = caseSensitive ? 'g' : 'gi';
                    searchPattern = new RegExp(target, flags);
                } else {
                    const escapedTarget = target.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    const flags = caseSensitive ? 'g' : 'gi';
                    searchPattern = new RegExp(escapedTarget, flags);
                }
            } catch (error) {
                return {
                    success: false,
                    error: `Invalid regular expression: ${target}`
                };
            }

            // 查找匹配项
            const matches: Array<{ index: number, length: number, text: string }> = [];
            let match;
            while ((match = searchPattern.exec(sourceText)) !== null) {
                matches.push({
                    index: match.index,
                    length: match[0].length,
                    text: match[0]
                });
                if (!all) break; // 如果不是替换全部，只找第一个
                if (searchPattern.lastIndex === match.index) {
                    searchPattern.lastIndex++; // 避免无限循环
                }
            }

            if (matches.length === 0) {
                return {
                    success: false,
                    error: `Text not found in ${lineNumber ? `line ${lineNumber}` : 'document'}: "${target}"`
                };
            }

            // 准备编辑变更（从后往前处理，避免位置偏移）
            const changes: Array<{ from: number; to?: number; insert: string }> = [];

            for (let i = matches.length - 1; i >= 0; i--) {
                const match = matches[i];
                const absoluteFrom = searchRange.from + match.index;
                const absoluteTo = absoluteFrom + match.length;

                switch (operation) {
                    case 'insert_before':
                        changes.push({
                            from: absoluteFrom,
                            insert: content
                        });
                        break;

                    case 'insert_after':
                        changes.push({
                            from: absoluteTo,
                            insert: content
                        });
                        break;

                    case 'replace':
                    case 'find_replace':
                        changes.push({
                            from: absoluteFrom,
                            to: absoluteTo,
                            insert: content
                        });
                        break;

                    case 'delete':
                        changes.push({
                            from: absoluteFrom,
                            to: absoluteTo,
                            insert: ''
                        });
                        break;
                }
            }

            // 执行编辑操作
            const options = {
                userEvent: 'edit.text',
                scrollIntoView
            };

            const result = await globalMessageManager.writeCodeMirrorContent(windowId, changes, options);

            const operationNames: Record<string, string> = {
                insert_before: 'insert before',
                insert_after: 'insert after',
                replace: 'replace',
                delete: 'delete',
                find_replace: 'find and replace'
            };

            const operationName = operationNames[operation] || operation;
            const scopeDesc = lineNumber ? `line ${lineNumber}` : 'document';
            const countDesc = all ? `all (${matches.length} places)` : 'first place';

            return {
                success: true,
                message: `✅ ${scopeDesc} "${target}"${operationName} successfully (${countDesc})`,
                data: {
                    operation,
                    target,
                    content,
                    lineNumber,
                    matches: matches.length,
                    scope: lineNumber ? 'line' : 'document',
                    changes,
                    result
                },
                terminalOutput: context.source === 'terminal' ?
                    `✅ ${scopeDesc} "${target}"${operationName}: "${content}" (${countDesc})` :
                    undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to perform text editing operation'
            };
        }
    }
};

// 更新注册函数
export function registerWindowCommands() {
    // 原有的窗口命令
    commandRegistry.register(createWindowCommand);
    commandRegistry.register(listWindowsCommand);
    commandRegistry.register(moveWindowCommand);
    commandRegistry.register(closeWindowCommand);
    commandRegistry.register(createMultipleWindowsCommand);
    commandRegistry.register(closeAllWindowsCommand);

    // 智能窗口命令
    commandRegistry.register(currentWindowResizeCommand);
    commandRegistry.register(currentWindowMoveCommand);

    // 🆕 DOM操作命令
    commandRegistry.register(domQueryCommand);
    commandRegistry.register(domUpdateCommand);

    // 🆕 CodeMirror编辑器命令
    commandRegistry.register(codemirrorReadCommand);
    commandRegistry.register(codemirrorWriteCommand);

    // 🆕 样式支持命令
    commandRegistry.register(createStyledWindowCommand);

    // 🆕 CodeMirror按行读取命令  
    commandRegistry.register(codemirrorReadLineCommand);

    // 🆕 CodeMirror获取选区命令
    commandRegistry.register(codemirrorGetSelectionCommand);

    // 🆕 CodeMirror设置选区命令
    commandRegistry.register(codemirrorSetSelectionCommand);

    // 🆕 CodeMirror获取状态命令
    commandRegistry.register(codemirrorGetStateCommand);

    // 🆕 CodeMirror监听更新命令
    commandRegistry.register(codemirrorListenCommand);

    // 🆕 CodeMirror添加装饰命令
    commandRegistry.register(codemirrorAddDecorationCommand);

    // 🆕 CodeMirror语法查询命令
    commandRegistry.register(codemirrorQuerySyntaxCommand);

    // 🆕 CodeMirror历史操作命令
    commandRegistry.register(codemirrorHistoryCommand);

    // 🆕 CodeMirror AI分析命令
    commandRegistry.register(codemirrorAIAnalyzeCommand);

    // 🆕 CodeMirror按行范围读取命令
    commandRegistry.register(codemirrorReadLinesCommand);

    // 🆕 CodeMirror按行号写入命令
    commandRegistry.register(codemirrorWriteLineCommand);

    // 🆕 CodeMirror精确文本编辑命令
    commandRegistry.register(codemirrorEditTextCommand);
}

