/**
 * Terminal基础命令集合
 * 将旧的terminal命令迁移到统一命令系统
 */

import { CommandDefinition, CommandContext, CommandResult, commandRegistry } from './registry';
import { globalMessageManager } from '@/lib/floating-window-messages';
import { createCommandLink } from '@/lib/terminal/osc8-link-manager';
import { logoutManager } from '@/lib/auth/logout-manager';

// 清屏命令
const clearCommand: CommandDefinition = {
    name: 'clear',
    description: 'Clear the terminal screen',
    category: 'terminal',
    parameters: [],
    examples: ['clear'],
    async execute(args, context): Promise<CommandResult> {
        // 直接操作终端
        if (context.terminal) {
            // 支持两种清屏方式：原生xterm.js和readline
            if ('clear' in context.terminal && typeof context.terminal.clear === 'function') {
                context.terminal.clear();
            } else {
                // 如果没有clear方法，使用ANSI转义序列清屏
                context.terminal.write('\x1b[2J\x1b[H');
            }
        }

        return {
            success: true,
            shouldUpdateTerminal: context.source === 'terminal'
        };
    }
};

// 菜单命令
const menuCommand: CommandDefinition = {
    name: 'menu',
    description: 'Menu control command: open/close the right menu or display the traditional interactive menu',
    category: 'terminal',
    parameters: [
        {
            name: 'action',
            type: 'string',
            description: 'Menu operation type',
            required: false,
            enum: ['open', 'close', 'toggle'],
            default: undefined
        }
    ],
    examples: [
        'menu',
        'menu action=open',
        'menu action=close',
        'menu action=toggle'
    ],
    async execute(args, context): Promise<CommandResult> {
        const { action } = args;

        // 🎯 右侧菜单控制
        if (action && ['open', 'close', 'toggle'].includes(action)) {
            if (typeof window === 'undefined') {
                return {
                    success: false,
                    error: 'Menu control function is only available in the client environment'
                };
            }

            try {
                const { controlTerminalMenu } = await import('@/components/TerminalMenuManager');
                controlTerminalMenu(action as 'open' | 'close' | 'toggle', 'command');

                const messages = {
                    open: 'The right menu is open',
                    close: 'The right menu is closed',
                    toggle: 'The right menu status is switched'
                } as const;

                const message = `✅ ${messages[action as keyof typeof messages]}`;

                return {
                    success: true,
                    message,
                    // 🔇 已禁用终端确认输出 - 用户要求去除终端输出确认
                    // terminalOutput: context.source === 'terminal' ? message : undefined,
                    shouldUpdateTerminal: false
                };
            } catch (error) {
                return {
                    success: false,
                    error: `Menu control failed: ${error instanceof Error ? error.message : String(error)}`
                };
            }
        }

        // 💡 没有action参数时，显示传统的交互式菜单（向后兼容）
        const menuItems = [
            {
                label: 'PAPERS',
                command: 'open',
                params: {
                    type: 'path',
                    target: '/papers',
                    title: 'Academic papers',
                    width: 900,
                    height: 700,
                    x: 100,
                    y: 100
                }
            },
            {
                label: 'DOCS',
                command: 'open',
                params: {
                    type: 'path',
                    target: '/docs',
                    title: 'Document materials',
                    width: 800,
                    height: 650,
                    x: 150,
                    y: 120
                }
            },
            {
                label: 'NEWS',
                command: 'open',
                params: {
                    type: 'path',
                    target: '/news',
                    title: 'News and information',
                    width: 850,
                    height: 600,
                    x: 200,
                    y: 140
                }
            },
            {
                label: 'ABOUT',
                command: 'open',
                params: {
                    type: 'path',
                    target: '/about',
                    title: 'About page',
                    width: 750,
                    height: 550,
                    x: 250,
                    y: 160
                }
            },
            {
                label: 'CONTACT',
                command: 'open',
                params: {
                    type: 'path',
                    target: '/contact',
                    title: 'Contact information',
                    width: 700,
                    height: 500,
                    x: 300,
                    y: 180
                }
            }
        ];

        // 为终端渲染菜单
        if (context.terminal && context.source === 'terminal') {
            renderMenuToTerminal(context.terminal, menuItems);
        }

        return {
            success: true,
            data: { items: menuItems.map(item => item.label) },
            terminalOutput: undefined, // 菜单内容已经通过renderMenuToTerminal直接写入终端
            shouldUpdateTerminal: false // 避免重复输出
        };
    }
};



// Sixel图像命令
const sixelCommand: CommandDefinition = {
    name: 'sixel',
    description: 'Display Sixel format images in the terminal',
    category: 'terminal',
    parameters: [
        {
            name: 'action',
            type: 'string',
            description: 'Operation type',
            required: true,
            enum: ['test', 'demo', 'load', 'clear', 'help']
        },
        {
            name: 'path',
            type: 'string',
            description: 'Image file path',
            required: false
        }
    ],
    examples: ['sixel action=test', 'sixel action=demo', 'sixel action=load path=/images/test.png', 'sixel action=clear'],
    async execute(args, context): Promise<CommandResult> {
        const terminal = context.terminal;

        // 获取或创建Sixel插件
        let sixelPlugin: any;
        if (terminal && (terminal as any).sixelPlugin) {
            sixelPlugin = (terminal as any).sixelPlugin;
        } else if (terminal) {
            // 动态导入Sixel插件
            const { TerminalSixelPlugin } = await import('@/lib/terminal/plugins/TerminalSixelPlugin');
            sixelPlugin = new TerminalSixelPlugin((terminal as any)._originalTerminal || terminal);
            sixelPlugin.initialize();
            (terminal as any).sixelPlugin = sixelPlugin;
        }

        switch (args.action) {
            case 'test':
                if (terminal && context.source === 'terminal') {
                    return await displayTestSixelImage(sixelPlugin, terminal);
                }
                return {
                    success: true,
                    shouldUpdateTerminal: context.source === 'terminal'
                };

            case 'demo':
                if (terminal && context.source === 'terminal') {
                    return await displayDemoSixelImage(sixelPlugin, terminal);
                }
                return {
                    success: true,
                    shouldUpdateTerminal: context.source === 'terminal'
                };

            case 'load':
                if (!args.path) {
                    return {
                        success: false,
                        error: 'Please specify the image file path'
                    };
                }
                return {
                    success: true,
                    message: `Trying to load image: ${args.path}`,
                    data: { path: args.path },
                    terminalOutput: context.source === 'terminal' ?
                        `📷 Loading image: ${args.path}` : undefined,
                    shouldUpdateTerminal: context.source === 'terminal'
                };

            case 'clear':
                return {
                    success: true,
                    shouldUpdateTerminal: context.source === 'terminal'
                };

            case 'help':
                if (terminal && context.source === 'terminal') {
                    displaySixelHelp(terminal);
                }
                return {
                    success: true,
                    shouldUpdateTerminal: context.source === 'terminal'
                };

            default:
                return {
                    success: false,
                    error: `Unsupported operation: ${args.action}`
                };
        }
    }
};

// 登录命令
const loginCommand: CommandDefinition = {
    name: 'login',
    description: 'User login system',
    category: 'auth',
    parameters: [
        {
            name: 'email',
            type: 'string',
            description: 'Email address',
            required: false
        },
        {
            name: 'password',
            type: 'string',
            description: 'Password',
            required: false
        }
    ],
    examples: ['login', 'login email=<EMAIL>', 'login email=<EMAIL> password=mypass'],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { terminal } = context;

            if (!terminal || context.source !== 'terminal') {
                return {
                    success: false,
                    error: 'Login command can only be used in the terminal'
                };
            }

            // 检查当前登录状态
            const authState = getTerminalAuthState(terminal);
            const { isAuthenticated, username: currentUsername } = authState;

            // 如果已经登录
            if (isAuthenticated) {
                if (args.email) {
                    const targetEmail = args.email;
                    if (targetEmail !== currentUsername) {
                        terminal.writeln(`Logged in: ${currentUsername}`);
                        terminal.writeln(`To switch accounts, please execute logout`);
                        return { success: false, message: 'User already logged in' };
                    } else {
                        terminal.writeln(`Logged in: ${currentUsername}`);
                        return { success: true };
                    }
                } else {
                    terminal.writeln(`Logged in: ${currentUsername}`);
                    return { success: true };
                }
            }

            // 根据参数数量选择登录模式
            if (args.email && args.password) {
                // 直接登录模式
                return await performDirectLogin(terminal, args.email, args.password);
            } else if (args.email) {
                // 半交互模式：提供了邮箱，只需要密码
                if (!validateEmailFormat(args.email)) {
                    terminal.writeln(`Email format is invalid`);
                    return { success: false, message: 'Email format is invalid' };
                }

                const emailExists = await checkEmailExists(args.email);
                if (!emailExists) {
                    terminal.writeln('Email not registered');
                    return { success: false, message: 'Email not registered' };
                }

                return await performInteractiveLogin(terminal, args.email);
            } else {
                // 完全交互模式
                const inputEmail = await getEmailInput(terminal);
                if (!inputEmail) {
                    terminal.writeln('Login canceled');
                    return { success: false, message: 'Login canceled' };
                }

                return await performInteractiveLogin(terminal, inputEmail);
            }

        } catch (error) {
            context.terminal?.writeln('Login failed');
            return { success: false, message: 'Login failed' };
        }
    }
};

// 登出命令
const logoutCommand: CommandDefinition = {
    name: 'logout',
    description: 'User logout system',
    category: 'auth',
    parameters: [
        {
            name: 'force',
            type: 'boolean',
            description: 'Force logout',
            required: false,
            default: false
        }
    ],
    examples: ['logout', 'logout force=true'],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { terminal } = context;
            const forceLogout = args.force || false;

            if (!terminal || context.source !== 'terminal') {
                return {
                    success: false,
                    error: 'Logout command can only be used in the terminal'
                };
            }

            // 获取当前用户状态
            const authState = getTerminalAuthState(terminal);
            const { isAuthenticated, username: currentUsername } = authState;

            // 如果未登录
            if (!isAuthenticated) {
                terminal.writeln('ℹ️ You are not logged in');
                terminal.writeln('💡 Use \'login\' command to login');
                return { success: true };
            }

            // 如果是强制登出
            if (forceLogout) {
                terminal.writeln(`🔧 Force logout user: ${currentUsername}...`);
                try {
                    const supabaseClient = terminal._supabaseClient;
                    if (supabaseClient) {
                        const result = await logoutManager.logout(supabaseClient, { force: true });
                        if (result.success) {
                            terminal.writeln('✅ Force logout successful');
                        } else {
                            terminal.writeln(`❌ Force logout failed: ${result.error}`);
                            return { success: false, message: 'Force logout failed' };
                        }
                    }

                    // 等待认证状态更新
                    await waitForTerminalAuthState(terminal, 2000);

                    return { success: true };
                } catch (forceError) {
                    terminal.writeln(`❌ Force logout failed: ${forceError instanceof Error ? forceError.message : 'Unknown error'}`);
                    return { success: false, message: 'Force logout failed' };
                }
            }

            terminal.writeln(`🔓 Logging out user: ${currentUsername}...`);

            try {
                const supabaseClient = terminal._supabaseClient;
                if (!supabaseClient) {
                    terminal.writeln('❌ Authentication service unavailable');
                    return { success: false, message: 'Authentication service unavailable' };
                }

                // 使用统一的LogoutManager执行登出
                const logoutResult = await logoutManager.logout(supabaseClient, {
                    timeout: 3000,
                    clearStorage: true
                });

                if (logoutResult.success) {
                    terminal.writeln('✅ Logout successful');

                    // 等待认证状态更新
                    try {
                        await waitForTerminalAuthState(terminal, 1000);
                    } catch (error) {
                        // 忽略等待错误
                    }

                    return { success: true };
                } else {
                    terminal.writeln(`❌ Logout failed: ${logoutResult.error}`);
                    return { success: false, message: logoutResult.error || 'Logout failed' };
                }

            } catch (error) {
                const errorMsg = error instanceof Error ? error.message : 'Logout failed';
                terminal.writeln(`❌ Logout failed: ${errorMsg}`);
                return { success: false, message: errorMsg };
            }

        } catch (error) {
            const errorMsg = `Logout command execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
            context.terminal?.writeln(`❌ ${errorMsg}`);
            return { success: false, message: errorMsg };
        }
    }
};



// CodeMirror测试命令
const cmtestCommand: CommandDefinition = {
    name: 'cmtest',
    description: 'CodeMirror API complete test suite',
    category: 'editor',
    parameters: [
        {
            name: 'windowId',
            type: 'string',
            description: 'Target editor window ID',
            required: true
        },
        {
            name: 'testType',
            type: 'string',
            description: 'Test type',
            required: false,
            enum: ['all', 'read', 'write', 'selection', 'listen', 'syntax', 'history', 'ai'],
            default: 'all'
        },
        {
            name: 'quick',
            type: 'boolean',
            description: 'Quick test mode',
            required: false,
            default: false
        }
    ],
    examples: [
        'cmtest windowId=editor-123',
        'cmtest windowId=editor-123 testType=read',
        'cmtest windowId=editor-123 quick=true'
    ],
    async execute(args, context): Promise<CommandResult> {
        const { windowId, testType = 'all', quick = false } = args;

        if (context.terminal && context.source === 'terminal') {
            context.terminal.writeln('🧪 CodeMirror API test suite started');
            context.terminal.writeln(`📍 Target window: ${windowId}`);
            context.terminal.writeln(`🎯 Test type: ${testType}`);
            context.terminal.writeln('═══════════════════════════════════════\n');
        }

        try {
            // 简化版本的测试逻辑 - 这里可以扩展为完整的测试套件
            const testResult = await runBasicCodeMirrorTest(windowId, testType, context.terminal);

            return {
                success: testResult.success,
                message: testResult.success ? '✅ Test completed' : '❌ Test failed',
                data: testResult,
                terminalOutput: context.source === 'terminal' ? testResult.output : undefined,
                shouldUpdateTerminal: context.source === 'terminal'
            };
        } catch (error) {
            const errorMsg = `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`;

            if (context.terminal && context.source === 'terminal') {
                context.terminal.writeln(`❌ ${errorMsg}`);
            }

            return {
                success: false,
                error: errorMsg
            };
        }
    }
};

// 辅助函数：渲染菜单到终端（使用新的OSC8管理器）
function renderMenuToTerminal(terminal: any, menuItems: any[]) {
    terminal.writeln('');
    terminal.writeln('📋 Available menu items');
    terminal.writeln('─'.repeat(30));
    terminal.writeln('');

    menuItems.forEach((item) => {
        // 使用基础样式的OSC8链接，正确传递命令参数
        const clickableLabel = createCommandLink(item.label, item.command, item.params);
        terminal.writeln(`${clickableLabel}`);
    });

    terminal.writeln('');
    terminal.writeln('💡 Click the menu item name to execute the corresponding command');
    terminal.writeln('');
}

// 辅助函数：显示测试Sixel图像
async function displayTestSixelImage(sixelPlugin: any, terminal: any): Promise<CommandResult> {
    terminal.writeln('🖼️ Displaying Sixel test image...');
    terminal.writeln('');

    if (!sixelPlugin.getSixelSupport()) {
        terminal.writeln('⚠️ The current terminal may not support Sixel image display');
        terminal.writeln('💡 Please ensure that you are using a terminal emulator that supports Sixel');
        return {
            success: false,
            error: 'Terminal does not support Sixel format'
        };
    }

    // 显示测试图像
    sixelPlugin.displayTestImage();

    return {
        success: true
    };
}

// 辅助函数：显示演示Sixel图像
async function displayDemoSixelImage(sixelPlugin: any, terminal: any): Promise<CommandResult> {
    terminal.writeln('🖼️ Displaying Sixel demo image...');
    terminal.writeln('📏 Image size: 627x98 pixels');
    terminal.writeln('');

    if (!sixelPlugin.getSixelSupport()) {
        terminal.writeln('⚠️ The current terminal may not support Sixel image display');
        terminal.writeln('💡 Please ensure that you are using a terminal emulator that supports Sixel');
        return {
            success: false,
            error: 'Terminal does not support Sixel format'
        };
    }

    // 复杂的演示图像数据（来自旧系统）
    const demoSixelData = "\"1;1;627;98#0;2;0;0;0#1;2;0;6;91#1!20~#0!401~B!19@B!185~$#1!421?{!19}{-!20~#0!86~N!13F!266~!13F!22~#1!21~#0!185~$#1!106?o!13w!266?!13w-!20~#0!84~^@!14?!264~^@!13?!22~#1!21~#0!185~$#1!104?_}!14~!264?_}!13~-!20~#0!41~!14^!25~^^NB!16?!41~!16^!57~!11^!24~!18^!48~!16^!29~^^FB!15?!22~!21}!92~!16^!41~!18^!18~$#1!61?!14_!25?__o{!16~!41?!16_!57?!11_!24?!18_!48?!16_!29?__w{!15~!22?!21@!92?!16_!41?!18_-B!18~F!10?!20~??_ow{}}!20~}{{wo_!8?{!5}!36~#0!15~^NFFBB@@!23?@@BBFFN^!16~#1!21~?_ow{}}!15~#0!10~^^NFBB@@@!24?@@@BFFN^!25~^NFFBB@@!23?@@BBFFN^!10~!6@!36?!7~!21?!5~w!20?F!21~F!18?_{!11~^NNFBB@@@!22?@@@BFFN^!19~^NFBB@@@!24?@@@BBFFN^!6~${!18?w!10~!20?~~^NFB@@!20?@BBFN^!8~B!5@#1!51?_oww{{}}!23~}}{{wwo_#0!37?~^NFB@@#1!25?__ow{{}}}!24~}}}{wwo_!25?_oww{{}}!23~}}{{wwo_!10?!6}!36~!7?!21~!5?F!20~w!21?w!18~^B!11?_oow{{}}}!22~}}}{wwo_!19?_ow{{}}}!24~}}}{{wwo_-#0~o!16?_!11~#1!20~}!33~}w!6?!42~!9?_ow{}!41~}{w_!11?!21~}!21~!6?ow{!43~}w_!15?_ow}!42~}{w_!5?!42~!7?!21~!7?F^!19~w_!16?w!18~N@!8?ow{}!41~}{wo!10?o{}!43~}w_$?N!16~^#0!31?@!33?@F!6~!42?!9~^NFB@!41?@BF^!11~!21?@!21?!6~NFB!43?@F^!15~^NF@!42?@BF^!5~!42?!7~!21?!7~w_!19?F^!16~F!18?o}!8~NFB@!41?@BFN!10~NB@!43?@F^~~-~~#1!16~#0!12~#1!23~^NN!7FNN^!21~o!4?!7B!21~!14B!6?_w}!17~^NFBB!6@BBFF^!18~w!9?!42~@!4?w!18~^NF!7BFF^!19~{!11?_w}!19~^NFF!7BFFN^!16~}w???!7B!20~!15B!7?!21~!9?B^!19~{_!11?_w!18~F@!7?w}!17~^NFBBB!5@BBFFN!18~{_!6?}!18~`!10?@@BF^!13~^^]$";

    // 显示演示图像
    sixelPlugin.displaySixelImage(demoSixelData);

    return {
        success: true
    };
}

// 辅助函数：显示Sixel帮助信息
function displaySixelHelp(terminal: any): void {
    terminal.writeln('🖼️ Sixel image display command help');
    terminal.writeln('');
    terminal.writeln('Usage: sixel action=<operation>');
    terminal.writeln('');
    terminal.writeln('Available operations:');
    terminal.writeln('  test    - Display simple test image');
    terminal.writeln('  demo    - Display complex demo image');
    terminal.writeln('  load    - Load image from specified path');
    terminal.writeln('  clear   - Clear Sixel display area');
    terminal.writeln('  help    - Display this help information');
    terminal.writeln('');
    terminal.writeln('💡 Sixel is a format for displaying images in the terminal');
    terminal.writeln('🎯 Supported terminals: xterm, mintty, iTerm2(partial) etc.');
}

// 辅助函数：运行基础CodeMirror测试
async function runBasicCodeMirrorTest(windowId: string, testType: string, terminal?: any): Promise<any> {
    try {
        // 测试编辑器是否可用
        const state = await globalMessageManager.getCodeMirrorState(windowId, 3000);

        const result = {
            success: true,
            windowId,
            testType,
            editorState: state,
            output: `✅ Editor available (document length: ${state.docLength}, line count: ${state.lineCount})`
        };

        if (terminal) {
            terminal.writeln(result.output);
        }

        return result;
    } catch (error) {
        const result = {
            success: false,
            windowId,
            testType,
            error: error instanceof Error ? error.message : 'Unknown error',
            output: `❌ Editor unavailable: ${error instanceof Error ? error.message : 'Unknown error'}`
        };

        if (terminal) {
            terminal.writeln(result.output);
        }

        return result;
    }
}

//  AI命令
const aiCommand: CommandDefinition = {
    name: 'ai',
    description: 'AI conversation and intelligent assistant - supports intelligent question answering, function calling, and configuration management',
    category: 'ai',
    parameters: [
        {
            name: 'subcommand',
            type: 'string',
            description: 'Subcommand type',
            required: false,
            enum: ['chat', 'quick', 'reset', 'status', 'help', 'config', 'model']
        },
        {
            name: 'message',
            type: 'string',
            description: 'Message content to send',
            required: false
        },
        {
            name: 'provider',
            type: 'string',
            description: 'Set AI provider',
            required: false,
            enum: ['openai', 'deepseek', 'anthropic']
        },
        {
            name: 'key',
            type: 'string',
            description: 'Set API key for current provider',
            required: false
        },
        {
            name: 'openai_key',
            type: 'string',
            description: 'Set OpenAI API key',
            required: false
        },
        {
            name: 'deepseek_key',
            type: 'string',
            description: 'Set DeepSeek API key',
            required: false
        },
        {
            name: 'anthropic_key',
            type: 'string',
            description: 'Set Anthropic API key',
            required: false
        },
        {
            name: 'model_action',
            type: 'string',
            description: 'Model operation type',
            required: false,
            enum: ['list', 'set', 'current']
        },
        {
            name: 'model_name',
            type: 'string',
            description: 'Model name to set',
            required: false
        },
        {
            name: 'reset_config',
            type: 'boolean',
            description: 'Reset all AI configurations',
            required: false
        }
    ],
    examples: [
        'ai',
        'ai chat Create a test window',
        'ai quick What is JavaScript?',
        'ai reset',
        'ai status',
        'ai config',
        'ai config provider=deepseek',
        'ai config deepseek_key=sk-your-key-here',
        'ai config openai_key=sk-your-openai-key',
        'ai config reset_config=true',
        'ai model list',
        'ai model set model_name=deepseek-chat'
    ],
    async execute(args, context): Promise<CommandResult> {
        try {
            const { subcommand, message, provider, key, openai_key, deepseek_key, anthropic_key,
                model_action, model_name, reset_config } = args;

            // 处理配置相关的子命令（不需要AI服务已配置）
            if (subcommand?.toLowerCase() === 'config') {
                return await handleAIConfig(args, context);
            }

            if (subcommand?.toLowerCase() === 'model') {
                return await handleAIModel(args, context);
            }

            // 🆕 新系统：AI服务已迁移到服务器端，无需本地配置检查
            // 所有AI功能现在都通过统一API路由处理，支持访客和用户使用

            // 处理不同的子命令
            switch (subcommand?.toLowerCase()) {
                case 'status':
                    return await handleAIStatus(context);

                case 'reset':
                    return await handleAIReset(context);

                case 'help':
                    return handleAIHelp(context);

                case 'quick':
                    if (!message) {
                        return {
                            success: false,
                            error: 'Please provide the message to send',
                            terminalOutput: context.source === 'terminal' ?
                                '❌ Please provide the message to send\n   用法: ai quick <消息>' : undefined,
                            shouldUpdateTerminal: context.source === 'terminal'
                        };
                    }
                    return await handleAIMessage(message, context);

                case 'chat':
                    if (!message) {
                        // 进入AI模式
                        return handleEnterAIMode(context);
                    }
                    return await handleAIMessage(message, context);

                default:
                    // 没有子命令时进入AI模式，或将输入当作消息处理
                    if (!subcommand) {
                        return handleEnterAIMode(context);
                    } else {
                        // 将第一个参数当作消息的一部分
                        const fullMessage = subcommand + (message ? ' ' + message : '');
                        return await handleAIMessage(fullMessage, context);
                    }
            }
        } catch (error) {
            return {
                success: false,
                error: `AI command execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }
};

// AI辅助函数：处理AI状态查询
async function handleAIStatus(context: CommandContext): Promise<CommandResult> {
    try {
        // 获取用户状态信息
        const authState = getTerminalAuthState(context.terminal);

        let statusText = `📊 AI service status - server-side intelligent conversation system

🔧 Service configuration:
• System status: ✅ Ready (server-side processing)
• AI model: DeepSeek Chat (server configuration)
• Function support: Streaming conversation + Function Calling
• Response mode: Edge Runtime low latency

👤 User status:`;

        if (authState.isAuthenticated && authState.user) {
            // 尝试获取用户额度信息
            try {
                const response = await fetch('/api/me/ai-quota');
                if (response.ok) {
                    const quotaData = await response.json();
                    statusText += `
• Account type: Registered user (${authState.username})
• Remaining quota: ${quotaData.remaining} times
• Historical usage: ${quotaData.total} times
• Rate limit: 10 requests per minute`;
                } else {
                    statusText += `
• Account type: Registered user (${authState.username})
• Quota status: Unable to get (please refresh and try again)`;
                }
            } catch (error) {
                statusText += `
• Account type: Registered user (${authState.username})
• Quota status: Query failed`;
            }
        } else {
            statusText += `
• Account type: Guest user
• Daily quota: 10 free trials
• Rate limit: 3 requests per minute
• Upgrade hint: Register an account to get 100 free trials`;
        }

        statusText += `

🎯 Start using:
  ai                    # Enter AI conversation mode
  ai chat Hello         # Start conversation directly
  ai help              # View detailed help

💡 特色功能:
• 🖼️ Create floating window: "Create a test window"
• 🌐 Open web page: "Open a window displaying Google"  
• ⚡ Execute command: Use !<command> in AI mode
• 📊 Real-time response: Stream display of AI replies`;

        return {
            success: true,
            message: 'AI status query successful',
            data: {
                ready: true,
                serverSide: true,
                model: 'deepseek-chat',
                userType: authState.isAuthenticated ? 'registered' : 'guest',
                username: authState.username
            },
            terminalOutput: context.source === 'terminal' ? statusText : undefined,
            shouldUpdateTerminal: context.source === 'terminal'
        };
    } catch (error) {
        return {
            success: false,
            error: 'Failed to get AI status: ' + (error instanceof Error ? error.message : 'Unknown error')
        };
    }
}

// AI辅助函数：重置AI对话历史
async function handleAIReset(context: CommandContext): Promise<CommandResult> {
    try {
        // 🔄 使用新的前端Function Calling管理器重置历史
        const { getFunctionCallingManager } = await import('@/lib/ai/frontend-function-calling');
        const manager = getFunctionCallingManager();
        manager.clearHistory();

        return {
            success: true,
            message: 'AI conversation history has been cleared',
            shouldUpdateTerminal: context.source === 'terminal'
        };
    } catch (error) {
        return {
            success: false,
            error: 'Reset failed: ' + (error instanceof Error ? error.message : 'Unknown error')
        };
    }
}



// AI辅助函数：显示AI帮助
function handleAIHelp(context: CommandContext): CommandResult {
    const helpText = `🤖 AI assistant - server-side intelligent conversation system

📋 Basic commands:
  ai                     - Enter AI conversation mode
  ai chat [message]      - Start AI conversation or send message  
  ai quick <message>     - Quick AI question and answer (single time)
  ai help                - Display this help

💡 Features:
  • 🚀 Just-in-time use: No configuration required, server-side processing
  • 🎯 Intelligent conversation: Support continuous context conversation
  • ⚡ Function calling: AI can directly execute system commands
  • 🖼️ Window management: Create, move, and operate floating windows
  • 📡 Streaming response: Real-time display of AI replies
  • 🔒 Quota control: Guest free experience, user quota usage
  • 💬 Direct command: Use !<command> directly execute terminal commands in AI mode

🎯 Usage:
  ai                               - Enter AI conversation mode
  ai chat hello                    - Normal conversation
  ai chat create a test window     - Execute command
  ai quick what is React?          - Quick question and answer
  ai chat open a window displaying Google - Complex task

💬 AI mode instructions:
  Enter AI mode and start conversation by typing messages
  Use !<command> to directly execute terminal commands (e.g., !help, !window create)
  Type "exit" to leave AI mode

⚡ Direct command examples:
  !help                   - View all available commands
  !window create         - Create a new window
  !list_windows          - List all windows
  !theme list            - View theme list

🆓 Guest experience:
  • 10 free AI conversations per day
  • 3 requests per minute
  • Guide to register for more usage opportunities after exceeding the limit

👤 Registered user:
  • 100 AI conversation quota by default
  • 10 requests per minute
  • Real-time display of remaining quota
  • Support quota supplement

🔧 Technical architecture:
  Server-side API + Streaming response + Function Calling + Guest/user quota management`;

    return {
        success: true,
        data: { helpText },
        terminalOutput: context.source === 'terminal' ? helpText : undefined,
        shouldUpdateTerminal: context.source === 'terminal'
    };
}

// AI辅助函数：进入AI模式
function handleEnterAIMode(context: CommandContext): CommandResult {
    if (context.source === 'terminal' && context.terminal) {
        // 通知TerminalCommandManager进入AI模式
        if (context.terminal.commandManager) {
            context.terminal.commandManager.enableAIMode();
        }

        // 自动切换到AI主题
        try {
            const { globalThemeManager } = require('@/lib/terminal/ThemeManager');
            globalThemeManager.switchTheme('ai', 'auto');
        } catch (error) {
            // 静默处理主题切换错误，不影响AI模式功能
            console.warn('AI mode theme switch failed:', error);
        }

        const modeText = ``;

        return {
            success: true,
            data: { aiMode: true },
            terminalOutput: modeText,
            shouldUpdateTerminal: true
        };
    }

    return {
        success: true,
        message: 'AI mode is only available in Terminal',
        data: { aiMode: false }
    };
}

// 🆕 显示AI额度信息
export async function displayAIQuotaInfo(terminal: any): Promise<void> {
    try {
        // 获取用户状态 - 使用共享的用户API/hook
        const { useSupabase } = await import('@/components/SupabaseProvider');

        // 由于在服务端函数中无法直接使用React hooks，我们需要获取用户信息
        // 这里可以通过terminal上下文或其他方式获取用户状态
        const authState = getTerminalAuthState(terminal);

        if (authState.isAuthenticated && authState.user) {
            // 已登录用户：从API获取最新额度信息
            const response = await fetch('/api/me/ai-quota');
            if (response.ok) {
                const quotaData = await response.json();
                terminal.writeln(`💰 AI quota: ${quotaData.remaining}/${quotaData.total} | Model: ${quotaData.model || 'deepseek-chat'}`);
            }
        } else {
            // 访客：获取并显示具体的剩余次数
            try {
                const response = await fetch('/api/guest/ai-quota');
                if (response.ok) {
                    const quotaData = await response.json();
                    terminal.writeln(`🆓 Today's free experience: ${quotaData.remaining}/${quotaData.total} times remaining, register an account for more usage opportunities`);
                } else {
                    // 如果API失败，显示默认提示
                    terminal.writeln(`🆓 Today's free experience is limited, register an account for more usage opportunities`);
                }
            } catch (quotaError) {
                // 静默处理配额API错误，显示默认提示
                terminal.writeln(`🆓 Today's free experience is limited, register an account for more usage opportunities`);
            }
        }
    } catch (error) {
        // 静默处理错误，不影响AI对话体验
        console.warn('Failed to get AI quota information:', error);
    }
}

// 🆕 通过统一API路由处理AI消息




// AI辅助函数：处理AI消息 - 使用新的前端Function Calling管理器
async function handleAIMessage(message: string, context: CommandContext): Promise<CommandResult> {
    if (context.source !== 'terminal' || !context.terminal) {
        // 非终端调用，返回基本响应
        return {
            success: true,
            message: 'AI message received, but full conversation functionality is only available in Terminal',
            data: { message }
        };
    }

    try {
        const terminal = context.terminal;

        // 🌟 使用新的模块化架构
        const { getFunctionCallingManager } = await import('@/lib/ai/frontend-function-calling');
        const { createTerminalUIManager } = await import('@/lib/ai/terminal-ui-manager');

        const manager = getFunctionCallingManager();
        const uiManager = createTerminalUIManager(terminal);

        // 使用UI管理器创建回调
        const callbacks = uiManager.createUICallbacks();

        // 处理AI消息
        await manager.processUserMessage(message, callbacks);

        // 清理UI状态并显示最终结果
        uiManager.cleanup();
        await uiManager.displayFinalResult();

        return {
            success: true,
            data: { message: 'Conversation completed' }
        };

    } catch (error) {
        const errorMsg = 'AI service temporarily unavailable: ' + (error instanceof Error ? error.message : 'Unknown error');

        if (context.terminal) {
            // 使用UI管理器显示错误
            try {
                const { createTerminalUIManager } = await import('@/lib/ai/terminal-ui-manager');
                const uiManager = createTerminalUIManager(context.terminal);
                uiManager.displayError(errorMsg, process.env.NODE_ENV === 'development' ? error : undefined);
            } catch {
                // 如果UI管理器也失败，回退到基本显示
                context.terminal.write('\r\x1b[K');
                context.terminal.writeln(`❌ ${errorMsg}`);
                context.terminal.writeln('');
            }
        }

        return {
            success: false,
            error: errorMsg
        };
    }
}

// AI辅助函数：处理AI配置管理
async function handleAIConfig(args: any, context: CommandContext): Promise<CommandResult> {
    try {
        const configInfoText = `🤖 AI system configuration information - server-side intelligent conversation system

📊 System architecture:
• Deployment mode: Server-side processing, no local configuration
• AI model: DeepSeek Chat (cost optimization, performance excellence)
• API management: Unified server-side API key
• Function support: Streaming conversation + Function Calling

🔒 Access control:
• Guest user: 10 free experiences per day
• Registered user: 100 free conversations per default
• Rate limit: Intelligent frequency control
• Cost control: Server-side unified management

🎯 Usage:
  ai              # Enter AI conversation mode, ready to use
  ai chat hello    # Start conversation directly
  ai status       # View personal usage status
  ai help         # View detailed help

💡 System advantages:
• 🚀 Just-in-use: No configuration, access to experience
• 💰 Cost optimization: DeepSeek model, cheaper than OpenAI by 95%
• 🔒 Security controllable: API key isolated on server side
• 📊 Quota management: Intelligent allocation, prevent abuse
• ⚡ High performance: Edge Runtime, low latency response

📈 Upgrade instructions:
The new version of the AI system has been completely migrated to the server side, providing:
• Better security (API key isolation)
• Lower cost (bulk purchase, batch optimization)
• More stable service (professional maintenance)
• Easier use (zero configuration experience)

The local configuration function of the old version has been disabled, please use the new server-side AI directly!`;

        return {
            success: true,
            message: 'AI configuration information',
            terminalOutput: context.source === 'terminal' ? configInfoText : undefined,
            shouldUpdateTerminal: context.source === 'terminal'
        };

    } catch (error) {
        return {
            success: false,
            error: 'Configuration failed: ' + (error instanceof Error ? error.message : 'Unknown error')
        };
    }
}

// AI辅助函数：处理AI模型管理
async function handleAIModel(args: any, context: CommandContext): Promise<CommandResult> {
    try {
        const modelInfoText = `🤖 AI model information - server-side unified management

📊 Current model configuration:
• Using model: DeepSeek Chat
• Service provider: DeepSeek (cost optimization)
• Model characteristics: High performance, low cost, support Function Calling
• Management method: Server-side unified configuration

💡 Model advantages:
• 💰 Cost efficiency: Cheaper than OpenAI by 95%
• ⚡ High performance: Fast response speed, excellent quality
• 🔧 Full functionality: Support streaming conversation and Function Calling
• 🔒 Unified management: Server-side configuration, no personal maintenance

🎯 New system features:
In the new server-side system, the AI model is configured and optimized by the administrator:
• 📊 Performance monitoring: Real-time monitoring of model response quality
• 🔄 Automatic optimization: Automatically adjust parameters based on usage
• 💰 Cost control: Bulk purchase, reduce usage costs
• 🛡️ Security guarantee: API key isolation, prevent abuse

🎯 Usage suggestions:
  ai              # Start using directly, no configuration required
  ai chat hello    # Experience the powerful capabilities of DeepSeek Chat
  ai status       # View personal usage status

📈 Upgrade instructions:
The new version of the AI system has removed the local model configuration function, using the DeepSeek model optimized on the server side,
Providing a more stable, economical, and secure AI experience!`;

        return {
            success: true,
            message: 'AI model information',
            terminalOutput: context.source === 'terminal' ? modelInfoText : undefined,
            shouldUpdateTerminal: context.source === 'terminal'
        };



    } catch (error) {
        return {
            success: false,
            error: 'Model operation failed: ' + (error instanceof Error ? error.message : 'Unknown error')
        };
    }
}

// ========== 登录/登出辅助函数 ==========

// 获取终端认证状态 - 按照AuthUtils.ts迁移的完整实现
function getTerminalAuthState(terminal: any): {
    isAuthenticated: boolean;
    username: string;
    user?: any;
    isLoading?: boolean;
} {
    let user: any = null;
    let isLoading = false;
    let isAuthenticated = false;
    let username = 'Guest';

    try {
        // 方法1: 从认证插件获取状态
        const authPlugin = terminal.authPromptPlugin;
        if (authPlugin && typeof authPlugin.getAuthState === 'function') {
            try {
                const authState = authPlugin.getAuthState();
                user = authState.user;
                isLoading = authState.isLoading;

                // 智能处理加载状态：如果明确没有用户，不应该显示加载中
                if (!user && isLoading) {
                    isLoading = false;
                }
            } catch (error) {
                // 静默处理错误
            }
        }

        // 方法2: 从当前提示符推断状态（作为备用方法）
        if (authPlugin && typeof authPlugin.getCurrentPrompt === 'function') {
            try {
                const currentPrompt = authPlugin.getCurrentPrompt();

                // 根据提示符判断状态
                if (currentPrompt.startsWith('Guest')) {
                    // Guest状态，明确未登录
                    isAuthenticated = false;
                    username = 'Guest';
                    if (user || isLoading) {
                        // 重置用户状态
                        user = null;
                        isLoading = false;
                    }
                } else if (currentPrompt.startsWith('...')) {
                    // 加载状态，保持当前状态但不覆盖
                } else {
                    // 其他状态，尝试从提示符提取用户名
                    const promptMatch = currentPrompt.match(/^([^\s😊]+)/);
                    if (promptMatch) {
                        username = promptMatch[1];
                        isAuthenticated = true;

                        // 如果认证插件没有用户信息，但提示符显示已登录，强制设置为非加载状态
                        if (!user && isLoading) {
                            isLoading = false;
                        }
                    }
                }
            } catch (error) {
                // 静默处理错误
            }
        }

        // 方法3: 回退到简单的getCurrentUser检查
        if (!isAuthenticated && authPlugin?.getCurrentUser) {
            try {
                const currentUser = authPlugin.getCurrentUser();
                if (currentUser && currentUser.email !== 'Guest') {
                    isAuthenticated = true;
                    username = currentUser.email;
                    user = currentUser;
                }
            } catch (error) {
                // 静默处理错误
            }
        }

        // 方法4: 直接从Supabase客户端检查（最后的备用方法）
        if (!user && !isAuthenticated) {
            const supabaseClient = terminal._supabaseClient;
            if (supabaseClient?.auth?.getUser) {
                try {
                    // 尝试同步获取当前会话（不等待异步结果）
                    const sessionPromise = supabaseClient.auth.getSession();
                    if (sessionPromise && typeof sessionPromise.then !== 'function') {
                        // 如果返回的不是Promise，直接使用
                        const session = sessionPromise;
                        if (session?.data?.session?.user) {
                            user = session.data.session.user;
                            isAuthenticated = true;
                            username = user.email || 'User';
                        }
                    }
                } catch (error) {
                    // 静默处理
                }
            }
        }

        // 确定最终的认证状态
        if (user && !isAuthenticated) {
            isAuthenticated = true;
            username = user.email || user.user_metadata?.name || 'User';
        }

        // 最终安全检查：确保逻辑一致性
        if (!user && !isAuthenticated) {
            // 明确无用户状态，应该显示Guest而不是加载中
            isLoading = false;
            username = 'Guest';
        }

        return {
            isAuthenticated,
            username,
            user,
            isLoading
        };

    } catch (error) {
        return {
            isAuthenticated: false,
            username: 'Guest',
            user: null,
            isLoading: false
        };
    }
}

// 验证邮箱格式
function validateEmailFormat(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 检查邮箱是否存在
async function checkEmailExists(email: string): Promise<boolean> {
    try {
        const response = await fetch('/api/auth/check-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email }),
        });

        if (response.ok) {
            const data = await response.json();
            return data.exists;
        } else {
            return true; // 假设存在，避免阻塞登录
        }
    } catch (error) {
        return true; // 假设存在，避免阻塞登录
    }
}

// 获取邮箱输入
async function getEmailInput(terminal: any): Promise<string | null> {
    try {
        const readline = getTerminalReadline(terminal);
        if (!readline) {
            return null;
        }

        const maxAttempts = 3;
        let attempts = 0;

        while (attempts < maxAttempts) {
            attempts++;

            const promptText = attempts === 1 ? 'Email: ' : `Email (${attempts}/${maxAttempts}): `;
            const email = await readline.read(promptText);

            if (!email || email.trim() === '') {
                return null;
            }

            const trimmedEmail = email.trim();

            if (!validateEmailFormat(trimmedEmail)) {
                terminal.writeln('Invalid email format');
                if (attempts >= maxAttempts) {
                    return null;
                }
                continue;
            }

            const emailExists = await checkEmailExists(trimmedEmail);
            if (!emailExists) {
                terminal.writeln('Email not registered');
                if (attempts >= maxAttempts) {
                    return null;
                }
                continue;
            }

            return trimmedEmail;
        }

        return null;
    } catch (error) {
        return null;
    }
}

// 获取终端readline实例
function getTerminalReadline(terminal: any): any {
    if (terminal.readline) {
        return terminal.readline;
    }

    if (terminal._originalTerminal && terminal._originalTerminal.readline) {
        return terminal._originalTerminal.readline;
    }

    return null;
}

// 安全的密码输入
async function getPasswordInput(terminal: any, promptText: string = 'Password: '): Promise<string | null> {
    try {
        const password = await getPasswordInputSecure(terminal, promptText);

        if (password === null) {
            const readline = getTerminalReadline(terminal);
            if (!readline) {
                return null;
            }

            const basicPassword = await readline.read(promptText);
            if (!basicPassword || basicPassword.trim() === '' || basicPassword.length < 6) {
                return null;
            }
            return basicPassword.trim();
        }

        if (password.trim() === '' || password.length < 6) {
            return null;
        }

        return password.trim();
    } catch (error) {
        return null;
    }
}

// 安全的密码输入（使用*号隐藏）
async function getPasswordInputSecure(terminal: any, promptText: string): Promise<string | null> {
    return new Promise((resolve) => {
        let password = '';
        let isReading = true;

        const originalTerminal = terminal._originalTerminal || terminal;

        if (!originalTerminal.write || !originalTerminal.onData) {
            return resolve(null);
        }

        try {
            originalTerminal.write(promptText);

            const handleKeyPress = (data: string) => {
                if (!isReading) return;

                const key = data.charCodeAt(0);

                if (key === 13) { // Enter
                    isReading = false;
                    originalTerminal.write('\r\n');
                    resolveWithCleanup(password);
                } else if (key === 3) { // Ctrl+C
                    isReading = false;
                    originalTerminal.write('^C\r\n');
                    resolveWithCleanup(null);
                } else if (key === 127 || key === 8) { // Backspace
                    if (password.length > 0) {
                        password = password.slice(0, -1);
                        originalTerminal.write('\b \b');
                    }
                } else if (key >= 32 && key <= 126) { // 可打印字符
                    password += data;
                    originalTerminal.write('*');
                }
            };

            const dataListener = originalTerminal.onData(handleKeyPress);

            const timeout = setTimeout(() => {
                if (isReading) {
                    isReading = false;
                    originalTerminal.write('\r\n');
                    resolveWithCleanup(null);
                }
            }, 60000);

            const cleanup = () => {
                clearTimeout(timeout);
                if (dataListener && dataListener.dispose) {
                    dataListener.dispose();
                }
            };

            const resolveWithCleanup = (value: string | null) => {
                cleanup();
                resolve(value);
            };

        } catch (error) {
            resolve(null);
        }
    });
}

// 执行直接登录
async function performDirectLogin(terminal: any, email: string, password: string): Promise<CommandResult> {
    return await performLoginWithCredentials(terminal, email, password);
}

// 执行交互式登录
async function performInteractiveLogin(terminal: any, email: string): Promise<CommandResult> {
    const maxAttempts = 3;
    let attempts = 0;

    while (attempts < maxAttempts) {
        attempts++;

        try {
            const promptText = attempts === 1 ? 'Password: ' : `Password (${attempts}/${maxAttempts}): `;
            let password = await getPasswordInputSecure(terminal, promptText);

            if (password === null) {
                const readline = getTerminalReadline(terminal);
                if (!readline) {
                    return { success: false, message: 'Input failed' };
                }
                password = await readline.read(promptText);
            }

            if (!password || password.trim() === '') {
                terminal.writeln('Login cancelled');
                return { success: false, message: 'Login cancelled' };
            }

            const trimmedPassword = password.trim();

            if (trimmedPassword.length < 6) {
                terminal.writeln('Password must be at least 6 characters');
                if (attempts >= maxAttempts) {
                    return { success: false, message: 'Exceeded maximum attempts' };
                }
                continue;
            }

            const result = await performLoginWithCredentials(terminal, email, trimmedPassword);

            if (result.success) {
                return result;
            } else {
                if (result.message && (
                    result.message.includes('Email or password error') ||
                    result.message.includes('Invalid login credentials')
                )) {
                    terminal.writeln(`Password error (${attempts}/${maxAttempts})`);

                    if (attempts >= maxAttempts) {
                        return { success: false, message: 'Exceeded maximum attempts' };
                    }

                    continue;
                } else {
                    return result;
                }
            }

        } catch (error) {
            if (attempts >= maxAttempts) {
                return { success: false, message: 'Exceeded maximum attempts' };
            }
        }
    }

    return { success: false, message: 'Exceeded maximum attempts' };
}

// 执行登录操作（核心逻辑）
async function performLoginWithCredentials(terminal: any, email: string, password: string): Promise<CommandResult> {
    try {
        const supabaseClient = terminal._supabaseClient;
        if (!supabaseClient) {
            terminal.writeln('Authentication service unavailable');
            return { success: false, message: 'Authentication service unavailable' };
        }

        let authListenerResolved = false;
        const authStatePromise = new Promise<{ success: boolean; user?: any; session?: any }>((resolve) => {
            const { data: authListener } = supabaseClient.auth.onAuthStateChange((event: string, session: any) => {
                if (event === 'SIGNED_IN' && session?.user && !authListenerResolved) {
                    authListenerResolved = true;
                    authListener.subscription.unsubscribe();
                    resolve({ success: true, user: session.user, session });
                }
            });

            setTimeout(() => {
                if (!authListenerResolved) {
                    authListenerResolved = true;
                    authListener.subscription.unsubscribe();
                    resolve({ success: false });
                }
            }, 5000);
        });

        const loginPromise = supabaseClient.auth.signInWithPassword({
            email: String(email),
            password: String(password)
        });

        const timeoutPromise = new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), 3000)
        );

        try {
            const { data, error } = await Promise.race([loginPromise, timeoutPromise]);

            if (error) {
                let errorMessage = 'Login failed';

                if (error.message.includes('Invalid login credentials')) {
                    errorMessage = 'Email or password error';
                } else if (error.message.includes('Email not confirmed')) {
                    errorMessage = 'Email not verified';
                } else if (error.message.includes('Too many requests')) {
                    errorMessage = 'Too many requests';
                }

                terminal.writeln(errorMessage);
                return { success: false, message: errorMessage };
            }

            if (data.user) {
                updateTerminalAuthState(terminal, data.user, data.session);

                // 等待认证状态稳定
                try {
                    await waitForTerminalAuthState(terminal, 2000);
                } catch (error) {
                    // 忽略等待错误
                }

                return { success: true };
            }

        } catch (timeoutError) {
            const authResult = await authStatePromise;

            if (authResult.success && authResult.user) {
                updateTerminalAuthState(terminal, authResult.user, authResult.session);

                // 等待认证状态稳定
                try {
                    await waitForTerminalAuthState(terminal, 2000);
                } catch (error) {
                    // 忽略等待错误
                }

                return { success: true };
            }
        }

        // 等待一段时间让认证状态更新
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 再次检查认证状态
        const currentState = getTerminalAuthState(terminal);
        if (currentState.isAuthenticated && currentState.username !== 'Guest') {
            return { success: true };
        }

        terminal.writeln('Login failed');
        return { success: false, message: 'Login failed' };

    } catch (error) {
        terminal.writeln('Login failed');
        return { success: false, message: 'Login failed' };
    }
}

// 更新终端认证状态
function updateTerminalAuthState(terminal: any, user: any, session: any): void {
    try {
        const authPlugin = terminal.authPromptPlugin;
        if (authPlugin?.setAuthState) {
            authPlugin.setAuthState(user, session, false);
        }
    } catch (error) {
        // 静默处理
    }
}

// 快速登出策略 - 优化性能的生产级实现
async function performQuickLogout(terminal: any): Promise<{ success: boolean; error?: string }> {
    // 获取Supabase客户端
    const supabaseClient = terminal._supabaseClient;
    if (!supabaseClient?.auth?.signOut) {
        return { success: false, error: 'Authentication service unavailable' };
    }

    // 并行执行后端API和客户端登出，设置短超时
    const backendLogoutPromise = fetch("/api/auth/logout", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
    }).catch(() => ({ status: 500 }));

    const clientLogoutPromise = supabaseClient.auth.signOut().catch((err: any) => ({ error: err }));

    // 2秒超时保护
    const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Logout operation timeout')), 2000)
    );

    try {
        // 等待后端API结果（优先级最高）
        const backendResult = await Promise.race([backendLogoutPromise, timeoutPromise]);

        if (backendResult && backendResult.status === 200) {
            // 立即同步更新认证状态
            updateAuthStateImmediately(terminal);

            // 等待认证状态稳定
            try {
                await waitForTerminalAuthState(terminal, 1000);
            } catch (error) {
                // 忽略等待错误
            }

            return { success: true };
        }

        // 如果后端失败，检查客户端结果
        const clientResult = await Promise.race([clientLogoutPromise,
            new Promise(resolve => setTimeout(() => resolve({ error: new Error('Client timeout') }), 1000))
        ]);

        if (clientResult && !clientResult.error) {
            updateAuthStateImmediately(terminal);

            // 等待认证状态稳定
            try {
                await waitForTerminalAuthState(terminal, 1000);
            } catch (error) {
                // 忽略等待错误
            }

            return { success: true };
        }

        return { success: false, error: 'Logout operation failed or timed out' };

    } catch (error) {
        return { success: false, error: error instanceof Error ? error.message : 'Logout exception' };
    }
}



// 立即同步更新认证状态
function updateAuthStateImmediately(terminal: any): void {
    try {
        const authPlugin = terminal.authPromptPlugin;
        if (authPlugin?.setAuthState) {
            authPlugin.setAuthState(null, null, false);
        }
    } catch (error) {
        // 静默处理错误
    }
}

// 等待认证状态稳定 - 按照AuthUtils.ts迁移
async function waitForTerminalAuthState(terminal: any, maxWaitMs: number = 3000): Promise<{
    user: any;
    isAuthenticated: boolean;
    username: string;
}> {
    const startTime = Date.now();
    let lastState = getTerminalAuthState(terminal);

    while (Date.now() - startTime < maxWaitMs) {
        const currentState = getTerminalAuthState(terminal);

        // 检查状态是否发生了有意义的变化
        const hasChanged = (
            lastState.isAuthenticated !== currentState.isAuthenticated ||
            lastState.username !== currentState.username
        );

        // 如果状态稳定（不再加载中）或检测到状态变化，则返回
        if (!currentState.isLoading || hasChanged) {
            return {
                user: currentState.user,
                isAuthenticated: currentState.isAuthenticated,
                username: currentState.username
            };
        }

        lastState = currentState;

        // 等待100ms再检查
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 超时后返回当前状态
    const finalState = getTerminalAuthState(terminal);
    return {
        user: finalState.user,
        isAuthenticated: finalState.isAuthenticated,
        username: finalState.username
    };
}

// 调试认证状态 - 用于诊断认证问题
function debugTerminalAuthState(terminal: any): string {
    const state = getTerminalAuthState(terminal);

    let debugInfo = `🔍 Authentication status debugging information:\n`;
    debugInfo += `  isAuthenticated: ${state.isAuthenticated}\n`;
    debugInfo += `  username: ${state.username}\n`;
    debugInfo += `  user: ${state.user ? 'Has user object' : 'No user object'}\n`;
    debugInfo += `  isLoading: ${state.isLoading}\n`;

    // 检查认证插件状态
    const authPlugin = terminal.authPromptPlugin;
    if (authPlugin) {
        debugInfo += `  authPlugin: 存在\n`;

        if (typeof authPlugin.getCurrentPrompt === 'function') {
            try {
                const prompt = authPlugin.getCurrentPrompt();
                debugInfo += `  currentPrompt: "${prompt}"\n`;
            } catch (error) {
                debugInfo += `  currentPrompt: Failed to get\n`;
            }
        }

        if (typeof authPlugin.getAuthState === 'function') {
            try {
                const authState = authPlugin.getAuthState();
                debugInfo += `  authState.user: ${authState.user ? 'Has' : 'No'}\n`;
                debugInfo += `  authState.isLoading: ${authState.isLoading}\n`;
            } catch (error) {
                debugInfo += `  authState: Failed to get\n`;
            }
        }

        if (typeof authPlugin.getCurrentUser === 'function') {
            try {
                const currentUser = authPlugin.getCurrentUser();
                debugInfo += `  currentUser: ${currentUser ? currentUser.email || 'No email' : 'No'}\n`;
            } catch (error) {
                debugInfo += `  currentUser: Failed to get\n`;
            }
        }
    } else {
        debugInfo += `  authPlugin: Does not exist\n`;
    }

    // 检查Supabase客户端
    const supabaseClient = terminal._supabaseClient;
    if (supabaseClient) {
        debugInfo += `  supabaseClient: Exists\n`;
    } else {
        debugInfo += `  supabaseClient: Does not exist\n`;
    }

    return debugInfo;
}

// 强制清除认证状态 - 按照AuthUtils.ts迁移
async function forceLogoutTerminal(terminal: any): Promise<void> {
    // 调用后端API确保服务端登出
    try {
        await fetch("/api/auth/logout", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
        });
    } catch (error) {
        // 静默处理错误
    }

    // 尝试Supabase客户端登出
    const supabaseClient = terminal._supabaseClient;
    if (supabaseClient) {
        try {
            await supabaseClient.auth.signOut();
        } catch (error) {
            // 静默处理错误
        }
    }

    // 清除认证插件状态
    const authPlugin = terminal.authPromptPlugin;
    if (authPlugin && typeof authPlugin.setAuthState === 'function') {
        try {
            authPlugin.setAuthState(null, null, false);
        } catch (error) {
            // 静默处理错误
        }
    }

    // 尝试刷新认证状态
    if (authPlugin && typeof authPlugin.refreshAuthState === 'function') {
        try {
            authPlugin.refreshAuthState();
        } catch (error) {
            // 静默处理错误
        }
    }

    // 清除浏览器存储（localStorage/sessionStorage）
    try {
        // 清除常见的Supabase认证相关存储
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.includes('supabase') || key.includes('auth') || key.includes('session'))) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
    } catch (error) {
        // 静默处理错误
    }
}



// 调试命令
const debugCommand: CommandDefinition = {
    name: 'debug',
    description: 'System debugging information',
    category: 'system',
    parameters: [
        {
            name: 'type',
            type: 'string',
            description: 'Debug type',
            required: false,
            enum: ['auth', 'system', 'all'],
            default: 'auth'
        }
    ],
    examples: ['debug', 'debug type=auth', 'debug type=system'],
    async execute(args, context): Promise<CommandResult> {
        if (!context.terminal || context.source !== 'terminal') {
            return {
                success: false,
                error: 'debug command can only be used in terminal'
            };
        }

        const { type = 'auth' } = args;
        const { terminal } = context;

        let debugOutput = '';

        if (type === 'auth' || type === 'all') {
            debugOutput += debugTerminalAuthState(terminal);
            debugOutput += '\n';
        }

        if (type === 'system' || type === 'all') {
            debugOutput += '🖥️ System status debugging information:\n';
            debugOutput += `  Terminal type: ${terminal.constructor?.name || 'Unknown'}\n`;
            debugOutput += `  Has original terminal: ${!!terminal._originalTerminal}\n`;
            debugOutput += `  Has readline: ${!!terminal.readline}\n`;
            debugOutput += `  Has OSC8 manager: ${!!terminal.osc8LinkManager}\n`;
            debugOutput += `  Has sixelPlugin: ${!!terminal.sixelPlugin}\n`;
            debugOutput += `  Has commandManager: ${!!terminal.commandManager}\n`;
        }

        // 直接写入终端，避免格式化问题
        debugOutput.split('\n').forEach(line => {
            if (line.trim()) {
                terminal.writeln(line);
            }
        });

        return {
            success: true
        };
    }
};

// Exit命令 - 退出AI模式
const exitCommand: CommandDefinition = {
    name: 'exit',
    description: 'Exit AI mode',
    category: 'system',
    parameters: [],
    examples: ['exit'],
    async execute(args, context): Promise<CommandResult> {
        if (context.source === 'terminal' && context.terminal) {
            const terminal = context.terminal;

            // 检查是否在AI模式
            if (terminal.commandManager && terminal.commandManager.isInAIMode()) {
                // 退出AI模式
                terminal.commandManager.disableAIMode();

                return {
                    success: true,
                    terminalOutput: '',
                    shouldUpdateTerminal: true
                };
            } else {
                return {
                    success: true,
                    terminalOutput: '💡 Currently not in AI mode',
                    shouldUpdateTerminal: true
                };
            }
        }

        return {
            success: true,
            message: 'Exit command is only available in Terminal'
        };
    }
};



// 注册所有终端命令
export function registerTerminalCommands() {
    commandRegistry.register(clearCommand);
    commandRegistry.register(menuCommand);
    commandRegistry.register(sixelCommand);
    commandRegistry.register(loginCommand);
    commandRegistry.register(logoutCommand);
    commandRegistry.register(cmtestCommand);
    commandRegistry.register(debugCommand);
    commandRegistry.register(aiCommand);
    commandRegistry.register(exitCommand);
}

// 导出AI辅助函数供TerminalCommandManager使用
export { handleAIMessage }; 