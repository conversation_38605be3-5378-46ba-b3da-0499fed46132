/**
 * 企业级审计日志系统
 * 记录所有敏感操作和安全事件
 */

import { nanoid } from "nanoid";
import { prisma } from "@/lib/prisma";

// 审计日志类型
export interface AuditLogEntry {
  id?: string;
  userId: string;
  action: string;
  resourceType: string;
  resourceId: string;
  reason?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp?: Date;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

// 审计日志配置
interface AuditConfig {
  enabled: boolean;
  retentionDays: number;
  batchSize: number;
  flushInterval: number;
}

class AuditLogger {
  private config: AuditConfig;
  private logQueue: AuditLogEntry[] = [];
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.config = {
      enabled: process.env.AUDIT_LOGGING_ENABLED !== 'false',
      retentionDays: parseInt(process.env.AUDIT_RETENTION_DAYS || '90'),
      batchSize: parseInt(process.env.AUDIT_BATCH_SIZE || '100'),
      flushInterval: parseInt(process.env.AUDIT_FLUSH_INTERVAL || '5000'),
    };

    // 启动定时刷新
    if (this.config.enabled) {
      this.startFlushTimer();
    }
  }

  /**
   * 创建审计日志条目
   */
  async create(entry: AuditLogEntry): Promise<string> {
    if (!this.config.enabled) {
      return 'audit-disabled';
    }

    const auditId = nanoid();
    const logEntry: AuditLogEntry = {
      id: auditId,
      ...entry,
      timestamp: new Date(),
      severity: this.determineSeverity(entry.action),
    };

    // 高严重性事件立即写入
    if (logEntry.severity === 'CRITICAL' || logEntry.severity === 'HIGH') {
      await this.writeToDatabase([logEntry]);
    } else {
      // 其他事件加入队列
      this.logQueue.push(logEntry);
      
      // 队列满时立即刷新
      if (this.logQueue.length >= this.config.batchSize) {
        await this.flush();
      }
    }

    return auditId;
  }

  /**
   * 批量创建审计日志
   */
  async createBatch(entries: AuditLogEntry[]): Promise<string[]> {
    if (!this.config.enabled) {
      return entries.map(() => 'audit-disabled');
    }

    const auditIds = entries.map(() => nanoid());
    const logEntries = entries.map((entry, index) => ({
      id: auditIds[index],
      ...entry,
      timestamp: new Date(),
      severity: this.determineSeverity(entry.action),
    }));

    await this.writeToDatabase(logEntries);
    return auditIds;
  }

  /**
   * 查询审计日志
   */
  async query(filters: {
    userId?: string;
    action?: string;
    resourceType?: string;
    resourceId?: string;
    startDate?: Date;
    endDate?: Date;
    severity?: string;
    limit?: number;
    offset?: number;
  }) {
    try {
      if (!prisma || !prisma.auditLog) {
        console.warn('Audit log table not available');
        return [];
      }

      const where: any = {};

      if (filters.userId) where.userId = filters.userId;
      if (filters.action) where.action = { contains: filters.action };
      if (filters.resourceType) where.resourceType = filters.resourceType;
      if (filters.resourceId) where.resourceId = filters.resourceId;
      if (filters.severity) where.severity = filters.severity;

      if (filters.startDate || filters.endDate) {
        where.timestamp = {};
        if (filters.startDate) where.timestamp.gte = filters.startDate;
        if (filters.endDate) where.timestamp.lte = filters.endDate;
      }

      return await prisma.auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        take: filters.limit || 100,
        skip: filters.offset || 0,
      });
    } catch (error) {
      console.error('Failed to query audit logs:', error);
      return [];
    }
  }

  /**
   * 刷新队列到数据库
   */
  private async flush(): Promise<void> {
    if (this.logQueue.length === 0) return;

    const entries = [...this.logQueue];
    this.logQueue = [];

    try {
      await this.writeToDatabase(entries);
    } catch (error) {
      console.error('Failed to flush audit logs:', error);
      // 失败时重新加入队列
      this.logQueue.unshift(...entries);
    }
  }

  /**
   * 写入数据库
   */
  private async writeToDatabase(entries: AuditLogEntry[]): Promise<void> {
    try {
      // 检查 prisma 和 auditLog 是否存在
      if (!prisma || !prisma.auditLog) {
        console.warn('Audit log table not available, skipping database write');
        return;
      }

      // 使用单个创建而不是批量创建，以避免潜在的兼容性问题
      for (const entry of entries) {
        await prisma.auditLog.create({
          data: {
            id: entry.id!,
            userId: entry.userId,
            action: entry.action,
            resourceType: entry.resourceType,
            resourceId: entry.resourceId,
            reason: entry.reason,
            metadata: entry.metadata || undefined,
            ipAddress: entry.ipAddress,
            userAgent: entry.userAgent,
            timestamp: entry.timestamp!,
            severity: entry.severity!,
          },
        });
      }
    } catch (error) {
      console.error('Failed to write audit logs to database:', error);
      // 不抛出错误，避免阻塞主要功能
      console.warn('Audit logging disabled due to database error');
    }
  }

  /**
   * 确定事件严重性
   */
  private determineSeverity(action: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const criticalActions = [
      'PERMISSION_ESCALATION',
      'UNAUTHORIZED_ACCESS_ATTEMPT',
      'DATA_BREACH',
      'SYSTEM_COMPROMISE'
    ];

    const highActions = [
      'PAPER_ACCESS_DENIED',
      'PERMISSION_CHECK_ERROR',
      'ROLE_ASSIGNMENT_FAILED',
      'AUTHENTICATION_FAILED'
    ];

    const mediumActions = [
      'PAPER_ACCESS_GRANTED',
      'COMMENT_CREATED',
      'ROLE_ASSIGNMENT_SUCCESS',
      'STATUS_CHANGED'
    ];

    if (criticalActions.some(a => action.includes(a))) return 'CRITICAL';
    if (highActions.some(a => action.includes(a))) return 'HIGH';
    if (mediumActions.some(a => action.includes(a))) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * 启动定时刷新
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush().catch(error => {
        console.error('Scheduled audit log flush failed:', error);
      });
    }, this.config.flushInterval);
  }

  /**
   * 停止定时刷新
   */
  public stopFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }

  /**
   * 清理过期日志
   */
  async cleanup(): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);

    const result = await prisma.auditLog.deleteMany({
      where: {
        timestamp: {
          lt: cutoffDate,
        },
      },
    });

    return result.count;
  }

  /**
   * 获取统计信息
   */
  async getStats(timeRange: 'day' | 'week' | 'month' = 'day') {
    const now = new Date();
    const startDate = new Date();
    
    switch (timeRange) {
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
    }

    const [totalLogs, severityStats, actionStats] = await Promise.all([
      prisma.auditLog.count({
        where: { timestamp: { gte: startDate } }
      }),
      prisma.auditLog.groupBy({
        by: ['severity'],
        where: { timestamp: { gte: startDate } },
        _count: { severity: true }
      }),
      prisma.auditLog.groupBy({
        by: ['action'],
        where: { timestamp: { gte: startDate } },
        _count: { action: true },
        orderBy: { _count: { action: 'desc' } },
        take: 10
      })
    ]);

    return {
      totalLogs,
      severityStats,
      actionStats,
      timeRange,
      startDate,
      endDate: now
    };
  }
}

// 单例实例
export const auditLog = new AuditLogger();

// 优雅关闭处理
process.on('SIGTERM', async () => {
  console.log('Flushing audit logs before shutdown...');
  await auditLog['flush']();
  auditLog.stopFlushTimer();
});

process.on('SIGINT', async () => {
  console.log('Flushing audit logs before shutdown...');
  await auditLog['flush']();
  auditLog.stopFlushTimer();
});
