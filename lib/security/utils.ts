/**
 * 安全工具函数
 * 提供各种安全相关的实用功能
 */

import { NextRequest } from "next/server";
import { headers } from "next/headers";
import crypto from "crypto";
import { z } from "zod";

/**
 * 获取客户端真实IP地址
 */
export async function getClientIP(req?: NextRequest): Promise<string> {
  if (req) {
    // 从 NextRequest 获取 IP
    const forwarded = req.headers.get('x-forwarded-for');
    const realIP = req.headers.get('x-real-ip');
    const cfConnectingIP = req.headers.get('cf-connecting-ip');

    if (cfConnectingIP) return cfConnectingIP;
    if (realIP) return realIP;
    if (forwarded) return forwarded.split(',')[0].trim();

    return 'unknown';
  } else {
    // 从 headers() 获取 IP（服务端组件）
    try {
      const headersList = await headers();
      const forwarded = headersList.get('x-forwarded-for');
      const realIP = headersList.get('x-real-ip');
      const cfConnectingIP = headersList.get('cf-connecting-ip');

      if (cfConnectingIP) return cfConnectingIP;
      if (realIP) return realIP;
      if (forwarded) return forwarded.split(',')[0].trim();

      return 'unknown';
    } catch {
      return 'unknown';
    }
  }
}

/**
 * 获取用户代理字符串
 */
export async function getUserAgent(req?: NextRequest): Promise<string> {
  if (req) {
    return req.headers.get('user-agent') || 'unknown';
  } else {
    try {
      const headersList = await headers();
      return headersList.get('user-agent') || 'unknown';
    } catch {
      return 'unknown';
    }
  }
}

/**
 * 生成安全的随机字符串
 */
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * 哈希敏感数据
 */
export function hashSensitiveData(data: string, salt?: string): string {
  const actualSalt = salt || crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512');
  return `${actualSalt}:${hash.toString('hex')}`;
}

/**
 * 验证哈希数据
 */
export function verifySensitiveData(data: string, hashedData: string): boolean {
  const [salt, hash] = hashedData.split(':');
  const verifyHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512');
  return hash === verifyHash.toString('hex');
}

/**
 * 清理和验证输入数据
 */
export const inputValidation = {
  // 清理 HTML 标签
  sanitizeHtml: (input: string): string => {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .trim();
  },

  // 验证邮箱格式
  email: z.string().email().transform(email => email.toLowerCase().trim()),

  // 验证 CUID 格式
  cuid: z.string().cuid(),

  // 验证 URL 格式
  url: z.string().url(),

  // 清理用户输入（保留换行符和制表符）
  cleanUserInput: (input: string, maxLength: number = 1000): string => {
    return input
      .slice(0, maxLength)
      // 移除有害控制字符，但保留：
      // - \t (0x09) 制表符
      // - \n (0x0A) 换行符
      // - \r (0x0D) 回车符
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, '')
      .trim();
  },

  // 验证文件名
  filename: z.string()
    .min(1)
    .max(255)
    .regex(/^[a-zA-Z0-9._-]+$/, 'Invalid filename format'),
};

/**
 * 速率限制器
 */
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private windowMs: number;
  private maxRequests: number;

  constructor(windowMs: number = 15 * 60 * 1000, maxRequests: number = 100) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;

    // 定期清理过期记录
    setInterval(() => this.cleanup(), this.windowMs);
  }

  /**
   * 检查是否超过速率限制
   */
  isRateLimited(identifier: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    
    // 移除过期请求
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return true;
    }

    // 记录新请求
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return false;
  }

  /**
   * 获取剩余请求次数
   */
  getRemainingRequests(identifier: string): number {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    return Math.max(0, this.maxRequests - validRequests.length);
  }

  /**
   * 清理过期记录
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [identifier, requests] of this.requests.entries()) {
      const validRequests = requests.filter(time => now - time < this.windowMs);
      if (validRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, validRequests);
      }
    }
  }
}

/**
 * CSRF Token 管理
 */
export class CSRFTokenManager {
  private static instance: CSRFTokenManager;
  private tokens: Map<string, { token: string; expires: number }> = new Map();
  private readonly tokenLifetime = 60 * 60 * 1000; // 1小时

  static getInstance(): CSRFTokenManager {
    if (!CSRFTokenManager.instance) {
      CSRFTokenManager.instance = new CSRFTokenManager();
    }
    return CSRFTokenManager.instance;
  }

  /**
   * 生成 CSRF Token
   */
  generateToken(sessionId: string): string {
    const token = generateSecureToken(32);
    const expires = Date.now() + this.tokenLifetime;
    
    this.tokens.set(sessionId, { token, expires });
    
    // 清理过期 token
    this.cleanup();
    
    return token;
  }

  /**
   * 验证 CSRF Token
   */
  verifyToken(sessionId: string, token: string): boolean {
    const stored = this.tokens.get(sessionId);
    
    if (!stored || stored.expires < Date.now()) {
      this.tokens.delete(sessionId);
      return false;
    }

    return stored.token === token;
  }

  /**
   * 清理过期 token
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [sessionId, data] of this.tokens.entries()) {
      if (data.expires < now) {
        this.tokens.delete(sessionId);
      }
    }
  }
}

/**
 * 安全头部设置
 */
export const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self'",
    "frame-ancestors 'none'",
  ].join('; '),
};

/**
 * 检查可疑活动
 */
export function detectSuspiciousActivity(
  userAgent: string,
  ipAddress: string,
  requestPath: string
): { suspicious: boolean; reasons: string[] } {
  const reasons: string[] = [];

  // 检查用户代理
  if (!userAgent || userAgent === 'unknown' || userAgent.length < 10) {
    reasons.push('Suspicious or missing user agent');
  }

  // 检查常见爬虫特征
  const botPatterns = [
    /bot/i, /crawler/i, /spider/i, /scraper/i,
    /curl/i, /wget/i, /python/i, /java/i
  ];
  
  if (botPatterns.some(pattern => pattern.test(userAgent))) {
    reasons.push('Bot-like user agent detected');
  }

  // 检查 IP 地址
  if (ipAddress === 'unknown' || ipAddress === '127.0.0.1') {
    reasons.push('Suspicious IP address');
  }

  // 检查请求路径
  const suspiciousPatterns = [
    /\.\.\//, // 路径遍历
    /\.(php|asp|jsp)$/, // 非预期文件类型
    /<script/i, // XSS 尝试
    /union.*select/i, // SQL 注入尝试
  ];

  if (suspiciousPatterns.some(pattern => pattern.test(requestPath))) {
    reasons.push('Suspicious request pattern');
  }

  return {
    suspicious: reasons.length > 0,
    reasons
  };
}

/**
 * 创建安全的响应头
 */
export function createSecureHeaders(): Record<string, string> {
  return {
    ...securityHeaders,
    'X-Request-ID': generateSecureToken(16),
    'X-Timestamp': new Date().toISOString(),
  };
}
