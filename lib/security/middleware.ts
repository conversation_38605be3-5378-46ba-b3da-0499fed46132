/**
 * 企业级安全中间件
 * 提供速率限制、安全检查、CSRF保护等功能
 */

import { NextRequest, NextResponse } from "next/server";
import { RateLimiter, detectSuspiciousActivity, getClientIP, getUserAgent, createSecureHeaders } from "./utils";
import { auditLog } from "./audit";

// 全局速率限制器实例
const globalRateLimiter = new RateLimiter(60 * 1000, 100); // 每分钟100次请求
const apiRateLimiter = new RateLimiter(15 * 60 * 1000, 1000); // 每15分钟1000次API请求
const authRateLimiter = new RateLimiter(15 * 60 * 1000, 10); // 每15分钟10次认证尝试

// 安全配置
interface SecurityConfig {
  enableRateLimit: boolean;
  enableSuspiciousActivityDetection: boolean;
  enableAuditLogging: boolean;
  blockSuspiciousRequests: boolean;
  maxRequestSize: number; // bytes
}

const defaultConfig: SecurityConfig = {
  enableRateLimit: process.env.SECURITY_RATE_LIMIT !== 'false',
  enableSuspiciousActivityDetection: process.env.SECURITY_SUSPICIOUS_DETECTION !== 'false',
  enableAuditLogging: process.env.SECURITY_AUDIT_LOGGING !== 'false',
  blockSuspiciousRequests: process.env.SECURITY_BLOCK_SUSPICIOUS !== 'false',
  maxRequestSize: parseInt(process.env.SECURITY_MAX_REQUEST_SIZE || '10485760'), // 10MB
};

/**
 * 安全中间件主函数
 */
export async function securityMiddleware(
  req: NextRequest,
  config: Partial<SecurityConfig> = {}
): Promise<NextResponse | null> {
  const finalConfig = { ...defaultConfig, ...config };
  const clientIP = await getClientIP(req);
  const userAgent = await getUserAgent(req);
  const pathname = req.nextUrl.pathname;
  const method = req.method;

  try {
    // 1. 请求大小检查
    const contentLength = req.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > finalConfig.maxRequestSize) {
      await logSecurityEvent('REQUEST_TOO_LARGE', {
        clientIP,
        userAgent,
        pathname,
        contentLength: parseInt(contentLength),
      });

      return NextResponse.json(
        { error: 'Request too large' },
        { status: 413, headers: createSecureHeaders() }
      );
    }

    // 2. 可疑活动检测
    if (finalConfig.enableSuspiciousActivityDetection) {
      const suspiciousCheck = detectSuspiciousActivity(userAgent, clientIP, pathname);
      
      if (suspiciousCheck.suspicious) {
        await logSecurityEvent('SUSPICIOUS_ACTIVITY', {
          clientIP,
          userAgent,
          pathname,
          reasons: suspiciousCheck.reasons,
        });

        if (finalConfig.blockSuspiciousRequests) {
          return NextResponse.json(
            { error: 'Request blocked' },
            { status: 403, headers: createSecureHeaders() }
          );
        }
      }
    }

    // 3. 速率限制检查
    if (finalConfig.enableRateLimit) {
      const rateLimitResult = checkRateLimit(req, clientIP, pathname, method);
      
      if (rateLimitResult.blocked) {
        await logSecurityEvent('RATE_LIMIT_EXCEEDED', {
          clientIP,
          userAgent,
          pathname,
          method,
          rateLimitType: rateLimitResult.type,
        });

        return NextResponse.json(
          {
            error: 'Rate limit exceeded',
            retryAfter: rateLimitResult.retryAfter,
          },
          {
            status: 429,
            headers: {
              ...createSecureHeaders(),
              'Retry-After': (rateLimitResult.retryAfter || 60).toString(),
            }
          }
        );
      }
    }

    // 4. 记录正常请求（仅API端点）
    if (finalConfig.enableAuditLogging && pathname.startsWith('/api/')) {
      await logSecurityEvent('API_REQUEST', {
        clientIP,
        userAgent,
        pathname,
        method,
      });
    }

    return null; // 继续处理请求

  } catch (error) {
    console.error('Security middleware error:', error);
    
    await logSecurityEvent('MIDDLEWARE_ERROR', {
      clientIP,
      userAgent,
      pathname,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    // 安全策略：出错时允许请求通过，但记录错误
    return null;
  }
}

/**
 * 速率限制检查
 */
function checkRateLimit(
  req: NextRequest,
  clientIP: string,
  pathname: string,
  method: string
): { blocked: boolean; type?: string; retryAfter?: number } {
  
  // 认证端点特殊限制
  if (pathname.includes('/auth/') || pathname.includes('/login') || pathname.includes('/register')) {
    const authKey = `auth:${clientIP}`;
    if (authRateLimiter.isRateLimited(authKey)) {
      return { blocked: true, type: 'auth', retryAfter: 900 }; // 15分钟
    }
  }

  // API端点限制
  if (pathname.startsWith('/api/')) {
    const apiKey = `api:${clientIP}`;
    if (apiRateLimiter.isRateLimited(apiKey)) {
      return { blocked: true, type: 'api', retryAfter: 900 }; // 15分钟
    }
  }

  // 全局限制
  const globalKey = `global:${clientIP}`;
  if (globalRateLimiter.isRateLimited(globalKey)) {
    return { blocked: true, type: 'global', retryAfter: 60 }; // 1分钟
  }

  return { blocked: false };
}

/**
 * 记录安全事件
 */
async function logSecurityEvent(
  action: string,
  metadata: Record<string, any>
): Promise<void> {
  try {
    await auditLog.create({
      userId: 'system',
      action: `SECURITY_${action}`,
      resourceType: 'SECURITY',
      resourceId: 'middleware',
      reason: `Security event: ${action}`,
      metadata,
      ipAddress: metadata.clientIP,
      userAgent: metadata.userAgent,
    });
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
}

/**
 * 检查请求是否来自可信来源
 */
export function isTrustedRequest(req: NextRequest): boolean {
  const origin = req.headers.get('origin');
  const referer = req.headers.get('referer');
  const host = req.headers.get('host');

  // 检查是否来自同一域名
  if (origin && host) {
    try {
      const originUrl = new URL(origin);
      return originUrl.host === host;
    } catch {
      return false;
    }
  }

  // 检查 referer
  if (referer && host) {
    try {
      const refererUrl = new URL(referer);
      return refererUrl.host === host;
    } catch {
      return false;
    }
  }

  // 对于没有 origin 和 referer 的请求（如直接访问），允许通过
  return true;
}

/**
 * 验证请求头安全性
 */
export function validateRequestHeaders(req: NextRequest): {
  valid: boolean;
  issues: string[];
} {
  const issues: string[] = [];

  // 检查 Content-Type
  const contentType = req.headers.get('content-type');
  if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
    if (!contentType) {
      issues.push('Missing Content-Type header');
    } else if (!contentType.includes('application/json') && 
               !contentType.includes('multipart/form-data') &&
               !contentType.includes('application/x-www-form-urlencoded')) {
      issues.push('Unsupported Content-Type');
    }
  }

  // 检查 User-Agent
  const userAgent = req.headers.get('user-agent');
  if (!userAgent || userAgent.length < 10) {
    issues.push('Suspicious or missing User-Agent');
  }

  // 检查可疑头部
  const suspiciousHeaders = [
    'x-forwarded-host',
    'x-original-url',
    'x-rewrite-url',
  ];

  for (const header of suspiciousHeaders) {
    if (req.headers.get(header)) {
      issues.push(`Suspicious header: ${header}`);
    }
  }

  return {
    valid: issues.length === 0,
    issues,
  };
}

/**
 * 创建安全响应
 */
export function createSecureResponse(
  data: any,
  status: number = 200,
  additionalHeaders: Record<string, string> = {}
): NextResponse {
  return NextResponse.json(data, {
    status,
    headers: {
      ...createSecureHeaders(),
      ...additionalHeaders,
    },
  });
}

/**
 * 获取速率限制状态
 */
export function getRateLimitStatus(clientIP: string): {
  global: { remaining: number; resetTime: Date };
  api: { remaining: number; resetTime: Date };
  auth: { remaining: number; resetTime: Date };
} {
  const now = new Date();
  
  return {
    global: {
      remaining: globalRateLimiter.getRemainingRequests(`global:${clientIP}`),
      resetTime: new Date(now.getTime() + 60 * 1000),
    },
    api: {
      remaining: apiRateLimiter.getRemainingRequests(`api:${clientIP}`),
      resetTime: new Date(now.getTime() + 15 * 60 * 1000),
    },
    auth: {
      remaining: authRateLimiter.getRemainingRequests(`auth:${clientIP}`),
      resetTime: new Date(now.getTime() + 15 * 60 * 1000),
    },
  };
}
