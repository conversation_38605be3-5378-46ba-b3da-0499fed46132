// Review页面角色头像工具
// 用于审稿匿名制，每个角色使用固定颜色和缩写字母

interface RoleAvatarConfig {
  abbreviation: string;  // 缩写字母
  color: string;         // 背景颜色 (Hex)
  textColor: string;     // 文字颜色 (Hex)
}

// 角色头像配置
const ROLE_AVATAR_CONFIG: Record<string, RoleAvatarConfig> = {
  // 作者
  'Author': {
    abbreviation: 'A',
    color: '#3b82f6',     // 现代蓝色 (更亮更现代)
    textColor: '#ffffff'
  },

  // 编辑
  'Editor': {
    abbreviation: 'E',
    color: '#10b981',     // 现代绿色 (更清新)
    textColor: '#ffffff'
  },

  // 审稿人 1-9 - 全新的现代配色方案
  'Reviewer 1': {
    abbreviation: 'R1',
    color: '#ef4444',     // 现代红色 (更鲜艳)
    textColor: '#ffffff'
  },
  'Reviewer 2': {
    abbreviation: 'R2',
    color: '#f97316',     // 现代橙色 (更温暖)
    textColor: '#ffffff'
  },
  'Reviewer 3': {
    abbreviation: 'R3',
    color: '#8b5cf6',     // 现代紫色 (替代土黄色)
    textColor: '#ffffff'
  },
  'Reviewer 4': {
    abbreviation: 'R4',
    color: '#06b6d4',     // 现代青色 (清新明亮)
    textColor: '#ffffff'
  },
  'Reviewer 5': {
    abbreviation: 'R5',
    color: '#ec4899',     // 现代粉色 (活泼可爱)
    textColor: '#ffffff'
  },
  'Reviewer 6': {
    abbreviation: 'R6',
    color: '#84cc16',     // 现代绿黄 (自然活力)
    textColor: '#ffffff'
  },
  'Reviewer 7': {
    abbreviation: 'R7',
    color: '#f59e0b',     // 现代金色 (温暖高贵)
    textColor: '#ffffff'
  },
  'Reviewer 8': {
    abbreviation: 'R8',
    color: '#6366f1',     // 现代靛蓝 (深邃稳重)
    textColor: '#ffffff'
  },
  'Reviewer 9': {
    abbreviation: 'R9',
    color: '#14b8a6',     // 现代蓝绿 (清新专业)
    textColor: '#ffffff'
  },
  
  // 默认配置（用于未知角色）
  'Unknown': {
    abbreviation: 'U',
    color: '#6b7280',     // 灰色
    textColor: '#ffffff'
  }
};

/**
 * 根据角色标签获取头像配置
 */
export function getRoleAvatarConfig(roleLabel: string | null): RoleAvatarConfig {
  if (!roleLabel) {
    return ROLE_AVATAR_CONFIG['Unknown'];
  }
  
  // 直接匹配
  if (ROLE_AVATAR_CONFIG[roleLabel]) {
    return ROLE_AVATAR_CONFIG[roleLabel];
  }
  
  // 检查是否是审稿人（处理各种可能的格式）
  const reviewerMatch = roleLabel.match(/Reviewer\s*(\d+)/i);
  if (reviewerMatch) {
    const reviewerNum = parseInt(reviewerMatch[1]);
    const reviewerKey = `Reviewer ${reviewerNum}`;
    if (ROLE_AVATAR_CONFIG[reviewerKey]) {
      return ROLE_AVATAR_CONFIG[reviewerKey];
    }
    
    // 如果超过9个审稿人，使用循环颜色
    const colorIndex = ((reviewerNum - 1) % 9) + 1;
    return ROLE_AVATAR_CONFIG[`Reviewer ${colorIndex}`];
  }
  
  // 检查是否包含Editor
  if (roleLabel.toLowerCase().includes('editor')) {
    return ROLE_AVATAR_CONFIG['Editor'];
  }
  
  // 检查是否包含Author
  if (roleLabel.toLowerCase().includes('author')) {
    return ROLE_AVATAR_CONFIG['Author'];
  }
  
  // 未知角色，返回默认配置
  return ROLE_AVATAR_CONFIG['Unknown'];
}

/**
 * 创建角色头像的React样式对象
 */
export function createRoleAvatarStyle(roleLabel: string | null, size: 'small' | 'medium' | 'large' = 'medium') {
  const config = getRoleAvatarConfig(roleLabel);
  
  // 根据大小调整字体
  const fontSizes = {
    small: '10px',
    medium: '12px', 
    large: '14px'
  };
  
  return {
    backgroundColor: config.color,
    color: config.textColor,
    fontSize: fontSizes[size],
    fontWeight: 'bold' as const,
    display: 'flex' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    borderRadius: '50%' as const,
    border: 'none' as const,
    userSelect: 'none' as const,
  };
}

/**
 * 获取角色缩写字母
 */
export function getRoleAbbreviation(roleLabel: string | null): string {
  const config = getRoleAvatarConfig(roleLabel);
  return config.abbreviation;
} 