import { createClient } from "@supabase/supabase-js";

import {
  createClient as createServerClient,
  createRouteHandlerClient,
} from "@/utils/supabase/server";

// 使用服务角色密钥创建的管理员客户端（用于后端操作）
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!, // 服务器端 Key
  { auth: { persistSession: false } }
);

// 使用匿名密钥的服务器组件客户端（使用用户会话）
export async function getSupabaseServer() {
  return createServerClient();
}

// 使用匿名密钥的路由处理客户端（使用用户会话）
export async function getSupabaseRoute() {
  return createRouteHandlerClient();
}
