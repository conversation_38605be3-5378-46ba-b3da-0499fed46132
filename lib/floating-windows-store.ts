import { create } from 'zustand';

export interface FloatingWindow {
    id: string;
    title: string;
    url?: string;
    originalUrl?: string; // 🆕 原始URL（用于外部链接显示）
    content?: string;
    x: number;
    y: number;
    width: number;
    height: number;
    zIndex: number;
    isClosing?: boolean;
    isVisible?: boolean;
    type?: 'web' | 'image';
    imageUrl?: string;
    imageAlt?: string;
    // 🆕 动画相关字段
    isAnimating?: boolean;      // 是否正在执行缓动动画
    animationType?: 'move' | 'resize' | 'both';  // 动画类型
    animationDuration?: number; // 动画持续时间(ms)
    animationEasing?: string;   // 缓动函数
    // 🆕 全屏功能相关字段
    isMaximized?: boolean;      // 是否处于全屏状态
    originalBounds?: {          // 全屏前的原始位置和尺寸
        x: number;
        y: number;
        width: number;
        height: number;
    };
}

interface FloatingWindowsState {
    windows: FloatingWindow[];
    maxZIndex: number;
    addWindow: (window: Omit<FloatingWindow, 'zIndex' | 'isClosing' | 'isVisible'> & { id?: string }) => string;
    removeWindow: (id: string) => void;
    startClosing: (id: string) => void;
    updateWindow: (id: string, updates: Partial<FloatingWindow>) => void;
    bringToFront: (id: string) => void;
    // 🆕 缓动更新方法
    animatedUpdateWindow: (
        id: string,
        updates: Partial<FloatingWindow>,
        options?: {
            duration?: number;
            easing?: string;
            type?: 'move' | 'resize' | 'both';
        }
    ) => void;
    // 🆕 全屏功能方法
    toggleMaximize: (id: string) => void;
}

export const useFloatingWindows = create<FloatingWindowsState>((set, get) => {
    // 🤖 AI事件监听器 - 监听AI创建窗口的事件
    if (typeof window !== 'undefined') {
        window.addEventListener('ai_create_window', ((event: CustomEvent) => {
            // console.log('🎯 收到AI窗口创建事件:', event.detail);
            const { addWindow } = get();
            const windowData = event.detail;

            // 调用addWindow创建窗口
            const windowId = addWindow(windowData);
            // console.log('✅ AI窗口已创建:', windowId);
        }) as EventListener);

        // console.log('🎧 AI事件监听器已注册');
    }

    return {
        windows: [],
        maxZIndex: 50, // 起始z-index值

        addWindow: (windowData) => {
            // 🔧 修复：使用传入的ID，如果没有才生成新ID
            const id = windowData.id || (Date.now().toString(36) + Math.random().toString(36).substr(2));
            // console.log(`[FloatingWindowStore] 添加窗口，使用ID: ${id}`, windowData.id ? '(传入ID)' : '(生成ID)');

            set((state) => {
                const newZIndex = state.maxZIndex + 1;
                const newWindow: FloatingWindow = {
                    ...windowData,
                    id,
                    zIndex: newZIndex,
                    isClosing: false,
                    isVisible: false, // 初始不可见，用于渐入动画
                    type: windowData.type || 'web', // 默认为web类型
                };
                return {
                    windows: [...state.windows, newWindow],
                    maxZIndex: newZIndex,
                };
            });

            // 短暂延迟后显示窗口，实现渐入效果
            setTimeout(() => {
                set((state) => ({
                    windows: state.windows.map(window =>
                        window.id === id ? { ...window, isVisible: true } : window
                    ),
                }));
            }, 50);

            return id;
        },

        startClosing: (id) => {
            // console.log(`[FloatingWindowStore] 开始关闭动画: ${id}`);
            set((state) => ({
                windows: state.windows.map(window =>
                    window.id === id ? { ...window, isClosing: true } : window
                ),
            }));
            // console.log(`[FloatingWindowStore] 关闭动画状态已设置: ${id}`);
        },

        removeWindow: (id) => {
            // console.log(`[FloatingWindowStore] 移除窗口: ${id}`);
            set((state) => {
                const newWindows = state.windows.filter(window => window.id !== id);
                // console.log(`[FloatingWindowStore] 窗口已移除: ${id}, 剩余窗口数: ${newWindows.length}`);
                return { windows: newWindows };
            });
        },

        updateWindow: (id, updates) => set((state) => ({
            windows: state.windows.map(window =>
                window.id === id ? { ...window, ...updates } : window
            ),
        })),

        bringToFront: (id) => set((state) => {
            const newZIndex = state.maxZIndex + 1;
            return {
                windows: state.windows.map(window =>
                    window.id === id ? { ...window, zIndex: newZIndex } : window
                ),
                maxZIndex: newZIndex,
            };
        }),

        // 🆕 缓动更新窗口（用于命令行操作）
        animatedUpdateWindow: (id, updates, options = {}) => {
            const {
                duration = 300,
                easing = 'ease-out',
                type = 'both'
            } = options;

            // 首先设置动画状态
            set((state) => ({
                windows: state.windows.map(window =>
                    window.id === id ? {
                        ...window,
                        isAnimating: true,
                        animationType: type,
                        animationDuration: duration,
                        animationEasing: easing
                    } : window
                ),
            }));

            // 然后应用更新（CSS transition会处理动画）
            setTimeout(() => {
                set((state) => ({
                    windows: state.windows.map(window =>
                        window.id === id ? { ...window, ...updates } : window
                    ),
                }));
            }, 10); // 短暂延迟确保CSS transition被触发

            // 动画完成后清理状态
            setTimeout(() => {
                set((state) => ({
                    windows: state.windows.map(window =>
                        window.id === id ? {
                            ...window,
                            isAnimating: false,
                            animationType: undefined,
                            animationDuration: undefined,
                            animationEasing: undefined
                        } : window
                    ),
                }));
            }, duration + 50); // 额外50ms确保动画完全结束
        },

        // 🆕 切换全屏状态
        toggleMaximize: (id) => {
            set((state) => {
                const window = state.windows.find(w => w.id === id);
                if (!window) return state;

                if (window.isMaximized) {
                    // 恢复到原始尺寸和位置
                    const originalBounds = window.originalBounds;
                    if (!originalBounds) return state;

                    return {
                        windows: state.windows.map(w =>
                            w.id === id ? {
                                ...w,
                                x: originalBounds.x,
                                y: originalBounds.y,
                                width: originalBounds.width,
                                height: originalBounds.height,
                                isMaximized: false,
                                originalBounds: undefined,
                                isAnimating: true,
                                animationType: 'both' as const,
                                animationDuration: 300,
                                animationEasing: 'ease-out'
                            } : w
                        ),
                    };
                } else {
                    // 保存当前位置和尺寸，然后全屏
                    const screenWidth = typeof globalThis !== 'undefined' ? globalThis.innerWidth : 1920;
                    const screenHeight = typeof globalThis !== 'undefined' ? globalThis.innerHeight : 1080;

                    return {
                        windows: state.windows.map(w =>
                            w.id === id ? {
                                ...w,
                                originalBounds: {
                                    x: w.x,
                                    y: w.y,
                                    width: w.width,
                                    height: w.height
                                },
                                x: 0,
                                y: 0,
                                width: screenWidth,
                                height: screenHeight,
                                isMaximized: true,
                                isAnimating: true,
                                animationType: 'both' as const,
                                animationDuration: 300,
                                animationEasing: 'ease-out'
                            } : w
                        ),
                    };
                }
            });

            // 清除动画状态
            setTimeout(() => {
                set((state) => ({
                    windows: state.windows.map(window =>
                        window.id === id ? {
                            ...window,
                            isAnimating: false,
                            animationType: undefined,
                            animationDuration: undefined,
                            animationEasing: undefined
                        } : window
                    ),
                }));
            }, 300);
        },
    };
});