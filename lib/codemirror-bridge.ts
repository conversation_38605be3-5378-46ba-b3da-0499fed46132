// CodeMirror 桥接模块 - 连接消息系统和 CodeMirror 编辑器
// 设计原则：模块化、类型安全、可扩展

import {
    globalMessageManager,
    CodeMirrorOperationMessage,
    CodeMirrorOperationResponseMessage,
    CodeMirrorUpdateMessage,
    CodeMirrorAIMessage,
    CodeMirrorAIResponseMessage
} from './floating-window-messages';

// CodeMirror 编辑器接口（抽象层，兼容不同 CodeMirror 版本）
export interface CodeMirrorEditor {
    // 基础属性
    state: {
        doc: {
            toString(): string;
            sliceDoc(from?: number, to?: number): string;
            line(n: number): { number: number; from: number; to: number; text: string; };
            lineAt(pos: number): { number: number; from: number; to: number; text: string; };
            length: number;
        };
        selection: {
            main: { anchor: number; head: number; };
            ranges: Array<{ anchor: number; head: number; }>;
        };
        readOnly: boolean;
    };

    // 基础方法
    dispatch(spec: {
        changes?: Array<{ from: number; to?: number; insert?: string; }>;
        selection?: { anchor: number; head?: number; } | Array<{ anchor: number; head?: number; }>;
        effects?: any[];
        scrollIntoView?: boolean;
        userEvent?: string;
    }): void;

    // 监听器
    updateListener?: {
        of(callback: (update: any) => void): any;
    };

    // 扩展功能（可选）
    syntaxTree?: any;
    history?: {
        undo(): boolean;
        redo(): boolean;
        clear(): void;
    };
    decorations?: any;
}

export class CodeMirrorBridge {
    private editor: CodeMirrorEditor | null = null;
    private windowId: string;
    private listeners: Map<string, (update: any) => void> = new Map();
    private updateCallback: ((update: any) => void) | null = null;
    private isReady = false;

    constructor(windowId: string) {
        this.windowId = windowId;
        this.initializeMessageHandlers();
    }

    // 设置 CodeMirror 编辑器实例
    public setEditor(editor: CodeMirrorEditor): void {
        this.editor = editor;
        this.isReady = true;
        this.setupUpdateListener();
        console.log(`[CodeMirrorBridge] Editor ready for window ${this.windowId}`);
    }

    // 检查编辑器是否就绪
    public isEditorReady(): boolean {
        return this.isReady && this.editor !== null;
    }

    // 初始化消息处理器
    private initializeMessageHandlers(): void {
        // 处理 CodeMirror 操作消息
        globalMessageManager.on('CODEMIRROR_OPERATION', async (message: CodeMirrorOperationMessage) => {
            if (message.windowId !== this.windowId) return;

            try {
                const result = await this.handleOperation(message);

                if (message.payload.expectResponse) {
                    await globalMessageManager.sendCodeMirrorResponse(
                        message.id,
                        true,
                        message.payload.operation,
                        result
                    );
                }
            } catch (error) {
                console.error('[CodeMirrorBridge] Operation error:', error);

                if (message.payload.expectResponse) {
                    await globalMessageManager.sendCodeMirrorResponse(
                        message.id,
                        false,
                        message.payload.operation,
                        undefined,
                        error instanceof Error ? error.message : 'Unknown error'
                    );
                }
            }
        });

        // 处理 CodeMirror AI 消息
        globalMessageManager.on('CODEMIRROR_AI', async (message: CodeMirrorAIMessage) => {
            if (message.windowId !== this.windowId) return;

            try {
                const result = await this.handleAIOperation(message);

                if (message.payload.expectResponse) {
                    await globalMessageManager.sendCodeMirrorAIResponse(
                        message.id,
                        message.payload.action,
                        true,
                        result
                    );
                }
            } catch (error) {
                console.error('[CodeMirrorBridge] AI operation error:', error);

                if (message.payload.expectResponse) {
                    await globalMessageManager.sendCodeMirrorAIResponse(
                        message.id,
                        message.payload.action,
                        false,
                        undefined,
                        undefined,
                        error instanceof Error ? error.message : 'Unknown error'
                    );
                }
            }
        });
    }

    // 处理 CodeMirror 操作
    private async handleOperation(message: CodeMirrorOperationMessage): Promise<any> {
        if (!this.editor) {
            throw new Error('CodeMirror editor not available');
        }

        const { operation, target, data, options } = message.payload;

        switch (operation) {
            case 'read':
                return this.handleReadOperation(target, data);

            case 'write':
                return this.handleWriteOperation(target, data, options);

            case 'listen':
                return this.handleListenOperation(data);

            case 'decorate':
                return this.handleDecorateOperation(data);

            case 'syntax':
                return this.handleSyntaxOperation(data);

            case 'history':
                return this.handleHistoryOperation(data);

            default:
                throw new Error(`Unsupported operation: ${operation}`);
        }
    }

    // 处理读取操作
    private handleReadOperation(target?: string, data?: any): any {
        if (!this.editor) throw new Error('Editor not available');

        switch (target) {
            case 'doc':
                if (data?.lineNumber !== undefined) {
                    // 按行读取
                    const line = this.editor.state.doc.line(data.lineNumber);
                    return { line };
                } else if (data?.range) {
                    // 按范围读取
                    const { from, to } = data.range;
                    const content = this.editor.state.doc.sliceDoc(from, to);
                    return { content };
                } else {
                    // 读取全文
                    const content = this.editor.state.doc.toString();
                    return { content };
                }

            case 'selection':
                return {
                    selection: {
                        main: this.editor.state.selection.main,
                        ranges: this.editor.state.selection.ranges
                    }
                };

            case 'state':
                return {
                    state: {
                        docLength: this.editor.state.doc.length,
                        lineCount: this.editor.state.doc.line(this.editor.state.doc.length)?.number || 0,
                        readOnly: this.editor.state.readOnly
                    }
                };

            case 'viewport':
                // 需要从编辑器获取视口信息
                return { viewport: { from: 0, to: this.editor.state.doc.length } };

            default:
                throw new Error(`Unsupported read target: ${target}`);
        }
    }

    // 处理写入操作
    private handleWriteOperation(target?: string, data?: any, options?: any): any {
        if (!this.editor) throw new Error('Editor not available');

        switch (target) {
            case 'doc':
                if (data?.changes) {
                    const spec: any = {
                        changes: data.changes,
                        userEvent: options?.userEvent,
                        scrollIntoView: options?.scrollIntoView
                    };
                    this.editor.dispatch(spec);
                    return { changes: data.changes };
                }
                break;

            case 'selection':
                if (data?.selection) {
                    const spec: any = {
                        selection: data.selection,
                        userEvent: options?.userEvent,
                        scrollIntoView: options?.scrollIntoView
                    };
                    this.editor.dispatch(spec);
                    return { success: true };
                }
                break;

            default:
                throw new Error(`Unsupported write target: ${target}`);
        }

        return { success: false };
    }

    // 处理监听操作
    private handleListenOperation(data?: any): any {
        const callbackId = data?.callback || `listener_${Date.now()}`;
        const listenType = data?.listenType || 'all';

        // 如果已经有监听器，先移除
        if (this.updateCallback) {
            // 这里需要根据实际的 CodeMirror API 来移除监听器
        }

        // 设置新的监听器
        this.updateCallback = (update: any) => {
            const shouldNotify =
                listenType === 'all' ||
                (listenType === 'doc' && update.docChanged) ||
                (listenType === 'selection' && update.selectionSet) ||
                (listenType === 'viewport' && update.viewportChanged);

            if (shouldNotify) {
                this.notifyUpdate(callbackId, listenType, update);
            }
        };

        this.listeners.set(callbackId, this.updateCallback);
        return { callbackId };
    }

    // 处理装饰操作
    private handleDecorateOperation(data?: any): any {
        // 这里需要根据实际的 CodeMirror 装饰 API 来实现
        // 由于装饰系统比较复杂，这里提供一个基础框架
        console.log('[CodeMirrorBridge] Decoration operation:', data);
        return { success: true };
    }

    // 处理语法操作
    private handleSyntaxOperation(data?: any): any {
        if (!this.editor?.syntaxTree) {
            throw new Error('Syntax tree not available');
        }

        const { syntaxQuery } = data;

        switch (syntaxQuery?.type) {
            case 'tree':
                return { syntax: { tree: this.editor.syntaxTree } };

            case 'node':
                // 根据位置获取语法节点
                if (syntaxQuery.position !== undefined) {
                    // 这里需要根据实际的语法树 API 来实现
                    return { syntax: { nodes: [] } };
                }
                break;

            default:
                throw new Error(`Unsupported syntax query type: ${syntaxQuery?.type}`);
        }

        return { syntax: null };
    }

    // 处理历史操作
    private handleHistoryOperation(data?: any): any {
        if (!this.editor?.history) {
            throw new Error('History not available');
        }

        const { historyAction } = data;

        switch (historyAction) {
            case 'undo':
                return { success: this.editor.history.undo() };

            case 'redo':
                return { success: this.editor.history.redo() };

            case 'clear':
                this.editor.history.clear();
                return { success: true };

            default:
                throw new Error(`Unsupported history action: ${historyAction}`);
        }
    }

    // 处理 AI 操作
    private async handleAIOperation(message: CodeMirrorAIMessage): Promise<any> {
        const { action, context, prompt, options } = message.payload;

        // 获取当前编辑器状态作为上下文
        const editorContext = {
            ...context,
            content: context.content || this.editor?.state.doc.toString() || '',
            selection: context.selection || this.editor?.state.selection.main,
            cursor: context.cursor || this.editor?.state.selection.main.head
        };

        // 这里可以集成实际的 AI 服务
        console.log('[CodeMirrorBridge] AI operation:', { action, context: editorContext, prompt, options });

        // 模拟 AI 响应
        const mockResult = {
            analysis: action === 'analyze' ? '代码分析结果...' : undefined,
            suggestions: action === 'suggest' ? [
                {
                    description: '建议优化代码结构',
                    changes: [{ from: 0, to: 10, insert: '优化后的代码' }],
                    confidence: 0.8
                }
            ] : undefined,
            explanation: action === 'explain' ? '代码解释...' : undefined,
            completion: action === 'complete' ? '代码补全内容' : undefined,
            refactored: action === 'refactor' ? '重构后的代码' : undefined
        };

        return mockResult;
    }

    // 设置更新监听器
    private setupUpdateListener(): void {
        if (!this.editor?.updateListener) return;

        const updateExtension = this.editor.updateListener.of((update: any) => {
            if (this.updateCallback) {
                this.updateCallback(update);
            }
        });

        // 将扩展添加到编辑器（这里需要根据实际的 CodeMirror API 调整）
        // this.editor.dispatch({ effects: StateEffect.appendConfig.of(updateExtension) });
    }

    // 通知更新
    private notifyUpdate(callbackId: string, updateType: string, update: any): void {
        const data = {
            docChanged: update.docChanged,
            selectionChanged: update.selectionSet,
            viewportChanged: update.viewportChanged,
            focused: update.focusChanged,

            // 具体的更改信息
            changes: update.changes ? this.extractChanges(update.changes) : undefined,
            selection: update.state?.selection ? {
                main: update.state.selection.main,
                ranges: update.state.selection.ranges
            } : undefined,
            viewport: update.viewportChanged ? { from: 0, to: update.state.doc.length } : undefined,
            state: {
                docLength: update.state.doc.length,
                lineCount: update.state.doc.line(update.state.doc.length)?.number || 0,
                readOnly: update.state.readOnly
            }
        };

        globalMessageManager.sendCodeMirrorUpdate(
            this.windowId,
            updateType as any,
            data,
            callbackId
        );
    }

    // 提取更改信息
    private extractChanges(changes: any): Array<{ from: number; to: number; inserted: string; deleted: string; }> {
        const result: Array<{ from: number; to: number; inserted: string; deleted: string; }> = [];

        // 这里需要根据实际的 CodeMirror changes API 来实现
        changes.iterChanges((fromA: number, toA: number, fromB: number, toB: number, inserted: any) => {
            result.push({
                from: fromA,
                to: toA,
                inserted: inserted.toString(),
                deleted: '' // 需要计算删除的内容
            });
        });

        return result;
    }

    // 清理资源
    public dispose(): void {
        this.listeners.clear();
        this.updateCallback = null;
        this.editor = null;
        this.isReady = false;
        console.log(`[CodeMirrorBridge] Bridge disposed for window ${this.windowId}`);
    }
}

// 全局桥接实例管理器
export class CodeMirrorBridgeManager {
    private static bridges: Map<string, CodeMirrorBridge> = new Map();

    // 获取或创建桥接实例
    public static getBridge(windowId: string): CodeMirrorBridge {
        if (!this.bridges.has(windowId)) {
            this.bridges.set(windowId, new CodeMirrorBridge(windowId));
        }
        return this.bridges.get(windowId)!;
    }

    // 移除桥接实例
    public static removeBridge(windowId: string): void {
        const bridge = this.bridges.get(windowId);
        if (bridge) {
            bridge.dispose();
            this.bridges.delete(windowId);
        }
    }

    // 获取所有活动的桥接实例
    public static getAllBridges(): Map<string, CodeMirrorBridge> {
        return new Map(this.bridges);
    }
}

// 供外部使用的便捷函数
export const getCodeMirrorBridge = (windowId: string) => CodeMirrorBridgeManager.getBridge(windowId);
export const removeCodeMirrorBridge = (windowId: string) => CodeMirrorBridgeManager.removeBridge(windowId); 