import { PaperStatus } from "@prisma/client";

// 为每个Paper状态定义徽章颜色和样式
export type StatusConfig = {
  variant: "default" | "secondary" | "destructive" | "outline";
  color?: string; // 自定义颜色（可选）
  label?: string; // 显示的标签（可选，如果未提供则使用状态本身）
};

// 将ENUM格式（如FIRST_CONTENT_CHECK）转换为更友好的格式（如First Content Check）
function formatEnumName(enumName: string): string {
  return enumName
    .split("_")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

// 所有Paper状态的颜色配置
export const paperStatusConfig: Record<PaperStatus, StatusConfig> = {
  // 提交阶段
  SUBMITTED: {
    variant: "default",
    color: "#3B82F6", // 蓝色
    label: "Submitted",
  },
  FIRST_CONTENT_CHECK: {
    variant: "secondary",
    color: "#6366F1", // 靛蓝色
    label: "First Content Check",
  },

  // 审稿阶段
  REVIEWER_NEEDED: {
    variant: "secondary",
    color: "#8B5CF6", // 紫色
    label: "Reviewer Needed",
  },
  REVIEWER_INVITED: {
    variant: "secondary",
    color: "#A855F7", // 中紫色
    label: "Reviewer Invited",
  },
  UNDER_REVIEW: {
    variant: "secondary",
    color: "#D946EF", // 洋红色
    label: "Under Review",
  },
  FIRST_REVIEW_DONE: {
    variant: "secondary",
    color: "#EC4899", // 粉色
    label: "First Review Done",
  },

  // 回复阶段
  WATING_AUTHOR_REPLY: {
    variant: "secondary",
    color: "#F43F5E", // 玫红色
    label: "Waiting Author Reply",
  },
  WATING_REVIEWER_REPLY: {
    variant: "secondary",
    color: "#EF4444", // 红色
    label: "Waiting Reviewer Reply",
  },
  SECOND_REVIEW_DONE: {
    variant: "secondary",
    color: "#F97316", // 橙色
    label: "Second Review Done",
  },
  WAITING_MATERIAL_UPDATE: {
    variant: "secondary",
    color: "#F59E0B", // 琥珀色
    label: "Waiting Material Update",
  },
  REVISION_SUBMITTED: {
    variant: "secondary",
    color: "#84CC16", // 酸橙色
    label: "Revision Submitted",
  },

  // 最终阶段
  ACCEPTED: {
    variant: "default",
    color: "#10B981", // 绿色
    label: "Accepted",
  },
  REJECTED: {
    variant: "destructive",
    color: "#991B1B", // 深红色
    label: "Rejected",
  },
  PUBLISHED: {
    variant: "default",
    color: "#059669", // 深绿色
    label: "Published",
  },
  ARCHIVED: {
    variant: "outline",
    color: "#6B7280", // 灰色
    label: "Archived",
  },
  IN_PRESS: {
    variant: "default",
    color: "#0EA5E9", // 天蓝色
    label: "In Press",
  },
  RETRACTED: {
    variant: "destructive",
    color: "#991B1B", // 深红色
    label: "Retracted",
  },
  TASK_WATING_APPROVAL: {
    variant: "secondary",
    color: "#FBBF24", // 黄色
    label: "Task Waiting Approval",
  },
  WAITING_PAYMENT: {
    variant: "secondary",
    color: "#9333EA", // 紫色
    label: "Waiting Payment",
  },
  PAYMENT_RECEIVED: {
    variant: "default",
    color: "#06B6D4", // 青色
    label: "Payment Received",
  },
};

// 获取特定状态的徽章配置
export function getStatusBadgeConfig(status: PaperStatus): StatusConfig {
  return paperStatusConfig[status] || { variant: "secondary" };
}

// 为徽章生成样式类
export function getStatusBadgeClass(status: PaperStatus): string {
  const config = getStatusBadgeConfig(status);

  if (config.color) {
    // 返回自定义样式
    return `bg-[${config.color}] text-white border-transparent`;
  }

  // 使用默认variant样式
  return "";
}
