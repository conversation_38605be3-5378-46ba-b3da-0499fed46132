/**
 * 用户同步工具 - 在Auth登录后自动同步到Prisma数据库
 * 创建/更新User记录和默认角色绑定
 */
import { Role, ScopeType } from "@prisma/client";
import { User } from "@supabase/supabase-js";

import { prisma } from "./prisma";
import { DEFAULT_AVATAR_COLOR } from "./userColorManager";

/**
 * 同步Supabase Auth用户到Prisma User表
 * 如果用户不存在则创建，并为新用户分配默认角色
 */
export async function syncUserToPrisma(authUser: User): Promise<boolean> {
  if (!authUser) return false;

  try {
    // 1. 查找用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { id: authUser.id },
    });

    // 2. 不存在则创建新用户记录
    if (!existingUser) {
      console.log(`创建新用户记录: ${authUser.id}, ${authUser.email}`);

      // 从user_metadata提取基本信息
      const userData = {
        id: authUser.id,
        email: authUser.email || "",
        name: authUser.user_metadata?.name || authUser.email?.split("@")[0] || "User",
        emailVerified: authUser.email_confirmed_at ? new Date(authUser.email_confirmed_at) : null,
        image: authUser.user_metadata?.avatar_url || null,
        avatarColor: DEFAULT_AVATAR_COLOR, // 为新用户分配黑色头像颜色
      };

      // 创建用户记录
      await prisma.user.create({ data: userData });

      console.log(`已为用户 ${authUser.id} 创建记录并分配头像颜色: ${userData.avatarColor}`);

      // 3. 为新用户创建默认角色绑定 (AUTHOR@GLOBAL)
      await prisma.roleBinding.create({
        data: {
          principalId: authUser.id,
          principalType: "USER",
          role: Role.AUTHOR,
          scopeType: ScopeType.GLOBAL,
        },
      });

      console.log(`已为用户 ${authUser.id} 创建默认角色: AUTHOR@GLOBAL`);
      return true;
    }

    // 4. 已存在则更新基本信息
    else {
      await prisma.user.update({
        where: { id: authUser.id },
        data: {
          name: authUser.user_metadata?.name || existingUser.name,
          image: authUser.user_metadata?.avatar_url || existingUser.image,
          emailVerified: authUser.email_confirmed_at
            ? new Date(authUser.email_confirmed_at)
            : existingUser.emailVerified,
        },
      });

      // 5. 检查是否已有角色绑定，没有则创建默认角色
      const hasRoles = await prisma.roleBinding.findFirst({
        where: { principalId: authUser.id },
      });

      if (!hasRoles) {
        await prisma.roleBinding.create({
          data: {
            principalId: authUser.id,
            principalType: "USER",
            role: Role.AUTHOR,
            scopeType: ScopeType.GLOBAL,
          },
        });
        console.log(`已为现有用户 ${authUser.id} 补充默认角色: AUTHOR@GLOBAL`);
      }

      return true;
    }
  } catch (error) {
    console.error("同步用户失败:", error);
    return false;
  }
}

/**
 * 从Prisma同步角色到Supabase Auth用户元数据
 */
export async function syncRolesToAuthUser(userId: string, supabaseAdmin: any): Promise<boolean> {
  try {
    // 1. 获取用户所有角色
    const roleBindings = await prisma.roleBinding.findMany({
      where: { principalId: userId, principalType: "USER" },
      select: { role: true, scopeType: true, scopeId: true },
    });

    if (roleBindings.length === 0) {
      console.warn(`用户 ${userId} 没有任何角色绑定`);
      return false;
    }

    // 转换格式，确保与middleware期望的结构匹配
    const formattedRoles = roleBindings.map(binding => ({
      role: binding.role,
      scope: binding.scopeType, // 添加scope字段（middleware使用）
      scopeType: binding.scopeType, // 保留原scopeType字段（向后兼容）
      scopeId: binding.scopeId,
    }));

    // 2. 获取用户的 avatarColor
    const userData = await prisma.user.findUnique({
      where: { id: userId },
      select: { avatarColor: true },
    });

    // 3. 更新到Supabase Auth用户元数据
    const { error, data } = await supabaseAdmin.auth.admin.getUserById(userId);
    if (error) {
      console.error(`获取用户(${userId})失败:`, error);
      return false;
    }

    const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(userId, {
      user_metadata: {
        ...data.user.user_metadata,
        roles: formattedRoles,
        avatarColor: userData?.avatarColor || data.user.user_metadata?.avatarColor,
      },
    });

    if (updateError) {
      console.error(`更新用户元数据失败:`, updateError);
      return false;
    }

    console.log(`已同步 ${formattedRoles.length} 个角色和头像颜色到用户元数据`);
    return true;
  } catch (error) {
    console.error("同步角色到Auth元数据失败:", error);
    return false;
  }
}

/**
 * 同步用户的 avatarColor 到 Supabase Auth 用户元数据
 */
export async function syncAvatarColorToAuth(userId: string, supabaseAdmin: any): Promise<boolean> {
  try {
    // 1. 获取用户的 avatarColor
    const userData = await prisma.user.findUnique({
      where: { id: userId },
      select: { avatarColor: true },
    });

    if (!userData) {
      console.warn(`用户 ${userId} 不存在`);
      return false;
    }

    // 2. 获取当前用户元数据
    const { error, data } = await supabaseAdmin.auth.admin.getUserById(userId);
    if (error) {
      console.error(`获取用户(${userId})失败:`, error);
      return false;
    }

    // 3. 更新 avatarColor 到用户元数据
    const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(userId, {
      user_metadata: {
        ...data.user.user_metadata,
        avatarColor: userData.avatarColor,
      },
    });

    if (updateError) {
      console.error(`更新用户头像颜色失败:`, updateError);
      return false;
    }

    console.log(`已同步用户 ${userId} 的头像颜色: ${userData.avatarColor}`);
    return true;
  } catch (error) {
    console.error("同步头像颜色到Auth元数据失败:", error);
    return false;
  }
}
