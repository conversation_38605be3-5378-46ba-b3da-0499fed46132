/**
 * 企业级权限检查系统
 * 统一的权限验证逻辑，支持审计日志和安全检查
 */

import { Role, ScopeType, PaperStatus } from "@prisma/client";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { auditLog } from "@/lib/security/audit";
import { getClientIP } from "@/lib/security/utils";

// 输入验证 Schema
const userIdSchema = z.string().min(1, "User ID cannot be empty");
const paperIdSchema = z.string().cuid("Invalid paper ID format");

// 权限检查结果类型
export interface PermissionResult {
  allowed: boolean;
  userRole: Role | null;
  scopeType: ScopeType | null;
  reason?: string;
  auditId?: string;
}

// 权限检查选项
export interface PermissionOptions {
  requireAudit?: boolean;
  ipAddress?: string;
  userAgent?: string;
  skipCache?: boolean;
}

/**
 * 检查用户对论文的访问权限
 */
export async function checkPaperAccess(
  userId: string,
  paperId: string,
  requiredRoles: Role[],
  options: PermissionOptions = {}
): Promise<PermissionResult> {
  try {
    // 输入验证
    const validUserId = userIdSchema.parse(userId);
    const validPaperId = paperIdSchema.parse(paperId);

    // 并行查询用户角色绑定和论文信息
    const [bindings, paper] = await Promise.all([
      prisma.roleBinding.findMany({
        where: { 
          principalId: validUserId, 
          principalType: "USER" 
        },
        select: { 
          role: true, 
          scopeType: true, 
          scopeId: true,
          condition: true 
        },
      }),
      prisma.paper.findUnique({
        where: { id: validPaperId },
        select: { 
          id: true, 
          authorId: true, 
          issueId: true, 
          status: true,
          needsAttention: true 
        },
      }),
    ]);

    if (!paper) {
      const result: PermissionResult = {
        allowed: false,
        userRole: null,
        scopeType: null,
        reason: "Paper not found"
      };
      
      if (options.requireAudit) {
        try {
          result.auditId = await auditLog.create({
            userId: validUserId,
            action: 'PAPER_ACCESS_DENIED',
            resourceType: 'PAPER',
            resourceId: validPaperId,
            reason: 'Paper not found',
            ipAddress: options.ipAddress,
            userAgent: options.userAgent,
          });
        } catch (auditError) {
          console.warn('Audit logging failed:', auditError);
          result.auditId = 'audit-failed';
        }
      }
      
      return result;
    }

    // 权限检查逻辑
    const permissionCheck = checkRolePermissions(bindings, paper, requiredRoles, validUserId);
    
    // 审计日志 (非阻塞)
    if (options.requireAudit) {
      try {
        permissionCheck.auditId = await auditLog.create({
          userId: validUserId,
          action: permissionCheck.allowed ? 'PAPER_ACCESS_GRANTED' : 'PAPER_ACCESS_DENIED',
          resourceType: 'PAPER',
          resourceId: validPaperId,
          reason: permissionCheck.reason,
          metadata: {
            userRole: permissionCheck.userRole,
            scopeType: permissionCheck.scopeType,
            paperStatus: paper.status,
          },
          ipAddress: options.ipAddress,
          userAgent: options.userAgent,
        });
      } catch (auditError) {
        console.warn('Audit logging failed, but continuing with permission check:', auditError);
        permissionCheck.auditId = 'audit-failed';
      }
    }

    return permissionCheck;

  } catch (error) {
    console.error('Permission check failed:', error);
    
    const result: PermissionResult = {
      allowed: false,
      userRole: null,
      scopeType: null,
      reason: error instanceof z.ZodError ? 'Invalid input format' : 'Permission check failed'
    };

    if (options.requireAudit) {
      try {
        result.auditId = await auditLog.create({
          userId,
          action: 'PERMISSION_CHECK_ERROR',
          resourceType: 'PAPER',
          resourceId: paperId,
          reason: result.reason,
          metadata: { error: error instanceof Error ? error.message : 'Unknown error' },
          ipAddress: options.ipAddress,
          userAgent: options.userAgent,
        });
      } catch (auditError) {
        console.warn('Audit logging failed:', auditError);
        result.auditId = 'audit-failed';
      }
    }

    return result;
  }
}

/**
 * 核心权限检查逻辑
 */
function checkRolePermissions(
  bindings: Array<{
    role: Role;
    scopeType: ScopeType;
    scopeId: string | null;
    condition?: any;
  }>,
  paper: {
    id: string;
    authorId: string;
    issueId: string | null;
    status: PaperStatus;
    needsAttention: boolean;
  },
  requiredRoles: Role[],
  userId: string
): PermissionResult {
  
  // 检查是否有任何允许的角色
  for (const binding of bindings) {
    if (!requiredRoles.includes(binding.role)) continue;

    // 全局管理员权限
    if ((binding.role === Role.ADMIN || binding.role === Role.CHIEF_EDITOR) && 
        binding.scopeType === ScopeType.GLOBAL) {
      return {
        allowed: true,
        userRole: binding.role,
        scopeType: binding.scopeType,
        reason: 'Global administrator access'
      };
    }

    // 期刊编辑权限
    if (binding.role === Role.ISSUE_EDITOR && 
        binding.scopeType === ScopeType.ISSUE && 
        binding.scopeId === paper.issueId) {
      return {
        allowed: true,
        userRole: binding.role,
        scopeType: binding.scopeType,
        reason: 'Issue editor access'
      };
    }

    // 论文级权限（编辑/审稿人）
    if ((binding.role === Role.PAPER_EDITOR || binding.role === Role.REVIEWER) &&
        binding.scopeType === ScopeType.PAPER &&
        binding.scopeId === paper.id) {
      return {
        allowed: true,
        userRole: binding.role,
        scopeType: binding.scopeType,
        reason: `${binding.role.toLowerCase()} access`
      };
    }

    // 作者权限 - 需要同时满足角色绑定和作者身份
    if (binding.role === Role.AUTHOR && 
        binding.scopeType === ScopeType.GLOBAL && 
        paper.authorId === userId) {
      return {
        allowed: true,
        userRole: binding.role,
        scopeType: binding.scopeType,
        reason: 'Author access'
      };
    }
  }

  return {
    allowed: false,
    userRole: null,
    scopeType: null,
    reason: 'No matching role permissions found'
  };
}

/**
 * 检查用户是否可以发表评论
 */
export async function checkCommentPermission(
  userId: string,
  paperId: string,
  options: PermissionOptions = {}
): Promise<PermissionResult & { canReply: boolean; roleLabel: string }> {
  const basePermission = await checkPaperAccess(
    userId,
    paperId,
    [Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR, Role.PAPER_EDITOR, Role.REVIEWER, Role.AUTHOR],
    options
  );

  if (!basePermission.allowed) {
    return {
      ...basePermission,
      canReply: false,
      roleLabel: 'Unauthorized'
    };
  }

  // 并行获取论文信息和审稿人分配信息
  const [paper, reviewAssignment] = await Promise.all([
    prisma.paper.findUnique({
      where: { id: paperId },
      select: {
        authorCanReply: true,
        reviewersCanReply: true,
        authorId: true
      },
    }),
    // 如果是审稿人，获取其分配信息以确定编号
    basePermission.userRole === Role.REVIEWER
      ? prisma.reviewAssignment.findFirst({
          where: {
            paperId: paperId,
            reviewerId: userId
          },
          select: { alias: true }
        })
      : Promise.resolve(null)
  ]);

  if (!paper) {
    return {
      ...basePermission,
      canReply: false,
      roleLabel: 'Paper not found'
    };
  }

  let canReply = false;
  let roleLabel = 'User';

  switch (basePermission.userRole) {
    case Role.ADMIN:
    case Role.CHIEF_EDITOR:
    case Role.ISSUE_EDITOR:
    case Role.PAPER_EDITOR:
      canReply = true;
      roleLabel = 'Editor';
      break;

    case Role.REVIEWER:
      canReply = paper.reviewersCanReply;
      // 🔧 修复：使用审稿人分配的alias作为角色标签
      if (reviewAssignment?.alias) {
        const alias = reviewAssignment.alias.trim();

        // 检查alias是否已经包含"Reviewer"前缀，避免重复
        if (alias.toLowerCase().startsWith('reviewer')) {
          // 如果已经包含"Reviewer"，直接使用
          roleLabel = alias;
        } else {
          // 如果只是数字，添加"Reviewer"前缀
          roleLabel = `Reviewer ${alias}`;
        }
      } else {
        // 回退到默认格式
        roleLabel = 'Reviewer';
      }
      break;

    case Role.AUTHOR:
      canReply = paper.authorCanReply && paper.authorId === userId;
      roleLabel = 'Author';
      break;

    default:
      canReply = false;
      roleLabel = 'Unauthorized';
  }

  return {
    ...basePermission,
    canReply,
    roleLabel
  };
}

/**
 * 获取用户在特定论文上的最高权限角色
 */
export async function getUserHighestRole(
  userId: string,
  paperId: string
): Promise<Role | null> {
  const permission = await checkPaperAccess(
    userId,
    paperId,
    [Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR, Role.PAPER_EDITOR, Role.REVIEWER, Role.AUTHOR]
  );

  return permission.userRole;
}
