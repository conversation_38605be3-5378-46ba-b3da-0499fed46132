/**
 * 统一的登出管理器
 * 提供鲁棒的登出逻辑，确保状态同步和UI更新
 */

import { SupabaseClient } from '@supabase/supabase-js';

export interface LogoutOptions {
  /** 是否强制登出（跳过网络请求） */
  force?: boolean;
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 是否清除本地存储 */
  clearStorage?: boolean;
}

export interface LogoutResult {
  success: boolean;
  error?: string;
  forced?: boolean;
}

export class LogoutManager {
  private static instance: LogoutManager | null = null;
  private isLoggingOut = false;

  private constructor() {}

  static getInstance(): LogoutManager {
    if (!LogoutManager.instance) {
      LogoutManager.instance = new LogoutManager();
    }
    return LogoutManager.instance;
  }

  /**
   * 执行登出操作
   */
  async logout(
    supabase: SupabaseClient,
    options: LogoutOptions = {}
  ): Promise<LogoutResult> {
    // 防止重复登出
    if (this.isLoggingOut) {
      return { success: false, error: 'Logout already in progress' };
    }

    this.isLoggingOut = true;

    const {
      force = false,
      timeout = 3000,
      clearStorage = true
    } = options;

    try {
      if (force) {
        return await this.forceLogout(supabase, clearStorage);
      }

      return await this.normalLogout(supabase, timeout, clearStorage);
    } finally {
      this.isLoggingOut = false;
    }
  }

  /**
   * 正常登出流程
   */
  private async normalLogout(
    supabase: SupabaseClient,
    timeout: number,
    clearStorage: boolean
  ): Promise<LogoutResult> {
    // 设置超时保护
    const timeoutPromise = new Promise<LogoutResult>((resolve) => {
      setTimeout(() => {
        resolve(this.forceLogout(supabase, clearStorage));
      }, timeout);
    });

    const logoutPromise = this.performLogout(supabase, clearStorage);

    try {
      const result = await Promise.race([logoutPromise, timeoutPromise]);
      return result;
    } catch (error) {
      // 如果正常登出失败，执行强制登出
      return await this.forceLogout(supabase, clearStorage);
    }
  }

  /**
   * 执行实际的登出操作
   */
  private async performLogout(
    supabase: SupabaseClient,
    clearStorage: boolean
  ): Promise<LogoutResult> {
    // 并行执行登出操作
    const logoutPromises = [
      // 服务器端登出
      fetch("/api/auth/logout", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      }).catch(() => {}),
      
      // Supabase 客户端登出
      supabase.auth.signOut().catch(() => {})
    ];

    // 等待所有登出操作完成
    await Promise.allSettled(logoutPromises);

    // 清除本地存储
    if (clearStorage) {
      this.clearLocalStorage();
    }

    // 触发状态更新
    this.triggerAuthRefresh();

    return { success: true };
  }

  /**
   * 强制登出
   */
  private async forceLogout(
    supabase: SupabaseClient,
    clearStorage: boolean
  ): Promise<LogoutResult> {
    // 清除本地存储
    if (clearStorage) {
      this.clearLocalStorage();
    }

    // 尝试Supabase登出（不等待结果）
    supabase.auth.signOut().catch(() => {});

    // 触发状态更新
    this.triggerAuthRefresh();

    return { success: true, forced: true };
  }

  /**
   * 清除本地存储
   */
  private clearLocalStorage(): void {
    try {
      localStorage.clear();
      sessionStorage.clear();
    } catch (e) {
      // 静默处理错误
    }
  }

  /**
   * 触发认证状态刷新
   */
  private triggerAuthRefresh(): void {
    const triggerRefresh = () => {
      // 触发force-auth-refresh事件
      const forceRefreshEvent = new CustomEvent('force-auth-refresh');
      window.dispatchEvent(forceRefreshEvent);

      // 触发auth-state-changed事件
      const stateChangeEvent = new CustomEvent('auth-state-changed', {
        detail: { event: 'SIGNED_OUT', user: null, session: null }
      });
      window.dispatchEvent(stateChangeEvent);
    };

    // 立即触发
    triggerRefresh();
    
    // 延迟触发，确保状态更新
    setTimeout(triggerRefresh, 100);
    setTimeout(triggerRefresh, 300);
    setTimeout(triggerRefresh, 500);
  }

  /**
   * 检查是否正在登出
   */
  isLoggingOutNow(): boolean {
    return this.isLoggingOut;
  }
}

// 导出单例实例
export const logoutManager = LogoutManager.getInstance();
