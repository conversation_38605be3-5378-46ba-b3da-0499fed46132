/**
 * LogoutManager 测试文件
 * 验证logout功能的鲁棒性
 */

import { LogoutManager } from '../logout-manager';

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    signOut: jest.fn()
  }
};

// Mock fetch
global.fetch = jest.fn();

// Mock window events
const mockEventListeners: { [key: string]: Function[] } = {};
Object.defineProperty(window, 'addEventListener', {
  value: jest.fn((event: string, callback: Function) => {
    if (!mockEventListeners[event]) {
      mockEventListeners[event] = [];
    }
    mockEventListeners[event].push(callback);
  })
});

Object.defineProperty(window, 'dispatchEvent', {
  value: jest.fn((event: CustomEvent) => {
    const listeners = mockEventListeners[event.type] || [];
    listeners.forEach(listener => listener(event));
  })
});

// Mock localStorage and sessionStorage
const mockStorage = {
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', { value: mockStorage });
Object.defineProperty(window, 'sessionStorage', { value: mockStorage });

describe('LogoutManager', () => {
  let logoutManager: LogoutManager;

  beforeEach(() => {
    logoutManager = LogoutManager.getInstance();
    jest.clearAllMocks();
    mockStorage.clear.mockClear();
  });

  describe('正常登出流程', () => {
    it('应该成功执行正常登出', async () => {
      // Mock successful responses
      (global.fetch as jest.Mock).mockResolvedValue({ ok: true });
      mockSupabaseClient.auth.signOut.mockResolvedValue({ error: null });

      const result = await logoutManager.logout(mockSupabaseClient as any);

      expect(result.success).toBe(true);
      expect(result.forced).toBeUndefined();
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/logout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      expect(mockSupabaseClient.auth.signOut).toHaveBeenCalled();
      expect(mockStorage.clear).toHaveBeenCalledTimes(2); // localStorage and sessionStorage
    });

    it('应该在网络错误时仍然成功', async () => {
      // Mock network failures
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));
      mockSupabaseClient.auth.signOut.mockRejectedValue(new Error('Auth error'));

      const result = await logoutManager.logout(mockSupabaseClient as any);

      expect(result.success).toBe(true);
      expect(mockStorage.clear).toHaveBeenCalledTimes(2);
    });
  });

  describe('强制登出流程', () => {
    it('应该成功执行强制登出', async () => {
      const result = await logoutManager.logout(mockSupabaseClient as any, { force: true });

      expect(result.success).toBe(true);
      expect(result.forced).toBe(true);
      expect(mockStorage.clear).toHaveBeenCalledTimes(2);
    });

    it('强制登出不应该等待网络请求', async () => {
      const slowFetch = jest.fn(() => new Promise(resolve => setTimeout(resolve, 5000)));
      (global.fetch as jest.Mock).mockImplementation(slowFetch);

      const startTime = Date.now();
      const result = await logoutManager.logout(mockSupabaseClient as any, { force: true });
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(1000); // 应该很快完成
    });
  });

  describe('超时处理', () => {
    it('应该在超时后执行强制登出', async () => {
      // Mock slow responses
      const slowPromise = new Promise(resolve => setTimeout(resolve, 5000));
      (global.fetch as jest.Mock).mockReturnValue(slowPromise);
      mockSupabaseClient.auth.signOut.mockReturnValue(slowPromise);

      const result = await logoutManager.logout(mockSupabaseClient as any, { timeout: 1000 });

      expect(result.success).toBe(true);
      expect(result.forced).toBe(true);
    });
  });

  describe('并发控制', () => {
    it('应该防止重复登出', async () => {
      const promise1 = logoutManager.logout(mockSupabaseClient as any);
      const promise2 = logoutManager.logout(mockSupabaseClient as any);

      const [result1, result2] = await Promise.all([promise1, promise2]);

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(false);
      expect(result2.error).toBe('Logout already in progress');
    });
  });

  describe('事件触发', () => {
    it('应该触发认证刷新事件', async () => {
      const forceRefreshSpy = jest.fn();
      const authStateChangeSpy = jest.fn();

      window.addEventListener('force-auth-refresh', forceRefreshSpy);
      window.addEventListener('auth-state-changed', authStateChangeSpy);

      await logoutManager.logout(mockSupabaseClient as any);

      // 应该触发多次事件以确保可靠性
      expect(forceRefreshSpy).toHaveBeenCalled();
      expect(authStateChangeSpy).toHaveBeenCalled();
    });
  });

  describe('存储清除', () => {
    it('应该清除本地存储', async () => {
      await logoutManager.logout(mockSupabaseClient as any);

      expect(mockStorage.clear).toHaveBeenCalledTimes(2);
    });

    it('应该在clearStorage=false时跳过存储清除', async () => {
      await logoutManager.logout(mockSupabaseClient as any, { clearStorage: false });

      expect(mockStorage.clear).not.toHaveBeenCalled();
    });

    it('应该处理存储清除错误', async () => {
      mockStorage.clear.mockImplementation(() => {
        throw new Error('Storage error');
      });

      const result = await logoutManager.logout(mockSupabaseClient as any);

      expect(result.success).toBe(true); // 应该仍然成功
    });
  });

  describe('单例模式', () => {
    it('应该返回相同的实例', () => {
      const instance1 = LogoutManager.getInstance();
      const instance2 = LogoutManager.getInstance();

      expect(instance1).toBe(instance2);
    });
  });
});
