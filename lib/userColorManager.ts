/**
 * 统一的用户颜色管理器
 * 提供高性能的用户颜色获取，支持缓存和批量获取
 *
 * 特性：
 * - 内存缓存避免重复数据库查询
 * - 批量请求优化网络性能
 * - 防抖机制减少API调用
 * - 智能回退策略确保可用性
 */

// 用户颜色缓存
const userColorCache = new Map<string, string>();

// 标记哪些用户已经从数据库获取过颜色（避免重复查询）
const userColorFetched = new Set<string>();

// 批量请求的防抖处理
let batchRequestTimer: NodeJS.Timeout | null = null;
let pendingUserIds = new Set<string>();
let pendingResolvers = new Map<string, Array<(color: string) => void>>();

// 配置常量
const BATCH_DEBOUNCE_MS = 100; // 批量请求防抖时间
const MAX_BATCH_SIZE = 100; // 最大批量请求大小

/**
 * 默认头像颜色（黑色）
 */
export const DEFAULT_AVATAR_COLOR = '#000000' as const;

/**
 * 从缓存获取用户颜色
 * @param userId 用户ID
 * @returns 缓存的颜色值，如果不存在则返回null
 */
export function getCachedUserColor(userId: string): string | null {
    if (!userId) return null;
    return userColorCache.get(userId) || null;
}

/**
 * 设置用户颜色到缓存
 * @param userId 用户ID
 * @param color 颜色值（Hex格式）
 */
export function setCachedUserColor(userId: string, color: string): void {
    if (!userId || !color) return;
    userColorCache.set(userId, color);
    userColorFetched.add(userId);
}

/**
 * 批量设置用户颜色到缓存
 * @param userColors 用户ID到颜色的映射
 */
export function setBatchCachedUserColors(userColors: Record<string, string>): void {
    Object.entries(userColors).forEach(([userId, color]) => {
        if (userId && color) {
            userColorCache.set(userId, color);
            userColorFetched.add(userId);
        }
    });
}

/**
 * 获取用户颜色（同步版本，优先从缓存获取）
 * 如果缓存中没有，返回默认黑色
 *
 * @param userId 用户ID
 * @returns 用户颜色（Hex格式）
 */
export function getUserColorSync(userId: string): string {
    if (!userId) return DEFAULT_AVATAR_COLOR;

    // 优先从缓存获取
    const cachedColor = userColorCache.get(userId);
    if (cachedColor) {
        return cachedColor;
    }

    // 使用默认颜色作为回退，并缓存以避免重复计算
    userColorCache.set(userId, DEFAULT_AVATAR_COLOR);
    return DEFAULT_AVATAR_COLOR;
}

/**
 * 获取用户颜色（异步版本，会尝试从数据库获取最新颜色）
 *
 * @param userId 用户ID
 * @returns Promise<用户颜色（Hex格式）>
 */
export async function getUserColorAsync(userId: string): Promise<string> {
    if (!userId) return DEFAULT_AVATAR_COLOR;

    // 如果已经从数据库获取过，直接返回缓存
    if (userColorFetched.has(userId)) {
        const cachedColor = userColorCache.get(userId);
        if (cachedColor) {
            return cachedColor;
        }
    }

    // 添加到批量请求队列
    return new Promise((resolve, reject) => {
        try {
            // 如果已经有相同用户ID的请求在等待，添加到解析器列表
            if (!pendingResolvers.has(userId)) {
                pendingResolvers.set(userId, []);
            }
            pendingResolvers.get(userId)!.push(resolve);

            // 添加到待处理用户ID集合
            pendingUserIds.add(userId);

            // 设置防抖定时器
            if (batchRequestTimer) {
                clearTimeout(batchRequestTimer);
            }

            batchRequestTimer = setTimeout(processBatchRequest, BATCH_DEBOUNCE_MS);
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * 处理批量请求
 * 使用防抖机制批量处理用户颜色请求，提高性能
 */
async function processBatchRequest(): Promise<void> {
    if (pendingUserIds.size === 0) return;

    // 限制批量大小以避免过大的请求
    const userIds = Array.from(pendingUserIds).slice(0, MAX_BATCH_SIZE);
    const resolvers = new Map(pendingResolvers);

    // 清空待处理队列
    pendingUserIds.clear();
    pendingResolvers.clear();
    batchRequestTimer = null;

    try {
        // 批量获取用户颜色
        const userColors = await fetchBatchUserColors(userIds);

        // 更新缓存并解析 Promise
        userIds.forEach(userId => {
            const color = userColors[userId] || DEFAULT_AVATAR_COLOR;
            userColorCache.set(userId, color);
            userColorFetched.add(userId);

            // 解析所有等待的 Promise
            const userResolvers = resolvers.get(userId) || [];
            userResolvers.forEach(resolve => resolve(color));
        });
    } catch (error) {
        // 静默处理错误，使用默认颜色确保系统可用性
        if (process.env.NODE_ENV === 'development') {
            console.error('批量获取用户颜色失败:', error);
        }

        // 发生错误时，使用默认颜色解析 Promise
        userIds.forEach(userId => {
            const fallbackColor = DEFAULT_AVATAR_COLOR;
            userColorCache.set(userId, fallbackColor);
            userColorFetched.add(userId);

            const userResolvers = resolvers.get(userId) || [];
            userResolvers.forEach(resolve => resolve(fallbackColor));
        });
    }
}

/**
 * 批量从数据库获取用户颜色
 * @param userIds 用户ID数组
 * @returns 用户ID到颜色的映射
 */
async function fetchBatchUserColors(userIds: string[]): Promise<Record<string, string>> {
    if (!userIds.length) return {};

    try {
        const response = await fetch('/api/users/colors', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ userIds }),
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data.userColors || {};
    } catch (error) {
        // 在开发环境下记录错误，生产环境静默处理
        if (process.env.NODE_ENV === 'development') {
            console.error('获取用户颜色API失败:', error);
        }
        return {};
    }
}

/**
 * 预加载用户颜色（用于已知用户列表的场景）
 * 适用于评论列表等需要批量显示用户头像的场景
 *
 * @param userIds 需要预加载的用户ID数组
 */
export async function preloadUserColors(userIds: string[]): Promise<void> {
    if (!userIds.length) return;

    // 过滤出未从数据库获取过的用户ID
    const uncachedUserIds = userIds.filter(id => id && !userColorFetched.has(id));

    if (uncachedUserIds.length === 0) return;

    try {
        const userColors = await fetchBatchUserColors(uncachedUserIds);
        setBatchCachedUserColors(userColors);
    } catch (error) {
        // 静默处理错误，不影响主要功能
        if (process.env.NODE_ENV === 'development') {
            console.error('预加载用户颜色失败:', error);
        }
    }
}

/**
 * 清空颜色缓存
 * 主要用于测试或需要强制刷新缓存的场景
 */
export function clearUserColorCache(): void {
    userColorCache.clear();
    userColorFetched.clear();
}

/**
 * 获取缓存统计信息
 * 用于调试和监控缓存性能
 */
export function getCacheStats(): {
    size: number;
    fetchedCount: number;
    keys: string[]
} {
    return {
        size: userColorCache.size,
        fetchedCount: userColorFetched.size,
        keys: Array.from(userColorCache.keys()),
    };
}
