/**
 * 🎛️ Terminal菜单配置系统 - 简化版本
 * 提供类型安全的菜单配置接口和工具函数
 */

import React from 'react';

export interface TerminalMenuItemStyle {
    /** 字体颜色 */
    color?: string;
    /** 字体粗细 */
    fontWeight?: 'normal' | 'bold' | 'lighter' | 'bolder' | number;
    /** 字体斜度 */
    fontStyle?: 'normal' | 'italic' | 'oblique';
    /** 悬停时的字体颜色 */
    hoverColor?: string;
}

export interface TerminalMenuItem {
    /** 唯一标识符 */
    id: string;
    /** 显示标签 */
    label: string;
    /** 要执行的命令 */
    command: string;
    /** 所属类别ID */
    category?: string;
    /** 自定义样式 */
    style?: TerminalMenuItemStyle;
}

export interface TerminalMenuCategory {
    /** 唯一标识符 */
    id: string;
    /** 显示标签 */
    label: string;
}

export interface TerminalMenuConfig {
    /** 菜单类别列表 */
    categories: TerminalMenuCategory[];
    /** 菜单项列表 */
    items: TerminalMenuItem[];
}

// 简化的菜单配置
export const defaultTerminalMenuConfig: TerminalMenuConfig = {
    categories: [
        {
            id: 'window',
            label: '窗口管理'
        },
        {
            id: 'system',
            label: '系统命令'
        },
        {
            id: 'file',
            label: '文件操作'
        },
        {
            id: 'editor',
            label: '编辑器'
        },
        {
            id: 'quick',
            label: '快捷操作'
        }
    ],
    items: [
        // 窗口管理
        {
            id: 'about',
            label: 'ABOUT',
            command: 'create_window type=path target=/about width=0.8 height=0.8 x=0.1 y=0.1 title=ABOUT',
            category: 'window',
            style: {
                color: '#000000',
                hoverColor: '#103DFF',
                fontWeight: 'normal',
                fontStyle: 'normal',
            }
        },
        {
            id: 'preprint',
            label: 'PREPRINT',
            command: 'create_window type=path target=/papers?paperType=PREPRINT width=0.8 height=0.8 x=0.1 y=0.1 title=PAPERS',
            category: 'window',
            style: {
                color: '#be5151',
                hoverColor: '#103DFF',
                fontWeight: 'normal',
                fontStyle: 'normal',
            }
        },
        {
            id: 'gallery_short',
            label: 'GALLERY PAPER',
            command: 'create_window type=path target=/papers?paperType=GALLERY width=0.8 height=0.8 x=0.1 y=0.1 title=PAPERS',
            category: 'window',
            style: {
                color: '#059669',
                fontWeight: 'normal',
                fontStyle: 'normal',
                hoverColor: '#103DFF'
            }
        },
        {
            id: 'full_paper',
            label: 'FULL PAPER',
            command: 'create_window type=path target=/papers?paperType=FULL width=0.8 height=0.8 x=0.1 y=0.1 title=PAPERS',
            category: 'window',
            style: {
                color: '#7c3aed',
                fontWeight: 'normal',
                fontStyle: 'normal',
                hoverColor: '#103DFF'
            }
        },
        {
            id: 'news',
            label: 'NEWS',
            command: 'create_window type=path target=/news width=0.8 height=0.8 x=0.1 y=0.1 title=NEWS',
            category: 'window',
            style: {
                color: '#000000',
                hoverColor: '#103DFF',
                fontWeight: 'normal',
                fontStyle: 'normal',
            }
        },
        {
            id: 'docs',
            label: 'DOCS',
            command: 'create_window type=path target=/docs width=0.8 height=0.8 x=0.1 y=0.1 title=DOCS',
            category: 'window',
            style: {
                color: '#000000',
                hoverColor: '#103DFF',
                fontWeight: 'normal',
                fontStyle: 'normal',
            }
        },
        {
            id: 'submit',
            label: 'SUBMIT',
            command: 'create_window type=path target=/submit width=0.8 height=0.8 x=0.1 y=0.1 title=SUBMIT',
            category: 'window',
            style: {
                color: '#000000',
                hoverColor: '#103DFF',
                fontWeight: 'normal',
                fontStyle: 'normal',
            }
        },

    ]
};

// 菜单项按类别分组的辅助函数
export function getMenuItemsByCategory(config: TerminalMenuConfig): Record<string, TerminalMenuItem[]> {
    const grouped: Record<string, TerminalMenuItem[]> = {};

    config.categories.forEach(category => {
        grouped[category.id] = config.items.filter(item => item.category === category.id);
    });

    return grouped;
}

// 根据ID查找菜单项
export function findMenuItem(config: TerminalMenuConfig, id: string): TerminalMenuItem | undefined {
    return config.items.find(item => item.id === id);
}

// 根据类别查找菜单项
export function getMenuItemsInCategory(config: TerminalMenuConfig, categoryId: string): TerminalMenuItem[] {
    return config.items.filter(item => item.category === categoryId);
}

