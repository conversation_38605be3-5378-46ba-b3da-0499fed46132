/**
 * 行号功能相关的常量和配置
 */

export const LINE_NUMBER_CONFIG = {
  /** 估算的行高（像素） */
  ESTIMATED_LINE_HEIGHT: 20,
  
  /** 行号列宽度 */
  COLUMN_WIDTH: '8mm',
  
  /** 行号列距离页面边缘的距离 */
  COLUMN_OFFSET: '2mm',
  
  /** 行号字体大小 */
  FONT_SIZE: '7pt',
  
  /** 行号透明度 */
  OPACITY: 1.0,
} as const;

/**
 * 检查状态是否应该显示行号
 * @param status 文档状态
 * @returns 是否显示行号
 */
export function shouldShowLineNumbers(status?: string): boolean {
  return status?.toString().toLowerCase().trim() !== 'published';
}

/**
 * 计算每页行号数量
 * @param contentHeight 页面内容高度（像素）
 * @returns 每页行号数量
 */
export function calculateLinesPerPage(contentHeight: number): number {
  return Math.floor(contentHeight / LINE_NUMBER_CONFIG.ESTIMATED_LINE_HEIGHT);
}

/**
 * 计算页面起始行号
 * @param pageIndex 页面索引（从0开始）
 * @param linesPerPage 每页行数
 * @returns 起始行号
 */
export function calculateStartLineNumber(pageIndex: number, linesPerPage: number): number {
  return pageIndex * linesPerPage + 1;
}
