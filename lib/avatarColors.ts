// 32种精心挑选的头像颜色
export const AVATAR_COLORS = [
    "#e11d48", // 红色
    "#dc2626", // 深红
    "#ea580c", // 橙红
    "#d97706", // 橙色
    "#ca8a04", // 金黄
    "#65a30d", // 草绿
    "#16a34a", // 绿色
    "#059669", // 翠绿
    "#0d9488", // 青绿
    "#0891b2", // 青色
    "#0284c7", // 天蓝
    "#2563eb", // 蓝色
    "#4f46e5", // 靛蓝
    "#7c3aed", // 紫色
    "#9333ea", // 深紫
    "#c026d3", // 品红
    "#db2777", // 玫红
    "#be123c", // 深玫红
    "#b91c1c", // 暗红
    "#c2410c", // 棕橙
    "#a16207", // 棕黄
    "#4d7c0f", // 深绿
    "#15803d", // 森林绿
    "#047857", // 深翠绿
    "#0f766e", // 深青绿
    "#0e7490", // 深青
    "#0369a1", // 深蓝
    "#1d4ed8", // 皇家蓝
    "#000000", // 黑色（默认）
    "#7c2d12", // 棕色
    "#57534e", // 灰棕
    "#374151", // 深灰
] as const;

/**
 * 随机选择一个头像颜色
 */
export function getRandomAvatarColor(): string {
    const randomIndex = Math.floor(Math.random() * AVATAR_COLORS.length);
    return AVATAR_COLORS[randomIndex];
}

/**
 * 根据用户ID生成一致的头像颜色（确保同一用户总是得到相同颜色）
 */
export function getConsistentAvatarColor(userId: string): string {
    // 使用用户ID生成一致的索引
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
        const char = userId.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }

    const index = Math.abs(hash) % AVATAR_COLORS.length;
    return AVATAR_COLORS[index];
}

/**
 * 验证颜色代码是否有效
 */
export function isValidHexColor(color: string): boolean {
    return /^#[0-9A-F]{6}$/i.test(color);
}

/**
 * 获取颜色的亮度值（用于判断是否需要白色文字）
 */
export function getColorLuminance(hexColor: string): number {
    const hex = hexColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // 计算相对亮度
    return (0.299 * r + 0.587 * g + 0.114 * b) / 255;
}

/**
 * 根据背景色确定文字颜色（黑色或白色）
 */
export function getContrastTextColor(backgroundColor: string): string {
    const luminance = getColorLuminance(backgroundColor);
    return luminance > 0.5 ? '#000000' : '#ffffff';
} 