import { ITheme } from '@xterm/xterm';

/**
 * 终端主题接口扩展
 */
export interface TerminalTheme extends ITheme {
    name: string;
    description: string;
}

/**
 * 默认主题（当前使用的）
 */
export const DefaultTheme: TerminalTheme = {
    name: 'default',
    description: '默认主题',
    background: '#ffffff',
    foreground: '#0021FF',
    cursor: '#00ff00',
    selectionBackground: '#264f78',
    black: '#000000',
    red: '#cd3131',
    green: '#0dbc79',
    yellow: '#e5e510',
    blue: '#2472c8',
    magenta: '#bc3fbc',
    cyan: '#11a8cd',
    white: '#e5e5e5',
    brightBlack: '#666666',
    brightRed: '#f14c4c',
    brightGreen: '#23d18b',
    brightYellow: '#f5f543',
    brightBlue: '#3b8eea',
    brightMagenta: '#d670d6',
    brightCyan: '#29b8db',
    brightWhite: '#e5e5e5'
};

/**
 * AI模式主题（橘黄色调）
 */
export const AITheme: TerminalTheme = {
    name: 'ai',
    description: 'AI模式主题',
    background: '#ffffff',
    foreground: '#0021FF',
    cursor: '#ff6b35',
    selectionBackground: '#4a3728',
    black: '#000000',
    red: '#ee380c',
    green: '#32cd32',
    yellow: '#ffd700',
    blue: '#1e90ff',
    magenta: '#ff69b4',
    cyan: '#00ced1',
    white: '#f5deb3',
    brightBlack: '#696969',
    brightRed: '#ff7f50',
    brightGreen: '#90ee90',
    brightYellow: '#ffff00',
    brightBlue: '#87ceeb',
    brightMagenta: '#dda0dd',
    brightCyan: '#afeeee',
    brightWhite: '#fffaf0'
};

/**
 * 深色主题
 */
export const DarkTheme: TerminalTheme = {
    name: 'dark',
    description: '深色主题',
    background: '#1e1e1e',
    foreground: '#d4d4d4',
    cursor: '#00ff00',
    selectionBackground: '#264f78',
    black: '#000000',
    red: '#f44747',
    green: '#4ec9b0',
    yellow: '#ffcc02',
    blue: '#569cd6',
    magenta: '#c586c0',
    cyan: '#4fc1ff',
    white: '#cccccc',
    brightBlack: '#666666',
    brightRed: '#f44747',
    brightGreen: '#4ec9b0',
    brightYellow: '#ffcc02',
    brightBlue: '#569cd6',
    brightMagenta: '#c586c0',
    brightCyan: '#4fc1ff',
    brightWhite: '#ffffff'
};

/**
 * 矩阵主题（绿色黑客风格）
 */
export const MatrixTheme: TerminalTheme = {
    name: 'matrix',
    description: '矩阵主题',
    background: '#000000',
    foreground: '#00ff00',
    cursor: '#00ff00',
    selectionBackground: '#003300',
    black: '#000000',
    red: '#800000',
    green: '#00ff00',
    yellow: '#808000',
    blue: '#000080',
    magenta: '#800080',
    cyan: '#008080',
    white: '#c0c0c0',
    brightBlack: '#808080',
    brightRed: '#ff0000',
    brightGreen: '#00ff00',
    brightYellow: '#ffff00',
    brightBlue: '#0000ff',
    brightMagenta: '#ff00ff',
    brightCyan: '#00ffff',
    brightWhite: '#ffffff'
};

/**
 * 温暖主题（暖色调）
 */
export const WarmTheme: TerminalTheme = {
    name: 'warm',
    description: '温暖主题',
    background: '#2d1b1b',
    foreground: '#f4e4c1',
    cursor: '#ff9500',
    selectionBackground: '#5d3a3a',
    black: '#2d1b1b',
    red: '#e06c75',
    green: '#98c379',
    yellow: '#e5c07b',
    blue: '#61afef',
    magenta: '#c678dd',
    cyan: '#56b6c2',
    white: '#f4e4c1',
    brightBlack: '#5c6370',
    brightRed: '#e06c75',
    brightGreen: '#98c379',
    brightYellow: '#e5c07b',
    brightBlue: '#61afef',
    brightMagenta: '#c678dd',
    brightCyan: '#56b6c2',
    brightWhite: '#ffffff'
};

/**
 * 海洋主题（蓝色调）
 */
export const OceanTheme: TerminalTheme = {
    name: 'ocean',
    description: '海洋主题',
    background: '#0f1419',
    foreground: '#b3b1ad',
    cursor: '#4dbf99',
    selectionBackground: '#253340',
    black: '#01060e',
    red: '#ea6c73',
    green: '#91b362',
    yellow: '#f9af4f',
    blue: '#53bdfa',
    magenta: '#fae994',
    cyan: '#90e1c6',
    white: '#c7c7c7',
    brightBlack: '#686868',
    brightRed: '#f07178',
    brightGreen: '#c2d94c',
    brightYellow: '#ffb454',
    brightBlue: '#59c2ff',
    brightMagenta: '#ffee99',
    brightCyan: '#95e6cb',
    brightWhite: '#ffffff'
};

/**
 * 所有可用主题的集合
 */
export const AVAILABLE_THEMES = {
    default: DefaultTheme,
    dark: DarkTheme,
    ai: AITheme,
    matrix: MatrixTheme,
    warm: WarmTheme,
    ocean: OceanTheme
} as const;

/**
 * 主题名称类型
 */
export type ThemeName = keyof typeof AVAILABLE_THEMES;

/**
 * 获取所有主题名称
 */
export function getThemeNames(): ThemeName[] {
    return Object.keys(AVAILABLE_THEMES) as ThemeName[];
}

/**
 * 获取主题列表
 */
export function getThemeList(): TerminalTheme[] {
    return Object.values(AVAILABLE_THEMES);
}

/**
 * 根据名称获取主题
 */
export function getThemeByName(name: string): TerminalTheme | undefined {
    return AVAILABLE_THEMES[name as ThemeName];
}

/**
 * 检查主题是否存在
 */
export function isValidThemeName(name: string): name is ThemeName {
    return name in AVAILABLE_THEMES;
} 