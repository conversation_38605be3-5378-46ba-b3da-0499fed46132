import { Terminal } from '@xterm/xterm';
import { TerminalTheme, ThemeName, getThemeByName, DefaultTheme, AVAILABLE_THEMES } from './themes';

/**
 * 主题变更事件类型
 */
export interface ThemeChangeEvent {
    oldTheme: TerminalTheme;
    newTheme: TerminalTheme;
    source: 'manual' | 'command' | 'auto';
}

/**
 * 主题管理器配置
 */
export interface ThemeManagerConfig {
    defaultTheme?: ThemeName;
    enableHistory?: boolean;
    maxHistorySize?: number;
}

/**
 * 终端主题管理器
 * 负责管理终端主题的切换、历史记录和状态维护
 */
export class ThemeManager {
    private terminal: Terminal | null = null;
    private currentTheme: TerminalTheme;
    private themeHistory: TerminalTheme[] = [];
    private listeners: ((event: ThemeChangeEvent) => void)[] = [];
    private config: Required<ThemeManagerConfig>;

    constructor(config: ThemeManagerConfig = {}) {
        this.config = {
            defaultTheme: 'default',
            enableHistory: true,
            maxHistorySize: 10,
            ...config
        };

        // 设置初始主题
        this.currentTheme = getThemeByName(this.config.defaultTheme) || DefaultTheme;
    }

    /**
     * 设置终端实例
     */
    setTerminal(terminal: Terminal): void {
        this.terminal = terminal;
        // 应用当前主题到终端
        this.applyThemeToTerminal(this.currentTheme);
    }

    /**
     * 获取当前主题
     */
    getCurrentTheme(): TerminalTheme {
        return this.currentTheme;
    }

    /**
     * 获取当前主题名称
     */
    getCurrentThemeName(): ThemeName {
        return this.currentTheme.name as ThemeName;
    }

    /**
     * 切换到指定主题
     */
    switchTheme(themeName: ThemeName, source: 'manual' | 'command' | 'auto' = 'manual'): boolean {
        const newTheme = getThemeByName(themeName);
        if (!newTheme) {
            console.warn(`[ThemeManager] 主题 "${themeName}" 不存在`);
            return false;
        }

        const oldTheme = this.currentTheme;

        // 如果是同一个主题，不需要切换
        if (oldTheme.name === newTheme.name) {
            return true;
        }

        // 添加到历史记录
        if (this.config.enableHistory) {
            this.addToHistory(oldTheme);
        }

        // 更新当前主题
        this.currentTheme = newTheme;

        // 应用主题到终端
        this.applyThemeToTerminal(newTheme);

        // 触发事件
        this.notifyThemeChange({ oldTheme, newTheme, source });

        return true;
    }

    /**
     * 切换到上一个主题
     */
    switchToPreviousTheme(): boolean {
        if (!this.config.enableHistory || this.themeHistory.length === 0) {
            return false;
        }

        const previousTheme = this.themeHistory.pop()!;
        const oldTheme = this.currentTheme;

        this.currentTheme = previousTheme;
        this.applyThemeToTerminal(previousTheme);
        this.notifyThemeChange({ oldTheme, newTheme: previousTheme, source: 'manual' });

        return true;
    }

    /**
     * 重置到默认主题
     */
    resetToDefault(): boolean {
        return this.switchTheme(this.config.defaultTheme, 'manual');
    }

    /**
     * 获取所有可用主题
     */
    getAvailableThemes(): TerminalTheme[] {
        return Object.values(AVAILABLE_THEMES);
    }

    /**
     * 获取主题历史记录
     */
    getThemeHistory(): TerminalTheme[] {
        return [...this.themeHistory];
    }

    /**
     * 清空主题历史记录
     */
    clearHistory(): void {
        this.themeHistory = [];
    }

    /**
     * 监听主题变更事件
     */
    onThemeChange(callback: (event: ThemeChangeEvent) => void): () => void {
        this.listeners.push(callback);

        // 返回取消监听的函数
        return () => {
            const index = this.listeners.indexOf(callback);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }

    /**
     * 预览主题（临时应用，不添加到历史记录）
     */
    previewTheme(themeName: ThemeName): boolean {
        const theme = getThemeByName(themeName);
        if (!theme) {
            return false;
        }

        this.applyThemeToTerminal(theme);
        return true;
    }

    /**
     * 取消预览，恢复当前主题
     */
    cancelPreview(): void {
        this.applyThemeToTerminal(this.currentTheme);
    }

    /**
     * 获取主题配置信息
     */
    getThemeInfo(themeName?: ThemeName): { name: string; description: string; colors: any } | null {
        const theme = themeName ? getThemeByName(themeName) : this.currentTheme;
        if (!theme) return null;

        return {
            name: theme.name,
            description: theme.description,
            colors: {
                background: theme.background,
                foreground: theme.foreground,
                cursor: theme.cursor,
                selectionBackground: theme.selectionBackground
            }
        };
    }

    /**
     * 应用主题到终端
     */
    private applyThemeToTerminal(theme: TerminalTheme): void {
        if (!this.terminal) return;

        try {
            // 创建主题对象（去掉扩展属性）
            const terminalTheme = { ...theme };
            delete (terminalTheme as any).name;
            delete (terminalTheme as any).description;

            // 保持透明背景设置，但确保ANSI颜色正常工作
            terminalTheme.background = '#00000000';

            // 确保所有ANSI背景色都设置为透明，避免黑色背景问题
            // 这些颜色主要用于ANSI转义序列的背景色
            if (terminalTheme.black && terminalTheme.black !== 'transparent') {
                // 保留前景色，但确保不会用作背景色
                terminalTheme.black = terminalTheme.black;
            }

            // 应用主题
            this.terminal.options.theme = terminalTheme;

            // 刷新终端显示
            this.terminal.refresh(0, this.terminal.rows - 1);
        } catch (error) {
            console.error('[ThemeManager] 应用主题失败:', error);
        }
    }

    /**
     * 添加主题到历史记录
     */
    private addToHistory(theme: TerminalTheme): void {
        // 避免连续相同主题
        if (this.themeHistory.length > 0 &&
            this.themeHistory[this.themeHistory.length - 1].name === theme.name) {
            return;
        }

        this.themeHistory.push(theme);

        // 限制历史记录大小
        if (this.themeHistory.length > this.config.maxHistorySize) {
            this.themeHistory.shift();
        }
    }

    /**
     * 通知主题变更事件
     */
    private notifyThemeChange(event: ThemeChangeEvent): void {
        this.listeners.forEach(callback => {
            try {
                callback(event);
            } catch (error) {
                console.error('[ThemeManager] 主题变更事件回调出错:', error);
            }
        });
    }

    /**
     * 销毁主题管理器
     */
    dispose(): void {
        this.terminal = null;
        this.listeners = [];
        this.themeHistory = [];
    }
}

/**
 * 全局主题管理器实例
 */
export const globalThemeManager = new ThemeManager({
    defaultTheme: 'default',
    enableHistory: true,
    maxHistorySize: 10
}); 