import { commandExecutor, commandRegistry, CommandContext, hasCommand } from '@/lib/commands';
import { globalOSC8LinkManager, TerminalOSC8LinkManager } from './osc8-link-manager';
import { shouldBypassAIMode, getMatchingRoutingRule, CommandSource } from './CommandRoutingStrategy';

export class TerminalCommandManager {
    private isAIModeEnabled = false;
    private currentAISessionId: string | null = null;
    private isCommandSystemInitialized = false;
    private osc8LinkManager: TerminalOSC8LinkManager;

    constructor() {
        // 延迟初始化，避免循环依赖
        this.osc8LinkManager = globalOSC8LinkManager;

        // 设置命令执行器，让OSC 8链接能够执行命令
        this.osc8LinkManager.setCommandExecutor(async (command: string, params?: Record<string, any>, terminal?: any) => {
            return await this.executeCommandFromLink(command, params, terminal);
        });
    }

    // 确保命令系统已初始化（延迟初始化）
    private async ensureCommandSystemReady(): Promise<void> {
        if (this.isCommandSystemInitialized) {
            return;
        }

        try {
            // 动态导入以避免循环依赖
            const { ensureCommandsInitialized } = await import('@/lib/commands/init');
            ensureCommandsInitialized();
            this.isCommandSystemInitialized = true;
            // 初始化日志已由 ensureCommandsInitialized 输出，避免重复
        } catch (error) {
            console.error('❌ 终端命令管理器初始化失败:', error);
            throw error;
        }
    }

    // 设置浮动窗口函数 - 保留兼容性，但窗口管理现在在统一命令系统中
    setAddWindowFunction(addWindow: (window: any) => string) {
        // 窗口管理现在通过统一命令系统处理
        // 静默处理，避免重复日志
    }

    setRemoveWindowFunction(removeWindow: (id: string) => void) {
        // 窗口管理现在通过统一命令系统处理
        // 静默处理，避免重复日志
    }

    setStartClosingFunction(startClosing: (id: string) => void) {
        // 窗口管理现在通过统一命令系统处理
        // 静默处理，避免重复日志
    }

    // 保留兼容性方法
    getRegistry() {
        return commandRegistry;
    }

    // 核心执行方法 - 统一处理所有命令
    async executeCommand(input: string, terminal: any, source: CommandSource = 'user') {
        const trimmedInput = input.trim();

        if (!trimmedInput) {
            return { success: true };
        }

        // 确保命令系统已初始化
        try {
            await this.ensureCommandSystemReady();
        } catch (error) {
            terminal.writeln('❌ 命令系统初始化失败，请稍后重试');
            return {
                success: false,
                message: '命令系统初始化失败'
            };
        }

        // 🚀 使用智能命令路由策略
        const shouldBypassAI = shouldBypassAIMode(trimmedInput, source);

        // 处理AI模式
        if (this.isAIModeEnabled && !shouldBypassAI) {
            try {
                // AI模式下的特殊处理
                await this.handleAIInput(trimmedInput, terminal);
                return { success: true };
            } catch (error) {
                return {
                    success: false,
                    message: `❌ AI处理失败: ${error instanceof Error ? error.message : '未知错误'}`
                };
            }
        }

        // 解析命令和参数
        const { commandName, args } = this.parseCommand(trimmedInput);

        // 检查命令是否存在
        if (!hasCommand(commandName)) {
            return {
                success: false,
                message: `❌ 未知命令: ${commandName}。输入 'help' 查看可用命令。`
            };
        }

        try {
            // 构建执行上下文
            const context: CommandContext = {
                source: 'terminal',
                terminal,
                sessionId: this.currentAISessionId || undefined
            };

            // 执行统一命令
            const result = await commandExecutor.execute(commandName, args, context);

            // 处理结果
            if (!result.success && result.error) {
                terminal.writeln(`❌ ${result.error}`);
            }
            // 🔇 已禁用成功确认消息 - 用户要求去除终端输出确认
            // else if (result.message && !result.terminalOutput) {
            //     // 如果没有专门的终端输出，显示通用消息
            //     terminal.writeln(`✅ ${result.message}`);
            // }

            return result;
        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : '未知错误';
            terminal.writeln(`❌ 命令执行失败: ${errorMsg}`);
            return {
                success: false,
                message: errorMsg
            };
        }
    }

    // 解析命令和参数
    private parseCommand(input: string): { commandName: string; args: Record<string, any> } {
        const parts = input.split(/\s+/);
        const commandName = parts[0];
        const args: Record<string, any> = {};

        // 解析参数 (支持 key=value 格式)
        for (let i = 1; i < parts.length; i++) {
            const part = parts[i];
            const equalIndex = part.indexOf('=');

            if (equalIndex > 0) {
                const key = part.substring(0, equalIndex);
                let value: any = part.substring(equalIndex + 1);

                // 尝试解析为布尔值或数字
                if (value === 'true') value = true;
                else if (value === 'false') value = false;
                else if (!isNaN(Number(value)) && value !== '') value = Number(value);

                args[key] = value;
            } else {
                // 位置参数处理
                if (commandName === 'sixel' && !args.action) {
                    args.action = part;
                }
                // 可以根据需要扩展更多位置参数处理
            }
        }

        return { commandName, args };
    }



    // AI模式处理
    private async handleAIInput(input: string, terminal: any) {
        const trimmedInput = input.trim();

        // AI模式的退出处理
        if (trimmedInput.toLowerCase() === 'exit') {
            // 使用disableAIMode方法来正确退出AI模式（包括主题恢复）
            this.disableAIMode();
            terminal.writeln('');
            terminal.writeln('👋 已退出AI模式，主题已恢复');
            return;
        }

        // 处理空输入
        if (!trimmedInput) {
            return;
        }



        try {
            // 使用统一命令系统的AI消息处理
            const { executeCommand } = require('@/lib/commands');
            const context = {
                source: 'terminal' as const,
                terminal,
                sessionId: this.currentAISessionId || undefined
            };

            // 调用AI命令的消息处理功能
            const { handleAIMessage } = await import('@/lib/commands/terminal-commands');
            await handleAIMessage(trimmedInput, context);
        } catch (error) {
            terminal.writeln(`❌ AI处理失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }



    // AI模式控制
    enableAIMode(sessionId?: string) {
        this.isAIModeEnabled = true;
        this.currentAISessionId = sessionId || null;
    }

    disableAIMode() {
        this.isAIModeEnabled = false;
        this.currentAISessionId = null;

        // 退出AI模式时恢复之前的主题
        try {
            const { globalThemeManager } = require('@/lib/terminal/ThemeManager');
            const currentTheme = globalThemeManager.getCurrentThemeName();

            // 如果当前是AI主题，则恢复到上一个主题
            if (currentTheme === 'ai') {
                const restored = globalThemeManager.switchToPreviousTheme();
                if (!restored) {
                    // 如果没有历史记录，恢复到默认主题
                    globalThemeManager.resetToDefault();
                }
            }
        } catch (error) {
            // 静默处理主题恢复错误
            console.warn('[AI模式] 主题恢复失败:', error);
        }
    }

    isInAIMode(): boolean {
        return this.isAIModeEnabled;
    }

    getCurrentAISessionId(): string | null {
        return this.currentAISessionId;
    }

    // OSC 8 链接相关方法
    getOSC8LinkManager(): TerminalOSC8LinkManager {
        return this.osc8LinkManager;
    }

    // 为OSC 8链接执行命令的专用方法
    private async executeCommandFromLink(command: string, params?: Record<string, any>, terminal?: any) {
        try {
            // 确保命令系统已初始化
            await this.ensureCommandSystemReady();

            // 检查命令是否存在
            if (!hasCommand(command)) {
                return {
                    success: false,
                    error: `未知命令: ${command}`
                };
            }

            // 构建执行上下文（从链接调用的命令，如果有terminal则设为terminal，否则为api）
            const context: CommandContext = {
                source: terminal ? 'terminal' : 'api',
                terminal: terminal,
                sessionId: this.currentAISessionId || undefined
            };

            // 执行统一命令
            const result = await commandExecutor.execute(command, params || {}, context);

            // 如果有terminal实例，命令执行完成后重新启动prompt循环
            if (terminal && typeof terminal.restartPromptLoop === 'function') {
                setTimeout(() => {
                    terminal.restartPromptLoop();
                }, 10);
            }

            return result;
        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : '未知错误';
            return {
                success: false,
                error: errorMsg
            };
        }
    }

    // 兼容性方法
    addCommand(handler: any) {
        console.warn('⚠️ addCommand已废弃，请使用统一的命令注册系统');
    }

    removeCommand(name: string) {
        console.warn('⚠️ removeCommand已废弃，请使用统一的命令注册系统');
    }
} 