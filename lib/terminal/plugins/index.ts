export {
    TerminalAuthPromptPlugin,
    createAuthPromptPlugin
} from './TerminalAuthPromptPlugin';
export type { AuthState, PromptConfig } from './TerminalAuthPromptPlugin';

export {
    TerminalSixelPlugin,
    createSixelPlugin,
    StandardSixelConfig,
    HighQualitySixelConfig,
    CompactSixelConfig
} from './TerminalSixelPlugin';
export type { SixelConfig } from './TerminalSixelPlugin';

export {
    TerminalOSC8LinkManager,
    globalOSC8LinkManager,
    createLink,
    createCommandLink,
    createUrlLink,
    createFileLink,
    createEmailLink,
    LinkStyles,
    createStyledCommandLink,
    createStyledUrlLink,
    createButtonCommandLink,
    createSuccessCommandLink,
    createWarningCommandLink,
    createErrorCommandLink,
    createImportantCommandLink,
    createCustomColorCommandLink,
    createRainbowCommandLink,
    createCleanCommandLink,
    createTextCommandLink,
    createLinkStyleCommandLink
} from '../osc8-link-manager';
export type { OSC8<PERSON>inkOptions, OSC8LinkStyle, LinkHandler, CommandExecutor } from '../osc8-link-manager'; 