import { Terminal } from '@xterm/xterm';
import { ImageAddon, IImageAddonOptions } from '@xterm/addon-image';

/**
 * Sixel图像配置接口
 */
export interface SixelConfig {
    enableSixel?: boolean;
    maxImageWidth?: number;
    maxImageHeight?: number;
}

/**
 * 终端Sixel图像显示插件
 * 
 * 这个插件负责：
 * 1. 启用终端的Sixel图像支持
 * 2. 提供显示Sixel图像的方法
 * 3. 管理图像显示配置
 */
export class TerminalSixelPlugin {
    private terminal: Terminal;
    private config: SixelConfig;
    private imageAddon: ImageAddon | null = null;
    private isInitialized: boolean = false;

    constructor(terminal: Terminal, config?: SixelConfig) {
        this.terminal = terminal;
        this.config = {
            enableSixel: true,
            maxImageWidth: 800,
            maxImageHeight: 600,
            ...config
        };
    }

    /**
 * 初始化Sixel插件
 */
    initialize(): void {
        if (this.isInitialized) {
            return;
        }

        try {
            // 启用Sixel支持
            if (this.config.enableSixel) {
                // 创建并加载官方Image Addon，配置Sixel支持
                const addonOptions: IImageAddonOptions = {
                    enableSizeReports: true,
                    pixelLimit: 16777216,
                    sixelSupport: true,
                    sixelScrolling: true,
                    sixelPaletteLimit: 256,
                    sixelSizeLimit: 25000000,
                    storageLimit: 128,
                    showPlaceholder: true,
                    iipSupport: false
                };

                this.imageAddon = new ImageAddon(addonOptions);
                this.terminal.loadAddon(this.imageAddon);
            }

            this.isInitialized = true;
        } catch (error) {
            console.error('[TerminalSixelPlugin] 初始化失败:', error);
            throw error;
        }
    }

    /**
 * 显示Sixel图像
 * @param sixelData Sixel编码的图像数据
 * @param withNewline 是否在图像后添加换行
 */
    displaySixelImage(sixelData: string, withNewline: boolean = true): void {
        if (!this.isInitialized || !this.imageAddon) {
            console.warn('[TerminalSixelPlugin] 插件未初始化或Image Addon不可用');
            return;
        }

        try {
            // 构建完整的Sixel序列
            const fullSixelSequence = `\x1bPq${sixelData}\x1b\\`;

            // 发送到终端
            this.terminal.write(fullSixelSequence);

            if (withNewline) {
                this.terminal.write('\r\n');
            }
        } catch (error) {
            console.error('[TerminalSixelPlugin] 显示Sixel图像失败:', error);
        }
    }

    /**
 * 显示测试图像
 */
    displayTestImage(): void {
        // 创建一个标准的Sixel测试图像 - 12x12像素的红色方块
        const testSixelData = '"1;1;12;12#0;2;100;0;0#0!12~-!12~-!12~-!12~-!12~-!12~-!12~-!12~-!12~-!12~-!12~-!12~-';

        this.terminal.writeln('🖼️ 显示Sixel测试图像:');
        this.displaySixelImage(testSixelData);
    }

    /**
     * 获取插件配置
     */
    getConfig(): SixelConfig {
        return { ...this.config };
    }

    /**
     * 更新插件配置
     */
    updateConfig(newConfig: Partial<SixelConfig>): void {
        this.config = { ...this.config, ...newConfig };
    }

    /**
     * 检查Sixel支持状态
     */
    getSixelSupport(): boolean {
        return !!this.config.enableSixel && this.isInitialized && !!this.imageAddon;
    }

    /**
     * 销毁插件，清理资源
     */
    dispose(): void {
        this.imageAddon = null;
        this.isInitialized = false;
    }
}

/**
 * 工厂函数：创建Sixel插件实例
 */
export function createSixelPlugin(terminal: Terminal, config?: SixelConfig): TerminalSixelPlugin {
    return new TerminalSixelPlugin(terminal, config);
}

/**
 * 预设配置：标准Sixel配置
 */
export const StandardSixelConfig: SixelConfig = {
    enableSixel: true,
    maxImageWidth: 800,
    maxImageHeight: 600
};

/**
 * 预设配置：高质量Sixel配置
 */
export const HighQualitySixelConfig: SixelConfig = {
    enableSixel: true,
    maxImageWidth: 1200,
    maxImageHeight: 900
};

/**
 * 预设配置：紧凑Sixel配置
 */
export const CompactSixelConfig: SixelConfig = {
    enableSixel: true,
    maxImageWidth: 400,
    maxImageHeight: 300
}; 