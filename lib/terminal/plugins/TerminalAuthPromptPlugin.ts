import { SupabaseClient, Session, User } from '@supabase/supabase-js';

/**
 * 认证状态接口
 */
export interface AuthState {
    user: User | null;
    session: Session | null;
    isLoading: boolean;
}

/**
 * 提示符配置接口
 */
export interface PromptConfig {
    authenticatedFormat: (userName: string) => string;
    guestFormat: () => string;
    loadingFormat: () => string;
}

/**
 * 终端认证提示符插件
 * 
 * 这个插件负责：
 * 1. 监听 Supabase 认证状态变化
 * 2. 根据用户登录状态生成不同的终端提示符
 * 3. 提供解耦的方式获取当前提示符文本
 */
export class TerminalAuthPromptPlugin {
    private authState: AuthState = {
        user: null,
        session: null,
        isLoading: true
    };

    private config: PromptConfig;
    private supabase: SupabaseClient | null = null;
    private authListener: any = null;
    private stateChangeCallbacks: Array<(state: AuthState) => void> = [];
    private lastStateUpdate: number = Date.now();

    constructor(config?: Partial<PromptConfig>) {
        this.config = {
            authenticatedFormat: (userName: string) => `${userName} => `,
            guestFormat: () => 'Guest => ',
            loadingFormat: () => '... => ',
            ...config
        };
    }

    /**
     * 初始化插件，设置 Supabase 客户端并开始监听认证状态
     */
    async initialize(supabaseClient: SupabaseClient, initialUser?: User | null, initialSession?: Session | null, initialIsLoading?: boolean): Promise<void> {
        try {
            this.supabase = supabaseClient;

            // 如果提供了初始状态，直接设置
            if (initialUser !== undefined || initialSession !== undefined || initialIsLoading !== undefined) {
                this.setAuthState(initialUser || null, initialSession || null, initialIsLoading || false);
            } else {
                // 智能初始化：先快速检查本地状态，避免不必要的loading
                await this.smartInitialize();
            }

            // 监听认证状态变化
            this.startAuthListener();
        } catch (error) {
            throw error;
        }
    }

    /**
     * 获取当前提示符文本
     */
    getCurrentPrompt(): string {
        // 优先检查用户状态，即使在加载中
        if (this.authState.user) {
            // 优先使用用户元数据中的名称，回退到邮箱用户名部分
            const userName = this.getUserDisplayName(this.authState.user);
            return this.config.authenticatedFormat(userName);
        }

        // 如果没有用户但在加载中，显示加载状态
        // 但是增加超时保护，避免长时间显示加载状态
        if (this.authState.isLoading) {
            // 检查是否加载时间过长（超过1秒就显示Guest状态）
            const loadingTime = Date.now() - (this.lastStateUpdate || 0);
            if (loadingTime > 1000) {
                return this.config.guestFormat();
            }
            return this.config.loadingFormat();
        }

        // 默认显示访客状态
        return this.config.guestFormat();
    }

    /**
     * 获取当前认证状态
     */
    getAuthState(): AuthState {
        return { ...this.authState };
    }

    /**
     * 添加状态变化回调函数
     */
    onStateChange(callback: (state: AuthState) => void): () => void {
        this.stateChangeCallbacks.push(callback);

        // 返回取消注册函数
        return () => {
            const index = this.stateChangeCallbacks.indexOf(callback);
            if (index > -1) {
                this.stateChangeCallbacks.splice(index, 1);
            }
        };
    }

    /**
     * 手动刷新认证状态
     */
    async refreshAuthState(): Promise<void> {
        if (!this.supabase) return;

        await this.updateAuthState();
    }

    /**
     * 直接设置认证状态（从 SupabaseProvider 获取的状态）
     */
    setAuthState(user: User | null, session: Session | null, isLoading: boolean): void {
        const newState: AuthState = {
            user,
            session,
            isLoading
        };

        this.updateState(newState);
    }

    /**
     * 智能初始化：快速确定初始状态，避免长时间显示loading
     */
    private async smartInitialize(): Promise<void> {
        try {
            // 快速检查本地存储，看是否有明显的登录迹象
            const hasLocalAuthData = this.checkLocalAuthData();

            if (!hasLocalAuthData) {
                // 没有本地认证数据，立即设置为Guest状态
                this.setAuthState(null, null, false);

                // 异步验证状态（不阻塞界面显示）
                this.asyncVerifyAuthState();
                return;
            }

            // 有本地认证数据，快速验证（设置短超时）
            await this.quickAuthCheck();

        } catch (error) {
            this.setAuthState(null, null, false);
        }
    }

    /**
     * 检查本地是否有认证数据
     */
    private checkLocalAuthData(): boolean {
        try {
            // 检查常见的Supabase认证相关存储
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.includes('supabase') && key.includes('auth')) {
                    const value = localStorage.getItem(key);
                    if (value && value.includes('access_token')) {
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    /**
     * 快速认证检查（1秒超时）
     */
    private async quickAuthCheck(): Promise<void> {
        if (!this.supabase) return;

        try {
            // 设置很短的超时时间
            const quickTimeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('快速检查超时')), 1000)
            );

            const sessionPromise = this.supabase.auth.getSession();
            const result = await Promise.race([sessionPromise, quickTimeout]) as any;

            if (result?.data?.session?.user) {
                // 有用户，获取详细信息
                const userResult = await Promise.race([
                    this.supabase.auth.getUser(),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('获取用户超时')), 1000))
                ]) as any;

                this.setAuthState(
                    userResult?.data?.user || result.data.session.user,
                    result.data.session,
                    false
                );
            } else {
                // 无用户
                this.setAuthState(null, null, false);
            }
        } catch (error) {
            this.setAuthState(null, null, false);
        }
    }

    /**
     * 异步验证认证状态（不阻塞界面）
     */
    private asyncVerifyAuthState(): void {
        if (!this.supabase) return;

        // 延迟执行，不影响初始显示
        setTimeout(async () => {
            try {
                await this.updateAuthState();
            } catch (error) {
                // 静默处理错误
            }
        }, 2000); // 2秒后验证
    }

    /**
     * 销毁插件，清理资源
     */
    dispose(): void {
        if (this.authListener) {
            this.authListener.data.subscription.unsubscribe();
            this.authListener = null;
        }

        this.stateChangeCallbacks = [];
        this.supabase = null;
    }

    /**
     * 开始监听认证状态变化
     */
    private startAuthListener(): void {
        if (!this.supabase) return;

        try {
            this.authListener = this.supabase.auth.onAuthStateChange(async (event, session) => {
                // 验证用户身份
                let user: User | null = null;
                if (session?.user) {
                    try {
                        const { data } = await this.supabase!.auth.getUser();
                        user = data.user;
                    } catch (error) {
                        // 静默处理错误
                    }
                }

                const newState: AuthState = {
                    user,
                    session,
                    isLoading: false
                };

                this.updateState(newState);
            });
        } catch (error) {
            // 静默处理错误
        }
    }

    /**
     * 更新认证状态
     */
    private async updateAuthState(): Promise<void> {
        if (!this.supabase) return;

        try {
            this.updateState({ ...this.authState, isLoading: true });

            // 添加超时保护
            const getUserPromise = this.supabase.auth.getUser();
            const userTimeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('获取用户信息超时')), 3000)
            );

            let userData, userError;
            try {
                const result = await Promise.race([getUserPromise, userTimeout]) as any;
                userData = result.data;
                userError = result.error;
            } catch (timeoutError) {
                userData = null;
                userError = null;
            }

            if (userError) {
                if (userError.name !== "AuthSessionMissingError" &&
                    !/Auth session missing/i.test(userError.message || "")) {
                    // 静默处理非预期错误
                }
            }

            // 添加超时保护
            const getSessionPromise = this.supabase.auth.getSession();
            const sessionTimeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('获取会话信息超时')), 3000)
            );

            let sessionData;
            try {
                const result = await Promise.race([getSessionPromise, sessionTimeout]) as any;
                sessionData = result.data;
            } catch (timeoutError) {
                sessionData = null;
            }

            const newState: AuthState = {
                user: userData?.user || null,
                session: sessionData?.session || null,
                isLoading: false
            };

            this.updateState(newState);
        } catch (error) {
            this.updateState({
                user: null,
                session: null,
                isLoading: false
            });
        }
    }

    /**
     * 更新状态并通知监听器
     */
    private updateState(newState: AuthState): void {
        const hasChanged = (
            this.authState.user?.id !== newState.user?.id ||
            this.authState.isLoading !== newState.isLoading
        );

        this.authState = newState;
        this.lastStateUpdate = Date.now(); // 更新状态变更时间

        if (hasChanged) {
            // 通知所有回调函数
            this.stateChangeCallbacks.forEach(callback => {
                try {
                    callback(newState);
                } catch (error) {
                    // 静默处理错误
                }
            });
        }
    }

    /**
     * 获取用户显示名称
     */
    private getUserDisplayName(user: User): string {
        // 1. 首先尝试用户元数据中的 name
        if (user.user_metadata?.name) {
            return user.user_metadata.name;
        }

        // 2. 然后尝试应用元数据中的 name
        if (user.app_metadata?.name) {
            return user.app_metadata.name;
        }

        // 3. 最后使用邮箱的用户名部分
        if (user.email) {
            return user.email.split('@')[0];
        }

        // 4. 兜底方案
        return 'User';
    }
}

/**
 * 工厂函数：创建带有默认配置的认证提示符插件
 */
export function createAuthPromptPlugin(config?: Partial<PromptConfig>): TerminalAuthPromptPlugin {
    return new TerminalAuthPromptPlugin(config);
}

