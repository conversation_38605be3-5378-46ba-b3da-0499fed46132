/**
 * 🚀 命令路由策略管理器
 *
 * 提供智能命令路由决策，解决AI模式下菜单命令执行的问题
 * 采用策略模式，支持灵活的路由规则配置
 */

import { hasCommand } from '@/lib/commands';

export type CommandSource = 'user' | 'window' | 'menu';

/**
 * 性能优化：使用Set进行O(1)查找
 */
const SYSTEM_COMMANDS = new Set(['exit', 'help', 'status', 'clear']);
const AI_COMMANDS = new Set(['ai']);

export interface CommandRoutingRule {
    /** 规则名称 */
    name: string;
    /** 规则描述 */
    description: string;
    /** 规则优先级（数字越大优先级越高） */
    priority: number;
    /** 规则匹配函数 */
    matches: (input: string, source: CommandSource) => boolean;
    /** 是否绕过AI模式 */
    shouldBypassAI: boolean;
}

/**
 * 默认命令路由规则集
 */
export const DEFAULT_ROUTING_RULES: CommandRoutingRule[] = [
    {
        name: 'menu-commands',
        description: '来自菜单的所有命令',
        priority: 90,
        matches: (input, source) => source === 'menu',
        shouldBypassAI: true
    },
    {
        name: 'window-commands',
        description: '来自浮窗的所有命令',
        priority: 80,
        matches: (input, source) => source === 'window',
        shouldBypassAI: true
    },
    {
        name: 'system-commands',
        description: '系统核心命令',
        priority: 70,
        matches: (input, source) => {
            const commandName = input.split(' ', 1)[0].toLowerCase();
            return SYSTEM_COMMANDS.has(commandName);
        },
        shouldBypassAI: true
    },
    {
        name: 'ai-mode-commands',
        description: 'AI模式相关命令',
        priority: 60,
        matches: (input, source) => {
            const commandName = input.split(' ', 1)[0].toLowerCase();
            return AI_COMMANDS.has(commandName);
        },
        shouldBypassAI: false
    },
    {
        name: 'registered-commands',
        description: '所有已注册的命令（动态检查）',
        priority: 50,
        matches: (input, source) => {
            if (source !== 'user') return false;
            const commandName = input.split(' ', 1)[0].toLowerCase();
            try {
                return hasCommand(commandName);
            } catch (error) {
                // 静默处理错误，避免影响主流程
                return false;
            }
        },
        shouldBypassAI: true
    },
    {
        name: 'default-user-input',
        description: '默认用户输入（进入AI模式）',
        priority: 0,
        matches: (input, source) => source === 'user',
        shouldBypassAI: false
    }
];

/**
 * 命令路由策略管理器
 */
export class CommandRoutingStrategy {
    private rules: CommandRoutingRule[] = [];
    private readonly ruleCache = new Map<string, boolean>();

    constructor(rules: CommandRoutingRule[] = DEFAULT_ROUTING_RULES) {
        this.rules = [...rules].sort((a, b) => b.priority - a.priority);
    }

    /**
     * 添加路由规则
     */
    addRule(rule: CommandRoutingRule): void {
        if (!rule.name || typeof rule.matches !== 'function') {
            throw new Error('Invalid rule: name and matches function are required');
        }

        this.rules.push(rule);
        this.rules.sort((a, b) => b.priority - a.priority);
        this.clearCache();
    }

    /**
     * 移除路由规则
     */
    removeRule(name: string): boolean {
        const index = this.rules.findIndex(rule => rule.name === name);
        if (index !== -1) {
            this.rules.splice(index, 1);
            this.clearCache();
            return true;
        }
        return false;
    }

    /**
     * 获取所有规则（只读副本）
     */
    getRules(): readonly CommandRoutingRule[] {
        return Object.freeze([...this.rules]);
    }

    /**
     * 决定是否应该绕过AI模式（带缓存优化）
     */
    shouldBypassAIMode(input: string, source: CommandSource): boolean {
        if (!input || !source) {
            return false;
        }

        const cacheKey = `${input}:${source}`;
        const cached = this.ruleCache.get(cacheKey);
        if (cached !== undefined) {
            return cached;
        }

        const result = this.findMatchingRule(input, source)?.shouldBypassAI ?? false;

        // 缓存结果（限制缓存大小）
        if (this.ruleCache.size < 100) {
            this.ruleCache.set(cacheKey, result);
        }

        return result;
    }

    /**
     * 获取匹配的规则信息
     */
    getMatchingRule(input: string, source: CommandSource): CommandRoutingRule | null {
        return this.findMatchingRule(input, source);
    }

    /**
     * 重置为默认规则
     */
    resetToDefault(): void {
        this.rules = [...DEFAULT_ROUTING_RULES].sort((a, b) => b.priority - a.priority);
        this.clearCache();
    }

    /**
     * 清理资源
     */
    dispose(): void {
        this.rules.length = 0;
        this.clearCache();
    }

    /**
     * 内部方法：查找匹配的规则
     */
    private findMatchingRule(input: string, source: CommandSource): CommandRoutingRule | null {
        try {
            for (const rule of this.rules) {
                if (rule.matches(input, source)) {
                    return rule;
                }
            }
        } catch (error) {
            // 静默处理规则匹配错误，避免影响主流程
            if (process.env.NODE_ENV === 'development') {
                console.warn('[CommandRouting] Rule matching error:', error);
            }
        }
        return null;
    }

    /**
     * 内部方法：清除缓存
     */
    private clearCache(): void {
        this.ruleCache.clear();
    }
}

/**
 * 全局命令路由策略实例管理
 */
class CommandRoutingManager {
    private static instance: CommandRoutingStrategy | null = null;

    static getInstance(): CommandRoutingStrategy {
        if (!this.instance) {
            this.instance = new CommandRoutingStrategy();
        }
        return this.instance;
    }

    static dispose(): void {
        if (this.instance) {
            this.instance.dispose();
            this.instance = null;
        }
    }
}

/**
 * 便捷函数：检查是否应该绕过AI模式
 */
export function shouldBypassAIMode(input: string, source: CommandSource): boolean {
    try {
        if (!input || typeof input !== 'string' || !source) {
            return false;
        }
        return CommandRoutingManager.getInstance().shouldBypassAIMode(input, source);
    } catch (error) {
        // 静默处理错误，返回安全的默认值
        return false;
    }
}

/**
 * 便捷函数：获取匹配的路由规则
 */
export function getMatchingRoutingRule(input: string, source: CommandSource): CommandRoutingRule | null {
    try {
        if (!input || typeof input !== 'string' || !source) {
            return null;
        }
        return CommandRoutingManager.getInstance().getMatchingRule(input, source);
    } catch (error) {
        // 静默处理错误，返回null
        return null;
    }
}

/**
 * 便捷函数：获取全局路由策略实例（用于高级配置）
 */
export function getGlobalRoutingStrategy(): CommandRoutingStrategy {
    return CommandRoutingManager.getInstance();
}

/**
 * 便捷函数：清理全局资源（用于测试或应用关闭）
 */
export function disposeGlobalRoutingStrategy(): void {
    CommandRoutingManager.dispose();
}
