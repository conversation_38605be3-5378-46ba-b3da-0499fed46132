/**
 * OSC 8 超链接管理器
 * 提供全局的超链接创建和管理功能，支持内部命令和外部URL
 */

export interface OSC8LinkOptions {
    /** 自定义样式类名 */
    className?: string;
    /** 工具提示文本 */
    tooltip?: string;
    /** 是否启用悬停效果 */
    enableHover?: boolean;
    /** 链接参数（用于命令链接） */
    params?: Record<string, any>;
    /** 文本样式配置 */
    style?: OSC8LinkStyle;
}

export interface OSC8LinkStyle {
    /** 文本颜色 */
    color?: 'black' | 'red' | 'green' | 'yellow' | 'blue' | 'magenta' | 'cyan' | 'white' |
    'brightBlack' | 'brightRed' | 'brightGreen' | 'brightYellow' | 'brightBlue' |
    'brightMagenta' | 'brightCyan' | 'brightWhite' | string;
    /** 背景颜色 */
    backgroundColor?: 'black' | 'red' | 'green' | 'yellow' | 'blue' | 'magenta' | 'cyan' | 'white' |
    'brightBlack' | 'brightRed' | 'brightGreen' | 'brightYellow' | 'brightBlue' |
    'brightMagenta' | 'brightCyan' | 'brightWhite' | string;
    /** 是否加粗 */
    bold?: boolean;
    /** 是否斜体 */
    italic?: boolean;
    /** 是否删除线 */
    strikethrough?: boolean;
    /** 是否反转颜色 */
    reverse?: boolean;
    /** 是否闪烁 */
    blink?: boolean;
    /** 自定义RGB颜色 (格式: "255,128,0") */
    customColor?: string;
    /** 自定义RGB背景色 (格式: "255,128,0") */
    customBackgroundColor?: string;
}

export interface LinkHandler {
    activate: (event: MouseEvent, uri: string) => void;
    hover?: (event: MouseEvent, uri: string) => void;
    leave?: (event: MouseEvent, uri: string) => void;
}

export type CommandExecutor = (command: string, params?: Record<string, any>, terminal?: any) => Promise<any>;

/**
 * OSC 8 超链接管理器类
 */
export class TerminalOSC8LinkManager {
    private commandExecutor: CommandExecutor | null = null;
    private linkHandlers: Map<string, LinkHandler> = new Map();
    private defaultLinkHandler: LinkHandler | null = null;
    private isEnabled: boolean = true;
    private terminalInstance: any = null;

    constructor() {
        this.setupDefaultHandlers();
    }

    /**
     * 将样式配置转换为ANSI转义序列
     */
    private styleToAnsi(style?: OSC8LinkStyle): { start: string; end: string } {
        if (!style) {
            return { start: '', end: '' };
        }

        const ansiCodes: string[] = [];
        const resetCodes: string[] = [];

        // 标准颜色映射
        const colorMap: Record<string, string> = {
            black: '30', red: '31', green: '32', yellow: '33',
            blue: '34', magenta: '35', cyan: '36', white: '37',
            brightBlack: '90', brightRed: '91', brightGreen: '92', brightYellow: '93',
            brightBlue: '94', brightMagenta: '95', brightCyan: '96', brightWhite: '97'
        };

        const backgroundColorMap: Record<string, string> = {
            black: '40', red: '41', green: '42', yellow: '43',
            blue: '44', magenta: '45', cyan: '46', white: '47',
            brightBlack: '100', brightRed: '101', brightGreen: '102', brightYellow: '103',
            brightBlue: '104', brightMagenta: '105', brightCyan: '106', brightWhite: '107'
        };

        // 文本装饰
        if (style.bold) {
            ansiCodes.push('1');
            resetCodes.push('22'); // 取消加粗
        }
        if (style.italic) {
            ansiCodes.push('3');
            resetCodes.push('23'); // 取消斜体
        }
        if (style.strikethrough) {
            ansiCodes.push('9');
            resetCodes.push('29'); // 取消删除线
        }
        if (style.reverse) {
            ansiCodes.push('7');
            resetCodes.push('27'); // 取消反转
        }
        if (style.blink) {
            ansiCodes.push('5');
            resetCodes.push('25'); // 取消闪烁
        }

        // 前景色
        if (style.color) {
            if (colorMap[style.color]) {
                ansiCodes.push(colorMap[style.color]);
                resetCodes.push('39'); // 重置前景色
            }
        }

        // 背景色
        if (style.backgroundColor) {
            if (backgroundColorMap[style.backgroundColor]) {
                ansiCodes.push(backgroundColorMap[style.backgroundColor]);
                resetCodes.push('49'); // 重置背景色
            }
        }

        // 自定义RGB颜色
        if (style.customColor) {
            const rgb = style.customColor.split(',').map(s => s.trim());
            if (rgb.length === 3) {
                ansiCodes.push(`38;2;${rgb[0]};${rgb[1]};${rgb[2]}`);
                resetCodes.push('39'); // 重置前景色
            }
        }

        // 自定义RGB背景色
        if (style.customBackgroundColor) {
            const rgb = style.customBackgroundColor.split(',').map(s => s.trim());
            if (rgb.length === 3) {
                ansiCodes.push(`48;2;${rgb[0]};${rgb[1]};${rgb[2]}`);
                resetCodes.push('49'); // 重置背景色
            }
        }

        const start = ansiCodes.length > 0 ? `\x1b[${ansiCodes.join(';')}m` : '';
        const end = resetCodes.length > 0 ? `\x1b[${resetCodes.join(';')}m` : '';

        return { start, end };
    }

    /**
     * 设置命令执行器
     */
    setCommandExecutor(executor: CommandExecutor): void {
        this.commandExecutor = executor;
    }

    /**
     * 设置终端实例
     */
    setTerminalInstance(terminal: any): void {
        this.terminalInstance = terminal;
    }

    /**
     * 设置默认链接处理器
     */
    setDefaultLinkHandler(handler: LinkHandler): void {
        this.defaultLinkHandler = handler;
    }

    /**
     * 为特定协议注册链接处理器
     */
    registerLinkHandler(protocol: string, handler: LinkHandler): void {
        this.linkHandlers.set(protocol, handler);
    }

    /**
     * 启用/禁用链接管理器
     */
    setEnabled(enabled: boolean): void {
        this.isEnabled = enabled;
    }

    /**
     * 创建通用 OSC 8 超链接字符串
     * @param text 显示文本
     * @param uri 链接URI
     * @param options 选项配置
     * @returns OSC 8 超链接字符串
     */
    createLink(text: string, uri: string, options?: OSC8LinkOptions): string {
        if (!this.isEnabled) {
            return text; // 禁用时返回纯文本
        }

        // 获取样式的ANSI序列
        const { start: styleStart, end: styleEnd } = this.styleToAnsi(options?.style);

        // OSC 8 序列格式: [ANSI样式]\x1b]8;;URI\x07TEXT\x1b]8;;\x07[重置样式]
        return `${styleStart}\x1b]8;;${uri}\x07${text}\x1b]8;;\x07${styleEnd}`;
    }

    /**
     * 创建命令链接
     * @param text 显示文本
     * @param command 命令名称
     * @param params 命令参数
     * @param options 选项配置
     * @returns OSC 8 命令链接字符串
     */
    createCommandLink(
        text: string,
        command: string,
        params?: Record<string, any>,
        options?: OSC8LinkOptions
    ): string {
        // 构建命令URI
        let uri = `cmd:${command}`;

        // 如果有参数，将其编码到URI中
        if (params && Object.keys(params).length > 0) {
            const queryParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
                queryParams.append(key, String(value));
            });
            uri += `?${queryParams.toString()}`;
        }

        return this.createLink(text, uri, options);
    }

    /**
     * 创建URL链接
     * @param text 显示文本
     * @param url 外部URL
     * @param options 选项配置
     * @returns OSC 8 URL链接字符串
     */
    createUrlLink(text: string, url: string, options?: OSC8LinkOptions): string {
        // 确保URL格式正确
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = `https://${url}`;
        }

        return this.createLink(text, url, options);
    }

    /**
     * 创建文件链接
     * @param text 显示文本
     * @param filePath 文件路径
     * @param options 选项配置
     * @returns OSC 8 文件链接字符串
     */
    createFileLink(text: string, filePath: string, options?: OSC8LinkOptions): string {
        const uri = `file:${filePath}`;
        return this.createLink(text, uri, options);
    }

    /**
     * 创建邮件链接
     * @param text 显示文本
     * @param email 邮箱地址
     * @param options 选项配置
     * @returns OSC 8 邮件链接字符串
     */
    createEmailLink(text: string, email: string, options?: OSC8LinkOptions): string {
        const uri = `mailto:${email}`;
        return this.createLink(text, uri, options);
    }

    /**
     * 批量创建链接
     * @param links 链接配置数组
     * @returns 链接字符串数组
     */
    createBatchLinks(links: Array<{
        text: string;
        uri?: string;
        command?: string;
        url?: string;
        params?: Record<string, any>;
        options?: OSC8LinkOptions;
    }>): string[] {
        return links.map(link => {
            if (link.command) {
                return this.createCommandLink(link.text, link.command, link.params, link.options);
            } else if (link.url) {
                return this.createUrlLink(link.text, link.url, link.options);
            } else if (link.uri) {
                return this.createLink(link.text, link.uri, link.options);
            } else {
                return link.text; // 返回纯文本
            }
        });
    }

    /**
     * 处理链接点击事件
     * @param event 鼠标事件
     * @param uri 链接URI
     */
    handleLinkActivation(event: MouseEvent, uri: string): void {
        if (!this.isEnabled) return;

        // 解析URI协议
        const colonIndex = uri.indexOf(':');
        const protocol = colonIndex > 0 ? uri.substring(0, colonIndex) : 'unknown';

        // 查找对应的处理器
        let handler = this.linkHandlers.get(protocol);
        if (!handler && this.defaultLinkHandler) {
            handler = this.defaultLinkHandler;
        }

        if (handler) {
            handler.activate(event, uri);
        } else {
            // 默认处理逻辑
            this.handleDefaultActivation(event, uri, protocol);
        }
    }

    /**
     * 处理链接悬停事件
     */
    handleLinkHover(event: MouseEvent, uri: string): void {
        if (!this.isEnabled) return;

        const colonIndex = uri.indexOf(':');
        const protocol = colonIndex > 0 ? uri.substring(0, colonIndex) : 'unknown';
        const handler = this.linkHandlers.get(protocol) || this.defaultLinkHandler;

        if (handler?.hover) {
            handler.hover(event, uri);
        }
    }

    /**
     * 处理链接离开事件
     */
    handleLinkLeave(event: MouseEvent, uri: string): void {
        if (!this.isEnabled) return;

        const colonIndex = uri.indexOf(':');
        const protocol = colonIndex > 0 ? uri.substring(0, colonIndex) : 'unknown';
        const handler = this.linkHandlers.get(protocol) || this.defaultLinkHandler;

        if (handler?.leave) {
            handler.leave(event, uri);
        }
    }

    /**
     * 获取 Terminal 配置对象
     */
    getTerminalLinkHandlerConfig() {
        return {
            activate: (event: MouseEvent, uri: string) => this.handleLinkActivation(event, uri),
            hover: (event: MouseEvent, uri: string) => this.handleLinkHover(event, uri),
            leave: (event: MouseEvent, uri: string) => this.handleLinkLeave(event, uri),
            allowNonHttpProtocols: true
        };
    }

    /**
     * 设置默认处理器
     */
    private setupDefaultHandlers(): void {
        // 命令处理器
        this.registerLinkHandler('cmd', {
            activate: async (event, uri) => {
                try {
                    // 解析命令和参数
                    const cmdPart = uri.substring(4); // 移除 'cmd:' 前缀
                    const [command, queryString] = cmdPart.split('?');

                    let params: Record<string, any> = {};
                    if (queryString) {
                        const urlParams = new URLSearchParams(queryString);
                        urlParams.forEach((value, key) => {
                            params[key] = value;
                        });
                    }

                    if (this.commandExecutor) {
                        await this.commandExecutor(command, params, this.terminalInstance);
                    }
                } catch (error) {
                    // 静默处理错误
                }
            }
        });

        // HTTP/HTTPS 处理器
        this.registerLinkHandler('http', {
            activate: (event, uri) => {
                window.open(uri, '_blank');
            }
        });

        this.registerLinkHandler('https', {
            activate: (event, uri) => {
                window.open(uri, '_blank');
            }
        });

        // 文件处理器
        this.registerLinkHandler('file', {
            activate: (event, uri) => {
                // 这里可以实现文件打开逻辑
            }
        });

        // 邮件处理器
        this.registerLinkHandler('mailto', {
            activate: (event, uri) => {
                window.open(uri);
            }
        });
    }

    /**
 * 默认激活处理
 */
    private handleDefaultActivation(event: MouseEvent, uri: string, protocol: string): void {
        // 对于未知协议，尝试直接打开
        if (uri.startsWith('http://') || uri.startsWith('https://')) {
            window.open(uri, '_blank');
        }
    }
}

// 创建全局实例
export const globalOSC8LinkManager = new TerminalOSC8LinkManager();

// ========================= 预设样式 =========================

export const LinkStyles = {
    /** 默认链接样式 - 蓝色 */
    default: {
        color: 'blue' as const
    },

    /** 简洁样式 - 蓝色加粗 */
    clean: {
        color: 'blue' as const,
        bold: true
    },

    /** 成功样式 - 绿色加粗 */
    success: {
        color: 'brightGreen' as const,
        bold: true
    },

    /** 警告样式 - 黄色加粗 */
    warning: {
        color: 'brightYellow' as const,
        bold: true
    },

    /** 错误样式 - 红色加粗 */
    error: {
        color: 'brightRed' as const,
        bold: true
    },

    /** 信息样式 - 青色 */
    info: {
        color: 'cyan' as const
    },

    /** 重要样式 - 洋红色加粗 */
    important: {
        color: 'brightMagenta' as const,
        bold: true
    },

    /** 突出样式 - 反转颜色 */
    highlight: {
        reverse: true,
        bold: true
    },

    /** 禁用样式 - 灰色斜体 */
    disabled: {
        color: 'brightBlack' as const,
        italic: true
    },

    /** 按钮样式 - 白色文本蓝色背景 */
    button: {
        color: 'white' as const,
        backgroundColor: 'blue' as const,
        bold: true
    },

    /** 链接按钮样式 - 彩色按钮 */
    linkButton: {
        color: 'white' as const,
        backgroundColor: 'brightBlue' as const,
        bold: true
    },

    /** 文本样式 - 普通颜色 */
    text: {
        color: 'brightWhite' as const
    },

    /** 链接样式 - 青色加粗 */
    link: {
        color: 'brightCyan' as const,
        bold: true
    }
};

// 便捷函数导出
export const createLink = (text: string, uri: string, options?: OSC8LinkOptions) =>
    globalOSC8LinkManager.createLink(text, uri, options);

export const createCommandLink = (text: string, command: string, params?: Record<string, any>, options?: OSC8LinkOptions) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, options);

export const createUrlLink = (text: string, url: string, options?: OSC8LinkOptions) =>
    globalOSC8LinkManager.createUrlLink(text, url, options);

export const createFileLink = (text: string, filePath: string, options?: OSC8LinkOptions) =>
    globalOSC8LinkManager.createFileLink(text, filePath, options);

export const createEmailLink = (text: string, email: string, options?: OSC8LinkOptions) =>
    globalOSC8LinkManager.createEmailLink(text, email, options);

// ========================= 带样式的便捷函数 =========================

/**
 * 创建带预设样式的命令链接
 */
export const createStyledCommandLink = (text: string, command: string, styleType: keyof typeof LinkStyles, params?: Record<string, any>) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, { style: LinkStyles[styleType] });

/**
 * 创建带预设样式的URL链接
 */
export const createStyledUrlLink = (text: string, url: string, styleType: keyof typeof LinkStyles) =>
    globalOSC8LinkManager.createUrlLink(text, url, { style: LinkStyles[styleType] });

/**
 * 创建按钮样式的命令链接
 */
export const createButtonCommandLink = (text: string, command: string, params?: Record<string, any>) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, { style: LinkStyles.button });

/**
 * 创建成功样式的命令链接
 */
export const createSuccessCommandLink = (text: string, command: string, params?: Record<string, any>) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, { style: LinkStyles.success });

/**
 * 创建警告样式的命令链接
 */
export const createWarningCommandLink = (text: string, command: string, params?: Record<string, any>) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, { style: LinkStyles.warning });

/**
 * 创建错误样式的命令链接
 */
export const createErrorCommandLink = (text: string, command: string, params?: Record<string, any>) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, { style: LinkStyles.error });

/**
 * 创建重要样式的命令链接
 */
export const createImportantCommandLink = (text: string, command: string, params?: Record<string, any>) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, { style: LinkStyles.important });

/**
 * 创建自定义RGB颜色的命令链接
 */
export const createCustomColorCommandLink = (text: string, command: string, rgbColor: string, params?: Record<string, any>) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, {
        style: { customColor: rgbColor, bold: true }
    });

/**
 * 创建彩虹样式命令链接（循环颜色）
 */
export const createRainbowCommandLink = (text: string, command: string, params?: Record<string, any>) => {
    const colors = ['red', 'brightRed', 'yellow', 'brightYellow', 'green', 'brightGreen', 'cyan', 'brightCyan', 'blue', 'brightBlue', 'magenta', 'brightMagenta'] as const;
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    return globalOSC8LinkManager.createCommandLink(text, command, params, {
        style: { color: randomColor, bold: true }
    });
};

/**
 * 创建简洁样式命令链接（蓝色加粗）
 */
export const createCleanCommandLink = (text: string, command: string, params?: Record<string, any>) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, { style: LinkStyles.clean });

/**
 * 创建文本样式命令链接（普通白色）
 */
export const createTextCommandLink = (text: string, command: string, params?: Record<string, any>) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, { style: LinkStyles.text });

/**
 * 创建链接样式命令链接（青色加粗）
 */
export const createLinkStyleCommandLink = (text: string, command: string, params?: Record<string, any>) =>
    globalOSC8LinkManager.createCommandLink(text, command, params, { style: LinkStyles.link }); 