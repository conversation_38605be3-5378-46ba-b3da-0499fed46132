// 浮窗消息通信系统
// 设计原则：解耦、模块化、可扩展、类型安全

export interface BaseMessage {
    id: string;                    // 消息唯一标识
    type: string;                  // 消息类型
    timestamp: number;             // 时间戳
    windowId?: string;             // 目标窗口ID（可选，用于指定通信对象）
    source?: 'window' | 'terminal' | 'system'; // 消息来源
}

// 终端命令消息
export interface TerminalCommandMessage extends BaseMessage {
    type: 'TERMINAL_COMMAND';
    payload: {
        command: string;           // 要执行的命令
        params?: Record<string, any>; // 命令参数
        expectResponse?: boolean;  // 是否期望响应
    };
}

// 终端命令响应消息
export interface TerminalCommandResponseMessage extends BaseMessage {
    type: 'TERMINAL_COMMAND_RESPONSE';
    payload: {
        originalMessageId: string; // 原始命令消息ID
        success: boolean;          // 执行是否成功
        command: string;           // 执行的命令
        result?: any;              // 执行结果
        error?: string;            // 错误信息
    };
}

// 🆕 浮窗操作消息（Terminal → 浮窗）
export interface WindowOperationMessage extends BaseMessage {
    type: 'WINDOW_OPERATION';
    payload: {
        operation: 'query' | 'update' | 'move' | 'resize' | 'close' | 'focus' | 'minimize' | 'maximize' | 'dom_query' | 'dom_update' | 'dom_execute' | 'list_all';
        target?: string;           // 操作目标（CSS选择器、元素ID等）
        data?: any;               // 操作数据
        options?: {               // 操作选项
            selector?: string;    // CSS选择器
            attribute?: string;   // 属性名（如textContent、innerHTML、value等）
            method?: string;      // 执行的方法名
            args?: any[];         // 方法参数
            timeout?: number;     // 操作超时时间
        };
        expectResponse?: boolean; // 是否期望响应
    };
}

// 🆕 浮窗操作响应消息（浮窗 → Terminal）
export interface WindowOperationResponseMessage extends BaseMessage {
    type: 'WINDOW_OPERATION_RESPONSE';
    payload: {
        originalMessageId: string;
        success: boolean;
        operation: string;
        result?: any;
        error?: string;
    };
}

// AI查询消息
export interface AIQueryMessage extends BaseMessage {
    type: 'AI_QUERY';
    payload: {
        query: string;
        context?: string;
        model?: string;
        windowId?: string;         // 目标窗口（可选）
    };
}

// AI响应消息
export interface AIResponseMessage extends BaseMessage {
    type: 'AI_RESPONSE';
    payload: {
        originalMessageId: string;
        response: string;
        suggestions?: string[];    // 建议操作
        actions?: Array<{          // 自动执行的动作
            type: 'terminal_command' | 'window_operation';
            data: any;
        }>;
    };
}

// 窗口状态消息
export interface WindowStateMessage extends BaseMessage {
    type: 'WINDOW_STATE';
    payload: {
        state: 'ready' | 'loading' | 'error' | 'closed' | 'minimized' | 'maximized';
        data?: any;
        position?: { x: number; y: number };
        size?: { width: number; height: number };
    };
}

// 🆕 实时通知消息（Terminal → 浮窗，用于AI等功能）
export interface NotificationMessage extends BaseMessage {
    type: 'NOTIFICATION';
    payload: {
        level: 'info' | 'success' | 'warning' | 'error';
        title: string;
        message: string;
        duration?: number;         // 显示时长（毫秒）
        actions?: Array<{
            label: string;
            command: string;       // 点击时执行的命令
        }>;
    };
}

// 数据交换消息（通用）
export interface DataExchangeMessage extends BaseMessage {
    type: 'DATA_EXCHANGE';
    payload: {
        dataType: string;
        data: any;
        operation?: 'read' | 'write' | 'update' | 'delete';
    };
}

// 🆕 流式数据消息（支持大数据分块传输）
export interface StreamDataMessage extends BaseMessage {
    type: 'STREAM_DATA';
    payload: {
        streamId: string;         // 流标识
        chunkIndex: number;       // 分块索引
        totalChunks: number;      // 总分块数
        isComplete: boolean;      // 是否为最后一块
        data: string;             // 数据内容（Base64编码）
        metadata?: {              // 元数据
            contentType?: string;
            encoding?: string;
            originalSize?: number;
        };
    };
}

// 🆕 AI交互消息（扩展版AI查询）
export interface AIInteractionMessage extends BaseMessage {
    type: 'AI_INTERACTION';
    payload: {
        action: 'query' | 'stream' | 'complete' | 'cancel';
        query?: string;           // AI查询内容
        context?: {               // 上下文信息
            windowId?: string;
            domContext?: Array<{  // DOM上下文
                selector: string;
                content: string;
                type: 'text' | 'html' | 'value';
            }>;
            history?: Array<{     // 对话历史
                role: 'user' | 'assistant';
                content: string;
                timestamp: number;
            }>;
        };
        streamId?: string;        // 流式响应标识
        model?: string;           // AI模型选择
        options?: {
            temperature?: number;
            maxTokens?: number;
            stream?: boolean;     // 是否流式响应
        };
    };
}

// 🆕 AI响应消息（扩展版，支持流式）
export interface AIInteractionResponseMessage extends BaseMessage {
    type: 'AI_INTERACTION_RESPONSE';
    payload: {
        originalMessageId: string;
        action: 'chunk' | 'complete' | 'error';
        content?: string;         // 响应内容
        streamId?: string;        // 流标识
        chunkIndex?: number;      // 流块索引
        isComplete?: boolean;     // 是否完成
        suggestions?: Array<{     // AI建议的操作
            type: 'dom_update' | 'terminal_command' | 'window_operation';
            description: string;
            payload: any;
        }>;
        error?: string;
    };
}

// 🆕 获取所有窗口列表消息（Terminal → 系统）
export interface WindowListQueryMessage extends BaseMessage {
    type: 'WINDOW_LIST_QUERY';
    payload: {
        includeHidden?: boolean;   // 是否包含隐藏窗口
        expectResponse: boolean;   // 期望响应
    };
}

// 🆕 窗口列表响应消息（系统 → Terminal）
export interface WindowListResponseMessage extends BaseMessage {
    type: 'WINDOW_LIST_RESPONSE';
    payload: {
        originalMessageId: string;
        windows: Array<{
            id: string;
            title: string;
            url?: string;
            x: number;
            y: number;
            width: number;
            height: number;
            zIndex: number;
            isVisible: boolean;
            isClosing: boolean;
            type?: string;
        }>;
        count: number;
    };
}

// 🆕 CodeMirror 操作消息（Terminal → Editor 浮窗）
export interface CodeMirrorOperationMessage extends BaseMessage {
    type: 'CODEMIRROR_OPERATION';
    payload: {
        operation: 'read' | 'write' | 'listen' | 'decorate' | 'selection' | 'cursor' | 'syntax' | 'history';
        target?: 'doc' | 'selection' | 'viewport' | 'state' | 'editor';
        data?: {
            // 读取操作
            range?: { from?: number; to?: number; };  // 读取范围
            lineNumber?: number;                       // 按行读取

            // 写入操作  
            changes?: Array<{                          // 文档更改
                from: number;
                to?: number;
                insert?: string;
            }>;
            selection?: {                              // 选区设置
                anchor: number;
                head?: number;
            } | Array<{ anchor: number; head?: number; }>;

            // 监听操作
            listenType?: 'doc' | 'selection' | 'viewport' | 'all';
            callback?: string;                         // 回调函数标识

            // 装饰操作
            decoration?: {
                type: 'mark' | 'widget' | 'line';
                from: number;
                to?: number;
                class?: string;
                attributes?: Record<string, string>;
                content?: string;                      // widget 内容
            };

            // 语法查询
            syntaxQuery?: {
                type: 'tree' | 'node' | 'cursor';
                position?: number;
                nodeType?: string;
            };

            // 历史操作
            historyAction?: 'undo' | 'redo' | 'clear';
        };
        options?: {
            timeout?: number;
            userEvent?: string;                        // 用户事件标识
            scrollIntoView?: boolean;                  // 是否滚动到视图
            preventEmptyChanges?: boolean;             // 是否阻止空更改
        };
        expectResponse?: boolean;
    };
}

// 🆕 CodeMirror 操作响应消息（Editor 浮窗 → Terminal）
export interface CodeMirrorOperationResponseMessage extends BaseMessage {
    type: 'CODEMIRROR_OPERATION_RESPONSE';
    payload: {
        originalMessageId: string;
        success: boolean;
        operation: string;
        result?: {
            // 读取结果
            content?: string;                          // 文档内容
            selection?: {                              // 选区信息
                main: { anchor: number; head: number; };
                ranges?: Array<{ anchor: number; head: number; }>;
            };
            line?: {                                   // 行信息
                number: number;
                from: number;
                to: number;
                text: string;
            };

            // 状态信息
            state?: {
                docLength: number;
                lineCount: number;
                readOnly: boolean;
            };

            // 语法信息
            syntax?: {
                tree?: any;                            // 语法树
                nodes?: Array<{
                    type: string;
                    from: number;
                    to: number;
                    name?: string;
                }>;
            };

            // 视口信息
            viewport?: {
                from: number;
                to: number;
            };

            // 写入确认
            changes?: Array<{
                from: number;
                to: number;
                inserted: string;
            }>;
        };
        error?: string;
        warning?: string;                              // 警告信息
    };
}

// 🆕 CodeMirror 实时更新消息（Editor 浮窗 → Terminal，用于监听）
export interface CodeMirrorUpdateMessage extends BaseMessage {
    type: 'CODEMIRROR_UPDATE';
    payload: {
        updateType: 'doc' | 'selection' | 'viewport' | 'focus';
        callbackId?: string;                           // 对应的监听器ID
        data: {
            // 文档更改
            docChanged?: boolean;
            changes?: Array<{
                from: number;
                to: number;
                inserted: string;
                deleted: string;
            }>;

            // 选区更改
            selectionChanged?: boolean;
            selection?: {
                main: { anchor: number; head: number; };
                ranges?: Array<{ anchor: number; head: number; }>;
            };

            // 视口更改
            viewportChanged?: boolean;
            viewport?: {
                from: number;
                to: number;
            };

            // 焦点状态
            focused?: boolean;

            // 文档状态
            state?: {
                docLength: number;
                lineCount: number;
                readOnly: boolean;
            };
        };
        timestamp: number;
    };
}

// 🆕 CodeMirror AI 集成消息（支持 AI 操作的特殊消息）
export interface CodeMirrorAIMessage extends BaseMessage {
    type: 'CODEMIRROR_AI';
    payload: {
        action: 'analyze' | 'suggest' | 'refactor' | 'complete' | 'explain';
        context: {
            selection?: { from: number; to: number; };  // 当前选区
            cursor?: number;                            // 光标位置
            content?: string;                           // 相关内容
            language?: string;                          // 语言类型
            filename?: string;                          // 文件名
        };
        prompt?: string;                                // AI 提示词
        options?: {
            model?: string;
            temperature?: number;
            maxTokens?: number;
            includeContext?: boolean;                   // 是否包含上下文
            contextRange?: number;                      // 上下文范围（字符数）
        };
        expectResponse?: boolean;
    };
}

// 🆕 CodeMirror AI 响应消息
export interface CodeMirrorAIResponseMessage extends BaseMessage {
    type: 'CODEMIRROR_AI_RESPONSE';
    payload: {
        originalMessageId: string;
        action: string;
        success: boolean;
        result?: {
            analysis?: string;                          // 代码分析结果
            suggestions?: Array<{                       // 建议列表
                description: string;
                changes: Array<{
                    from: number;
                    to?: number;
                    insert: string;
                }>;
                confidence?: number;                    // 置信度
            }>;
            explanation?: string;                       // 代码解释
            completion?: string;                        // 代码补全
            refactored?: string;                        // 重构后的代码
        };
        metadata?: {
            model?: string;
            tokensUsed?: number;
            processingTime?: number;
        };
        error?: string;
    };
}

// 所有消息类型的联合类型
export type FloatingWindowMessage =
    | TerminalCommandMessage
    | TerminalCommandResponseMessage
    | WindowOperationMessage
    | WindowOperationResponseMessage
    | AIQueryMessage
    | AIResponseMessage
    | WindowStateMessage
    | NotificationMessage
    | DataExchangeMessage
    | StreamDataMessage
    | AIInteractionMessage
    | AIInteractionResponseMessage
    | WindowListQueryMessage
    | WindowListResponseMessage
    | CodeMirrorOperationMessage
    | CodeMirrorOperationResponseMessage
    | CodeMirrorUpdateMessage
    | CodeMirrorAIMessage
    | CodeMirrorAIResponseMessage;

// 消息处理器类型
export type MessageHandler<T extends FloatingWindowMessage = FloatingWindowMessage> = (message: T) => void | Promise<void>;

export class FloatingWindowMessageManager {
    private handlers: Map<string, Set<MessageHandler>> = new Map();
    private pendingRequests: Map<string, {
        resolve: (value: any) => void;
        reject: (error: any) => void;
        timeout: NodeJS.Timeout;
    }> = new Map();

    private messageQueue: FloatingWindowMessage[] = [];
    private processing = false;

    constructor() {
        this.initializePostMessageListener();
        this.initializeResponseHandlers();
    }

    private initializePostMessageListener() {
        if (typeof window !== 'undefined') {
            window.addEventListener('message', (event) => {
                this.handlePostMessage(event);
            });
        }
    }

    // 🆕 初始化响应处理器 - 统一处理所有响应消息
    private initializeResponseHandlers() {
        // 处理 CodeMirror 操作响应
        this.on('CODEMIRROR_OPERATION_RESPONSE', (message: CodeMirrorOperationResponseMessage) => {
            // console.log('[MessageManager] 收到 CodeMirror 响应:', message.payload);
            this.resolveResponse(
                message.payload.originalMessageId,
                message.payload.result,
                message.payload.error
            );
        });

        // 处理 CodeMirror AI 响应
        this.on('CODEMIRROR_AI_RESPONSE', (message: CodeMirrorAIResponseMessage) => {
            // console.log('[MessageManager] 收到 CodeMirror AI 响应:', message.payload);
            this.resolveResponse(
                message.payload.originalMessageId,
                message.payload.result,
                message.payload.error
            );
        });

        // 处理窗口操作响应
        this.on('WINDOW_OPERATION_RESPONSE', (message: WindowOperationResponseMessage) => {
            // console.log('[MessageManager] 收到窗口操作响应:', message.payload);
            this.resolveResponse(
                message.payload.originalMessageId,
                message.payload.result,
                message.payload.error
            );
        });

        // 处理终端命令响应
        this.on('TERMINAL_COMMAND_RESPONSE', (message: TerminalCommandResponseMessage) => {
            // console.log('[MessageManager] 收到终端命令响应:', message.payload);
            this.resolveResponse(
                message.payload.originalMessageId,
                message.payload.result,
                message.payload.error
            );
        });

        // 处理窗口列表响应
        this.on('WINDOW_LIST_RESPONSE', (message: WindowListResponseMessage) => {
            // console.log('[MessageManager] 收到窗口列表响应:', message.payload);
            this.resolveResponse(
                message.payload.originalMessageId,
                { windows: message.payload.windows, count: message.payload.count },
                undefined
            );
        });

        // 处理 AI 交互响应
        this.on('AI_INTERACTION_RESPONSE', (message: AIInteractionResponseMessage) => {
            // console.log('[MessageManager] 收到 AI 交互响应:', message.payload);
            this.resolveResponse(
                message.payload.originalMessageId,
                message.payload,
                message.payload.error
            );
        });
    }

    private async handlePostMessage(event: MessageEvent) {
        try {
            const message = event.data as FloatingWindowMessage;
            if (!this.isValidMessage(message)) return;

            await this.dispatchMessage(message);
        } catch (error) {
            // console.error('[MessageManager] Error handling PostMessage:', error);
        }
    }

    private isValidMessage(data: any): data is FloatingWindowMessage {
        return data &&
            typeof data.id === 'string' &&
            typeof data.type === 'string' &&
            typeof data.timestamp === 'number';
    }

    // 注册消息处理器
    public on<T extends FloatingWindowMessage>(
        messageType: T['type'],
        handler: MessageHandler<T>
    ): () => void {
        if (!this.handlers.has(messageType)) {
            this.handlers.set(messageType, new Set());
        }

        this.handlers.get(messageType)!.add(handler as MessageHandler);

        // 返回取消注册函数
        return () => {
            const handlers = this.handlers.get(messageType);
            if (handlers) {
                handlers.delete(handler as MessageHandler);
                if (handlers.size === 0) {
                    this.handlers.delete(messageType);
                }
            }
        };
    }

    // 发送消息
    public async dispatchMessage(message: FloatingWindowMessage): Promise<void> {
        this.messageQueue.push(message);
        if (!this.processing) {
            await this.processMessageQueue();
        }
    }

    private async processMessageQueue(): Promise<void> {
        this.processing = true;

        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift()!;
            await this.processMessage(message);
        }

        this.processing = false;
    }

    private async processMessage(message: FloatingWindowMessage): Promise<void> {
        const handlers = this.handlers.get(message.type);
        if (!handlers || handlers.size === 0) return;

        const promises = Array.from(handlers).map(handler => {
            try {
                return Promise.resolve(handler(message));
            } catch (error) {
                // console.error(`[MessageManager] Handler error for ${message.type}:`, error);
                return Promise.resolve();
            }
        });

        await Promise.allSettled(promises);
    }

    private generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // ===== 便捷发送方法 =====

    // 发送命令到终端
    public async sendTerminalCommand(command: string, params?: Record<string, any>, windowId?: string): Promise<string> {
        const messageId = this.generateMessageId();
        const message: TerminalCommandMessage = {
            id: messageId,
            type: 'TERMINAL_COMMAND',
            timestamp: Date.now(),
            windowId,
            source: 'window',
            payload: {
                command,
                params,
                expectResponse: true
            }
        };

        await this.dispatchMessage(message);
        return messageId;
    }

    // 发送命令响应
    public async sendTerminalResponse(originalMessageId: string, success: boolean, command: string, result?: any, error?: string): Promise<void> {
        const message: TerminalCommandResponseMessage = {
            id: this.generateMessageId(),
            type: 'TERMINAL_COMMAND_RESPONSE',
            timestamp: Date.now(),
            source: 'terminal',
            payload: {
                originalMessageId,
                success,
                command,
                result,
                error
            }
        };

        await this.dispatchMessage(message);
    }

    // 🆕 发送浮窗操作命令（Terminal → 浮窗）
    public async sendWindowOperation(
        windowId: string,
        operation: 'query' | 'update' | 'move' | 'resize' | 'close' | 'focus' | 'minimize' | 'maximize',
        data?: any,
        target?: string,
        expectResponse: boolean = true
    ): Promise<string> {
        const messageId = this.generateMessageId();
        const message: WindowOperationMessage = {
            id: messageId,
            type: 'WINDOW_OPERATION',
            timestamp: Date.now(),
            windowId,
            source: 'terminal',
            payload: {
                operation,
                target,
                data,
                expectResponse
            }
        };

        await this.dispatchMessage(message);
        return messageId;
    }

    // 🆕 发送浮窗操作响应（浮窗 → Terminal）
    public async sendWindowOperationResponse(originalMessageId: string, success: boolean, operation: string, result?: any, error?: string): Promise<void> {
        const message: WindowOperationResponseMessage = {
            id: this.generateMessageId(),
            type: 'WINDOW_OPERATION_RESPONSE',
            timestamp: Date.now(),
            source: 'window',
            payload: {
                originalMessageId,
                success,
                operation,
                result,
                error
            }
        };

        await this.dispatchMessage(message);
    }

    // 🆕 发送通知消息（Terminal → 浮窗）
    public async sendNotification(
        windowId: string,
        level: 'info' | 'success' | 'warning' | 'error',
        title: string,
        message: string,
        options?: {
            duration?: number;
            actions?: Array<{ label: string; command: string }>;
        }
    ): Promise<string> {
        const messageId = this.generateMessageId();
        const notification: NotificationMessage = {
            id: messageId,
            type: 'NOTIFICATION',
            timestamp: Date.now(),
            windowId,
            source: 'terminal',
            payload: {
                level,
                title,
                message,
                duration: options?.duration,
                actions: options?.actions
            }
        };

        await this.dispatchMessage(notification);
        return messageId;
    }

    // 🆕 Promise风格的请求-响应（用于AI等需要等待结果的场景）
    public async requestWithResponse<T = any>(
        message: Omit<FloatingWindowMessage, 'id' | 'timestamp'>,
        timeout: number = 10000
    ): Promise<T> {
        const messageId = this.generateMessageId();
        const fullMessage = {
            ...message,
            id: messageId,
            timestamp: Date.now()
        } as FloatingWindowMessage;

        // console.log('[MessageManager] 发送带响应的请求:', { messageId, type: fullMessage.type, windowId: fullMessage.windowId });

        return new Promise<T>((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                // console.log('[MessageManager] 请求超时:', messageId);
                this.pendingRequests.delete(messageId);
                reject(new Error(`Request timeout (${timeout}ms)`));
            }, timeout);

            this.pendingRequests.set(messageId, {
                resolve,
                reject,
                timeout: timeoutId
            });

            // console.log('[MessageManager] 存储待处理请求:', messageId, 'pending count:', this.pendingRequests.size);
            this.dispatchMessage(fullMessage);
        });
    }

    // 🆕 处理响应消息，解决对应的Promise
    public resolveResponse(originalMessageId: string, result: any, error?: string): void {
        // console.log('[MessageManager] 解析响应:', { originalMessageId, hasResult: !!result, error, pendingCount: this.pendingRequests.size });

        const pending = this.pendingRequests.get(originalMessageId);
        if (pending) {
            // console.log('[MessageManager] 找到待处理请求，解析 Promise:', originalMessageId);
            clearTimeout(pending.timeout);
            this.pendingRequests.delete(originalMessageId);

            if (error) {
                // console.log('[MessageManager] 解析失败:', error);
                pending.reject(new Error(error));
            } else {
                // console.log('[MessageManager] 解析成功:', result);
                pending.resolve(result);
            }
        } else {
            // 可能是重复响应或延迟响应，记录调试信息但不作为警告
            // console.debug('[MessageManager] 响应已处理或请求已超时:', originalMessageId, 'current pending:', Array.from(this.pendingRequests.keys()));
        }
    }

    // 🆕 发送DOM查询请求（Terminal → 浮窗）
    public async sendDOMQuery(
        windowId: string,
        selector: string,
        attribute: string = 'textContent',
        timeout: number = 5000
    ): Promise<any> {
        return await this.requestWithResponse({
            type: 'WINDOW_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'dom_query',
                options: {
                    selector,
                    attribute,
                    timeout
                },
                expectResponse: true
            }
        }, timeout);
    }

    // 🆕 发送DOM更新请求（Terminal → 浮窗）
    public async sendDOMUpdate(
        windowId: string,
        selector: string,
        content: string,
        attribute: string = 'textContent',
        timeout: number = 5000
    ): Promise<any> {
        return await this.requestWithResponse({
            type: 'WINDOW_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'dom_update',
                data: content,
                options: {
                    selector,
                    attribute,
                    timeout
                },
                expectResponse: true
            }
        }, timeout);
    }

    // 🆕 发送DOM方法执行请求（Terminal → 浮窗）
    public async sendDOMExecute(
        windowId: string,
        selector: string,
        method: string,
        args: any[] = [],
        timeout: number = 5000
    ): Promise<any> {
        return await this.requestWithResponse({
            type: 'WINDOW_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'dom_execute',
                options: {
                    selector,
                    method,
                    args,
                    timeout
                },
                expectResponse: true
            }
        }, timeout);
    }

    // 🆕 发送流式数据（支持大数据分块传输）
    public async sendStreamData(
        windowId: string,
        data: string,
        chunkSize: number = 1024 * 64 // 64KB per chunk
    ): Promise<string> {
        const streamId = this.generateMessageId();
        const chunks = this.chunkData(data, chunkSize);

        for (let i = 0; i < chunks.length; i++) {
            const message: StreamDataMessage = {
                id: this.generateMessageId(),
                type: 'STREAM_DATA',
                timestamp: Date.now(),
                windowId,
                source: 'terminal',
                payload: {
                    streamId,
                    chunkIndex: i,
                    totalChunks: chunks.length,
                    isComplete: i === chunks.length - 1,
                    data: btoa(chunks[i]), // Base64编码
                    metadata: {
                        contentType: 'text/plain',
                        encoding: 'base64',
                        originalSize: data.length
                    }
                }
            };

            await this.dispatchMessage(message);

            // 添加小延迟避免消息过于密集
            if (i < chunks.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 10));
            }
        }

        return streamId;
    }

    // 🆕 发送AI交互请求（支持流式响应）
    public async sendAIInteraction(
        query: string,
        context?: {
            windowId?: string;
            domContext?: Array<{ selector: string; content: string; type: 'text' | 'html' | 'value' }>;
            history?: Array<{ role: 'user' | 'assistant'; content: string; timestamp: number }>;
        },
        options?: {
            model?: string;
            temperature?: number;
            maxTokens?: number;
            stream?: boolean;
        }
    ): Promise<string> {
        const messageId = this.generateMessageId();
        const message: AIInteractionMessage = {
            id: messageId,
            type: 'AI_INTERACTION',
            timestamp: Date.now(),
            windowId: context?.windowId,
            source: 'terminal',
            payload: {
                action: 'query',
                query,
                context,
                streamId: options?.stream ? this.generateMessageId() : undefined,
                options
            }
        };

        await this.dispatchMessage(message);
        return messageId;
    }

    // 🆕 数据分块工具
    private chunkData(data: string, chunkSize: number): string[] {
        const chunks: string[] = [];
        for (let i = 0; i < data.length; i += chunkSize) {
            chunks.push(data.slice(i, i + chunkSize));
        }
        return chunks;
    }

    // 🆕 查询所有窗口列表（Terminal → 系统）
    public async queryAllWindows(includeHidden: boolean = false, timeout: number = 5000): Promise<any[]> {
        try {
            const response = await this.requestWithResponse({
                type: 'WINDOW_LIST_QUERY',
                source: 'terminal',
                payload: {
                    includeHidden,
                    expectResponse: true
                }
            }, timeout);

            return response.windows || [];
        } catch (error) {
            // console.warn('[MessageManager] Failed to query windows:', error);
            return [];
        }
    }

    // 🆕 发送窗口列表响应（系统 → Terminal）
    public async sendWindowListResponse(originalMessageId: string, windows: any[]): Promise<void> {
        const message: WindowListResponseMessage = {
            id: this.generateMessageId(),
            type: 'WINDOW_LIST_RESPONSE',
            timestamp: Date.now(),
            source: 'system',
            payload: {
                originalMessageId,
                windows: windows.map(window => ({
                    id: window.id,
                    title: window.title,
                    url: window.url,
                    x: window.x,
                    y: window.y,
                    width: window.width,
                    height: window.height,
                    zIndex: window.zIndex,
                    isVisible: window.isVisible ?? true,
                    isClosing: window.isClosing ?? false,
                    type: window.type
                })),
                count: windows.length
            }
        };

        await this.dispatchMessage(message);
    }

    // 🆕 获取所有活动窗口信息（用于AI上下文）
    public async getAllWindowStates(): Promise<any[]> {
        return await this.queryAllWindows(false);
    }

    // ===== CodeMirror 相关便捷方法 =====

    // 🆕 读取 CodeMirror 文档内容
    public async readCodeMirrorContent(
        windowId: string,
        range?: { from?: number; to?: number; },
        timeout: number = 5000
    ): Promise<string> {
        // console.log('[MessageManager] 请求读取 CodeMirror 内容:', { windowId, range });
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'read',
                target: 'doc',
                data: { range },
                expectResponse: true
            }
        }, timeout);

        // console.log('[MessageManager] 收到 CodeMirror 内容响应:', response);
        return response.result?.content || response.content || '';
    }

    // 🆕 按行读取 CodeMirror 内容
    public async readCodeMirrorLine(
        windowId: string,
        lineNumber: number,
        timeout: number = 5000
    ): Promise<{ number: number; from: number; to: number; text: string; }> {
        // console.log('[MessageManager] 请求读取 CodeMirror 行内容:', { windowId, lineNumber });
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'read',
                target: 'doc',
                data: { lineNumber },
                expectResponse: true
            }
        }, timeout);

        // console.log('[MessageManager] 收到 CodeMirror 行内容响应:', response);
        // 优先从直接的line字段获取，其次从result.line，最后使用默认值
        const lineData = response.line || response.result?.line || { number: lineNumber, from: 0, to: 0, text: '' };
        // console.log('[MessageManager] 解析的行数据:', lineData);
        return lineData;
    }

    // 🆕 获取 CodeMirror 选区信息
    public async getCodeMirrorSelection(
        windowId: string,
        timeout: number = 5000
    ): Promise<{ main: { anchor: number; head: number; }; ranges?: Array<{ anchor: number; head: number; }>; }> {
        // console.log('[MessageManager] 请求获取选区信息:', { windowId });
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'read',
                target: 'selection',
                expectResponse: true
            }
        }, timeout);

        // console.log('[MessageManager] 收到选区响应:', response);

        // 🔧 修复数据解析：尝试多种可能的数据结构
        const selectionData = response.selection || response.result?.selection || response.result;
        // console.log('[MessageManager] 解析选区数据:', selectionData);

        if (selectionData?.main) {
            return selectionData;
        }

        // 如果没有找到有效数据，返回默认值
        // console.warn('[MessageManager] 没有找到有效的选区数据，使用默认值');
        return { main: { anchor: 0, head: 0 } };
    }

    // 🆕 写入 CodeMirror 内容
    public async writeCodeMirrorContent(
        windowId: string,
        changes: Array<{ from: number; to?: number; insert?: string; }>,
        options?: {
            userEvent?: string;
            scrollIntoView?: boolean;
        },
        timeout: number = 5000
    ): Promise<Array<{ from: number; to: number; inserted: string; }>> {
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'write',
                target: 'doc',
                data: { changes },
                options,
                expectResponse: true
            }
        }, timeout);

        return response.result?.changes || [];
    }

    // 🆕 设置 CodeMirror 选区
    public async setCodeMirrorSelection(
        windowId: string,
        selection: { anchor: number; head?: number; } | Array<{ anchor: number; head?: number; }>,
        options?: {
            scrollIntoView?: boolean;
            userEvent?: string;
        },
        timeout: number = 5000
    ): Promise<boolean> {
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'write',
                target: 'selection',
                data: { selection },
                options,
                expectResponse: true
            }
        }, timeout);

        return response.success;
    }

    // 🆕 监听 CodeMirror 更新
    public async listenCodeMirrorUpdates(
        windowId: string,
        listenType: 'doc' | 'selection' | 'viewport' | 'all' = 'all',
        callbackId?: string,
        timeout: number = 5000
    ): Promise<string> {
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'listen',
                data: {
                    listenType,
                    callback: callbackId || this.generateMessageId()
                },
                expectResponse: true
            }
        }, timeout);

        return response.result?.callbackId || callbackId || '';
    }

    // 🆕 添加 CodeMirror 装饰
    public async addCodeMirrorDecoration(
        windowId: string,
        decoration: {
            type: 'mark' | 'widget' | 'line';
            from: number;
            to?: number;
            class?: string;
            attributes?: Record<string, string>;
            content?: string;
        },
        timeout: number = 5000
    ): Promise<boolean> {
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'decorate',
                data: { decoration },
                expectResponse: true
            }
        }, timeout);

        return response.success;
    }

    // 🆕 查询 CodeMirror 语法信息
    public async queryCodeMirrorSyntax(
        windowId: string,
        syntaxQuery: {
            type: 'tree' | 'node' | 'cursor';
            position?: number;
            nodeType?: string;
        },
        timeout: number = 5000
    ): Promise<any> {
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'syntax',
                data: { syntaxQuery },
                expectResponse: true
            }
        }, timeout);

        return response.result?.syntax || null;
    }

    // 🆕 执行 CodeMirror 历史操作
    public async executeCodeMirrorHistory(
        windowId: string,
        action: 'undo' | 'redo' | 'clear',
        timeout: number = 5000
    ): Promise<boolean> {
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'history',
                data: { historyAction: action },
                expectResponse: true
            }
        }, timeout);

        return response.success;
    }

    // 🆕 获取 CodeMirror 编辑器状态
    public async getCodeMirrorState(
        windowId: string,
        timeout: number = 5000
    ): Promise<{ docLength: number; lineCount: number; readOnly: boolean; }> {
        // console.log('[MessageManager] 请求获取 CodeMirror 状态:', { windowId });
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_OPERATION',
            windowId,
            source: 'terminal',
            payload: {
                operation: 'read',
                target: 'state',
                expectResponse: true
            }
        }, timeout);

        // console.log('[MessageManager] 收到 CodeMirror 状态响应:', response);
        return response.result?.state || response.state || { docLength: 0, lineCount: 0, readOnly: false };
    }

    // 🆕 发送 CodeMirror AI 请求
    public async sendCodeMirrorAI(
        windowId: string,
        action: 'analyze' | 'suggest' | 'refactor' | 'complete' | 'explain',
        context: {
            selection?: { from: number; to: number; };
            cursor?: number;
            content?: string;
            language?: string;
            filename?: string;
        },
        prompt?: string,
        options?: {
            model?: string;
            temperature?: number;
            maxTokens?: number;
            includeContext?: boolean;
            contextRange?: number;
        },
        timeout: number = 10000
    ): Promise<any> {
        const response = await this.requestWithResponse({
            type: 'CODEMIRROR_AI',
            windowId,
            source: 'terminal',
            payload: {
                action,
                context,
                prompt,
                options,
                expectResponse: true
            }
        }, timeout);

        return response.result || null;
    }

    // 🆕 发送 CodeMirror 操作响应
    public async sendCodeMirrorResponse(
        originalMessageId: string,
        success: boolean,
        operation: string,
        result?: any,
        error?: string,
        warning?: string
    ): Promise<void> {
        const message: CodeMirrorOperationResponseMessage = {
            id: this.generateMessageId(),
            type: 'CODEMIRROR_OPERATION_RESPONSE',
            timestamp: Date.now(),
            source: 'window',
            payload: {
                originalMessageId,
                success,
                operation,
                result,
                error,
                warning
            }
        };

        await this.dispatchMessage(message);
    }

    // 🆕 发送 CodeMirror 更新通知
    public async sendCodeMirrorUpdate(
        windowId: string,
        updateType: 'doc' | 'selection' | 'viewport' | 'focus',
        data: any,
        callbackId?: string
    ): Promise<void> {
        const message: CodeMirrorUpdateMessage = {
            id: this.generateMessageId(),
            type: 'CODEMIRROR_UPDATE',
            timestamp: Date.now(),
            windowId,
            source: 'window',
            payload: {
                updateType,
                callbackId,
                data,
                timestamp: Date.now()
            }
        };

        await this.dispatchMessage(message);
    }

    // 🆕 发送 CodeMirror AI 响应
    public async sendCodeMirrorAIResponse(
        originalMessageId: string,
        action: string,
        success: boolean,
        result?: any,
        metadata?: {
            model?: string;
            tokensUsed?: number;
            processingTime?: number;
        },
        error?: string
    ): Promise<void> {
        const message: CodeMirrorAIResponseMessage = {
            id: this.generateMessageId(),
            type: 'CODEMIRROR_AI_RESPONSE',
            timestamp: Date.now(),
            source: 'window',
            payload: {
                originalMessageId,
                action,
                success,
                result,
                metadata,
                error
            }
        };

        await this.dispatchMessage(message);
    }
}

// 全局消息管理器实例
export const globalMessageManager = new FloatingWindowMessageManager(); 