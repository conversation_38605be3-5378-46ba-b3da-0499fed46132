/**
 * 前端Function Calling管理器 (重构版)
 * 基于最佳实践：模块化 + 类型安全 + 错误处理 + 资源管理
 */

import { commandExecutor } from '@/lib/commands';
import { getAIConfig, getSystemMessage } from './ai-config';
import { StreamProcessor, CancellationToken, AIStreamError } from './stream-processor';

// 简化的类型定义（避免循环依赖）
interface Message {
    role: 'user' | 'assistant' | 'system' | 'tool';
    content?: string;
    tool_calls?: ToolCall[];
    tool_call_id?: string;
}

interface ToolCall {
    id: string;
    type: 'function';
    function: {
        name: string;
        arguments: string;
    };
}

interface StreamCallbacks {
    onContent?: (content: string, isComplete: boolean) => void;
    onToolCall?: (toolCall: ToolCall, isComplete: boolean) => void;
    onStatus?: (status: string) => void;
    onError?: (error: string) => void;
    onComplete?: (finalMessage: string) => void;
}

interface FunctionExecutionResult {
    success: boolean;
    command: string;
    result?: any;
    message?: string;
    error?: string;
}

export class FrontendFunctionCallingManager {
    private conversationHistory: Message[] = [];
    private readonly config = getAIConfig();
    private readonly streamProcessor = new StreamProcessor();
    private currentCancellationToken?: CancellationToken;

    constructor() {
        this.initializeConversation();
    }

    /**
     * 初始化对话
     */
    private initializeConversation(): void {
        this.conversationHistory.push({
            role: 'system',
            content: getSystemMessage()
        });
    }

    /**
     * 🌟 核心方法：处理用户消息并管理完整的Function Calling流程
     */
    async processUserMessage(
        userMessage: string,
        callbacks: StreamCallbacks
    ): Promise<void> {
        // 验证消息
        const validation = this.validateMessage(userMessage);
        if (!validation.isValid) {
            callbacks.onError?.(validation.errors.join(', '));
            return;
        }

        // 创建取消令牌
        this.currentCancellationToken = new CancellationToken();

        try {
            // 添加用户消息到历史
            this.addUserMessage(userMessage);

            let iterationCount = 0;
            const maxIterations = this.config.functionCalling.maxIterations;

            while (iterationCount < maxIterations) {
                iterationCount++;
                callbacks.onStatus?.(`${this.config.ui.statusMessages.thinking} (${iterationCount} round)`);

                // 调用AI API
                const result = await this.processAIResponse(callbacks);

                if (!result.hasToolCalls) {
                    // 没有function call，对话结束
                    callbacks.onStatus?.(this.config.ui.statusMessages.completed);
                    if (result.content) {
                        callbacks.onComplete?.(result.content);
                    }
                    return;
                }

                // 有function call，继续下一轮
                callbacks.onStatus?.(`${this.config.ui.statusMessages.executing} continue AI thinking...`);
            }

            // 达到最大迭代次数
            const warning = `⚠️ reach the maximum processing rounds (${maxIterations}), conversation end`;
            callbacks.onError?.(warning);

        } catch (error) {
            this.handleError(error, callbacks);
        } finally {
            this.currentCancellationToken = undefined;
        }
    }

    /**
     * 🌊 处理AI响应 - 使用专门的流式处理器
     */
    private async processAIResponse(callbacks: StreamCallbacks): Promise<{
        hasToolCalls: boolean;
        content: string;
        toolCalls: ToolCall[];
    }> {
        const response = await this.makeAPIRequest();

        const result = await this.streamProcessor.processStream(
            response,
            callbacks,
            this.currentCancellationToken
        );

        // 处理响应结果
        if (result.hasToolCalls) {
            // 有Function Call，添加到历史并执行
            const assistantMessage: Message = {
                role: 'assistant',
                content: result.content || undefined,
                tool_calls: result.toolCalls
            };

            this.conversationHistory.push(assistantMessage);
            await this.executePendingToolCalls(result.toolCalls, callbacks);
        } else {
            // 没有Function Call，添加到历史
            if (result.content) {
                this.conversationHistory.push({
                    role: 'assistant',
                    content: result.content
                });
            }
        }

        return result;
    }

    /**
     * 发起API请求
     */
    private async makeAPIRequest(): Promise<Response> {
        const response = await fetch(this.config.apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                messages: this.conversationHistory,
                stream: true,
            }),
        });

        if (!response.ok) {
            throw new AIStreamError(
                `API request failed: ${response.status}`,
                'API_ERROR',
                { status: response.status, statusText: response.statusText }
            );
        }

        return response;
    }

    /**
     * ⚙️ 执行待处理的Tool Calls
     */
    private async executePendingToolCalls(
        toolCalls: ToolCall[],
        callbacks: StreamCallbacks
    ): Promise<void> {
        callbacks.onStatus?.(`🛠️ execute ${toolCalls.length} functions...`);

        for (let i = 0; i < toolCalls.length; i++) {
            const toolCall = toolCalls[i];

            try {
                this.currentCancellationToken?.throwIfCanceled();

                callbacks.onStatus?.(`⚙️ execute: ${toolCall.function.name} (${i + 1}/${toolCalls.length})`);

                // 标记Tool Call完成
                callbacks.onToolCall?.(toolCall, true);

                // 解析参数
                const args = this.parseToolCallArguments(toolCall.function.arguments);

                // 在前端执行函数
                const result = await this.executeFunctionCall(toolCall.function.name, args);

                // 添加Tool结果到历史
                this.addToolResult(toolCall.id, result);

                callbacks.onStatus?.(`✅ complete: ${toolCall.function.name}`);

            } catch (error) {
                const errorMsg = this.handleFunctionCallError(error, toolCall);

                // 添加错误结果到历史
                this.addToolResult(toolCall.id, {
                    success: false,
                    command: toolCall.function.name,
                    error: errorMsg
                });

                callbacks.onStatus?.(`❌ failed: ${toolCall.function.name} - ${errorMsg}`);
            }
        }
    }

    /**
     * 解析Tool Call参数
     */
    private parseToolCallArguments(argumentsStr: string): Record<string, any> {
        try {
            return JSON.parse(argumentsStr);
        } catch (error) {
            throw new AIStreamError(
                'Function parameter parsing failed: not a valid JSON format',
                'INVALID_ARGUMENTS',
                { arguments: argumentsStr, error }
            );
        }
    }

    /**
     * 添加Tool结果到历史
     */
    private addToolResult(toolCallId: string, result: FunctionExecutionResult): void {
        this.conversationHistory.push({
            role: 'tool',
            tool_call_id: toolCallId,
            content: JSON.stringify(result)
        });
    }

    /**
     * 处理函数调用错误
     */
    private handleFunctionCallError(error: any, toolCall: ToolCall): string {
        if (error instanceof AIStreamError && error.code === 'CANCELED') {
            return 'Operation canceled';
        }

        const errorMsg = error instanceof Error ? error.message : 'Function execution failed';
        console.error(`Function call error [${toolCall.function.name}]:`, error);
        return errorMsg;
    }

    /**
     * 🔧 在前端执行Function Call
     */
    private async executeFunctionCall(functionName: string, args: Record<string, any>): Promise<FunctionExecutionResult> {
        const context = {
            source: 'ai' as const,
            requestId: `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };

        try {
            const result = await commandExecutor.execute(functionName, args, context);

            if (result.success) {
                return {
                    success: true,
                    command: functionName,
                    result: result.data || result.message,
                    message: result.message
                };
            } else {
                return {
                    success: false,
                    command: functionName,
                    error: result.error || 'Function execution failed'
                };
            }
        } catch (error) {
            return {
                success: false,
                command: functionName,
                error: error instanceof Error ? error.message : 'Function execution exception'
            };
        }
    }

    /**
     * 添加用户消息到历史
     */
    private addUserMessage(message: string): void {
        this.conversationHistory.push({
            role: 'user',
            content: message
        });
    }

    /**
     * 验证用户消息
     */
    private validateMessage(message: string): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!message || typeof message !== 'string') {
            errors.push('Message cannot be empty');
        } else if (message.trim().length === 0) {
            errors.push('Message cannot only contain whitespace characters');
        } else if (message.length > this.config.security.maxMessageLength) {
            errors.push(`Message length cannot exceed ${this.config.security.maxMessageLength} characters`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 处理错误
     */
    private handleError(error: any, callbacks: StreamCallbacks): void {
        // 取消当前操作
        this.currentCancellationToken?.cancel();

        let errorMessage: string;

        if (error instanceof AIStreamError) {
            errorMessage = error.message;
            if (error.code === 'CANCELED') {
                callbacks.onStatus?.('Operation canceled');
                return;
            }
        } else if (error instanceof Error) {
            errorMessage = error.message;
        } else {
            errorMessage = 'Unknown error';
        }

        console.error('AI processing failed:', error);
        callbacks.onError?.(`${this.config.ui.statusMessages.error}: ${errorMessage}`);
    }

    /**
     * 获取对话状态
     */
    getState(): {
        history: Message[];
        isProcessing: boolean;
        currentIteration: number;
        hasActiveFunctionCalls: boolean;
    } {
        const hasActiveFunctionCalls = this.conversationHistory
            .slice(-5) // 检查最近5条消息
            .some(msg => msg.role === 'assistant' && msg.tool_calls && msg.tool_calls.length > 0);

        return {
            history: [...this.conversationHistory],
            isProcessing: !!this.currentCancellationToken,
            currentIteration: 0, // 可以在处理过程中跟踪
            hasActiveFunctionCalls
        };
    }

    /**
     * 取消当前处理
     */
    cancel(): void {
        this.currentCancellationToken?.cancel();
    }

    /**
     * 📝 获取对话历史
     */
    getConversationHistory(): Message[] {
        return [...this.conversationHistory];
    }

    /**
     * 🗑️ 清空对话历史
     */
    clearHistory(): void {
        // 取消当前处理
        this.cancel();

        // 重新初始化对话
        this.conversationHistory.length = 0;
        this.initializeConversation();

        // 验证历史长度
        this.trimHistoryIfNeeded();
    }

    /**
     * 修剪历史记录（如果太长）
     */
    private trimHistoryIfNeeded(): void {
        const maxLength = this.config.security.maxHistoryLength;
        if (this.conversationHistory.length > maxLength) {
            // 保留系统消息和最近的消息
            const systemMessages = this.conversationHistory.filter(msg => msg.role === 'system');
            const recentMessages = this.conversationHistory
                .filter(msg => msg.role !== 'system')
                .slice(-(maxLength - systemMessages.length));

            this.conversationHistory = [...systemMessages, ...recentMessages];
        }
    }
}

// 全局实例管理
let globalFunctionCallingManager: FrontendFunctionCallingManager | null = null;

/**
 * 获取全局Function Calling管理器实例
 */
export function getFunctionCallingManager(): FrontendFunctionCallingManager {
    if (!globalFunctionCallingManager) {
        globalFunctionCallingManager = new FrontendFunctionCallingManager();
    }
    return globalFunctionCallingManager;
}

/**
 * 重置Function Calling管理器
 */
export function resetFunctionCallingManager(): void {
    // 取消当前处理
    globalFunctionCallingManager?.cancel();
    globalFunctionCallingManager = null;
}

/**
 * 创建新的Function Calling管理器实例
 */
export function createFunctionCallingManager(): FrontendFunctionCallingManager {
    return new FrontendFunctionCallingManager();
}

// 导出类型和错误类
export type { StreamCallbacks, FunctionExecutionResult };
export { AIStreamError, CancellationToken }; 