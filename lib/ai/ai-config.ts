/**
 * AI系统统一配置管理
 * 集中管理所有AI相关的配置、常量和默认值
 */

// AI配置接口
export interface AIConfig {
  // API配置
  apiEndpoint: string;
  defaultModel: string;

  // 流式处理配置
  streaming: {
    enabled: boolean;
    chunkTimeout: number;
    maxRetries: number;
  };

  // Function Calling配置
  functionCalling: {
    maxIterations: number;
    timeout: number;
    retryDelay: number;
  };

  // 用户体验配置
  ui: {
    statusMessages: {
      thinking: string;
      executing: string;
      completed: string;
      error: string;
    };
    emojis: {
      user: string;
      assistant: string;
      status: string;
      success: string;
      error: string;
    };
  };

  // 安全配置
  security: {
    maxMessageLength: number;
    maxHistoryLength: number;
    allowedDomains: string[];
  };
}

// 默认配置
export const DEFAULT_AI_CONFIG: AIConfig = {
  apiEndpoint: '/api/ai/chat',
  defaultModel: 'deepseek-chat',

  streaming: {
    enabled: true,
    chunkTimeout: 30000,
    maxRetries: 3,
  },

  functionCalling: {
    maxIterations: 5,
    timeout: 60000,
    retryDelay: 1000,
  },

  ui: {
    statusMessages: {
      thinking: 'AI thinking...',
      executing: 'AI executing...',
      completed: 'AI completed',
      error: 'AI error',
    },
    emojis: {
      user: '👤',
      assistant: '🤖',
      status: '💬',
      success: '🎉',
      error: '🚫',
    },
  },

  security: {
    maxMessageLength: 10000,
    maxHistoryLength: 50,
    allowedDomains: [
      'localhost',
      '127.0.0.1',
      process.env.NEXT_PUBLIC_SITE_URL || ''
    ].filter(Boolean),
  },
};

// 系统消息模板
export const SYSTEM_MESSAGE_TEMPLATE = `你是一个智能终端助手，可以通过function calling执行各种命令。

🖥️ **主要功能**：
- 创建和管理浮动窗口
- 执行系统命令
- 提供帮助信息

请高效完成用户请求，保持回复简洁明了。`;

// 配置管理器
class AIConfigManager {
  private config: AIConfig = DEFAULT_AI_CONFIG;
  private readonly listeners: Set<(config: AIConfig) => void> = new Set();

  /**
   * 获取当前配置
   */
  getConfig(): AIConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<AIConfig>): void {
    this.config = { ...this.config, ...updates };
    this.notifyListeners();
  }

  /**
   * 监听配置变化
   */
  onConfigChange(listener: (config: AIConfig) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * 获取系统消息
   */
  getSystemMessage(): string {
    return SYSTEM_MESSAGE_TEMPLATE;
  }

  /**
   * 验证配置
   */
  validateConfig(config: Partial<AIConfig>): string[] {
    const errors: string[] = [];

    if (config.functionCalling?.maxIterations && config.functionCalling.maxIterations < 1) {
      errors.push('maxIterations must be at least 1');
    }

    if (config.security?.maxMessageLength && config.security.maxMessageLength < 100) {
      errors.push('maxMessageLength must be at least 100');
    }

    return errors;
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.config);
      } catch (error) {
        console.error('Config listener error:', error);
      }
    });
  }
}

// 全局配置管理器实例
export const aiConfigManager = new AIConfigManager();

// 便捷访问函数
export const getAIConfig = () => aiConfigManager.getConfig();
export const updateAIConfig = (updates: Partial<AIConfig>) => aiConfigManager.updateConfig(updates);
export const getSystemMessage = () => aiConfigManager.getSystemMessage(); 