/**
 * Terminal UI管理器
 * 专门处理AI对话在Terminal中的UI显示逻辑
 */

import { getAIConfig } from './ai-config';

interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

interface UICallbacks {
  onContent?: (content: string, isComplete: boolean) => void;
  onToolCall?: (toolCall: ToolCall, isComplete: boolean) => void;
  onStatus?: (status: string) => void;
  onError?: (error: string) => void;
  onComplete?: (finalMessage: string) => void;
}

/**
 * Terminal UI状态管理
 */
class UIState {
  private currentStatusLine = '';
  private aiResponseStarted = false;
  private lastOutputLength = 0;

  get statusLine(): string {
    return this.currentStatusLine;
  }

  set statusLine(value: string) {
    this.currentStatusLine = value;
  }

  get responseStarted(): boolean {
    return this.aiResponseStarted;
  }

  set responseStarted(value: boolean) {
    this.aiResponseStarted = value;
    if (value) {
      this.lastOutputLength = 0;
    }
  }

  get outputLength(): number {
    return this.lastOutputLength;
  }

  set outputLength(value: number) {
    this.lastOutputLength = value;
  }

  reset(): void {
    this.currentStatusLine = '';
    this.aiResponseStarted = false;
    this.lastOutputLength = 0;
  }
}

/**
 * Terminal UI管理器
 */
export class TerminalUIManager {
  private readonly config = getAIConfig();
  private readonly state = new UIState();

  constructor(private readonly terminal: any) {
    if (!terminal) {
      throw new Error('Terminal instance is required');
    }
  }

  /**
   * 显示用户消息
   */
  displayUserMessage(message: string): void {
    const emoji = this.config.ui.emojis.user;
    this.terminal.writeln(`${emoji} ${message}`);
    this.terminal.writeln('');
  }

  /**
   * 创建UI回调
   */
  createUICallbacks(): UICallbacks {
    return {
      onStatus: (status) => {
        this.updateStatus(`${this.config.ui.emojis.assistant} ${status}`);
      },
      onContent: (content, isComplete) => {
        this.handleContentUpdate(content, isComplete);
      },
      onToolCall: (toolCall, isComplete) => {
        if (isComplete) {
          this.updateStatus(`${this.config.ui.emojis.success} execute function: ${toolCall.function.name}`);
        } else {
          this.updateStatus(`🔧 parse function: ${toolCall.function.name}...`);
        }
      },
      onComplete: (finalMessage) => {
        this.handleCompletion(finalMessage);
      },
      onError: (error) => {
        this.handleError(error);
      }
    };
  }

  /**
   * 显示AI额度信息
   */
  async displayQuotaInfo(): Promise<void> {
    try {
      // displayAIQuotaInfo
      const { displayAIQuotaInfo } = await import('@/lib/commands/terminal-commands');
      await displayAIQuotaInfo(this.terminal);
    } catch (error) {
      console.warn('Display AI quota information failed:', error);
    }
  }

  /**
   * 清理并完成UI状态
   */
  cleanup(): void {
    // 🛡️ 完全不清理任何内容，只重置内部状态
    // 不调用任何清屏操作，保护AI回复内容
    this.state.reset();
  }

  /**
   * 处理内容更新
   */
  private handleContentUpdate(content: string, isComplete: boolean): void {
    if (!this.state.responseStarted) {
      this.forceCleanLine();
      this.terminal.write(`${this.config.ui.emojis.assistant} `);
      this.state.responseStarted = true;
    }

    const newContent = content.slice(this.state.outputLength);
    if (newContent) {
      this.terminal.write(newContent);
      this.state.outputLength = content.length;
    }

    if (isComplete) {
      this.terminal.writeln('');
    }
  }

  /**
   * 处理完成状态
   */
  private handleCompletion(finalMessage: string): void {
    // 如果已经有流式回复，什么都不做，保护AI回复内容
    if (this.state.responseStarted) {
      // 🛡️ 完全不清理，保护流式回复内容
      return;
    }

    // 如果没有流式回复但有最终消息，显示它
    this.clearStatusLineOnly();
    if (finalMessage) {
      this.terminal.writeln(`${this.config.ui.emojis.assistant} ${finalMessage}`);
    }
  }

  /**
   * 处理错误状态
   */
  private handleError(error: string): void {
    this.clearCurrentLine();
    this.terminal.writeln(`${this.config.ui.emojis.error} ${error}`);
  }

  /**
   * 更新状态显示
   */
  private updateStatus(status: string): void {
    // 🛡️ 如果AI回复已开始，不清理当前行，避免干扰AI内容
    if (this.state.responseStarted) {
      // AI回复期间，状态更新不显示，保护AI内容
      this.state.statusLine = status;
      return;
    }

    this.clearCurrentLine();
    this.state.statusLine = status;
    this.terminal.write(status);
  }

  /**
   * 清除当前状态行
   */
  private clearCurrentLine(): void {
    if (this.state.statusLine) {
      this.terminal.write('\r\x1b[K');
      this.state.statusLine = '';
    }
  }

  /**
   * 只清除状态行（更安全的版本）
   */
  private clearStatusLineOnly(): void {
    if (this.state.statusLine) {
      this.terminal.write('\r\x1b[K');
      this.state.statusLine = '';
    }
  }

  /**
   * 强制清除当前行
   */
  private forceCleanLine(): void {
    this.terminal.write('\r\x1b[K');
    this.state.statusLine = '';
  }

  /**
   * 显示最终结果
   */
  async displayFinalResult(): Promise<void> {
    // 🛡️ 无论如何都确保AI回复和额度信息之间有换行分隔
    // 保险起见，始终添加换行，确保格式正确
    this.terminal.writeln('');  // 强制换行分隔
    this.terminal.writeln('');
    // await this.displayQuotaInfo();
    // this.terminal.writeln('');
  }

  /**
   * 显示错误信息
   */
  displayError(error: string, details?: any): void {
    this.terminal.write('\r\x1b[K');
    this.terminal.writeln(`${this.config.ui.emojis.error} ${error}`);

    if (details && process.env.NODE_ENV === 'development') {
      this.terminal.writeln(`Detailed information: ${JSON.stringify(details, null, 2)}`);
    }

    this.terminal.writeln('');
  }

  /**
   * 验证Terminal实例
   */
  static validateTerminal(terminal: any): boolean {
    return terminal &&
      typeof terminal.write === 'function' &&
      typeof terminal.writeln === 'function';
  }
}

/**
 * 创建Terminal UI管理器工厂函数
 */
export function createTerminalUIManager(terminal: any): TerminalUIManager {
  if (!TerminalUIManager.validateTerminal(terminal)) {
    throw new Error('Invalid Terminal instance');
  }
  return new TerminalUIManager(terminal);
} 