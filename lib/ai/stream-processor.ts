/**
 * AI流式响应处理器
 * 专门处理SSE流式响应的解析和状态管理
 */

import { getAIConfig } from './ai-config';

// 简化的类型定义（避免循环依赖）
interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

interface ToolCallChunk {
  index: number;
  id?: string;
  type?: 'function';
  function?: {
    name?: string;
    arguments?: string;
  };
}

interface StreamCallbacks {
  onContent?: (content: string, isComplete: boolean) => void;
  onToolCall?: (toolCall: ToolCall, isComplete: boolean) => void;
  onStatus?: (status: string) => void;
  onError?: (error: string) => void;
}

interface StreamResult {
  hasToolCalls: boolean;
  content: string;
  toolCalls: ToolCall[];
}

/**
 * AI错误类
 */
export class AIStreamError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly details?: any
  ) {
    super(message);
    this.name = 'AIStreamError';
  }
}

/**
 * 取消令牌实现
 */
export class CancellationToken {
  private _isCanceled = false;
  private readonly _callbacks = new Set<() => void>();

  get isCanceled(): boolean {
    return this._isCanceled;
  }

  cancel(): void {
    if (this._isCanceled) return;
    this._isCanceled = true;
    this._callbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Cancellation callback error:', error);
      }
    });
  }

  onCanceled(callback: () => void): void {
    if (this._isCanceled) {
      callback();
      return;
    }
    this._callbacks.add(callback);
  }

  throwIfCanceled(): void {
    if (this._isCanceled) {
      throw new AIStreamError('Operation was canceled', 'CANCELED');
    }
  }
}

/**
 * 流式响应处理器
 */
export class StreamProcessor {
  private readonly config = getAIConfig();

  /**
   * 处理流式响应
   */
  async processStream(
    response: Response,
    callbacks: StreamCallbacks,
    cancellationToken?: CancellationToken
  ): Promise<StreamResult> {
    // 验证响应状态
    if (!response.ok) {
      throw new AIStreamError(
        `API request failed: ${response.status}`,
        'API_ERROR',
        { status: response.status, statusText: response.statusText }
      );
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new AIStreamError('Cannot get response stream', 'STREAM_ERROR');
    }

    let fullContent = '';
    const currentToolCalls: { [index: number]: ToolCall } = {};
    let hasToolCalls = false;
    const decoder = new TextDecoder();

    try {
      // 设置超时处理
      const timeoutPromise = this.createTimeoutPromise();

      while (true) {
        cancellationToken?.throwIfCanceled();

        // 读取数据块
        const result = await Promise.race([
          reader.read(),
          timeoutPromise
        ]);

        if (result.done) break;

        const chunk = decoder.decode(result.value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          cancellationToken?.throwIfCanceled();

          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();

            if (data === '[DONE]') {
              // 流式处理完成
              return {
                hasToolCalls,
                content: fullContent,
                toolCalls: Object.values(currentToolCalls)
              };
            }

            try {
              const parsed = JSON.parse(data);
              await this.processDelta(parsed, {
                currentToolCalls,
                fullContent: () => fullContent,
                setFullContent: (content) => { fullContent = content; },
                setHasToolCalls: (value) => { hasToolCalls = value; },
                callbacks
              });
            } catch (parseError) {
              // 静默忽略JSON解析错误，这在流式响应中是正常的
              console.debug('Stream JSON parse error (ignored):', parseError);
            }
          }
        }
      }

      // 正常结束但没有[DONE]标记
      return {
        hasToolCalls,
        content: fullContent,
        toolCalls: Object.values(currentToolCalls)
      };

    } catch (error) {
      if (error instanceof AIStreamError) {
        throw error;
      }
      throw new AIStreamError(
        'Stream processing failed',
        'STREAM_PROCESSING_ERROR',
        error
      );
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * 处理单个delta
   */
  private async processDelta(
    parsed: any,
    context: {
      currentToolCalls: { [index: number]: ToolCall };
      fullContent: () => string;
      setFullContent: (content: string) => void;
      setHasToolCalls: (value: boolean) => void;
      callbacks: StreamCallbacks;
    }
  ): Promise<void> {
    const delta = parsed.choices?.[0]?.delta;
    if (!delta) return;

    // 处理内容流式输出
    if (delta.content) {
      const newContent = context.fullContent() + delta.content;
      context.setFullContent(newContent);
      context.callbacks.onContent?.(newContent, false);
    }

    // 处理Tool Calls流式输出
    if (delta.tool_calls) {
      context.setHasToolCalls(true);

      for (const toolCallChunk of delta.tool_calls) {
        this.processToolCallChunk(toolCallChunk, context.currentToolCalls, context.callbacks);
      }
    }
  }

  /**
   * 处理Tool Call分块
   */
  private processToolCallChunk(
    toolCallChunk: ToolCallChunk,
    currentToolCalls: { [index: number]: ToolCall },
    callbacks: StreamCallbacks
  ): void {
    const index = toolCallChunk.index;

    // 初始化tool call
    if (!currentToolCalls[index]) {
      currentToolCalls[index] = {
        id: '',
        type: 'function',
        function: { name: '', arguments: '' }
      };
    }

    // 累积数据
    if (toolCallChunk.id) {
      currentToolCalls[index].id += toolCallChunk.id;
    }
    if (toolCallChunk.function?.name) {
      currentToolCalls[index].function.name += toolCallChunk.function.name;
    }
    if (toolCallChunk.function?.arguments) {
      currentToolCalls[index].function.arguments += toolCallChunk.function.arguments;
    }

    // 实时反馈Function Call状态
    callbacks.onToolCall?.(currentToolCalls[index], false);
  }

  /**
   * 创建超时Promise
   */
  private createTimeoutPromise(): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new AIStreamError(
          `Stream response timeout (${this.config.streaming.chunkTimeout}ms)`,
          'TIMEOUT'
        ));
      }, this.config.streaming.chunkTimeout);
    });
  }

  /**
   * 验证Tool Call完整性
   */
  validateToolCalls(toolCalls: ToolCall[]): string[] {
    const errors: string[] = [];

    for (const [index, toolCall] of toolCalls.entries()) {
      if (!toolCall.id) {
        errors.push(`Tool call ${index}: missing ID`);
      }
      if (!toolCall.function.name) {
        errors.push(`Tool call ${index}: missing function name`);
      }
      if (!toolCall.function.arguments) {
        errors.push(`Tool call ${index}: missing function arguments`);
      } else {
        try {
          JSON.parse(toolCall.function.arguments);
        } catch {
          errors.push(`Tool call ${index}: function arguments is not a valid JSON`);
        }
      }
    }

    return errors;
  }
} 