/**
 * AI系统前端类型定义
 * 专门用于服务器端转发架构的前端类型
 */

// ============================================================================
// 基础消息类型
// ============================================================================

/** AI消息角色 */
export type AIMessageRole = 'system' | 'user' | 'assistant' | 'tool';

/** AI消息接口 */
export interface AIMessage {
    role: AIMessageRole;
    content?: string;
    tool_calls?: ToolCall[];
    tool_call_id?: string;
}

// ============================================================================
// Function Call相关类型
// ============================================================================

/** Tool Call类型 */
export interface ToolCall {
    id: string;
    type: 'function';
    function: {
        name: string;
        arguments: string;
    };
}

/** Tool Call Chunk类型 */
export interface ToolCallChunk {
    index: number;
    id?: string;
    type?: 'function';
    function?: {
        name?: string;
        arguments?: string;
    };
}

// ============================================================================
// 流式响应相关类型
// ============================================================================

/** Stream Delta类型 */
export interface StreamDelta {
    role?: string;
    content?: string;
    tool_calls?: ToolCallChunk[];
}

/** Stream Choice类型 */
export interface StreamChoice {
    index: number;
    delta: StreamDelta;
    finish_reason?: string | null;
}

/** Stream Response类型 */
export interface StreamResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: StreamChoice[];
}

// ============================================================================
// 回调接口
// ============================================================================

/** Stream Callbacks类型 */
export interface StreamCallbacks {
    onContent?: (content: string, isComplete: boolean) => void;
    onToolCall?: (toolCall: ToolCall, isComplete: boolean) => void;
    onStatus?: (status: string) => void;
    onError?: (error: string) => void;
    onComplete?: (finalMessage: string) => void;
}

// ============================================================================
// Function执行结果
// ============================================================================

/** Function Execution Result类型 */
export interface FunctionExecutionResult {
    success: boolean;
    command: string;
    result?: any;
    message?: string;
    error?: string;
}

// ============================================================================
// AI API请求参数（仅用于前端到服务器的请求）
// ============================================================================

/** AI API Request类型 */
export interface AIAPIRequest {
    messages: AIMessage[];
    stream?: boolean;
    model?: string;
    temperature?: number;
    max_tokens?: number;
}

// ============================================================================
// 会话状态
// ============================================================================

/** Conversation State类型 */
export interface ConversationState {
    history: AIMessage[];
    isProcessing: boolean;
    currentIteration: number;
    hasActiveFunctionCalls: boolean;
    lastError?: AIError;
}

// ============================================================================
// 错误类型
// ============================================================================

/** AI错误类型 */
export type AIErrorType =
    | 'config_error'
    | 'network_error'
    | 'quota_error'
    | 'parsing_error'
    | 'timeout_error'
    | 'unknown_error';

/** AI错误 */
export class AIError extends Error {
    constructor(
        public type: AIErrorType,
        message: string,
        public details?: any
    ) {
        super(message);
        this.name = 'AIError';
    }
}

// ============================================================================
// Function Calling管理器接口
// ============================================================================

/** IFunction Calling Manager类型 */
export interface IFunctionCallingManager {
    processUserMessage(userMessage: string, callbacks: StreamCallbacks): Promise<void>;
    getConversationHistory(): AIMessage[];
    clearHistory(): void;
    getState(): ConversationState;
}

// ============================================================================
// 流式处理器接口
// ============================================================================

/** IStream Processor类型 */
export interface IStreamProcessor {
    processStream(
        response: Response,
        callbacks: StreamCallbacks
    ): Promise<{ hasToolCalls: boolean; content: string; toolCalls: ToolCall[] }>;
}

// ============================================================================
// 取消令牌接口
// ============================================================================

/** CancellationToken类型 */
export interface CancellationToken {
    isCanceled: boolean;
    cancel(): void;
    onCanceled(callback: () => void): void;
}

// ============================================================================
// 验证工具类型
// ============================================================================

/** Validation Result类型 */
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
}

/** Message Validator类型 */
export interface MessageValidator {
    validateMessage(message: string): ValidationResult;
    validateHistory(history: AIMessage[]): ValidationResult;
} 