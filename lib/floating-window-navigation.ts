/**
 * 浮窗导航工具
 * 提供统一的页面导航功能，支持浮窗和传统浏览器导航
 */

import { globalMessageManager } from '@/lib/floating-window-messages';

export interface NavigationOptions {
  /** 目标URL路径 */
  url: string;
  /** 窗口标题（仅浮窗模式） */
  title?: string;
  /** 窗口宽度（仅浮窗模式） */
  width?: number;
  /** 窗口高度（仅浮窗模式） */
  height?: number;
  /** 窗口X坐标（仅浮窗模式） */
  x?: number;
  /** 窗口Y坐标（仅浮窗模式） */
  y?: number;
  /** 是否强制使用浏览器导航（即使在浮窗中） */
  forceBrowserNavigation?: boolean;
}

/**
 * 检测当前页面是否在浮窗中运行
 */
export function isRunningInFloatingWindow(): boolean {
  try {
    // 检查是否在iframe中
    if (window.self !== window.top) {
      return true;
    }

    // 检查URL参数中是否有浮窗标识
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('_floating') || urlParams.has('_window')) {
      return true;
    }

    // 检查window对象上是否有浮窗标识
    if ((window as any).__FLOATING_WINDOW__ === true) {
      return true;
    }

    return false;
  } catch (error) {
    // 如果检查失败，默认认为不在浮窗中
    return false;
  }
}

/**
 * 智能导航函数
 * 根据当前环境选择最佳的导航方式
 */
export async function navigateToPage(options: NavigationOptions): Promise<boolean> {
  const {
    url,
    title,
    width = 900,
    height = 700,
    x,
    y,
    forceBrowserNavigation = false
  } = options;

  // 如果强制使用浏览器导航，直接跳转
  if (forceBrowserNavigation) {
    window.open(url, '_blank', 'noopener,noreferrer');
    return true;
  }

  // 检查是否在浮窗中运行
  const inFloatingWindow = isRunningInFloatingWindow();

  if (inFloatingWindow) {
    // 在浮窗中，尝试创建新浮窗
    return await createFloatingWindow(options);
  } else {
    // 在主窗口中，使用传统导航
    window.open(url, '_blank', 'noopener,noreferrer');
    return true;
  }
}

/**
 * 创建浮窗的多策略实现
 */
async function createFloatingWindow(options: NavigationOptions): Promise<boolean> {
  const { url, title, width = 0.6, height = 0.8, x = 0.1, y = 0.1 } = options;

  // 生成窗口标题
  const windowTitle = title || generateTitleFromUrl(url);

  // 构建create_window命令 - 支持位置和大小参数
  const command = `create_window type=path target=${url} x=${x || 0.1} y=${y || 0.1} width=${width || 0.6} height=${height || 0.8}`;

  let messageSent = false;

  // 策略1: 如果在浮窗中，使用postMessage发送到父窗口（使用完整协议）
  try {
    if (window.parent && window.parent !== window) {
      const messageId = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      // console.log(`📤 使用最新postMessage协议发送到父窗口`);
      // console.log(`🚀 发送terminal命令: ${command}`);

      window.parent.postMessage({
        id: messageId,
        type: 'TERMINAL_COMMAND',
        timestamp: Date.now(),
        windowId: undefined,
        source: 'window',
        payload: {
          command: command,
          params: undefined,
          expectResponse: true
        }
      }, '*');
      messageSent = true;
    }
  } catch (error) {
    console.error('❌ postMessage发送失败:', error);
  }

  // 策略2: 使用globalMessageManager
  if (!messageSent) {
    try {
      // console.log(`📤 使用globalMessageManager发送命令: ${command}`);
      await globalMessageManager.sendTerminalCommand(command);
      messageSent = true;
    } catch (error) {
      console.error('❌ GlobalMessageManager发送命令失败:', error);
    }
  }

  // 策略3: 降级到传统方式
  if (!messageSent) {
    // console.log(`📤 降级到传统新窗口方式`);
    window.open(url, '_blank', 'noopener,noreferrer');
    messageSent = true;
  }

  return messageSent;
}

/**
 * 从URL生成窗口标题
 */
function generateTitleFromUrl(url: string): string {
  try {
    // 移除查询参数和hash
    const cleanUrl = url.split('?')[0].split('#')[0];

    // 提取路径部分
    const pathParts = cleanUrl.split('/').filter(part => part.length > 0);

    if (pathParts.length === 0) {
      return '首页';
    }

    // 根据路径生成标题
    const lastPart = pathParts[pathParts.length - 1];

    // 特殊路径处理
    const pathTitleMap: Record<string, string> = {
      'papers': '学术论文',
      'docs': '文档资料',
      'news': '新闻资讯',
      'about': '关于页面',
      'contact': '联系方式',
      'submit': '提交论文',
      'terminal': '终端界面'
    };

    if (pathParts[0] === 'paper' && pathParts[1]) {
      return `论文详情: ${pathParts[1].slice(0, 8)}...`;
    }

    return pathTitleMap[lastPart] || `页面: ${lastPart}`;
  } catch (error) {
    return '页面';
  }
}

/**
 * 为React组件提供的Hook式导航函数
 */
export function useFloatingWindowNavigation() {
  const navigate = async (options: NavigationOptions) => {
    return await navigateToPage(options);
  };

  const isInFloatingWindow = isRunningInFloatingWindow();

  return {
    navigate,
    isInFloatingWindow,
    // 便捷方法
    navigateToPaper: (paperId: string, title?: string) =>
      navigate({
        url: `/paper/${paperId}`,
        title: title ? `论文: ${title}` : undefined,
        x: 0.1,
        y: 0.1,
        width: 0.6,
        height: 0.8
      }),
    navigateToPage: (path: string, title?: string) =>
      navigate({ url: path, title })
  };
}
