/**
 * 通知URL生成工具
 * 统一管理不同类型通知的跳转链接生成逻辑
 */

export type NotificationType = 
  | 'REVIEW_UPDATE'           // 审稿更新
  | 'PAPER_STATUS_CHANGE'     // 论文状态变更
  | 'COMMENT_ON_PAPER'        // 论文评论
  | 'REPLY_ON_COMMENT'        // 评论回复
  | 'PAPER_SUBMITTED'         // 论文提交
  | 'PAPER_ASSIGNED'          // 论文分配
  | 'REVIEW_DEADLINE'         // 审稿截止日期
  | 'SYSTEM_ANNOUNCEMENT';    // 系统公告

export interface NotificationUrlOptions {
  paperId?: string;
  commentId?: string;
  issueId?: string;
  userId?: string;
  anchor?: string;
}

/**
 * 生成通知跳转URL
 */
export function generateNotificationUrl(
  type: NotificationType,
  options: NotificationUrlOptions = {}
): string {
  const { paperId, commentId, issueId, userId, anchor } = options;

  switch (type) {
    case 'REVIEW_UPDATE':
    case 'PAPER_STATUS_CHANGE':
      // 审稿相关通知跳转到review页面
      if (!paperId) throw new Error('paperId is required for review notifications');
      
      let reviewUrl = `/papers/${paperId}/review`;
      if (commentId) {
        reviewUrl += `#comment-${commentId}`;
      } else if (anchor) {
        reviewUrl += `#${anchor}`;
      } else {
        reviewUrl += '#bottom';
      }
      return reviewUrl;

    case 'COMMENT_ON_PAPER':
    case 'REPLY_ON_COMMENT':
      // 发布后的评论和回复跳转到论文页面
      if (!paperId) throw new Error('paperId is required for paper notifications');
      
      let paperUrl = `/paper/${paperId}`;
      if (commentId) {
        paperUrl += `#comment-${commentId}`;
      } else if (anchor) {
        paperUrl += `#${anchor}`;
      }
      return paperUrl;

    case 'PAPER_SUBMITTED':
    case 'PAPER_ASSIGNED':
      // 论文管理相关通知
      if (paperId) {
        return `/papers/${paperId}/review`;
      } else if (issueId) {
        return `/issues/${issueId}`;
      } else {
        return '/papers';
      }

    case 'REVIEW_DEADLINE':
      // 审稿截止日期通知
      if (paperId) {
        return `/papers/${paperId}/review`;
      } else {
        return '/dashboard/reviews';
      }

    case 'SYSTEM_ANNOUNCEMENT':
      // 系统公告
      return '/notifications';

    default:
      // 默认跳转到通知页面
      console.warn(`Unknown notification type: ${type}`);
      return '/notifications';
  }
}

/**
 * 生成移动端友好的URL（使用查询参数而不是锚点）
 */
export function generateMobileNotificationUrl(
  type: NotificationType,
  options: NotificationUrlOptions = {}
): string {
  const { paperId, commentId, issueId, userId } = options;

  switch (type) {
    case 'REVIEW_UPDATE':
    case 'PAPER_STATUS_CHANGE':
      if (!paperId) throw new Error('paperId is required for review notifications');
      
      let reviewUrl = `/papers/${paperId}/review`;
      if (commentId) {
        reviewUrl += `?scrollTo=comment-${commentId}`;
      } else {
        reviewUrl += '?scrollTo=bottom';
      }
      return reviewUrl;

    case 'COMMENT_ON_PAPER':
    case 'REPLY_ON_COMMENT':
      if (!paperId) throw new Error('paperId is required for paper notifications');
      
      let paperUrl = `/paper/${paperId}`;
      if (commentId) {
        paperUrl += `?commentId=${commentId}`;
      }
      return paperUrl;

    default:
      // 其他类型使用标准URL
      return generateNotificationUrl(type, options);
  }
}

/**
 * 解析通知URL中的参数
 */
export function parseNotificationUrl(url: string): {
  type: 'review' | 'paper' | 'other';
  paperId?: string;
  commentId?: string;
  anchor?: string;
} {
  try {
    const urlObj = new URL(url, 'http://localhost');
    const pathname = urlObj.pathname;
    const hash = urlObj.hash.substring(1); // 移除 # 号
    const searchParams = urlObj.searchParams;

    // 解析路径
    if (pathname.includes('/review')) {
      const paperIdMatch = pathname.match(/\/papers\/([^\/]+)\/review/);
      const paperId = paperIdMatch?.[1];
      
      let commentId: string | undefined;
      let anchor: string | undefined;

      // 从锚点解析
      if (hash.startsWith('comment-')) {
        commentId = hash.substring(8); // 移除 'comment-' 前缀
      } else if (hash) {
        anchor = hash;
      }

      // 从查询参数解析（移动端）
      const scrollTo = searchParams.get('scrollTo');
      if (scrollTo?.startsWith('comment-')) {
        commentId = scrollTo.substring(8);
      } else if (scrollTo) {
        anchor = scrollTo;
      }

      return {
        type: 'review',
        paperId,
        commentId,
        anchor,
      };
    } else if (pathname.includes('/paper/')) {
      const paperIdMatch = pathname.match(/\/paper\/([^\/]+)/);
      const paperId = paperIdMatch?.[1];
      
      let commentId: string | undefined;
      let anchor: string | undefined;

      // 从锚点解析
      if (hash.startsWith('comment-')) {
        commentId = hash.substring(8);
      } else if (hash) {
        anchor = hash;
      }

      // 从查询参数解析
      const commentIdParam = searchParams.get('commentId');
      if (commentIdParam) {
        commentId = commentIdParam;
      }

      return {
        type: 'paper',
        paperId,
        commentId,
        anchor,
      };
    }

    return { type: 'other' };
  } catch (error) {
    console.error('Failed to parse notification URL:', error);
    return { type: 'other' };
  }
}

/**
 * 验证通知URL是否有效
 */
export function validateNotificationUrl(
  type: NotificationType,
  options: NotificationUrlOptions
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  switch (type) {
    case 'REVIEW_UPDATE':
    case 'PAPER_STATUS_CHANGE':
    case 'COMMENT_ON_PAPER':
    case 'REPLY_ON_COMMENT':
      if (!options.paperId) {
        errors.push('paperId is required');
      }
      break;

    case 'PAPER_SUBMITTED':
    case 'PAPER_ASSIGNED':
      if (!options.paperId && !options.issueId) {
        errors.push('paperId or issueId is required');
      }
      break;
  }

  // 验证ID格式（假设使用CUID）
  if (options.paperId && !/^c[a-z0-9]{24}$/.test(options.paperId)) {
    errors.push('Invalid paperId format');
  }

  if (options.commentId && !/^c[a-z0-9]{24}$/.test(options.commentId)) {
    errors.push('Invalid commentId format');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
