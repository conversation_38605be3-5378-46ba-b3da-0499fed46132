-- Rename the column without losing data
ALTER TABLE "Paper" RENAME COLUMN "createdAt" TO "submittedAt";

-- Add lastRevisedAt column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'Paper'
        AND column_name = 'lastRevisedAt'
    ) THEN
        ALTER TABLE "Paper" ADD COLUMN "lastRevisedAt" TIMESTAMP;
    END IF;
END$$;
