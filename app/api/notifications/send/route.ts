import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";
import { Role } from "@prisma/client";

export async function POST(request: NextRequest) {
    try {
        // 验证用户身份
        const supabase = await getSupabaseRoute();
        const { data: { session } } = await getSessionCompat(supabase);

        if (!session?.user) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { commentId, paperId, notifyType, message, notificationType } = await request.json();

        // 验证必要参数
        if (!commentId || !paperId || !notifyType) {
            return NextResponse.json({ error: "Missing required parameters" }, { status: 400 });
        }

        // 默认通知类型为REVIEW_UPDATE
        const finalNotificationType = notificationType || 'REVIEW_UPDATE';

        // 获取paper信息和相关角色
        const paper = await prisma.paper.findUnique({
            where: { id: paperId },
            select: {
                id: true,
                title: true,
                authorId: true,
                issueId: true
            }
        });

        if (!paper) {
            return NextResponse.json({ error: "Paper not found" }, { status: 404 });
        }

        // 获取作者信息
        const author = await prisma.user.findUnique({
            where: { id: paper.authorId },
            select: { id: true, name: true }
        });

        // 获取paper相关的角色绑定（paper编辑和审稿人）
        const paperRoleBindings = await prisma.roleBinding.findMany({
            where: {
                scopeId: paperId,
                role: { in: [Role.PAPER_EDITOR, Role.REVIEWER] }
            },
            select: {
                principalId: true,
                role: true
            }
        });

        // 获取issue编辑（如果paper已分配到issue）
        let issueEditors: { principalId: string }[] = [];
        if (paper.issueId) {
            issueEditors = await prisma.roleBinding.findMany({
                where: {
                    scopeId: paper.issueId,
                    role: Role.ISSUE_EDITOR
                },
                select: {
                    principalId: true
                }
            });
        }

        // 获取评论信息
        const comment = await prisma.reviewComment.findUnique({
            where: { id: commentId },
            select: {
                id: true,
                body: true,
                type: true,
                roleLabel: true,
                authorId: true
            }
        });

        if (!comment) {
            return NextResponse.json({ error: "Comment not found" }, { status: 404 });
        }

        // 获取全局管理员
        const adminBindings = await prisma.roleBinding.findMany({
            where: {
                role: { in: [Role.ADMIN, Role.CHIEF_EDITOR] },
                scopeType: "GLOBAL"
            },
            select: {
                principalId: true
            }
        });

        let recipients: string[] = [];

        // 根据通知类型确定接收者
        if (notifyType === 'AUTHOR') {
            if (author) {
                recipients.push(paper.authorId);
            }
        } else if (notifyType === 'REVIEWERS') {
            // 获取该论文的所有审稿人
            const reviewers = paperRoleBindings.filter(rb => rb.role === Role.REVIEWER);
            recipients.push(...reviewers.map(r => r.principalId));
        } else if (notifyType === 'EDITORS') {
            // 通知paper编辑、issue编辑和管理员
            const paperEditors = paperRoleBindings.filter(rb => rb.role === Role.PAPER_EDITOR);

            recipients.push(...paperEditors.map(pe => pe.principalId));
            recipients.push(...issueEditors.map(ie => ie.principalId));
            recipients.push(...adminBindings.map(ab => ab.principalId));
        } else if (notifyType === 'ADMINS') {
            // 仅通知管理员（用于新paper创建时）
            recipients.push(...adminBindings.map(ab => ab.principalId));
        }

        // 去重并排除发送者自己
        recipients = [...new Set(recipients)].filter(id => id !== session.user.id);

        if (recipients.length === 0) {
            return NextResponse.json({ error: "No users to notify" }, { status: 400 });
        }

        // 生成通知预览文本
        let preview = message || comment.body;
        if (preview.length > 100) {
            preview = preview.substring(0, 100) + "...";
        }

        // 根据通知类型生成不同的预览文本
        if (finalNotificationType === 'PAPER_STATUS_CHANGE') {
            // 提取状态变更信息
            const statusMatch = preview.match(/Status changed to \*\*(.+?)\*\*/);
            if (statusMatch) {
                preview = `Paper status updated to: ${statusMatch[1]}`;
            } else {
                preview = `Paper status updated`;
            }
        } else if (finalNotificationType === 'REVIEW_UPDATE') {
            // 审稿更新保持原有的评论内容
            if (comment.roleLabel?.startsWith('Reviewer')) {
                preview = `Reviewer posted a new comment: ${preview}`;
            } else if (comment.roleLabel === 'Author') {
                preview = `Author posted a new comment: ${preview}`;
            } else if (comment.roleLabel === 'Editor') {
                preview = `Editor posted a new comment: ${preview}`;
            }
        }

        // 获取发送者信息
        const sender = await prisma.user.findUnique({
            where: { id: session.user.id },
            select: { name: true }
        });

        // 批量创建通知
        const notifications = await Promise.all(
            recipients.map(async (recipientId) => {
                // 创建通知记录
                const notification = await prisma.notification.create({
                    data: {
                        type: finalNotificationType,
                        paperId: paperId,
                        commentId: commentId,
                        preview: preview,
                        paperTitle: paper.title,
                        actorId: session.user.id,
                        actorName: sender?.name || 'Unknown User'
                    }
                });

                // 创建接收者记录
                await prisma.notificationRecipient.create({
                    data: {
                        notificationId: notification.id,
                        userId: recipientId
                    }
                });

                return notification;
            })
        );

        return NextResponse.json({
            success: true,
            count: notifications.length,
            message: `Successfully sent ${notifications.length} notifications`
        });

    } catch (error) {
        console.error("Failed to send notification:", error);
        return NextResponse.json({ error: "Failed to send notification" }, { status: 500 });
    }
} 