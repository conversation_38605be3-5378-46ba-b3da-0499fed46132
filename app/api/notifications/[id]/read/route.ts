import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";

// 标记通知为已读
export async function PATCH(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    const supabase = await getSupabaseRoute();
    const {
        data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
        return NextResponse.json({ error: "需要登录" }, { status: 401 });
    }

    try {
        const { id } = await params;

        // 获取数据库用户
        const dbUser = await prisma.user.findUnique({
            where: { email: user.email as string },
            select: { id: true },
        });

        if (!dbUser) {
            return NextResponse.json({ error: "用户不存在" }, { status: 404 });
        }

        // 更新通知为已读
        const updatedNotification = await prisma.notificationRecipient.updateMany({
            where: {
                id: id,
                userId: dbUser.id,
                readAt: null, // 只更新未读的通知
            },
            data: {
                readAt: new Date(),
            },
        });

        if (updatedNotification.count === 0) {
            return NextResponse.json({ error: "通知不存在或已标记为已读" }, { status: 404 });
        }

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("标记通知为已读失败:", error);
        return NextResponse.json({ error: "标记通知为已读失败" }, { status: 500 });
    }
} 