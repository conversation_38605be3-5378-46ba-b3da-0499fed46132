import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";

// 获取用户通知列表
export async function GET(request: NextRequest) {
    const supabase = await getSupabaseRoute();
    const {
        data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
        return NextResponse.json({ error: "需要登录" }, { status: 401 });
    }

    try {
        const url = new URL(request.url);
        const page = parseInt(url.searchParams.get("page") || "1", 10);
        const limit = parseInt(url.searchParams.get("limit") || "10", 10);
        const skip = (page - 1) * limit;

        // 获取数据库用户
        const dbUser = await prisma.user.findUnique({
            where: { email: user.email as string },
            select: { id: true },
        });

        if (!dbUser) {
            return NextResponse.json({ error: "用户不存在" }, { status: 404 });
        }

        // 获取通知列表
        const notifications = await prisma.notificationRecipient.findMany({
            where: {
                userId: dbUser.id,
            },
            include: {
                notification: {
                    include: {
                        actor: {
                            select: {
                                id: true,
                                name: true,
                                image: true,
                            },
                        },
                    },
                },
            },
            orderBy: {
                notification: {
                    createdAt: "desc",
                },
            },
            skip,
            take: limit,
        });

        // 获取总数用于分页
        const total = await prisma.notificationRecipient.count({
            where: {
                userId: dbUser.id,
            },
        });

        // 格式化响应
        const formattedNotifications = notifications.map(recipient => ({
            id: recipient.id,
            notificationId: recipient.notificationId,
            readAt: recipient.readAt,
            archivedAt: recipient.archivedAt,
            muted: recipient.muted,
            notification: {
                id: recipient.notification.id,
                type: recipient.notification.type,
                paperId: recipient.notification.paperId,
                commentId: recipient.notification.commentId,
                preview: recipient.notification.preview,
                paperTitle: recipient.notification.paperTitle,
                actorName: recipient.notification.actorName,
                createdAt: recipient.notification.createdAt,
                actor: recipient.notification.actor,
            },
        }));

        return NextResponse.json({
            notifications: formattedNotifications,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: page * limit < total,
            },
        });
    } catch (error) {
        console.error("获取通知失败:", error);
        return NextResponse.json({ error: "获取通知失败" }, { status: 500 });
    }
}

// 获取未读通知数量
export async function HEAD(request: NextRequest) {
    const supabase = await getSupabaseRoute();
    const {
        data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
        return new NextResponse(null, { status: 401 });
    }

    try {
        // 获取数据库用户
        const dbUser = await prisma.user.findUnique({
            where: { email: user.email as string },
            select: { id: true },
        });

        if (!dbUser) {
            return new NextResponse(null, { status: 404 });
        }

        // 获取未读通知数量
        const unreadCount = await prisma.notificationRecipient.count({
            where: {
                userId: dbUser.id,
                readAt: null,
            },
        });

        return new NextResponse(null, {
            status: 200,
            headers: {
                "X-Unread-Count": unreadCount.toString(),
            },
        });
    } catch (error) {
        console.error("获取未读通知数量失败:", error);
        return new NextResponse(null, { status: 500 });
    }
} 