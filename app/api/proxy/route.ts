import { NextRequest, NextResponse } from 'next/server';
import { withCors } from '@/utils/cors';

/**
 * 代理API - 绕过X-Frame-Options限制
 * 允许在iframe中显示外部网站
 */
async function proxyHandler(req: NextRequest): Promise<NextResponse> {
    try {
        // 获取目标URL
        const url = req.nextUrl.searchParams.get('url');

        console.log(`[Proxy] 收到请求，URL参数: ${url}`);
        console.log(`[Proxy] 完整查询参数:`, Object.fromEntries(req.nextUrl.searchParams.entries()));

        if (!url) {
            console.error(`[Proxy] 错误：缺少URL参数`);
            console.log(`[Proxy] 请求路径: ${req.nextUrl.pathname}`);
            console.log(`[Proxy] 请求查询: ${req.nextUrl.search}`);

            return NextResponse.json(
                {
                    error: '缺少URL参数',
                    debug: {
                        pathname: req.nextUrl.pathname,
                        search: req.nextUrl.search,
                        searchParams: Object.fromEntries(req.nextUrl.searchParams.entries())
                    }
                },
                { status: 400 }
            );
        }

        // 验证URL格式
        let targetUrl: URL;
        try {
            targetUrl = new URL(url);
        } catch (error) {
            console.error(`[Proxy] URL格式错误: ${url}`, error);
            return NextResponse.json(
                { error: '无效的URL格式', url: url },
                { status: 400 }
            );
        }

        // 安全检查：只允许http和https协议
        if (!['http:', 'https:'].includes(targetUrl.protocol)) {
            return NextResponse.json(
                { error: '不支持的协议，只允许HTTP/HTTPS' },
                { status: 400 }
            );
        }

        console.log(`[Proxy] 代理请求: ${url}`);

        // 🚀 增强的请求配置
        const requestHeaders: Record<string, string> = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'cross-site',
            'Cache-Control': 'max-age=0',
        };

        // 传递原始请求的一些头部
        const forwardHeaders = ['referer', 'origin'];
        forwardHeaders.forEach(header => {
            const value = req.headers.get(header);
            if (value) {
                requestHeaders[header] = value;
            }
        });

        // 🔄 带重试的网络请求
        let response: Response;
        let attempts = 0;
        const maxAttempts = 3;

        while (attempts < maxAttempts) {
            try {
                response = await fetch(url, {
                    headers: requestHeaders,
                    // 设置合理的超时时间
                    signal: AbortSignal.timeout(20000),
                    // 🆕 添加重定向处理
                    redirect: 'follow'
                });

                if (response.ok || response.status < 500) {
                    break; // 成功或客户端错误，不重试
                }

                attempts++;
                console.warn(`[Proxy] 请求失败 (尝试 ${attempts}/${maxAttempts}): ${response.status}`);

                if (attempts < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
                }
            } catch (error) {
                attempts++;
                console.warn(`[Proxy] 请求异常 (尝试 ${attempts}/${maxAttempts}):`, error);

                if (attempts >= maxAttempts) {
                    throw error;
                }

                await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
            }
        }

        if (!response!.ok && response!.status >= 400) {
            console.warn(`[Proxy] 最终请求失败: ${response!.status} ${response!.statusText}`);

            // 🆕 对于资源文件404，返回空响应而不是错误页面
            if (response!.status === 404 && isResourceFile(url)) {
                return new NextResponse('/* Resource not found */', {
                    status: 200, // 返回200避免阻断页面加载
                    headers: {
                        'Content-Type': getContentTypeForExtension(url),
                        'Cache-Control': 'public, max-age=300',
                        'X-Proxy-Status': 'resource-fallback'
                    }
                });
            }

            return NextResponse.json(
                { error: `无法获取网站内容: ${response!.status} ${response!.statusText}` },
                { status: response!.status }
            );
        }

        // 获取内容类型
        const contentType = response!.headers.get('content-type') || '';
        const isHtml = contentType.includes('text/html');
        const isCss = contentType.includes('text/css') || url.endsWith('.css');
        const isJs = contentType.includes('javascript') || url.endsWith('.js');

        // 🆕 处理不同类型的内容
        if (isHtml) {
            return await handleHtmlContent(response!, targetUrl, req);
        } else if (isCss) {
            return await handleCssContent(response!, targetUrl, req);
        } else if (isJs) {
            return await handleJsContent(response!, targetUrl, req);
        } else {
            // 其他资源直接代理
            return await handleOtherContent(response!, req);
        }

    } catch (error) {
        console.error('[Proxy] 代理请求失败:', error);

        // 🆕 对于资源文件，返回友好的fallback
        const url = req.nextUrl.searchParams.get('url') || '';
        if (isResourceFile(url)) {
            return new NextResponse('/* Proxy error - resource fallback */', {
                status: 200,
                headers: {
                    'Content-Type': getContentTypeForExtension(url),
                    'X-Proxy-Status': 'error-fallback'
                }
            });
        }

        // 返回友好的错误页面
        const errorHtml = generateErrorPage(
            error instanceof Error ? error.message : '未知错误',
            url
        );

        return new NextResponse(errorHtml, {
            status: 500,
            headers: {
                'Content-Type': 'text/html; charset=utf-8',
                'X-Frame-Options': 'ALLOWALL',
            }
        });
    }
}

/**
 * 🆕 处理HTML内容
 */
async function handleHtmlContent(response: Response, targetUrl: URL, req: NextRequest): Promise<NextResponse> {
    let html = await response.text();
    html = processHtmlContent(html, targetUrl);

    return new NextResponse(html, {
        headers: {
            'Content-Type': 'text/html; charset=utf-8',
            'Cache-Control': 'public, max-age=300',
            'X-Frame-Options': 'ALLOWALL',
            'Content-Security-Policy': 'frame-ancestors *',
            'X-Proxy-Status': 'success'
        }
    });
}

/**
 * 🆕 处理CSS内容
 */
async function handleCssContent(response: Response, targetUrl: URL, req: NextRequest): Promise<NextResponse> {
    let css = await response.text();
    css = processCssContent(css, targetUrl);

    return new NextResponse(css, {
        headers: {
            'Content-Type': 'text/css; charset=utf-8',
            'Cache-Control': 'public, max-age=3600', // CSS缓存1小时
            'X-Proxy-Status': 'success'
        }
    });
}

/**
 * 🆕 处理JavaScript内容
 */
async function handleJsContent(response: Response, targetUrl: URL, req: NextRequest): Promise<NextResponse> {
    let js = await response.text();
    // 对于JS文件，暂时不做特殊处理，避免破坏功能

    return new NextResponse(js, {
        headers: {
            'Content-Type': 'application/javascript; charset=utf-8',
            'Cache-Control': 'public, max-age=3600', // JS缓存1小时
            'X-Proxy-Status': 'success'
        }
    });
}

/**
 * 🆕 处理其他资源内容
 */
async function handleOtherContent(response: Response, req: NextRequest): Promise<NextResponse> {
    const content = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'application/octet-stream';

    return new NextResponse(content, {
        headers: {
            'Content-Type': contentType,
            'Cache-Control': 'public, max-age=86400', // 静态资源缓存24小时
            'X-Proxy-Status': 'success'
        }
    });
}

/**
 * 🆕 处理CSS内容，修复资源路径
 */
function processCssContent(css: string, targetUrl: URL): string {
    const baseUrl = `${targetUrl.protocol}//${targetUrl.host}`;
    const currentOrigin = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

    // 修复CSS中的url()引用
    css = css.replace(/url\s*\(\s*['"]?([^'")]+)['"]?\s*\)/gi, (match, url) => {
        // 跳过data URLs和已经是绝对URL的
        if (url.startsWith('data:') || url.startsWith('http://') || url.startsWith('https://')) {
            return match;
        }

        // 处理相对路径
        let fullUrl;
        try {
            if (url.startsWith('//')) {
                fullUrl = targetUrl.protocol + url;
            } else if (url.startsWith('/')) {
                fullUrl = baseUrl + url;
            } else {
                // 相对于当前CSS文件的路径
                const cssDir = targetUrl.pathname.substring(0, targetUrl.pathname.lastIndexOf('/') + 1);
                fullUrl = baseUrl + cssDir + url;
            }

            // 转换为代理URL
            const proxyUrl = `${currentOrigin}/api/proxy?url=${encodeURIComponent(fullUrl)}`;
            return `url("${proxyUrl}")`;
        } catch (err) {
            console.warn('[Proxy] CSS URL处理失败:', url, err);
            return match; // 保持原样
        }
    });

    // 修复@import语句
    css = css.replace(/@import\s+['"]([^'"]+)['"]/gi, (match, url) => {
        if (url.startsWith('http://') || url.startsWith('https://')) {
            const proxyUrl = `${currentOrigin}/api/proxy?url=${encodeURIComponent(url)}`;
            return `@import "${proxyUrl}"`;
        }

        try {
            let fullUrl;
            if (url.startsWith('//')) {
                fullUrl = targetUrl.protocol + url;
            } else if (url.startsWith('/')) {
                fullUrl = baseUrl + url;
            } else {
                const cssDir = targetUrl.pathname.substring(0, targetUrl.pathname.lastIndexOf('/') + 1);
                fullUrl = baseUrl + cssDir + url;
            }

            const proxyUrl = `${currentOrigin}/api/proxy?url=${encodeURIComponent(fullUrl)}`;
            return `@import "${proxyUrl}"`;
        } catch (err) {
            console.warn('[Proxy] CSS @import处理失败:', url, err);
            return match;
        }
    });

    return css;
}

/**
 * 🆕 判断是否为资源文件
 */
function isResourceFile(url: string): boolean {
    const resourceExtensions = [
        '.css', '.js', '.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp',
        '.woff', '.woff2', '.ttf', '.eot', '.otf',
        '.ico', '.pdf', '.zip', '.mp3', '.mp4', '.webm'
    ];

    return resourceExtensions.some(ext => url.toLowerCase().includes(ext));
}

/**
 * 🆕 根据文件扩展名获取Content-Type
 */
function getContentTypeForExtension(url: string): string {
    const lowerUrl = url.toLowerCase();

    if (lowerUrl.includes('.css')) return 'text/css';
    if (lowerUrl.includes('.js')) return 'application/javascript';
    if (lowerUrl.includes('.json')) return 'application/json';
    if (lowerUrl.includes('.woff2')) return 'font/woff2';
    if (lowerUrl.includes('.woff')) return 'font/woff';
    if (lowerUrl.includes('.ttf')) return 'font/ttf';
    if (lowerUrl.includes('.eot')) return 'application/vnd.ms-fontobject';
    if (lowerUrl.includes('.svg')) return 'image/svg+xml';
    if (lowerUrl.includes('.png')) return 'image/png';
    if (lowerUrl.includes('.jpg') || lowerUrl.includes('.jpeg')) return 'image/jpeg';
    if (lowerUrl.includes('.gif')) return 'image/gif';
    if (lowerUrl.includes('.webp')) return 'image/webp';
    if (lowerUrl.includes('.ico')) return 'image/x-icon';

    return 'application/octet-stream';
}

/**
 * 处理HTML内容，移除限制并修复链接
 */
function processHtmlContent(html: string, targetUrl: URL): string {
    // 移除可能阻止iframe嵌入的meta标签
    html = html.replace(/<meta[^>]*http-equiv[^>]*X-Frame-Options[^>]*>/gi, '');
    html = html.replace(/<meta[^>]*name[^>]*viewport[^>]*>/gi, '');

    // 添加允许iframe嵌入的meta标签
    const allowFramesMeta = `
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <meta http-equiv="Content-Security-Policy" content="frame-ancestors *">
    `;

    // 在head标签中插入新的meta标签
    html = html.replace(/<head>/i, `<head>${allowFramesMeta}`);

    // 修复相对链接为绝对链接
    const baseUrl = `${targetUrl.protocol}//${targetUrl.host}`;
    const currentOrigin = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

    // 🔄 修复所有链接为代理链接
    html = html.replace(
        /href=["\']([^"']*)["\'/]/gi,
        (match, url) => {
            // 跳过锚点链接、javascript链接、mailto等
            if (url.startsWith('#') || url.startsWith('javascript:') || url.startsWith('mailto:') || url.startsWith('tel:')) {
                return match;
            }

            let fullUrl;
            if (url.startsWith('http://') || url.startsWith('https://')) {
                fullUrl = url;
            } else if (url.startsWith('//')) {
                fullUrl = targetUrl.protocol + url;
            } else {
                // 🆕 改进的相对路径处理
                try {
                    fullUrl = new URL(url, baseUrl).href;
                } catch (err) {
                    console.warn('[Proxy] HTML href处理失败:', url, err);
                    return match;
                }
            }

            // 将外部链接转换为代理链接
            const proxyUrl = `${currentOrigin}/api/proxy?url=${encodeURIComponent(fullUrl)}`;
            return `href="${proxyUrl}"`;
        }
    );

    // 🔄 修复所有资源链接（CSS、JS、图片等）为代理或绝对链接
    html = html.replace(
        /src=["\']([^"']*)["\'/]/gi,
        (match, url) => {
            if (url.startsWith('data:') || url.startsWith('blob:')) {
                return match; // 保持data和blob URLs不变
            }

            let fullUrl;
            if (url.startsWith('http://') || url.startsWith('https://')) {
                fullUrl = url;
            } else if (url.startsWith('//')) {
                fullUrl = targetUrl.protocol + url;
            } else {
                // 🆕 改进的相对路径处理
                try {
                    fullUrl = new URL(url, baseUrl).href;
                } catch (err) {
                    console.warn('[Proxy] HTML src处理失败:', url, err);
                    return match;
                }
            }

            // 🆕 智能代理：CSS/JS文件通过代理，其他资源直接使用绝对URL
            if (url.includes('.css') || url.includes('.js') || isResourceFile(url)) {
                const proxyUrl = `${currentOrigin}/api/proxy?url=${encodeURIComponent(fullUrl)}`;
                return `src="${proxyUrl}"`;
            } else {
                return `src="${fullUrl}"`;
            }
        }
    );

    // 🔄 修复表单action为代理链接
    html = html.replace(
        /action=["\']([^"']*)["\'/]/gi,
        (match, url) => {
            if (url.startsWith('#') || url.startsWith('javascript:')) {
                return match;
            }

            let fullUrl;
            if (url.startsWith('http://') || url.startsWith('https://')) {
                fullUrl = url;
            } else if (url.startsWith('//')) {
                fullUrl = targetUrl.protocol + url;
            } else {
                try {
                    fullUrl = new URL(url, baseUrl).href;
                } catch (err) {
                    console.warn('[Proxy] HTML action处理失败:', url, err);
                    return match;
                }
            }

            const proxyUrl = `${currentOrigin}/api/proxy?url=${encodeURIComponent(fullUrl)}`;
            return `action="${proxyUrl}"`;
        }
    );

    // 添加基础URL标签
    if (!html.includes('<base')) {
        html = html.replace(
            /<head>/i,
            `<head><base href="${baseUrl}/">`
        );
    }

    // 🚀 注入增强的代理脚本
    const proxyScript = `
    <script>
        // 代理模式标识
        window.PROXY_MODE = true;
        window.ORIGINAL_URL = "${targetUrl.href}";
        window.PROXY_BASE = "${baseUrl}";
        window.PROXY_ORIGIN = "${currentOrigin}";
        
        console.log('[Proxy Mode] 页面通过代理加载:', window.ORIGINAL_URL);
        console.log('[Proxy Mode] 脚本开始执行，document.readyState:', document.readyState);
        
        // 🚀 立即设置DOM查询支持 - 在脚本顶部就开始监听
        console.log('[Proxy Mode] 立即设置DOM查询支持监听器');
        window.addEventListener('message', function(event) {
            console.log('[Proxy Mode] 收到消息事件:', {
                type: event.data?.type,
                origin: event.origin,
                targetOrigin: window.location.origin,
                hasParent: window.parent !== window,
                source: event.source === window.parent ? 'parent' : (event.source === window ? 'self' : 'other')
            });
            
            if (event.data && event.data.type === 'DOM_QUERY_REQUEST') {
                const { requestId, selector, attribute } = event.data;
                console.log('[Proxy Mode] 立即处理DOM查询请求:', { requestId, selector, attribute });
                
                try {
                    let result;
                    const elements = document.querySelectorAll(selector);
                    console.log('[Proxy Mode] 查询结果:', elements.length, '个元素');
                    
                    if (elements.length === 0) {
                        result = {
                            type: 'no_elements',
                            message: '未找到匹配选择器 "' + selector + '" 的元素',
                            count: 0
                        };
                    } else if (elements.length === 1) {
                        const element = elements[0];
                        const value = element[attribute] || element.getAttribute(attribute) || '';
                        result = {
                            type: 'single_element',
                            value: value,
                            count: 1,
                            tagName: element.tagName.toLowerCase(),
                            innerHTML: element.innerHTML.slice(0, 200) + (element.innerHTML.length > 200 ? '...' : '')
                        };
                    } else {
                        const values = Array.from(elements).map(el => {
                            const value = el[attribute] || el.getAttribute(attribute) || '';
                            return {
                                value: value.slice(0, 100) + (value.length > 100 ? '...' : ''),
                                tagName: el.tagName.toLowerCase()
                            };
                        });
                        result = {
                            type: 'multiple_elements',
                            values: values,
                            count: elements.length,
                            summary: '找到 ' + elements.length + ' 个匹配元素'
                        };
                    }
                    
                    // 发送响应
                    console.log('[Proxy Mode] 发送DOM查询响应:', result);
                    event.source?.postMessage({
                        type: 'DOM_QUERY_RESPONSE',
                        requestId: requestId,
                        result: result
                    }, event.origin);
                    console.log('[Proxy Mode] DOM查询响应已发送完成');
                    
                } catch (error) {
                    console.error('[Proxy Mode] DOM查询执行失败:', error);
                    
                    // 发送错误响应
                    event.source?.postMessage({
                        type: 'DOM_QUERY_RESPONSE',
                        requestId: requestId,
                        error: error.message || '查询执行失败'
                    }, event.origin);
                }
            }
        });
        
        // 🔄 拦截所有链接点击
        function interceptLinkClicks() {
            document.addEventListener('click', function(e) {
                const link = e.target.closest('a');
                if (!link || !link.href) return;
                
                const href = link.getAttribute('href');
                
                // 跳过特殊链接
                if (!href || href.startsWith('#') || href.startsWith('javascript:') || 
                    href.startsWith('mailto:') || href.startsWith('tel:')) {
                    return;
                }
                
                // 如果已经是代理链接，不需要处理
                if (href.includes('/api/proxy?url=')) {
                    return;
                }
                
                // 阻止默认行为
                e.preventDefault();
                e.stopPropagation();
                
                // 构建完整URL
                let fullUrl;
                try {
                    if (href.startsWith('http://') || href.startsWith('https://')) {
                        fullUrl = href;
                    } else if (href.startsWith('//')) {
                        fullUrl = window.location.protocol + href;
                    } else {
                        fullUrl = new URL(href, window.PROXY_BASE).href;
                    }
                } catch (err) {
                    console.warn('[Proxy Mode] 无法解析链接:', href, err);
                    return;
                }
                
                // 通过代理加载
                const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(fullUrl);
                console.log('[Proxy Mode] 拦截链接点击:', href, '->', proxyUrl);
                
                // 在当前窗口加载代理URL
                window.location.href = proxyUrl;
            }, true);
        }
        
        // 🔄 拦截表单提交
        function interceptFormSubmissions() {
            document.addEventListener('submit', function(e) {
                const form = e.target;
                if (!form || form.tagName !== 'FORM') return;
                
                let action = form.getAttribute('action') || form.action || '';
                console.log('[Proxy Mode] 表单提交拦截:', {
                    action: action,
                    method: form.method,
                    formAction: form.action,
                    currentURL: window.location.href
                });
                
                // 如果没有action，使用当前页面URL
                if (!action || action === '' || action === '#') {
                    action = window.location.href;
                    console.log('[Proxy Mode] 使用当前页面URL作为表单action:', action);
                }
                
                if (action.startsWith('#') || action.startsWith('javascript:')) {
                    return;
                }
                
                // 如果已经是代理链接，检查是否正确格式化
                if (action.includes('/api/proxy')) {
                    // 如果已经指向我们的代理但没有url参数，需要修复
                    if (!action.includes('url=')) {
                        e.preventDefault();
                        
                        // 重新构建正确的代理URL
                        let targetUrl;
                        if (window.location.hostname.includes('google.') && form.querySelector('[name="q"]')) {
                            // Google搜索特殊处理
                            targetUrl = new URL('/search', window.PROXY_BASE).href;
                        } else {
                            targetUrl = window.location.href;
                        }
                        
                        const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(targetUrl);
                        form.action = proxyUrl;
                        console.log('[Proxy Mode] 修复代理表单action:', proxyUrl);
                        
                        // 重新提交表单
                        form.submit();
                        return;
                    }
                    return;
                }
                
                // 构建完整URL
                let fullUrl;
                try {
                    if (action.startsWith('http://') || action.startsWith('https://')) {
                        fullUrl = action;
                    } else if (action.startsWith('//')) {
                        fullUrl = window.location.protocol + action;
                    } else {
                        fullUrl = new URL(action, window.PROXY_BASE).href;
                    }
                } catch (err) {
                    console.warn('[Proxy Mode] 无法解析表单action:', action, err);
                    return;
                }
                
                // 更新表单action为代理URL
                const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(fullUrl);
                form.action = proxyUrl;
                console.log('[Proxy Mode] 拦截表单提交:', action, '->', proxyUrl);
            }, true);
        }
        
        // 🆕 拦截fetch请求
        function interceptFetch() {
            if (typeof window.fetch === 'function') {
                const originalFetch = window.fetch;
                window.fetch = function(input, init = {}) {
                    let url = input;
                    if (typeof input === 'object' && input.url) {
                        url = input.url;
                    }
                    
                    // 只拦截导航类型的请求
                    if (init.method === 'GET' || !init.method) {
                        try {
                            let fullUrl;
                            if (url.startsWith('http://') || url.startsWith('https://')) {
                                fullUrl = url;
                            } else if (url.startsWith('//')) {
                                fullUrl = window.location.protocol + url;
                            } else if (!url.startsWith('#') && !url.startsWith('javascript:') && !url.startsWith('data:')) {
                                fullUrl = new URL(url, window.PROXY_BASE).href;
                            } else {
                                return originalFetch.call(this, input, init);
                            }
                            
                            // 如果是不同域名的请求，通过代理
                            const targetDomain = new URL(fullUrl).hostname;
                            const currentDomain = new URL(window.PROXY_BASE).hostname;
                            
                            if (targetDomain !== currentDomain) {
                                const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(fullUrl);
                                console.log('[Proxy Mode] 拦截fetch请求:', url, '->', proxyUrl);
                                
                                if (typeof input === 'string') {
                                    return originalFetch.call(this, proxyUrl, init);
                                } else {
                                    return originalFetch.call(this, { ...input, url: proxyUrl }, init);
                                }
                            }
                        } catch (err) {
                            console.warn('[Proxy Mode] fetch拦截处理失败:', url, err);
                        }
                    }
                    
                    return originalFetch.call(this, input, init);
                };
            }
        }
        
        // 🆕 拦截XMLHttpRequest
        function interceptXHR() {
            if (typeof XMLHttpRequest !== 'undefined') {
                const originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                    // 只拦截GET请求的导航
                    if (method.toLowerCase() === 'get') {
                        try {
                            let fullUrl;
                            if (url.startsWith('http://') || url.startsWith('https://')) {
                                fullUrl = url;
                            } else if (url.startsWith('//')) {
                                fullUrl = window.location.protocol + url;
                            } else if (!url.startsWith('#') && !url.startsWith('javascript:') && !url.startsWith('data:')) {
                                fullUrl = new URL(url, window.PROXY_BASE).href;
                            } else {
                                return originalOpen.call(this, method, url, async, user, password);
                            }
                            
                            // 如果是不同域名的请求，通过代理
                            const targetDomain = new URL(fullUrl).hostname;
                            const currentDomain = new URL(window.PROXY_BASE).hostname;
                            
                            if (targetDomain !== currentDomain) {
                                const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(fullUrl);
                                console.log('[Proxy Mode] 拦截XHR请求:', url, '->', proxyUrl);
                                return originalOpen.call(this, method, proxyUrl, async, user, password);
                            }
                        } catch (err) {
                            console.warn('[Proxy Mode] XHR拦截处理失败:', url, err);
                        }
                    }
                    
                    return originalOpen.call(this, method, url, async, user, password);
                };
            }
        }
        
        // 🔄 拦截window.open
        const originalOpen = window.open;
        window.open = function(url, ...args) {
            if (!url) return originalOpen.call(this, url, ...args);
            
            let fullUrl;
            try {
                if (url.startsWith('http://') || url.startsWith('https://')) {
                    fullUrl = url;
                } else if (url.startsWith('//')) {
                    fullUrl = window.location.protocol + url;
                } else if (!url.startsWith('#') && !url.startsWith('javascript:')) {
                    fullUrl = new URL(url, window.PROXY_BASE).href;
                } else {
                    return originalOpen.call(this, url, ...args);
                }
            } catch (err) {
                return originalOpen.call(this, url, ...args);
            }
            
            const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(fullUrl);
            console.log('[Proxy Mode] 拦截window.open:', url, '->', proxyUrl);
            return originalOpen.call(this, proxyUrl, ...args);
        };
        
        // 🔄 拦截window.location赋值
        const originalLocation = window.location;
        Object.defineProperty(window, 'location', {
            get: function() {
                return originalLocation;
            },
            set: function(url) {
                if (!url) {
                    originalLocation.href = url;
                    return;
                }
                
                let fullUrl;
                try {
                    if (url.startsWith('http://') || url.startsWith('https://')) {
                        fullUrl = url;
                    } else if (url.startsWith('//')) {
                        fullUrl = window.location.protocol + url;
                    } else if (!url.startsWith('#') && !url.startsWith('javascript:')) {
                        fullUrl = new URL(url, window.PROXY_BASE).href;
                    } else {
                        originalLocation.href = url;
                        return;
                    }
                } catch (err) {
                    originalLocation.href = url;
                    return;
                }
                
                const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(fullUrl);
                console.log('[Proxy Mode] 拦截location设置:', url, '->', proxyUrl);
                originalLocation.href = proxyUrl;
            }
        });
        
        // 🆕 增强的资源加载错误处理
        function handleResourceErrors() {
            document.addEventListener('error', function(e) {
                const element = e.target;
                if (element.tagName === 'LINK' || element.tagName === 'SCRIPT' || element.tagName === 'IMG') {
                    console.warn('[Proxy Mode] 资源加载失败:', element.src || element.href);
                    // 可以在这里添加资源加载失败的处理逻辑
                }
            }, true);
        }
        


        // 监听DOM变化，处理动态添加的表单和链接
        function startDynamicFormMonitor() {
            if (typeof MutationObserver !== 'undefined') {
                const observer = new MutationObserver(function(mutations) {
                    let needsReprocessing = false;
                    
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            // 检查是否有新添加的表单
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === Node.ELEMENT_NODE) {
                                    const element = node;
                                    
                                    // 检查是否是表单或包含表单
                                    if (element.tagName === 'FORM' || element.querySelector('form')) {
                                        needsReprocessing = true;
                                    }
                                    
                                    // 检查是否包含搜索输入框
                                    if (element.querySelector('input[name="q"], input[type="search"], input[name="query"]')) {
                                        needsReprocessing = true;
                                    }
                                }
                            });
                        }
                        
                        // 检查属性变化（如表单action被动态修改）
                        if (mutation.type === 'attributes' && mutation.target.tagName === 'FORM') {
                            if (mutation.attributeName === 'action') {
                                needsReprocessing = true;
                            }
                        }
                    });
                    
                    if (needsReprocessing) {
                        // 延迟重新处理，避免过于频繁的调用
                        setTimeout(() => {
                            console.log('[Proxy Mode] 检测到DOM变化，重新处理表单');
                            processAllForms();
                            handleSpecialSearchEngines();
                        }, 100);
                    }
                });
                
                // 开始观察
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    attributeFilter: ['action', 'href']
                });
                
                console.log('[Proxy Mode] DOM变化监视器已启动');
            }
        }
        
        // 🆕 处理所有现有表单
        function processAllForms() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                let action = form.getAttribute('action') || form.action || '';
                
                // 如果没有action或action为空，根据页面类型设置默认action
                if (!action || action === '' || action === '#') {
                    if (window.location.hostname.includes('google.') && form.querySelector('[name="q"]')) {
                        action = '/search';
                    } else {
                        action = window.location.href;
                    }
                }
                
                if (action.startsWith('#') || action.startsWith('javascript:')) {
                    return;
                }
                
                // 如果已经是代理链接，检查格式是否正确
                if (action.includes('/api/proxy')) {
                    if (!action.includes('url=')) {
                        // 修复不完整的代理URL
                        let targetUrl;
                        if (window.location.hostname.includes('google.') && form.querySelector('[name="q"]')) {
                            targetUrl = new URL('/search', window.PROXY_BASE).href;
                        } else {
                            targetUrl = window.location.href;
                        }
                        
                        const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(targetUrl);
                        form.action = proxyUrl;
                        console.log('[Proxy Mode] 修复预处理表单:', action, '->', proxyUrl);
                    }
                    return;
                }
                
                try {
                    let fullUrl;
                    if (action.startsWith('http://') || action.startsWith('https://')) {
                        fullUrl = action;
                    } else if (action.startsWith('//')) {
                        fullUrl = window.location.protocol + action;
                    } else {
                        fullUrl = new URL(action, window.PROXY_BASE).href;
                    }
                    
                    const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(fullUrl);
                    form.action = proxyUrl;
                    console.log('[Proxy Mode] 预处理表单:', action, '->', proxyUrl);
                } catch (err) {
                    console.warn('[Proxy Mode] 表单预处理失败:', action, err);
                }
            });
        }

        // 🆕 特殊处理Google搜索和其他常见搜索引擎
        function handleSpecialSearchEngines() {
            // 修复Google搜索表单的辅助函数
            function fixGoogleSearchForm(form) {
                if (!form || !form.querySelector('[name="q"]')) return;
                
                let action = form.action || form.getAttribute('action') || '';
                console.log('[Proxy Mode] 检查Google表单action:', action);
                
                // 如果action指向我们的代理但格式不正确，修复它
                if (action.includes('/api/proxy') && !action.includes('url=')) {
                    const targetUrl = new URL('/search', window.PROXY_BASE).href;
                    const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(targetUrl);
                    form.action = proxyUrl;
                    console.log('[Proxy Mode] 修复Google表单action:', proxyUrl);
                    return true;
                }
                
                // 如果action为空或不正确，设置为搜索
                if (!action || action === '' || action === window.location.pathname || action === '/') {
                    const targetUrl = new URL('/search', window.PROXY_BASE).href;
                    const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(targetUrl);
                    form.action = proxyUrl;
                    console.log('[Proxy Mode] 设置Google表单action:', proxyUrl);
                    return true;
                }
                
                return false;
            }
            
            // Google搜索特殊处理
            if (window.location.hostname.includes('google.')) {
                // 立即修复所有Google搜索表单
                const googleForms = document.querySelectorAll('form');
                googleForms.forEach(form => {
                    if (form.querySelector('[name="q"]')) {
                        fixGoogleSearchForm(form);
                    }
                });
                
                // 监听搜索框的Enter键
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        const target = e.target;
                        if (target && (target.name === 'q' || target.type === 'search')) {
                            const form = target.closest('form');
                            if (form) {
                                // 在提交前修复表单
                                fixGoogleSearchForm(form);
                            }
                        }
                    }
                }, true);
                
                // 监听搜索按钮点击
                document.addEventListener('click', function(e) {
                    const target = e.target;
                    if (target && (target.type === 'submit' || target.getAttribute('jsaction') || target.classList.contains('gNO89b'))) {
                        const form = target.closest('form');
                        if (form && form.querySelector('[name="q"]')) {
                            // 在点击时立即修复表单
                            fixGoogleSearchForm(form);
                        }
                    }
                }, true);
                
                // 监听表单提交事件（作为最后的保障）
                document.addEventListener('submit', function(e) {
                    const form = e.target;
                    if (form && form.querySelector('[name="q"]')) {
                        const wasFixed = fixGoogleSearchForm(form);
                        if (wasFixed) {
                            console.log('[Proxy Mode] 在提交时修复了Google表单');
                        }
                    }
                }, true);
            }
            
            // 通用搜索引擎处理
            const searchInputs = document.querySelectorAll('input[type="search"], input[name="q"], input[name="query"], input[name="search"]');
            searchInputs.forEach(input => {
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        const form = input.closest('form');
                        if (form) {
                            setTimeout(() => {
                                let action = form.action || form.getAttribute('action') || '';
                                if (!action || action === '') {
                                    action = window.location.href;
                                }
                                
                                if (!action.includes('/api/proxy?url=')) {
                                    try {
                                        const fullUrl = new URL(action, window.PROXY_BASE).href;
                                        const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(fullUrl);
                                        form.action = proxyUrl;
                                        console.log('[Proxy Mode] 通用搜索表单处理:', action, '->', proxyUrl);
                                    } catch (err) {
                                        console.warn('[Proxy Mode] 通用搜索表单处理失败:', err);
                                    }
                                }
                            }, 10);
                        }
                    }
                });
            });
        }

        // 🆕 监听DOM变化，处理动态添加的表单和链接
        function startDynamicLinkMonitor() {
            if (typeof MutationObserver !== 'undefined') {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === 1) { // Element node
                                    // 处理新添加的链接
                                    const links = node.querySelectorAll ? node.querySelectorAll('a[href]') : [];
                                    links.forEach(function(link) {
                                        const href = link.getAttribute('href');
                                        if (href && !href.startsWith('#') && !href.startsWith('javascript:') && 
                                            !href.startsWith('mailto:') && !href.startsWith('tel:') &&
                                            !href.includes('/api/proxy?url=')) {
                                            
                                            let fullUrl;
                                            try {
                                                if (href.startsWith('http://') || href.startsWith('https://')) {
                                                    fullUrl = href;
                                                } else if (href.startsWith('//')) {
                                                    fullUrl = window.location.protocol + href;
                                                } else {
                                                    fullUrl = new URL(href, window.PROXY_BASE).href;
                                                }
                                                
                                                const proxyUrl = window.PROXY_ORIGIN + '/api/proxy?url=' + encodeURIComponent(fullUrl);
                                                link.setAttribute('href', proxyUrl);
                                                console.log('[Proxy Mode] 修复动态链接:', href, '->', proxyUrl);
                                            } catch (err) {
                                                console.warn('[Proxy Mode] 无法处理动态链接:', href, err);
                                            }
                                        }
                                    });
                                }
                            });
                        }
                    });
                });
                
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
                
                console.log('[Proxy Mode] 动态链接监视器已启动');
            }
        }

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                interceptLinkClicks();
                interceptFormSubmissions();
                interceptFetch();
                interceptXHR();
                processAllForms();
                handleSpecialSearchEngines();
                handleResourceErrors();
                startDynamicLinkMonitor();
                startDynamicFormMonitor();
                console.log('[Proxy Mode] 所有拦截器和监视器已启动 (DOM查询支持在脚本顶部已启动)');
            });
        } else {
            interceptLinkClicks();
            interceptFormSubmissions();
            interceptFetch();
            interceptXHR();
            processAllForms();
            handleSpecialSearchEngines();
            handleResourceErrors();
            startDynamicLinkMonitor();
            startDynamicFormMonitor();
            console.log('[Proxy Mode] 所有拦截器和监视器已启动 (DOM查询支持在脚本顶部已启动)');
        }
    </script>
    `;

    html = html.replace(/<\/head>/i, `${proxyScript}</head>`);

    return html;
}

/**
 * 生成友好的错误页面
 */
function generateErrorPage(error: string, url: string): string {
    return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>代理加载失败</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                margin: 0;
                padding: 40px 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .error-container {
                background: rgba(255, 255, 255, 0.1);
                padding: 40px;
                border-radius: 20px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                text-align: center;
                max-width: 500px;
                width: 100%;
            }
            .error-icon {
                font-size: 4rem;
                margin-bottom: 20px;
            }
            h1 {
                margin: 0 0 20px 0;
                font-size: 1.5rem;
                font-weight: 600;
            }
            .error-message {
                background: rgba(255, 255, 255, 0.1);
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
                font-family: monospace;
                font-size: 0.9rem;
                word-break: break-all;
            }
            .url {
                background: rgba(255, 255, 255, 0.1);
                padding: 10px;
                border-radius: 8px;
                margin: 15px 0;
                font-size: 0.85rem;
                word-break: break-all;
            }
            .retry-button {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: all 0.3s ease;
                margin-top: 20px;
            }
            .retry-button:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }
            .tips {
                margin-top: 30px;
                font-size: 0.8rem;
                opacity: 0.8;
                line-height: 1.5;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">🌐</div>
            <h1>代理加载失败</h1>
            <p>无法通过代理访问目标网站</p>
            
            <div class="url">
                <strong>目标URL:</strong><br>
                ${url}
            </div>
            
            <div class="error-message">
                <strong>错误信息:</strong><br>
                ${error}
            </div>
            
            <button class="retry-button" onclick="location.reload()">
                🔄 重试加载
            </button>
            
            <div class="tips">
                💡 <strong>可能的原因:</strong><br>
                • 目标网站服务器繁忙或不可达<br>
                • 网站阻止了代理访问<br>
                • 网络连接问题<br>
                • 目标网站需要特殊的认证
            </div>
        </div>
    </body>
    </html>
    `;
}

// 使用CORS中间件包装
export const GET = withCors(proxyHandler); 