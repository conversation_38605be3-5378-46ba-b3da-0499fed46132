import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";

// 获取单个评论及其回复
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  try {
    const comment = await prisma.publishComment.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        replies: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
    });

    if (!comment) {
      return NextResponse.json({ error: "评论不存在" }, { status: 404 });
    }

    return NextResponse.json(comment);
  } catch (error) {
    console.error("获取评论失败:", error);
    return NextResponse.json({ error: "获取评论失败" }, { status: 500 });
  }
}

// 更新评论
export async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  // 使用Supabase路由客户端获取当前用户
  const supabase = await getSupabaseRoute();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  const { id } = await params;

  if (!user) {
    return NextResponse.json({ error: "需要登录" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { text } = body;

    if (!text) {
      return NextResponse.json({ error: "评论内容不能为空" }, { status: 400 });
    }

    // 获取当前评论检查权限
    const existingComment = await prisma.publishComment.findUnique({
      where: { id },
      include: { user: true },
    });

    if (!existingComment) {
      return NextResponse.json({ error: "评论不存在" }, { status: 404 });
    }

    // 获取数据库中的用户
    const dbUser = await prisma.user.findUnique({
      where: { email: user.email as string },
    });

    if (!dbUser || existingComment.userId !== dbUser.id) {
      return NextResponse.json({ error: "没有权限修改此评论" }, { status: 403 });
    }

    // 更新评论
    const updatedComment = await prisma.publishComment.update({
      where: { id },
      data: { text },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json(updatedComment);
  } catch (error) {
    console.error("更新评论失败:", error);
    return NextResponse.json({ error: "更新评论失败" }, { status: 500 });
  }
}

// 删除评论
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  // 使用Supabase路由客户端获取当前用户
  const supabase = await getSupabaseRoute();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  const { id } = await params;

  if (!user) {
    return NextResponse.json({ error: "需要登录" }, { status: 401 });
  }

  try {
    let body;
    try {
      body = await request.json();
    } catch (e) {
      body = {}; // 如果请求体为空，设置为空对象
    }

    const cascade = body?.cascade || false;

    // 获取当前评论检查权限
    const existingComment = await prisma.publishComment.findUnique({
      where: { id },
      include: {
        user: true,
        // 如果可能需要级联删除，获取子评论
        replies: cascade
          ? {
              select: { id: true },
            }
          : undefined,
      },
    });

    if (!existingComment) {
      return NextResponse.json({ error: "评论不存在" }, { status: 404 });
    }

    // 获取数据库中的用户
    const dbUser = await prisma.user.findUnique({
      where: { email: user.email as string },
    });

    if (!dbUser || existingComment.userId !== dbUser.id) {
      return NextResponse.json({ error: "没有权限删除此评论" }, { status: 403 });
    }

    // 如果有子评论且请求级联删除，则先删除所有子评论
    if (cascade && existingComment.replies && existingComment.replies.length > 0) {
      // 获取所有子评论ID
      const replyIds = existingComment.replies.map(reply => reply.id);

      // 批量删除子评论
      await prisma.publishComment.deleteMany({
        where: {
          id: { in: replyIds },
        },
      });
    }

    // 删除主评论
    await prisma.publishComment.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("删除评论失败:", error);
    return NextResponse.json({ error: "删除评论失败" }, { status: 500 });
  }
}
