import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { NotificationType } from "@prisma/client";

// 获取特定文章的所有评论
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const paperId = url.searchParams.get("paperId");

  if (!paperId) {
    return NextResponse.json({ error: "需要提供paperId参数" }, { status: 400 });
  }

  try {
    // 获取顶级评论及其回复
    const comments = await prisma.publishComment.findMany({
      where: {
        paperId,
        parentId: null, // 只获取顶级评论
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            avatarColor: true,
          },
        },
        replies: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
                avatarColor: true,
              },
            },
          },
          orderBy: {
            createdAt: "asc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(comments);
  } catch (error) {
    console.error("获取评论失败:", error);
    return NextResponse.json({ error: "获取评论失败" }, { status: 500 });
  }
}

// 创建新评论
export async function POST(request: NextRequest) {
  // 使用Supabase路由客户端获取当前用户
  const supabase = await getSupabaseRoute();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: "需要登录" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const {
      text,
      paperId,
      highlightId,
      pageIndex,
      yPosition,
      pageTop,
      parentId,
      x,
      y,
      width,
      height,
      replyToUserId, // 新增：被回复人的ID
      notifyAuthor, // 新增：是否通知作者的选项
    } = body;

    if (!text || !paperId) {
      return NextResponse.json({ error: "评论内容和文章ID不能为空" }, { status: 400 });
    }

    // 获取用户信息
    const dbUser = await prisma.user.findUnique({
      where: { email: user.email as string },
    });

    if (!dbUser) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 获取论文信息（用于通知）
    const paper = await prisma.paper.findUnique({
      where: { id: paperId },
      select: {
        id: true,
        title: true,
        authorId: true,
        author: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!paper) {
      return NextResponse.json({ error: "论文不存在" }, { status: 404 });
    }

    // 创建评论
    const comment = await prisma.publishComment.create({
      data: {
        text,
        paperId,
        highlightId: highlightId || undefined,
        pageIndex: pageIndex || undefined,
        yPosition: yPosition || undefined,
        pageTop: pageTop || undefined,
        x: x || undefined,
        y: y || undefined,
        width: width || undefined,
        height: height || undefined,
        userId: dbUser.id,
        parentId: parentId || undefined,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            avatarColor: true,
          },
        },
      },
    });

    // 创建通知
    try {
      let notificationData: {
        type: NotificationType;
        recipientIds: string[];
      } | null = null;

      const preview = text.substring(0, 100); // 前100个字符作为预览

      if (parentId && replyToUserId) {
        // 回复评论：通知被回复的人
        const recipientIds = [replyToUserId];

        // 如果勾选了notifyAuthor且论文作者不是当前用户且不是被回复的人，则也通知作者
        if (notifyAuthor && paper.authorId !== dbUser.id && paper.authorId !== replyToUserId) {
          recipientIds.push(paper.authorId);
        }

        notificationData = {
          type: NotificationType.REPLY_ON_COMMENT,
          recipientIds: recipientIds,
        };
      } else if (!parentId) {
        // 新评论：通知论文作者（如果不是作者自己评论）
        if (paper.authorId !== dbUser.id) {
          notificationData = {
            type: NotificationType.COMMENT_ON_PAPER,
            recipientIds: [paper.authorId],
          };
        }
      }

      // 如果有需要通知的用户，创建通知
      if (notificationData && notificationData.recipientIds.length > 0) {
        const notification = await prisma.notification.create({
          data: {
            actorId: dbUser.id,
            type: notificationData.type,
            paperId: paperId,
            commentId: comment.id,
            preview: preview,
            paperTitle: paper.title,
            actorName: dbUser.name,
          },
        });

        // 为每个收件人创建通知记录
        await prisma.notificationRecipient.createMany({
          data: notificationData.recipientIds.map(userId => ({
            notificationId: notification.id,
            userId: userId,
          })),
        });

        console.log(`已创建通知，类型: ${notificationData.type}, 收件人: ${notificationData.recipientIds.join(', ')}`);
      }
    } catch (notificationError) {
      // 通知创建失败不应该影响评论创建
      console.error("创建通知失败:", notificationError);
    }

    return NextResponse.json(comment);
  } catch (error) {
    console.error("创建评论失败:", error);
    return NextResponse.json({ error: "创建评论失败" }, { status: 500 });
  }
}
