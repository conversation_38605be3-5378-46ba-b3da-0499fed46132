/**
 * Review Comment Visibility API
 * 专门处理审稿评论的可见性控制
 */

import { Role, ScopeType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";
import { auditLog } from "@/lib/security/audit";
import { getClientIP, getUserAgent, createSecureHeaders } from "@/lib/security/utils";

/* 请求体验证 Schema */
const bodySchema = z
  .object({
    visibleToAuthor: z.boolean().optional(),
    visibleToReviewers: z.boolean().optional(),
  })
  .refine(
    d => typeof d.visibleToAuthor !== "undefined" || typeof d.visibleToReviewers !== "undefined",
    { message: "Payload must include at least one field" }
  );

const paramsSchema = z.object({
  id: z.string().cuid("Invalid comment ID format"),
});

/**
 * PATCH /api/review-comments/[id]/visibility
 * 更新审稿评论的可见性设置
 */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const headers = createSecureHeaders();
  
  try {
    /* 1. 参数验证 */
    const { id } = await params;
    const { id: commentId } = paramsSchema.parse({ id });

    /* 2. 请求体验证 */
    const body = bodySchema.parse(await req.json());

    /* 3. 身份验证 */
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401, headers }
      );
    }

    const userId = session.user.id;
    const clientIP = await getClientIP(req);
    const userAgent = await getUserAgent(req);

    /* 4. 获取评论和论文信息 */
    const comment = await prisma.reviewComment.findUnique({
      where: { id: commentId },
      select: { 
        id: true,
        paperId: true, 
        visibleToAuthor: true, 
        visibleToReviewers: true 
      },
    });

    if (!comment) {
      return NextResponse.json(
        { error: "Comment not found" }, 
        { status: 404, headers }
      );
    }

    const paper = await prisma.paper.findUnique({
      where: { id: comment.paperId },
      select: { 
        id: true,
        issueId: true,
        authorId: true 
      },
    });

    if (!paper) {
      return NextResponse.json(
        { error: "Paper not found" }, 
        { status: 404, headers }
      );
    }

    /* 5. 权限验证 - 只有编辑级别的用户可以修改可见性 */
    const roleBindings = await prisma.roleBinding.findMany({
      where: { 
        principalId: userId, 
        principalType: "USER" 
      },
      select: { 
        role: true, 
        scopeType: true, 
        scopeId: true 
      },
    });

    // 检查是否有编辑权限
    const hasEditPermission = roleBindings.some(binding => {
      // 全局管理员权限
      if ((binding.role === Role.ADMIN || binding.role === Role.CHIEF_EDITOR) && 
          binding.scopeType === ScopeType.GLOBAL) {
        return true;
      }

      // 期刊编辑权限
      if (binding.role === Role.ISSUE_EDITOR && 
          binding.scopeType === ScopeType.ISSUE && 
          binding.scopeId === paper.issueId) {
        return true;
      }

      // 论文编辑权限
      if (binding.role === Role.PAPER_EDITOR &&
          binding.scopeType === ScopeType.PAPER &&
          binding.scopeId === paper.id) {
        return true;
      }

      return false;
    });

    if (!hasEditPermission) {
      // 审计日志记录权限拒绝
      await auditLog.create({
        userId,
        action: 'COMMENT_VISIBILITY_DENIED',
        resourceType: 'REVIEW_COMMENT',
        resourceId: commentId,
        reason: 'Insufficient permissions',
        metadata: {
          paperId: comment.paperId,
          requestedChanges: body,
        },
        ipAddress: clientIP,
        userAgent: userAgent,
      });

      return NextResponse.json(
        { error: "Forbidden - Editor permissions required" }, 
        { status: 403, headers }
      );
    }

    /* 6. 检查是否有实际变更 */
    const hasChanges = (
      (typeof body.visibleToAuthor !== "undefined" && body.visibleToAuthor !== comment.visibleToAuthor) ||
      (typeof body.visibleToReviewers !== "undefined" && body.visibleToReviewers !== comment.visibleToReviewers)
    );

    if (!hasChanges) {
      return NextResponse.json({
        success: true,
        id: comment.id,
        visibleToAuthor: comment.visibleToAuthor,
        visibleToReviewers: comment.visibleToReviewers,
        message: "No changes needed"
      }, { headers });
    }

    /* 7. 更新可见性设置 */
    const updatedComment = await prisma.reviewComment.update({
      where: { id: commentId },
      data: {
        visibleToAuthor: body.visibleToAuthor ?? comment.visibleToAuthor,
        visibleToReviewers: body.visibleToReviewers ?? comment.visibleToReviewers,
      },
      select: {
        id: true,
        visibleToAuthor: true,
        visibleToReviewers: true,
      }
    });

    /* 8. 审计日志记录 */
    await auditLog.create({
      userId,
      action: 'COMMENT_VISIBILITY_UPDATED',
      resourceType: 'REVIEW_COMMENT',
      resourceId: commentId,
      reason: 'Visibility settings changed',
      metadata: {
        paperId: comment.paperId,
        oldValues: {
          visibleToAuthor: comment.visibleToAuthor,
          visibleToReviewers: comment.visibleToReviewers,
        },
        newValues: {
          visibleToAuthor: updatedComment.visibleToAuthor,
          visibleToReviewers: updatedComment.visibleToReviewers,
        },
        changes: body,
      },
      ipAddress: clientIP,
      userAgent: userAgent,
    });

    return NextResponse.json({
      success: true,
      id: updatedComment.id,
      visibleToAuthor: updatedComment.visibleToAuthor,
      visibleToReviewers: updatedComment.visibleToReviewers,
    }, { headers });

  } catch (error) {
    console.error('Visibility update failed:', error);
    
    return NextResponse.json(
      { 
        error: "Internal server error",
        details: error instanceof z.ZodError ? error.errors : undefined
      }, 
      { status: 500, headers }
    );
  }
}
