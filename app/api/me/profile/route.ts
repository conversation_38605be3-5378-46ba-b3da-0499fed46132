/**
 * 用户个人资料API
 * 获取当前用户的完整数据库信息，包括 avatarColor 等字段
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/utils/supabase/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
    try {
        // 获取当前用户
        const supabase = await createRouteHandlerClient();
        const { data: { user }, error: authError } = await supabase.auth.getUser();

        if (authError || !user) {
            return NextResponse.json(
                { error: '未授权访问' },
                { status: 401 }
            );
        }

        // 查询用户完整信息
        const userData = await prisma.user.findUnique({
            where: { id: user.id },
            select: {
                id: true,
                name: true,
                email: true,
                emailVerified: true,
                image: true,
                avatarColor: true,
                createdAt: true,
                aiReqRest: true,
                aiReqTotal: true,
                aiModel: true,
                aiLastReqTime: true
            }
        });

        if (!userData) {
            return NextResponse.json(
                { error: '用户信息不存在' },
                { status: 404 }
            );
        }

        // 返回用户信息
        return NextResponse.json(userData);

    } catch (error) {
        console.error('用户资料查询API错误:', error);

        return NextResponse.json(
            {
                error: '服务器内部错误',
                details: process.env.NODE_ENV === 'development' ?
                    (error instanceof Error ? error.message : '未知错误') : undefined
            },
            { status: 500 }
        );
    }
}
