// app/api/me/route.ts
import { NextRequest, NextResponse } from "next/server";

import { withCors } from "@/utils/cors";
import { createRouteHandlerClient } from "@/utils/supabase/server";

async function handler(req: NextRequest) {
  try {
    // 使用新的 SSR 创建客户端
    const supabase = await createRouteHandlerClient();

    // 先验证用户身份
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: "未授权" }, { status: 401 });
    }

    // 从用户元数据中获取角色，更详细地记录
    console.log("用户元数据:", JSON.stringify(user.user_metadata));

    // 兼容不同结构的角色数据
    let roleData = user.user_metadata?.roles || [];

    // 确保角色始终为数组
    if (!Array.isArray(roleData)) {
      roleData = roleData ? [roleData] : [];
    }

    // 角色兼容处理
    const roles = roleData.map((r: any) => {
      // 如果直接是字符串，转为对象
      if (typeof r === "string") {
        return { role: r, scope: "GLOBAL" };
      }
      return r;
    });

    return NextResponse.json({
      id: user.id,
      email: user.email,
      roles,
      metadata: user.user_metadata, // 返回全部元数据便于调试
    });
  } catch (error) {
    console.error("获取用户信息失败:", error);
    return NextResponse.json({ error: "获取用户信息失败" }, { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const GET = withCors(handler);
