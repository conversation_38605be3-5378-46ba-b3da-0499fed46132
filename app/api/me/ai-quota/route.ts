/**
 * 用户AI额度查询API
 * 用于Terminal显示用户剩余额度信息
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/utils/supabase/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
    try {
        // 获取当前用户
        const supabase = await createRouteHandlerClient();
        const { data: { user }, error: authError } = await supabase.auth.getUser();

        if (authError || !user) {
            return NextResponse.json(
                { error: '未授权访问' },
                { status: 401 }
            );
        }

        // 查询用户AI额度信息
        const userData = await prisma.user.findUnique({
            where: { id: user.id },
            select: {
                aiReqRest: true,
                aiReqTotal: true,
                aiModel: true,
                aiLastReqTime: true
            }
        });

        if (!userData) {
            return NextResponse.json(
                { error: '用户信息不存在' },
                { status: 404 }
            );
        }

        // 返回额度信息
        return NextResponse.json({
            remaining: userData.aiReqRest || 0,
            total: userData.aiReqTotal || 0,
            model: userData.aiModel || 'deepseek-chat',
            lastRequestTime: userData.aiLastReqTime,
            // 计算总配额（假设默认为100）
            quota: 100, // 可以根据用户等级动态调整
        });

    } catch (error) {
        console.error('AI额度查询API错误:', error);

        return NextResponse.json(
            {
                error: '服务器内部错误',
                details: process.env.NODE_ENV === 'development' ?
                    (error instanceof Error ? error.message : '未知错误') : undefined
            },
            { status: 500 }
        );
    }
} 