/**
 * 健康检查端点
 * 用于监控应用状态和依赖服务
 */

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { createSecureResponse } from "@/lib/security/middleware";

interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  checks: {
    database: HealthCheckResult;
    memory: HealthCheckResult;
    disk: HealthCheckResult;
    auditLog: HealthCheckResult;
  };
}

interface HealthCheckResult {
  status: 'pass' | 'warn' | 'fail';
  message: string;
  responseTime?: number;
  details?: any;
}

/**
 * GET /api/health
 * 返回应用健康状态
 */
export async function GET(req: NextRequest) {
  const startTime = Date.now();
  
  try {
    // 并行执行所有健康检查
    const [
      databaseCheck,
      memoryCheck,
      diskCheck,
      auditLogCheck
    ] = await Promise.allSettled([
      checkDatabase(),
      checkMemory(),
      checkDisk(),
      checkAuditLog()
    ]);

    // 处理检查结果
    const checks = {
      database: getCheckResult(databaseCheck),
      memory: getCheckResult(memoryCheck),
      disk: getCheckResult(diskCheck),
      auditLog: getCheckResult(auditLogCheck),
    };

    // 确定整体状态
    const overallStatus = determineOverallStatus(checks);

    const healthCheck: HealthCheck = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks,
    };

    const statusCode = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;

    return createSecureResponse(healthCheck, statusCode, {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'X-Health-Check-Duration': `${Date.now() - startTime}ms`,
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return createSecureResponse({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      uptime: process.uptime(),
    }, 503);
  }
}

/**
 * 检查数据库连接
 */
async function checkDatabase(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    // 执行简单查询测试连接
    await prisma.$queryRaw`SELECT 1`;
    
    const responseTime = Date.now() - startTime;
    
    return {
      status: responseTime < 1000 ? 'pass' : 'warn',
      message: responseTime < 1000 ? 'Database connection healthy' : 'Database response slow',
      responseTime,
    };
  } catch (error) {
    return {
      status: 'fail',
      message: 'Database connection failed',
      responseTime: Date.now() - startTime,
      details: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * 检查内存使用情况
 */
async function checkMemory(): Promise<HealthCheckResult> {
  const memUsage = process.memoryUsage();
  const totalMem = memUsage.heapTotal;
  const usedMem = memUsage.heapUsed;
  const memoryUsagePercent = (usedMem / totalMem) * 100;

  let status: 'pass' | 'warn' | 'fail' = 'pass';
  let message = 'Memory usage normal';

  if (memoryUsagePercent > 90) {
    status = 'fail';
    message = 'Critical memory usage';
  } else if (memoryUsagePercent > 75) {
    status = 'warn';
    message = 'High memory usage';
  }

  return {
    status,
    message,
    details: {
      heapUsed: `${Math.round(usedMem / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(totalMem / 1024 / 1024)}MB`,
      usagePercent: `${memoryUsagePercent.toFixed(1)}%`,
      external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
      rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
    },
  };
}

/**
 * 检查磁盘空间（简化版）
 */
async function checkDisk(): Promise<HealthCheckResult> {
  try {
    // 在生产环境中，这里应该检查实际的磁盘使用情况
    // 目前返回一个模拟的检查结果
    return {
      status: 'pass',
      message: 'Disk space sufficient',
      details: {
        note: 'Disk check not implemented - consider adding fs.statSync for production',
      },
    };
  } catch (error) {
    return {
      status: 'warn',
      message: 'Could not check disk space',
      details: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * 检查审计日志系统
 */
async function checkAuditLog(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    // 检查审计日志表是否可访问
    const recentLogs = await prisma.auditLog.count({
      where: {
        timestamp: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // 最近24小时
        },
      },
    });

    const responseTime = Date.now() - startTime;

    return {
      status: 'pass',
      message: 'Audit log system operational',
      responseTime,
      details: {
        recentLogsCount: recentLogs,
        period: '24 hours',
      },
    };
  } catch (error) {
    return {
      status: 'fail',
      message: 'Audit log system error',
      responseTime: Date.now() - startTime,
      details: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * 处理 Promise.allSettled 结果
 */
function getCheckResult(result: PromiseSettledResult<HealthCheckResult>): HealthCheckResult {
  if (result.status === 'fulfilled') {
    return result.value;
  } else {
    return {
      status: 'fail',
      message: 'Health check failed',
      details: result.reason instanceof Error ? result.reason.message : 'Unknown error',
    };
  }
}

/**
 * 确定整体健康状态
 */
function determineOverallStatus(checks: Record<string, HealthCheckResult>): 'healthy' | 'degraded' | 'unhealthy' {
  const results = Object.values(checks);
  
  const failCount = results.filter(r => r.status === 'fail').length;
  const warnCount = results.filter(r => r.status === 'warn').length;

  if (failCount > 0) {
    return 'unhealthy';
  } else if (warnCount > 0) {
    return 'degraded';
  } else {
    return 'healthy';
  }
}

/**
 * HEAD /api/health
 * 简化的健康检查，仅返回状态码
 */
export async function HEAD(req: NextRequest) {
  try {
    // 快速数据库检查
    await prisma.$queryRaw`SELECT 1`;
    return new NextResponse(null, { status: 200 });
  } catch {
    return new NextResponse(null, { status: 503 });
  }
}
