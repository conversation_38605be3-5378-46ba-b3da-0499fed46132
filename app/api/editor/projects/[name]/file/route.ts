// app/api/editor/projects/[name]/file/route.ts
import { createClient } from "@supabase/supabase-js";
import { NextResponse, NextRequest } from "next/server";

import {
  generateTimestampFilename,
  isTimestampFile,
  findLatestFile,
  getFilesToKeep,
  getHistoryPath,
  findFileOfType,
} from "@/app/editor/[name]/utils/filenameUtils";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* ---------- 计算前缀 ---------- */
async function getPrefix(req: NextRequest): Promise<string> {
  // 从URL中获取动态路由参数
  const { pathname } = req.nextUrl;
  const segments = pathname.split("/");
  const name = segments[segments.length - 2]; // [name]/file 格式获取name参数

  if (!name) {
    throw new Error("Missing project name");
  }

  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) throw new Error("Unauthorized");

  const uid = session.user.id;
  return `${uid}/paperEditor/${decodeURIComponent(name)}`;
}

/* ---------- GET /file?path=xxx ---------- */
async function getHandler(req: NextRequest) {
  try {
    const pfx = await getPrefix(req);
    const path = req.nextUrl.searchParams.get("path");
    if (!path) return NextResponse.json("Missing path", { status: 400 });

    // 处理特殊文件请求 (paper.md 和 references.bib)
    // 这里自动查找最新的带时间戳版本
    let actualPath = path;
    if (path === "paper.md" || path === "references.bib") {
      try {
        const { data: files, error: listError } = await serverSupabase.storage
          .from(BUCKET)
          .list(`${pfx}`);

        if (!listError && files) {
          const fileNames = files.map(f => f.name);
          const type = path === "paper.md" ? "paper" : "ref";
          const latestFile = findFileOfType(fileNames, path, type);

          if (latestFile) {
            actualPath = latestFile;
            console.log(`[file.GET] Map ${path} to latest version: ${actualPath}`);
          }
        }
      } catch (e) {
        console.error(`[file.GET] Error finding latest version ${path}:`, e);
        // 继续使用原始路径
      }
    }

    console.log(`[file.GET] Get file: ${pfx}/${actualPath}`);

    const { data, error } = await serverSupabase.storage
      .from(BUCKET)
      .download(`${pfx}/${actualPath}`);
    if (error) throw error;

    const txt = await data.text();
    return new NextResponse(txt, { status: 200 });
  } catch (err: unknown) {
    console.error("[file GET]", err);
    const message = err instanceof Error ? err.message : String(err);

    if (message === "Unauthorized") {
      return NextResponse.json(message, { status: 401 });
    }

    return NextResponse.json(message, { status: 500 });
  }
}

/* ---------- PUT /file ---------- */
async function putHandler(req: NextRequest) {
  try {
    const pfx = await getPrefix(req);
    const ctype = req.headers.get("content-type") ?? "";

    let path: string;
    let originalPath: string;
    let data: ArrayBuffer | Uint8Array; // ← 改成联合类型
    let mime: string;
    let isPaper = false;
    let isRef = false;
    const isNewFile = false;
    let needTimestampConversion = false;

    /* -------- 文本文件 (.md/.bib) -------- */
    if (ctype.startsWith("application/json")) {
      const { path: p, content, timestamp } = await req.json();
      if (!p) return NextResponse.json("Missing path", { status: 400 });

      originalPath = p;
      mime = "text/plain;charset=utf-8";
      data = new TextEncoder().encode(content); // Uint8Array

      // 检查是否是特殊文件 paper.md 或 references.bib
      isPaper = p === "paper.md";
      isRef = p === "references.bib";

      // 如果是特殊文件且需要时间戳命名
      if (isPaper || isRef) {
        const type = isPaper ? "paper" : "ref";
        needTimestampConversion = true;

        // 生成带时间戳的新文件名
        const newFilename = generateTimestampFilename(type);
        path = newFilename;
        console.log(`[file.PUT] Convert ${originalPath} to timestamped file name: ${path}`);
      } else {
        path = p;
      }

      console.log(
        `[file.PUT] Save text file: ${pfx}/${path}, size: ${data.byteLength} bytes, time: ${new Date().toISOString()}`
      );
    } else {
      /* -------- 二进制文件 (图片) -------- */
      originalPath = req.headers.get("x-file-path") ?? "";
      if (!originalPath) return NextResponse.json("Missing x-file-path header", { status: 400 });

      path = originalPath;
      mime = ctype || "application/octet-stream";
      data = await req.arrayBuffer(); // ArrayBuffer

      console.log(
        `[file.PUT] Upload binary file: ${pfx}/${path}, type: ${mime}, size: ${data.byteLength} bytes, time: ${new Date().toISOString()}`
      );
    }

    /* -------- 大小校验：10 MB -------- */
    const MAX = 10 * 1024 * 1024;
    const size = data.byteLength; // 两种类型都有 byteLength
    if (size > MAX) {
      console.warn(`[file.PUT] File too large: ${size} bytes, exceeds limit: ${MAX} bytes`);
      return NextResponse.json("File cannot exceed 10 MB", { status: 400 });
    }

    /* -------- 处理文件保存和历史记录 -------- */
    if (needTimestampConversion) {
      try {
        // 获取文件类型和原始文件名
        const type = isPaper ? "paper" : "ref";
        const originalFilename = isPaper ? "paper.md" : "references.bib";

        // 生成带时间戳的文件名（用于历史文件夹）
        const timestampFilename = generateTimestampFilename(type);

        // 检查当前文件列表
        const { data: existingFiles, error: listError } = await serverSupabase.storage
          .from(BUCKET)
          .list(`${pfx}`);

        if (listError) throw listError;

        const fileNames = existingFiles.map(f => f.name);

        // 创建history目录（如果不存在）
        const historyPath = getHistoryPath(pfx);
        try {
          const { data: historyCheck } = await serverSupabase.storage
            .from(BUCKET)
            .list(historyPath);
        } catch (e) {
          // 如果目录不存在，创建一个空文件标记目录
          await serverSupabase.storage
            .from(BUCKET)
            .upload(`${historyPath}/.keep`, new Uint8Array(0), { contentType: "text/plain" });
          console.log(`[file.PUT] Create history directory: ${historyPath}`);
        }

        // 将当前版本也保存到历史目录
        console.log(`[file.PUT] Save to history directory: ${historyPath}/${timestampFilename}`);

        await serverSupabase.storage
          .from(BUCKET)
          .upload(`${historyPath}/${timestampFilename}`, data, {
            upsert: true,
            contentType: mime,
          });

        // 获取历史目录文件列表
        const { data: historyFiles, error: historyError } = await serverSupabase.storage
          .from(BUCKET)
          .list(historyPath);

        if (!historyError && historyFiles) {
          const historyFilenames = historyFiles.map(f => f.name);
          // 仅保留最近的N个历史文件
          const filesToKeep = getFilesToKeep(historyFilenames, type);

          // 删除超出数量的旧历史文件
          const filesToDelete = historyFilenames.filter(
            name => isTimestampFile(name, type) && !filesToKeep.includes(name)
          );

          for (const fileToDelete of filesToDelete) {
            await serverSupabase.storage.from(BUCKET).remove([`${historyPath}/${fileToDelete}`]);
            console.log(`[file.PUT] Delete expired history file: ${fileToDelete}`);
          }
        }

        // 引用历史文件夹中的时间戳文件名，而不是原始变量中的path
        console.log(`[file.PUT] Note: Using original name ${originalFilename} to overwrite root directory file`);
        path = originalFilename;
      } catch (historyError) {
        console.error("[file.PUT] Error processing history file:", historyError);
        // 继续上传到根目录，即使历史处理失败
        if (isPaper) {
          path = "paper.md";
        } else if (isRef) {
          path = "references.bib";
        }
      }
    }

    /* -------- 上传至 Supabase -------- */
    console.log(`[file.PUT] Start uploading to Supabase storage: ${pfx}/${path}`);
    const startTime = Date.now();
    const { error, data: uploadData } = await serverSupabase.storage
      .from(BUCKET)
      .upload(`${pfx}/${path}`, data, {
        upsert: true,
        contentType: mime,
      });

    if (error) {
      console.error(`[file.PUT] Upload failed: ${error.message}`);
      throw error;
    }

    const endTime = Date.now();
    console.log(
      `[file.PUT] Upload successful: ${pfx}/${path}, time: ${endTime - startTime}ms, path: ${uploadData?.path || "Unknown"}`
    );

    return NextResponse.json({
      ok: true,
      path: path,
      originalPath: originalPath,
      size: size,
      timestamp: new Date().toISOString(),
    });
  } catch (err) {
    console.error("[file PUT]", err);
    const message = err instanceof Error ? err.message : String(err);

    if (message === "Unauthorized") {
      return NextResponse.json(message, { status: 401 });
    }

    return NextResponse.json(message, { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const GET = withCors(getHandler);
export const PUT = withCors(putHandler);
