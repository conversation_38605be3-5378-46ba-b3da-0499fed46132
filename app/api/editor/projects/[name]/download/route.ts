// app/api/editor/projects/[name]/download/route.ts
import type { FileObject } from "@supabase/storage-js";
import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
// 创建有服务角色权限的客户端，用于存储操作
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* 递归列 object ---------------------------------------------------------- */
async function listRecursive(prefix: string): Promise<FileObject[]> {
  console.log(`[download:listRecursive] 开始递归列举路径: ${prefix}`);
  const stack = [prefix];
  const out: FileObject[] = [];
  while (stack.length) {
    const path = stack.pop()!;
    console.log(`[download:listRecursive] 查询路径: ${path}`);

    const { data, error } = await serverSupabase.storage.from(BUCKET).list(path, { limit: 1000 });

    if (error) {
      console.error(`[download:listRecursive] 列举路径 ${path} 错误:`, error);
      throw error;
    }

    console.log(`[download:listRecursive] 路径 ${path} 下找到 ${data?.length || 0} 个项目`);

    for (const obj of data!) {
      const full = `${path}/${obj.name}`;
      out.push({ ...obj, name: full });
      if (obj.metadata?.is_directory || obj.id === null) {
        console.log(`[download:listRecursive] 发现目录: ${full}`);
        stack.push(full);
      } else {
        console.log(`[download:listRecursive] 发现文件: ${obj.name}`);
      }
    }
  }

  console.log(`[download:listRecursive] 完成递归列举, 一共找到 ${out.length} 个对象`);
  return out;
}

/* ---------------- GET /download --------------------------------------- */
export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ name: string }> } // 👈 Promise
): Promise<NextResponse> {
  try {
    /* 0) 解析动态参数 ---------------------------------------------------- */
    const { name: encodedName } = await params; // 必须 await

    /* 1) Auth ----------------------------------------------------------- */
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session) {
      console.error("[download] 未授权的访问尝试");
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    const uid = session.user.id;
    const name = decodeURIComponent(encodedName);
    const prefix = `${uid}/paperEditor/${name}`;

    console.log(`[download] 开始下载项目: ${name}, 用户ID: ${uid}`);
    console.log(`[download] 完整路径前缀: ${prefix}`);

    /* 2) 列对象 & 区分目录/文件 ----------------------------------------- */
    const objects = await listRecursive(prefix);

    if (!objects || objects.length === 0) {
      console.error(`[download] 项目不存在或为空: ${prefix}`);
      return NextResponse.json("Object not found", { status: 404 });
    }

    console.log(`[download] 找到 ${objects.length} 个对象，开始分类目录和文件`);

    const dirs: string[] = [];
    const filePaths: string[] = [];

    for (const obj of objects) {
      const rel = obj.name.replace(`${prefix}/`, "");
      if (rel.endsWith("/.init") || rel === ".init") {
        const dirPath = rel.replace(/\/?\.init$/, "/");
        console.log(`[download] 添加目录: ${dirPath}`);
        dirs.push(dirPath);
      } else if (!(obj.metadata?.is_directory || obj.id === null)) {
        console.log(`[download] 添加文件路径: ${rel}`);
        filePaths.push(rel);
      }
    }

    console.log(`[download] 分类完成: ${dirs.length} 个目录, ${filePaths.length} 个文件`);

    /* 3) 为文件生成 signed URL ----------------------------------------- */
    console.log(`[download] 开始为文件生成签名 URL`);
    const files = await Promise.all(
      filePaths.map(async rel => {
        console.log(`[download] 为文件创建签名 URL: ${rel}`);
        const fullPath = `${prefix}/${rel}`;
        const { data, error } = await serverSupabase.storage
          .from(BUCKET)
          .createSignedUrl(fullPath, 60);

        if (error) {
          console.error(`[download] 创建签名 URL 失败: ${fullPath}`, error);
          throw error;
        }

        return { path: rel, url: data!.signedUrl };
      })
    );

    console.log(`[download] 成功生成 ${files.length} 个文件的签名 URL`);
    console.log(`[download] 项目 ${name} 准备下载完成`);

    return NextResponse.json({ dirs, files });
  } catch (err: unknown) {
    console.error("[download] 错误:", err);
    const message = err instanceof Error ? err.message : "Storage error";
    return NextResponse.json(message, { status: 500 });
  }
}
