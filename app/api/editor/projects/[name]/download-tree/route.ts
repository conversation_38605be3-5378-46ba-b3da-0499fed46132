import type { FileObject } from "@supabase/storage-js";
import { createClient } from "@supabase/supabase-js";
import J<PERSON><PERSON><PERSON> from "jszip";
import { NextRequest, NextResponse } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* ---------- util: 递归列对象 + 调试 ---------- */
async function listRecursive(prefix: string): Promise<FileObject[]> {
  const stack = [prefix.replace(/\/$/, "")];
  const out: FileObject[] = [];
  console.log("[listRecursive] start", prefix);

  while (stack.length) {
    const p = stack.pop()!;
    console.log("  ⇒ list()", p);

    const { data, error } = await serverSupabase.storage.from(BUCKET).list(p, { limit: 1000 });
    if (error) throw error;

    for (const obj of data ?? []) {
      const full = p ? `${p}/${obj.name}` : obj.name;
      console.log("    •", full, { id: obj.id, dir: obj.metadata?.is_directory });

      if (obj.metadata?.is_directory || obj.id === null) {
        stack.push(full);
      } else {
        out.push({ ...obj, name: full });
      }
    }
  }
  return out;
}

/* ---------- mime helper ---------- */
function guessMime(path: string): string {
  const ext = path.split(".").pop()?.toLowerCase();
  switch (ext) {
    case "md":
    case "txt":
    case "bib":
      return "text/plain;charset=utf-8";
    case "png":
      return "image/png";
    case "jpg":
    case "jpeg":
      return "image/jpeg";
    case "gif":
      return "image/gif";
    default:
      return "application/octet-stream";
  }
}

/* ---------------- GET /download-tree ---------------- */
async function getHandler(req: NextRequest) {
  try {
    // 从URL中获取动态路由参数
    const { pathname } = req.nextUrl;
    const segments = pathname.split("/");
    const name = segments[segments.length - 2]; // [name]/download-tree 格式获取name参数

    if (!name) {
      return NextResponse.json("Missing project name", { status: 400 });
    }

    const supabase = await getSupabaseRoute();
    const {
      data: { session: sess },
    } = await getSessionCompat(supabase);
    if (!sess) return NextResponse.json("Unauthorized", { status: 401 });

    const uid = sess.user.id;
    const proj = decodeURIComponent(name);
    const rootPrefix = `${uid}/paperEditor/${proj}`; // 项目根
    const relPath = req.nextUrl.searchParams.get("path") ?? ""; // figures / notes/a.md

    console.log(`[download-tree] relPath = "${relPath || "(root)"}"`);

    /* ------ 判定文件 or 目录（只列父目录） ------ */
    let isFile = false;
    if (relPath) {
      const dir = relPath.includes("/") ? relPath.replace(/\/[^/]+$/, "") : "";
      const base = relPath.split("/").pop()!;
      const parentPrefix = dir ? `${rootPrefix}/${dir}` : rootPrefix; // 只列父目录

      const { data } = await serverSupabase.storage
        .from(BUCKET)
        .list(parentPrefix, { limit: 1000 });

      isFile = (data ?? []).some(
        o => o.name === base && !(o.metadata?.is_directory || o.id === null)
      );
    }

    /* ------------- A. 单文件 ------------- */
    if (isFile) {
      const { data, error } = await serverSupabase.storage
        .from(BUCKET)
        .download(`${rootPrefix}/${relPath}`);
      if (error) throw error;

      const buf = await data.arrayBuffer();
      console.log("download file OK", relPath, buf.byteLength, "bytes");

      return new NextResponse(new Uint8Array(buf), {
        status: 200,
        headers: {
          "content-disposition": `attachment; filename=${relPath.split("/").pop()!}`,
          "content-type": guessMime(relPath),
        },
      });
    }

    /* ------------- B. 目录 ZIP ------------- */
    const tgtPrefix = relPath ? `${rootPrefix}/${relPath}`.replace(/\/$/, "") : rootPrefix;
    const objs = await listRecursive(tgtPrefix);

    console.log(`[zip] total files = ${objs.length}`);

    const zip = new JSZip();
    const baseLen = tgtPrefix.length + 1;

    for (const obj of objs) {
      const key = obj.name;
      console.log("  → add", key);
      try {
        const { data } = await serverSupabase.storage.from(BUCKET).download(key);
        if (!data) {
          console.warn("    ! no data", key);
          continue;
        }
        zip.file(key.slice(baseLen), await data.arrayBuffer());
      } catch (e) {
        console.error("    ! download error", key, e);
      }
    }

    const blob = await zip.generateAsync({ type: "uint8array" });
    const zipName = (relPath || proj) + ".zip";

    console.log("zip size", blob.byteLength, "bytes");

    return new NextResponse(blob, {
      status: 200,
      headers: {
        "content-disposition": `attachment; filename=${zipName}`,
        "content-type": "application/zip",
      },
    });
  } catch (err) {
    console.error("[download-tree] fatal", err);
    const msg = err instanceof Error ? err.message : "Storage error";
    return NextResponse.json(msg, { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const GET = withCors(getHandler);
