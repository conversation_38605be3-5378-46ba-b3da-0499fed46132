// app/api/editor/projects/[name]/history/route.ts
import { createClient } from "@supabase/supabase-js";
import { NextResponse, NextRequest } from "next/server";

import {
  isTimestampFile,
  findLatestFile,
  getHistoryPath,
} from "@/app/editor/[name]/utils/filenameUtils";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* ---------- 计算前缀 ---------- */
async function getPrefix(req: NextRequest): Promise<string> {
  // 从URL中获取动态路由参数
  const { pathname } = req.nextUrl;
  const segments = pathname.split("/");
  const name = segments[segments.length - 2]; // [name]/history 格式获取name参数

  if (!name) {
    throw new Error("Missing project name");
  }

  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) throw new Error("Unauthorized");

  const uid = session.user.id;
  return `${uid}/paperEditor/${decodeURIComponent(name)}`;
}

/* ---------- GET /history?type=xxx ---------- */
async function getHandler(req: NextRequest) {
  try {
    const projectPrefix = await getPrefix(req);
    const fileType = req.nextUrl.searchParams.get("type") || null; // 'paper' 或 'ref' 或 null（返回所有文件）
    const latest = req.nextUrl.searchParams.get("latest") === "true";

    // 构建历史目录路径
    const historyPath = `${projectPrefix}/history`;
    console.log(`[history.GET] 获取历史目录文件: ${historyPath}, 类型: ${fileType || "所有"}`);

    // 列出历史目录中的所有文件
    const { data: files, error } = await serverSupabase.storage.from(BUCKET).list(historyPath);

    if (error) {
      console.error(`[history.GET] 列出历史文件时出错:`, error);
      if (error.message.includes("The resource was not found")) {
        // 历史目录不存在，返回空列表
        return NextResponse.json({
          files: [],
          latest: null,
          type: fileType,
        });
      }
      throw error;
    }

    if (!files || files.length === 0) {
      return NextResponse.json({
        files: [],
        latest: null,
        type: fileType,
      });
    }

    // 过滤文件类型（如果指定了）
    let filteredFiles = files;
    if (fileType === "paper" || fileType === "ref") {
      filteredFiles = files.filter(file => isTimestampFile(file.name, fileType as "paper" | "ref"));
    }

    // 如果请求最新文件
    let latestFile = null;
    if (latest && fileType && (fileType === "paper" || fileType === "ref")) {
      const fileNames = filteredFiles.map(f => f.name);
      latestFile = findLatestFile(fileNames, fileType as "paper" | "ref");
    }

    return NextResponse.json({
      files: filteredFiles,
      latest: latestFile,
      type: fileType,
    });
  } catch (err: unknown) {
    console.error("[history GET]", err);
    const message = err instanceof Error ? err.message : String(err);

    if (message === "Unauthorized") {
      return NextResponse.json(message, { status: 401 });
    }

    return NextResponse.json(message, { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const GET = withCors(getHandler);
