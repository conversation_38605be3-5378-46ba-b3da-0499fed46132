// app/api/editor/projects/[name]/copy/route.ts
import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

async function getPrefix(req: NextRequest): Promise<string> {
  // 从URL中获取动态路由参数
  const { pathname } = req.nextUrl;
  const segments = pathname.split("/");
  const name = segments[segments.length - 2]; // [name]/copy 格式获取name参数

  if (!name) {
    throw new Error("Missing project name");
  }

  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) throw new Error("Unauthorized");
  return `${session.user.id}/paperEditor/${decodeURIComponent(name)}`;
}

/* ---------- POST /copy { from, to } ---------- */
async function postHandler(req: NextRequest) {
  try {
    const { from, to } = await req.json();
    if (!from || !to) return NextResponse.json("缺少 from/to", { status: 400 });

    const pfx = await getPrefix(req);

    console.log(`[copy.POST] 复制文件: ${pfx}/${from} -> ${pfx}/${to}`);

    /* 拆分文件名与扩展名 */
    const parts = to.split("/");
    const fname = parts.pop()!;
    const dirPath = parts.join("/");
    const dotPos = fname.lastIndexOf(".");
    const base = dotPos === -1 ? fname : fname.slice(0, dotPos);
    const ext = dotPos === -1 ? "" : fname.slice(dotPos);

    let idx = 0;
    let target = to;

    /* ---------- 循环复制，遇冲突自动 + (n) ---------- */
    while (true) {
      const { error } = await serverSupabase.storage
        .from(BUCKET)
        .copy(`${pfx}/${from}`, `${pfx}/${target}`);

      if (!error) break; // 成功跳出

      /* -------- 判断"已存在"冲突 -------- */
      const status = (error as any).status ?? (error as any).statusCode ?? 0;

      const msg = String((error as any).message ?? "");

      const isExist =
        status === 409 || /already exists/i.test(msg) || /resource.*exists/i.test(msg);

      if (!isExist) throw error; // 其它错误直接抛

      idx += 1;
      const newName = `${base}(${idx})${ext}`;
      target = dirPath ? `${dirPath}/${newName}` : newName;
    }

    return NextResponse.json({ ok: true, path: target });
  } catch (e) {
    console.error("[copy POST]", e);
    const msg = e instanceof Error ? `${e.name}: ${e.message}` : String(e);

    if (msg.includes("Unauthorized")) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    return NextResponse.json(msg, { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const POST = withCors(postHandler);
