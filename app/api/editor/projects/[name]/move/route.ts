// app/api/editor/projects/[name]/move/route.ts
import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* ---------- user prefix 与 /file 中保持一致 ---------- */
async function getPrefix(req: NextRequest): Promise<string> {
  // 从URL中获取动态路由参数
  const { pathname } = req.nextUrl;
  const segments = pathname.split("/");
  const name = segments[segments.length - 2]; // [name]/move 格式获取name参数

  if (!name) {
    throw new Error("Missing project name");
  }

  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) throw new Error("Unauthorized");

  const uid = session.user.id;
  return `${uid}/paperEditor/${decodeURIComponent(name)}`;
}

/* ---------- POST /move { from, to } ---------- */
async function postHandler(req: NextRequest) {
  try {
    const { from, to } = await req.json();

    /* ---- 基础校验 ---- */
    if (!from || !to) return NextResponse.json("缺少 from/to", { status: 400 });
    if (from === to) return NextResponse.json("from 与 to 相同", { status: 400 });
    if (from.startsWith("/") || to.startsWith("/"))
      return NextResponse.json("路径必须是相对路径", { status: 400 });
    if (to.startsWith(from + "/"))
      return NextResponse.json("不能移动到自身子目录", { status: 400 });

    const pfx = await getPrefix(req);

    console.log(`[move.POST] 移动文件: ${pfx}/${from} -> ${pfx}/${to}`);

    /* ---- Supabase move ---- */
    const { error } = await serverSupabase.storage
      .from(BUCKET)
      .move(`${pfx}/${from}`, `${pfx}/${to}`);

    if (error) {
      /* ----------------- 统一判断"已存在" ----------------- */
      const status =
        (error as any).status ?? // storage-js ≥2
        (error as any).statusCode ??
        0; // storage-js 1.x

      const msg = String((error as any).message ?? "");

      const isExist =
        status === 409 || /already exists/i.test(msg) || /resource.*exists/i.test(msg);

      if (isExist) {
        return NextResponse.json("目标已存在", { status: 409 });
      }

      /* 其余错误继续抛出 */
      throw error;
    }

    return NextResponse.json({ ok: true });
  } catch (err: unknown) {
    console.error("[move POST]", err);
    const message = err instanceof Error ? err.message : String(err);

    if (message.includes("Unauthorized")) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    return NextResponse.json(message, { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const POST = withCors(postHandler);
