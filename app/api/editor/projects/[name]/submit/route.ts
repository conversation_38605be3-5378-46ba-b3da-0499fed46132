import { Category, PaperStatus, CommentType, PaperType } from "@prisma/client";
import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";
import yaml from "yaml";
import { z } from "zod";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

// 创建supabase客户端，使用服务端角色权限
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

// 存储桶配置
const SOURCE_BUCKET = "user-temp";
const TARGET_BUCKET = "submitted";

// 辅助函数：将字符串类型映射到 Prisma 枚举
function mapPaperType(paperType: "full" | "gallery" | "preprint"): PaperType {
  switch (paperType) {
    case "full":
      return PaperType.FULL;
    case "gallery":
      return PaperType.GALLERY;
    case "preprint":
      return PaperType.PREPRINT;
    default:
      return PaperType.FULL; // 默认值
  }
}

// YAML验证Schema
const schema = z.object({
  title: z.string().min(2, "title is required"),
  abstract: z.string().min(10, "abstract is too short"),
  tags: z.array(z.string()).min(1, "tags must have at least 1 item"),
  authors: z
    .array(
      z.object({
        name: z.string().min(1, "author name is required"),
        affiliations: z.array(z.number()).optional(), // 可选的附属机构ID数组
      })
    )
    .min(1, "at least one author is required"),
  affiliations: z
    .array(
      z.object({
        id: z.number().optional(), // 组织ID，可选
        name: z.string().min(1, "organization name is required"), // 组织名称
      })
    )
    .optional(),
  videoUrl: z.string().url().optional(),
  status: z.enum(["draft", "revision"]),
  paperID: z.string().nullable().optional(), // 可以为null、undefined或字符串
  paperType: z.enum(["full", "gallery", "preprint"], {
    errorMap: () => ({ message: "paperType must be full, gallery or preprint" }),
  }),
});

// 自定义类型，定义论文元数据
interface PaperMeta {
  title: string;
  abstract: string;
  tags: string[];
  authors: Array<{ name: string; affiliations?: number[] }>;
  affiliations?: Array<{ id?: number; name: string }>;
  videoUrl?: string;
  status: "draft" | "revision";
  paperID?: string | null; // 允许为null
  paperType: "full" | "gallery" | "preprint"; // 新增paperType字段
}

async function postHandler(req: NextRequest) {
  // 获取请求体中的编译好的HTML，如果提供的话
  let compiledHtml: string | null = null;
  try {
    const body = await req.json();
    if (body && typeof body.compiledHtml === "string") {
      compiledHtml = body.compiledHtml;
      console.log(`[submit] received compiled HTML, length: ${compiledHtml?.length || 0} bytes`);
    }
  } catch (e) {
    // 如果解析失败，假设请求体为空
    console.log("[submit] request body parsing failed or is empty, continue processing");
  }
  try {
    // 从URL中提取项目名称
    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const nameIndex = pathParts.findIndex(part => part === "projects") + 1;
    const projectName = decodeURIComponent(pathParts[nameIndex]);

    if (!projectName) {
      return NextResponse.json("project name is missing", { status: 400 });
    }

    console.log(`[submit] processing project submission: ${projectName}`);

    // 验证用户身份
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);

    if (!session) {
      return NextResponse.json("unauthorized access", { status: 401 });
    }

    const uid = session.user.id;
    const sourcePrefix = `${uid}/paperEditor/${projectName}`;

    console.log(`[submit] user ${uid} is submitting project ${projectName}`);

    // 获取paper.md文件内容
    const { data: fileData, error: fileError } = await serverSupabase.storage
      .from(SOURCE_BUCKET)
      .download(`${sourcePrefix}/paper.md`);

    if (fileError) {
      console.error("[submit] failed to get paper.md:", fileError);
      return NextResponse.json("failed to get paper.md", { status: 500 });
    }

    // 解析文件内容
    const content = await fileData.text();
    const match = content.match(/^---\n([\s\S]*?)\n---/);

    if (!match || !match[1]) {
      return NextResponse.json("YAML metadata not found", { status: 400 });
    }

    // 验证YAML
    try {
      const yamlContent = match[1];
      const obj = yaml.parse(yamlContent);
      const result = schema.safeParse(obj);

      if (!result.success) {
        console.error("[submit] YAML validation failed:", result.error);
        return NextResponse.json(
          "YAML validation failed: " + result.error.issues.map(i => i.message).join(", "),
          { status: 400 }
        );
      }

      // 使用自定义类型来确保类型安全
      const meta: PaperMeta = result.data;

      // 打印meta数据以便验证
      console.log("[submit] parsed YAML data:", {
        title: meta.title,
        abstract: meta.abstract?.substring(0, 50) + "...", // 只打印摘要的前50个字符
        tags: meta.tags,
        authors: meta.authors,
        status: meta.status,
        paperType: meta.paperType, // 添加paperType的调试信息
        paperID: meta.paperID || "not provided",
      });

      // 增加调试日志
      console.log("[submit] detailed check of paperID:", {
        value: meta.paperID,
        type: typeof meta.paperID,
        isEmpty: !meta.paperID,
        stringified: JSON.stringify(meta.paperID),
      });

      // 根据状态和paperID处理不同的逻辑
      if (meta.status === "draft") {
        // 如果是draft状态且提供了paperID（非null, 非undefined, 非空字符串），需要检查是否已存在
        if (meta.paperID && meta.paperID !== null && meta.paperID.trim() !== "") {
          // 检查paperID是否已存在
          const existingPaper = await prisma.paper.findUnique({
            where: { id: meta.paperID }, // paperID类型保持为string
          });

          if (existingPaper) {
            return NextResponse.json(
              {
                success: false,
                message: "paperID already exists, please check paperID or change status to revision",
              },
              { status: 400 }
            );
          }
        }

        // 继续原有的提交流程
        return await handleDraftSubmission(uid, sourcePrefix, meta, compiledHtml);
      } else if (meta.status === "revision") {
        // 如果是修订状态，必须有paperID
        if (!meta.paperID || meta.paperID === null || meta.paperID.trim() === "") {
          return NextResponse.json(
            {
              success: false,
              message: "revision version must provide paperID",
            },
            { status: 400 }
          );
        }

        // 检查paperID是否存在
        const existingPaper = await prisma.paper.findUnique({
          where: { id: meta.paperID }, // paperID类型保持为string
        });

        if (!existingPaper) {
          return NextResponse.json(
            {
              success: false,
              message: "specified paperID does not exist, cannot submit revision version",
            },
            { status: 404 }
          );
        }

        // 处理修订版本的提交
        return await handleRevisionSubmission(uid, sourcePrefix, meta, meta.paperID, compiledHtml); // 保持paperID为string
      }
    } catch (error) {
      console.error("[submit] processing failed:", error);
      return NextResponse.json(
        `submission failed: ${error instanceof Error ? error.message : String(error)}`,
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("[submit] unexpected error:", error);
    return NextResponse.json(
      `server internal error: ${error instanceof Error ? error.message : String(error)}`,
      { status: 500 }
    );
  }
}

// 处理草稿提交
async function handleDraftSubmission(
  uid: string,
  sourcePrefix: string,
  meta: PaperMeta,
  compiledHtml: string | null = null
) {
  console.log("[submit] processing draft submission");

  let paperId: string | null = null;
  let createdPaperRecord = false;
  let createdStorageStructure = false;
  let createdReviewComment = false;

  try {
    // 步骤1: 在数据库中创建Paper记录
    console.log("[submit] step 1: create paper record");
    const paper = await prisma.paper.create({
      data: {
        title: meta.title,
        status: PaperStatus.SUBMITTED, // 使用枚举值
        authorId: uid,
        abstract: meta.abstract,
        category: Category.UNKNOWN, // 使用枚举值而不是字符串
        type: mapPaperType(meta.paperType), // 使用辅助函数映射paperType
        needsAttention: true, // 修改为true，确保新提交的论文被关注
        issueId: null, // 暂不分配到特定期次

        // 正确填充数组字段
        paperTags: meta.tags, // 从YAML中获取标签
        authors: meta.authors.map(a => a.name), // 从YAML中获取作者姓名数组
        affiliations: meta.affiliations ? meta.affiliations.map(a => a.name) : [], // 从YAML中获取单位数组，如果没有则为空数组
        // @ts-ignore - 忽略类型检查，因为我们没法在不运行完整迁移的情况下更新类型
        submittedAt: new Date(), // 设置提交时间（原来的createdAt改名后）
        lastUpdatedAt: new Date(), // 设置最后更新时间
      } as any, // 暂时使用any类型避免可能的类型错误
    });

    paperId = paper.id;
    createdPaperRecord = true;
    console.log(`[submit] created paper record successfully, ID: ${paperId}`);

    // 步骤2: 在submitted存储桶中创建文件夹结构
    console.log(`[submit] step 2: create storage structure ${paperId}/draft`);
    const targetDirMarker = new Blob([""], { type: "text/plain" });

    // 创建版本文件夹标记
    const { error: dirError } = await serverSupabase.storage
      .from(TARGET_BUCKET)
      .upload(`${paperId}/draft/.init`, targetDirMarker);

    if (dirError) {
      console.error("[submit] failed to create target directory:", dirError);
      throw new Error(`failed to create target directory: ${dirError.message}`);
    }

    createdStorageStructure = true;

    // 步骤3: 复制所有文件
    console.log(`[submit] step 3: start copying files`);
    await copyFiles(sourcePrefix, `${paperId}/draft`);

    // 步骤3.1: 如果有编译好的HTML，保存为compiled.html
    if (compiledHtml) {
      console.log(`[submit] saving compiled.html to ${paperId}/draft/`);
      try {
        const htmlBlob = new Blob([compiledHtml], { type: "text/html" });
        const { error: htmlError } = await serverSupabase.storage
          .from(TARGET_BUCKET)
          .upload(`${paperId}/draft/compiled.html`, htmlBlob);

        if (htmlError) {
          console.error("[submit] failed to save compiled.html:", htmlError);
          // 继续处理，不要因为HTML保存失败而中断整个提交过程
        } else {
          console.log(`[submit] compiled.html saved successfully`);
        }
      } catch (htmlErr) {
        console.error("[submit] error processing compiled.html:", htmlErr);
        // 继续处理，不要因为HTML保存失败而中断整个提交过程
      }
    }

    // 步骤4: 创建时间线状态更新
    console.log(`[submit] step 4: create timeline status update`);
    await prisma.reviewComment.create({
      data: {
        paperId: paperId,
        authorId: uid,
        roleLabel: "Author",
        body: `Status changed to **${PaperStatus.SUBMITTED}**`,
        type: CommentType.STATUS_CHANGE,
        visibleToAuthor: true,
        visibleToReviewers: true,
      },
    });

    createdReviewComment = true;

    // 提交成功
    console.log(`[submit] draft submission completely successful, Paper ID: ${paperId}`);
    return NextResponse.json({
      success: true,
      paperId: paperId,
      message: "paper submission successful",
    });

  } catch (error) {
    console.error("[submit] draft submission failed:", error);

    // 开始清理操作
    console.log("[submit] start cleaning up failed submission records");

    if (paperId) {
      try {
        // 清理ReviewComment记录（如果已创建）
        if (createdReviewComment) {
          console.log(`[submit] cleaning up ReviewComment record for paper ${paperId}`);
          try {
            await prisma.reviewComment.deleteMany({
              where: { paperId: paperId }
            });
            console.log(`[submit] ReviewComment record cleaned up successfully`);
          } catch (commentError) {
            console.error(`[submit] failed to clean up ReviewComment record:`, commentError);
          }
        }

        // 清理存储文件夹和所有文件（如果已创建）
        if (createdStorageStructure) {
          console.log(`[submit] cleaning up storage structure ${paperId}/draft`);
          try {
            await cleanupStorageFolder(`${paperId}/draft`);
            console.log(`[submit] storage structure cleaned up successfully`);
          } catch (storageError) {
            console.error(`[submit] failed to clean up storage structure:`, storageError);
          }

          // 也清理父文件夹（如果为空的话）
          try {
            await cleanupStorageFolder(`${paperId}`);
            console.log(`[submit] parent folder cleaned up successfully`);
          } catch (e) {
            // 忽略父文件夹清理错误，可能包含其他版本
            console.log(`[submit] failed to clean up parent folder (may contain other files): ${e}`);
          }
        }

        // 最后清理Paper记录（如果已创建）
        if (createdPaperRecord) {
          console.log(`[submit] cleaning up paper record ${paperId}`);
          try {
            await prisma.paper.delete({ where: { id: paperId } });
            console.log(`[submit] paper record cleaned up successfully`);
          } catch (paperError) {
            console.error(`[submit] failed to clean up paper record:`, paperError);
          }
        }

        console.log(`[submit] cleanup completed for paper ${paperId}`);

      } catch (cleanupError) {
        console.error(`[submit] error during cleanup:`, cleanupError);
        // 即使清理失败，也要返回原始错误
      }
    }

    return NextResponse.json(
      `submission failed: ${error instanceof Error ? error.message : String(error)}`,
      { status: 500 }
    );
  }
}

// 处理修订版本提交
async function handleRevisionSubmission(
  uid: string,
  sourcePrefix: string,
  meta: PaperMeta,
  paperId: string,
  compiledHtml: string | null = null
) {
  console.log(`[submit] processing revision submission, paper ID: ${paperId}`);

  let revisionPrefix: string | null = null;
  let createdStorageStructure = false;
  let updatedPaperRecord = false;
  let createdReviewComment = false;
  let originalPaperData: any = null;

  try {
    // 备份原始Paper数据以便回滚
    originalPaperData = await prisma.paper.findUnique({
      where: { id: paperId },
      select: {
        status: true,
        title: true,
        abstract: true,
        paperTags: true,
        authors: true,
        affiliations: true,
        needsAttention: true,
      }
    });

    if (!originalPaperData) {
      throw new Error(`paper record does not exist: ${paperId}`);
    }

    // 确定修订文件夹名称
    revisionPrefix = await determineRevisionFolder(paperId);
    console.log(`[submit] using revision folder: ${revisionPrefix}`);

    // 创建修订文件夹
    const targetDirMarker = new Blob([""], { type: "text/plain" });
    const { error: dirError } = await serverSupabase.storage
      .from(TARGET_BUCKET)
      .upload(`${paperId}/${revisionPrefix}/.init`, targetDirMarker);

    if (dirError) {
      console.error("[submit] failed to create revision directory:", dirError);
      throw new Error(`failed to create revision directory: ${dirError.message}`);
    }

    createdStorageStructure = true;

    // 复制所有文件
    console.log(`[submit] start copying revision files`);
    await copyFiles(sourcePrefix, `${paperId}/${revisionPrefix}`);

    // 如果有编译好的HTML，保存为compiled.html
    if (compiledHtml) {
      console.log(`[submit] saving compiled.html to ${paperId}/${revisionPrefix}/`);
      try {
        const htmlBlob = new Blob([compiledHtml], { type: "text/html" });
        const { error: htmlError } = await serverSupabase.storage
          .from(TARGET_BUCKET)
          .upload(`${paperId}/${revisionPrefix}/compiled.html`, htmlBlob);

        if (htmlError) {
          console.error("[submit] failed to save compiled.html:", htmlError);
          // 继续处理，不要因为HTML保存失败而中断整个提交过程
        } else {
          console.log(`[submit] compiled.html saved successfully`);
        }
      } catch (htmlErr) {
        console.error("[submit] error processing compiled.html:", htmlErr);
        // 继续处理，不要因为HTML保存失败而中断整个提交过程
      }
    }

    // 更新论文状态为REVISION_SUBMITTED
    console.log(`[submit] updating paper record status`);
    await prisma.paper.update({
      where: { id: paperId },
      data: {
        status: PaperStatus.REVISION_SUBMITTED, // 使用新增加的REVISION_SUBMITTED状态
        title: meta.title,
        abstract: meta.abstract,
        type: mapPaperType(meta.paperType), // 使用辅助函数映射paperType
        paperTags: meta.tags,
        authors: meta.authors.map(a => a.name),
        affiliations: meta.affiliations ? meta.affiliations.map(a => a.name) : [],
        needsAttention: true, // 标记需要关注，提醒编辑有修订版本
        // @ts-ignore - 忽略类型检查，因为我们没法在不运行完整迁移的情况下更新类型
        lastUpdatedAt: new Date(), // 设置最后修订时间
      } as any, // 使用any类型避免类型错误
    });

    updatedPaperRecord = true;

    // 创建时间线状态更新
    console.log(`[submit] creating timeline status update`);
    await prisma.reviewComment.create({
      data: {
        paperId: paperId,
        authorId: uid,
        roleLabel: "Author",
        body: `Status changed to **${PaperStatus.REVISION_SUBMITTED}**`,
        type: CommentType.STATUS_CHANGE,
        visibleToAuthor: true,
        visibleToReviewers: true,
      },
    });

    createdReviewComment = true;

    // 提交成功
    console.log(`[submit] revision submission completely successful, Paper ID: ${paperId}, revision folder: ${revisionPrefix}`);
    return NextResponse.json({
      success: true,
      paperId: paperId,
      revisionFolder: revisionPrefix,
      message: "revision submission successful",
    });

  } catch (error) {
    console.error("[submit] revision submission failed:", error);

    // 开始清理操作
    console.log("[submit] start cleaning up failed revision submission records");

    try {
      // 清理ReviewComment记录（如果已创建）
      if (createdReviewComment) {
        console.log(`[submit] cleaning up latest ReviewComment record`);
        try {
          // 只删除刚才创建的状态更新评论
          await prisma.reviewComment.deleteMany({
            where: {
              paperId: paperId,
              authorId: uid,
              type: CommentType.STATUS_CHANGE,
              body: `Status changed to **${PaperStatus.REVISION_SUBMITTED}**`
            }
          });
          console.log(`[submit] ReviewComment record cleaned up successfully`);
        } catch (commentError) {
          console.error(`[submit] failed to clean up ReviewComment record:`, commentError);
        }
      }

      // 回滚Paper记录更新（如果已更新）
      if (updatedPaperRecord && originalPaperData) {
        console.log(`[submit] rolling back paper record to original status`);
        try {
          await prisma.paper.update({
            where: { id: paperId },
            data: originalPaperData
          });
          console.log(`[submit] paper record rolled back successfully`);
        } catch (rollbackError) {
          console.error(`[submit] failed to roll back paper record:`, rollbackError);
        }
      }

      // 清理存储文件夹和所有文件（如果已创建）
      if (createdStorageStructure && revisionPrefix) {
        console.log(`[submit] cleaning up revision storage structure ${paperId}/${revisionPrefix}`);
        try {
          await cleanupStorageFolder(`${paperId}/${revisionPrefix}`);
          console.log(`[submit] revision storage structure cleaned up successfully`);
        } catch (storageError) {
          console.error(`[submit] failed to clean up revision storage structure:`, storageError);
        }
      }

      console.log(`[submit] revision submission cleanup completed for paper ${paperId}`);

    } catch (cleanupError) {
      console.error(`[submit] error during revision submission cleanup:`, cleanupError);
      // 即使清理失败，也要返回原始错误
    }

    return NextResponse.json(
      `revision submission failed: ${error instanceof Error ? error.message : String(error)}`,
      { status: 500 }
    );
  }
}

// 确定修订文件夹名称
async function determineRevisionFolder(paperId: string): Promise<string> {
  // 检查是否已有revision文件夹
  const { data, error } = await serverSupabase.storage.from(TARGET_BUCKET).list(`${paperId}`);

  if (error || !data) {
    throw new Error("failed to list paper directory");
  }

  // 找出所有revision_开头的文件夹
  const revisionFolders = data
    .filter(item => item.id === null) // 只选择文件夹
    .map(item => item.name)
    .filter(name => name === "revision" || name.startsWith("revision_V"));

  if (!revisionFolders.includes("revision")) {
    return "revision"; // 如果没有revision文件夹，使用它
  }

  // 找出最大版本号
  let maxVersion = 1;
  revisionFolders.forEach(folder => {
    if (folder.startsWith("revision_V")) {
      const versionMatch = folder.match(/revision_V(\d+)/);
      if (versionMatch && versionMatch[1]) {
        const version = parseInt(versionMatch[1]);
        if (version > maxVersion) maxVersion = version;
      }
    }
  });

  return `revision_V${maxVersion + 1}`;
}

// 复制文件的通用函数
async function copyFiles(sourcePrefix: string, targetPrefix: string) {
  console.log(`[submit] copying files: ${sourcePrefix} -> ${targetPrefix}`);

  // 递归获取项目下所有文件
  async function listAllFiles(prefix: string): Promise<string[]> {
    const allFiles: string[] = [];
    const { data, error } = await serverSupabase.storage
      .from(SOURCE_BUCKET)
      .list(prefix, { sortBy: { column: "name", order: "asc" } });

    if (error) {
      console.error(`[submit] failed to list files ${prefix}:`, error);
      throw new Error(`failed to list files: ${error.message}`);
    }

    if (!data || data.length === 0) return allFiles;

    for (const item of data) {
      const itemPath = `${prefix}/${item.name}`;

      if (item.id === null) {
        // 这是一个目录，递归获取子文件
        const subFiles = await listAllFiles(itemPath);
        allFiles.push(...subFiles);
      } else {
        // 这是一个文件
        allFiles.push(itemPath);
      }
    }

    return allFiles;
  }

  try {
    // 获取所有源文件
    const sourceFiles = await listAllFiles(sourcePrefix);
    console.log(`[submit] found ${sourceFiles.length} source files to copy`);

    if (sourceFiles.length === 0) {
      console.log(`[submit] warning: source directory is empty or does not exist: ${sourcePrefix}`);
      return; // 不抛出错误，允许空目录提交
    }

    // 复制所有文件
    for (const sourcePath of sourceFiles) {
      const relativePath = sourcePath.substring(sourcePrefix.length + 1);
      const targetPath = `${targetPrefix}/${relativePath}`;

      // 跳过 .init 文件，这些是占位符文件，不需要复制
      if (relativePath.endsWith('/.init') || relativePath === '.init') {
        console.log(`[submit] skipping placeholder file: ${relativePath}`);
        continue;
      }

      console.log(`[submit] copying file: ${sourcePath} -> ${targetPath}`);

      try {
        // 先获取文件内容
        const { data, error: downloadError } = await serverSupabase.storage
          .from(SOURCE_BUCKET)
          .download(sourcePath);

        if (downloadError) {
          console.error(`[submit] failed to download file ${sourcePath}:`, downloadError);
          throw new Error(`failed to download source file ${sourcePath}: ${downloadError.message}`);
        }

        if (!data) {
          throw new Error(`downloaded file data is empty: ${sourcePath}`);
        }

        // 上传到目标位置
        const { error: uploadError } = await serverSupabase.storage
          .from(TARGET_BUCKET)
          .upload(targetPath, data, { upsert: true }); // 添加upsert选项

        if (uploadError) {
          console.error(`[submit] failed to upload file ${targetPath}:`, uploadError);
          throw new Error(`failed to upload target file ${targetPath}: ${uploadError.message}`);
        }

        console.log(`[submit] file copied successfully: ${relativePath}`);

      } catch (fileError) {
        console.error(`[submit] failed to process file ${sourcePath}:`, fileError);
        throw new Error(`failed to process file ${sourcePath}: ${fileError instanceof Error ? fileError.message : String(fileError)}`);
      }
    }

    console.log(`[submit] all files copied, total ${sourceFiles.length} files`);

  } catch (error) {
    console.error(`[submit] file copying process failed:`, error);
    throw error; // 重新抛出错误以触发清理机制
  }
}

// 新增：清理存储文件夹的通用函数
async function cleanupStorageFolder(folderPath: string): Promise<void> {
  console.log(`[cleanup] starting to clean up storage folder: ${folderPath}`);

  try {
    // 递归获取文件夹下的所有文件
    const allFiles = await listAllFilesRecursive(folderPath);

    if (allFiles.length === 0) {
      console.log(`[cleanup] folder ${folderPath} is empty or does not exist`);
      return;
    }

    console.log(`[cleanup] found ${allFiles.length} files to delete`);

    // 删除所有文件（包括.init文件）
    for (const filePath of allFiles) {
      try {
        const { error } = await serverSupabase.storage
          .from(TARGET_BUCKET)
          .remove([filePath]);

        if (error) {
          console.error(`[cleanup] failed to delete file ${filePath}:`, error);
        } else {
          console.log(`[cleanup] file deleted successfully: ${filePath}`);
        }
      } catch (fileError) {
        console.error(`[cleanup] error deleting file ${filePath}:`, fileError);
      }
    }

    console.log(`[cleanup] folder ${folderPath} cleaned up successfully`);

  } catch (error) {
    console.error(`[cleanup] failed to clean up folder ${folderPath}:`, error);
    throw error;
  }
}

// 新增：递归列出所有文件的函数
async function listAllFilesRecursive(prefix: string): Promise<string[]> {
  const allFiles: string[] = [];

  try {
    const { data, error } = await serverSupabase.storage
      .from(TARGET_BUCKET)
      .list(prefix, { sortBy: { column: "name", order: "asc" } });

    if (error) {
      console.error(`[cleanup] failed to list files ${prefix}:`, error);
      return allFiles; // 返回空数组而不是抛出错误
    }

    if (!data || data.length === 0) {
      return allFiles;
    }

    for (const item of data) {
      const itemPath = `${prefix}/${item.name}`;

      if (item.id === null) {
        // 这是一个目录，递归获取子文件
        const subFiles = await listAllFilesRecursive(itemPath);
        allFiles.push(...subFiles);
      } else {
        // 这是一个文件，添加到列表中
        allFiles.push(itemPath);
      }
    }
  } catch (error) {
    console.error(`[cleanup] error listing files ${prefix}:`, error);
  }

  return allFiles;
}

export const POST = withCors(postHandler as any);
