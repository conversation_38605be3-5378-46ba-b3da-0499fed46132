import { createClient } from "@supabase/supabase-js";
import { NextResponse, NextRequest } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/*
 * 强制刷新项目文件缓存
 * 此API在编译前调用，确保服务器使用最新文件版本
 */
async function refreshFilesHandler(req: NextRequest) {
  try {
    // 从URL中获取动态路由参数
    const { pathname } = req.nextUrl;
    const segments = pathname.split("/");
    const name = segments[segments.length - 2]; // [name]/refresh-files 格式获取name参数

    if (!name) {
      throw new Error("Missing project name");
    }

    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session) throw new Error("Unauthorized");

    const userId = session.user.id;
    const projectName = decodeURIComponent(name);
    const basePath = `${userId}/paperEditor/${projectName}`;

    // 读取请求体
    const body = await req.json();
    const timestamp = body.timestamp || Date.now();

    console.log(
      `[refresh-files.POST] 刷新项目文件缓存: ${projectName}, 用户ID: ${userId}, 时间戳: ${timestamp}`
    );

    // 执行刷新操作 - 列出文件以触发缓存刷新
    const { data, error } = await serverSupabase.storage.from(BUCKET).list(basePath, {
      limit: 100,
      offset: 0,
      sortBy: { column: "name", order: "asc" },
    });

    if (error) {
      console.error(`[refresh-files.POST] 刷新缓存错误:`, error);
      return new NextResponse("刷新缓存出错: " + error.message, { status: 500 });
    }

    // 如果存在paper.md文件，则单独拉取以确保刷新
    const paperMdPath = `${basePath}/paper.md`;
    try {
      const { data: fileData, error: fileError } = await serverSupabase.storage
        .from(BUCKET)
        .download(paperMdPath);

      if (fileError) {
        console.warn(`[refresh-files.POST] 未找到paper.md文件:`, fileError);
      } else {
        console.log(`[refresh-files.POST] 已刷新paper.md文件缓存，大小: ${fileData.size}字节`);
      }
    } catch (err) {
      console.warn(`[refresh-files.POST] 刷新paper.md文件异常:`, err);
    }

    // 成功返回结果
    return NextResponse.json({
      success: true,
      message: "文件缓存已刷新",
      fileCount: data?.length || 0,
      timestamp,
    });
  } catch (e: any) {
    console.error(`[refresh-files.POST] 服务器错误:`, e);

    const message = e instanceof Error ? e.message : String(e);
    if (message === "Unauthorized") {
      return NextResponse.json(message, { status: 401 });
    }

    return new NextResponse("服务器错误: " + message, { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const POST = withCors(refreshFilesHandler);
