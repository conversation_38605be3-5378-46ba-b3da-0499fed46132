// app/api/editor/projects/[name]/create-file/route.ts
import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* -------- 拼用户/项目前缀 -------- */
async function getPrefix(req: NextRequest): Promise<string> {
  // 从URL中获取动态路由参数
  const { pathname } = req.nextUrl;
  const segments = pathname.split("/");
  const name = segments[segments.length - 2]; // [name]/create-file 格式获取name参数

  if (!name) {
    throw new Error("Missing project name");
  }

  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) throw new Error("Unauthorized");

  return `${session.user.id}/paperEditor/${decodeURIComponent(name)}`;
}

/* -------- 根据扩展名推断 MIME -------- */
function guessMime(path: string): string {
  const ext = path.split(".").pop()?.toLowerCase();
  switch (ext) {
    case "md":
    case "txt":
    case "bib":
      return "text/plain;charset=utf-8";
    case "png":
      return "image/png";
    case "jpg":
    case "jpeg":
      return "image/jpeg";
    case "gif":
      return "image/gif";
    default:
      return "application/octet-stream";
  }
}

/* ---------------- POST /create-file ---------------- */
async function postHandler(req: NextRequest) {
  try {
    const { path, content = "" } = await req.json();

    if (!path || path.startsWith("/")) return NextResponse.json("非法 path", { status: 400 });

    const pfx = await getPrefix(req);
    const full = `${pfx}/${path}`;

    console.log(`[create-file.POST] 创建文件: ${full}`);

    /* ---------- 上传 ---------- */
    const mime = guessMime(path);
    const body = typeof content === "string" ? new TextEncoder().encode(content) : new Uint8Array();

    const { error } = await serverSupabase.storage.from(BUCKET).upload(full, body, {
      contentType: mime,
      upsert: false, // 已存在则报错
    });

    if (error) {
      const status = (error as any).status ?? (error as any).statusCode ?? 0;
      const msg = String(error.message ?? "");
      const isExist = status === 409 || /already exists/i.test(msg);

      if (isExist) return NextResponse.json("文件已存在", { status: 409 });

      throw error;
    }

    return NextResponse.json({ ok: true }, { status: 201 });
  } catch (e) {
    console.error("[create-file]", e);
    const msg = e instanceof Error ? e.message : "Server error";

    if (msg.includes("Unauthorized")) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    return NextResponse.json(msg, { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const POST = withCors(postHandler);
