// app/api/editor/projects/[name]/route.ts
import type { FileObject } from "@supabase/storage-js";
import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
// 创建带有完整管理权限的服务角色客户端，用于存储操作
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* ---------- 递归列对象 ---------- */
async function listRecursive(prefix: string): Promise<FileObject[]> {
  console.log(`[listRecursive] 开始递归列举路径: ${prefix}`);
  const stack = [prefix];
  const files: FileObject[] = [];
  while (stack.length) {
    const path = stack.pop()!;
    console.log(`[listRecursive] 查询路径: ${path}`);
    const { data, error } = await serverSupabase.storage.from(BUCKET).list(path, { limit: 1000 });

    if (error) {
      console.error(`[listRecursive] 列举路径 ${path} 错误:`, error);
      throw error;
    }

    console.log(`[listRecursive] 路径 ${path} 下找到 ${data?.length || 0} 个项目`);

    for (const obj of data!) {
      const full = `${path}/${obj.name}`;
      if (obj.id === null) {
        console.log(`[listRecursive] 发现目录: ${full}`);
        stack.push(full);
      } else {
        console.log(`[listRecursive] 发现文件: ${full}`);
        files.push({ ...obj, name: full });
      }
    }
  }
  console.log(`[listRecursive] 完成递归列举, 一共找到 ${files.length} 个文件`);
  return files;
}

/* ---------------- PATCH - 重命名 ---------------- */
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ name: string }> }) {
  const { name: oldRaw } = await params; // ⬅️ await Promise
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) return NextResponse.json("Unauthorized", { status: 401 });

  const uid = session.user.id;
  const oldName = decodeURIComponent(oldRaw);
  const { newName } = await req.json();
  if (!newName) return NextResponse.json("缺少新名称", { status: 400 });

  const fromPrefix = `${uid}/paperEditor/${oldName}`;
  const toPrefix = `${uid}/paperEditor/${newName}`;

  console.log(`[PATCH] 开始重命名项目: ${oldName} -> ${newName}, 用户ID: ${uid}`);
  console.log(`[PATCH] 从前缀: ${fromPrefix}`);
  console.log(`[PATCH] 到前缀: ${toPrefix}`);

  try {
    // 首先检查目标路径是否已存在
    const { data: checkExists } = await serverSupabase.storage
      .from(BUCKET)
      .list(`${uid}/paperEditor`, { search: newName });

    if (checkExists && checkExists.some(item => item.name === newName)) {
      console.error(`[PATCH] 目标路径已存在: ${newName}`);
      return NextResponse.json(`项目名 '${newName}' 已存在`, { status: 400 });
    }

    // 获取需要移动的文件列表
    const files = await listRecursive(fromPrefix);
    console.log(`[PATCH] 找到 ${files.length} 个需要移动的文件`);

    if (files.length === 0) {
      console.error(`[PATCH] 没有找到要移动的文件: ${fromPrefix}`);
      return NextResponse.json(`项目 '${oldName}' 不存在或为空`, { status: 404 });
    }

    // 1) 复制
    console.log(`[PATCH] 开始复制文件到新路径`);
    const copyResults = await Promise.all(
      files.map(async f => {
        const targetPath = f.name.replace(fromPrefix, toPrefix);
        console.log(`[PATCH] 复制: ${f.name} -> ${targetPath}`);
        return serverSupabase.storage.from(BUCKET).copy(f.name, targetPath);
      })
    );

    // 检查复制是否成功
    const copyErrors = copyResults.filter(result => result.error);
    if (copyErrors.length > 0) {
      console.error(`[PATCH] 复制过程中出现 ${copyErrors.length} 个错误:`, copyErrors);
      throw new Error(`复制文件失败: ${copyErrors[0].error?.message}`);
    }

    // 2) 删除原对象
    console.log(`[PATCH] 开始删除原文件, 共 ${files.length} 个`);
    const removePaths = files.map(f => f.name);
    const { error: removeError } = await serverSupabase.storage.from(BUCKET).remove(removePaths);

    if (removeError) {
      console.error(`[PATCH] 删除原文件失败:`, removeError);
      throw removeError;
    }

    console.log(`[PATCH] 项目重命名成功: ${oldName} -> ${newName}`);
    return NextResponse.json({ ok: true, message: `重命名成功: ${oldName} -> ${newName}` });
  } catch (err: unknown) {
    console.error("[PATCH rename] 错误:", err);
    const msg = err instanceof Error ? err.message : String(err);
    return NextResponse.json(msg, { status: 500 });
  }
}

/* ---------------- DELETE - 删除 ---------------- */
export async function DELETE(_req: NextRequest, { params }: { params: Promise<{ name: string }> }) {
  const { name: encodedName } = await params; // ⬅️ await Promise
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) return NextResponse.json("Unauthorized", { status: 401 });

  const uid = session.user.id;
  const name = decodeURIComponent(encodedName);
  const prefix = `${uid}/paperEditor/${name}`;

  console.log(`[DELETE] 开始删除项目: ${name}, 用户ID: ${uid}`);
  console.log(`[DELETE] 完整路径前缀: ${prefix}`);

  try {
    // 获取需要删除的文件列表
    const files = await listRecursive(prefix);
    console.log(`[DELETE] 找到 ${files.length} 个需要删除的文件`);

    if (files.length === 0) {
      console.warn(`[DELETE] 没有找到要删除的文件: ${prefix}`);
      return NextResponse.json({ ok: true, message: `项目 '${name}' 不存在或已经删除` });
    }

    // 列出所有将要删除的文件路径
    const filePaths = files.map(f => f.name);
    console.log(`[DELETE] 将要删除的文件:`, filePaths);

    // 执行删除操作
    const { data, error } = await serverSupabase.storage.from(BUCKET).remove(filePaths);

    if (error) {
      console.error(`[DELETE] 删除文件错误:`, error);
      throw error;
    }

    console.log(`[DELETE] 删除结果:`, data);
    console.log(`[DELETE] 项目删除成功: ${name}`);

    return NextResponse.json({ ok: true, message: `删除成功: ${name}` });
  } catch (err: unknown) {
    console.error("[DELETE project] 错误:", err);
    const msg = err instanceof Error ? err.message : String(err);
    return NextResponse.json(msg, { status: 500 });
  }
}
