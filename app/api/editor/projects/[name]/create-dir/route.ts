import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* -------- 拼用户/项目前缀 -------- */
async function getPrefix(req: NextRequest): Promise<string> {
  // 从URL中获取动态路由参数
  const { pathname } = req.nextUrl;
  const segments = pathname.split("/");
  const name = segments[segments.length - 2]; // [name]/create-dir 格式获取name参数

  if (!name) {
    throw new Error("Missing project name");
  }

  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) throw new Error("Unauthorized");

  return `${session.user.id}/paperEditor/${decodeURIComponent(name)}`;
}

/* ---------------- POST /create-dir ---------------- */
async function postHandler(req: NextRequest) {
  try {
    const { path } = await req.json();

    if (!path || path.startsWith("/")) return NextResponse.json("非法 path", { status: 400 });

    const pfx = await getPrefix(req);
    const full = `${pfx}/${path}`;

    console.log(`[create-dir.POST] 创建目录: ${full}`);

    // 在Supabase中创建目录需要上传一个占位文件
    // 通常使用.init或类似的隐藏文件
    const emptyFile = new Uint8Array(0);
    const { error } = await serverSupabase.storage.from(BUCKET).upload(`${full}/.init`, emptyFile, {
      contentType: "application/octet-stream",
      upsert: false,
    });

    if (error) {
      const status = (error as any).status ?? (error as any).statusCode ?? 0;
      const msg = String(error.message ?? "");
      const isExist = status === 409 || /already exists/i.test(msg);

      if (isExist) return NextResponse.json("目录已存在", { status: 409 });

      throw error;
    }

    return NextResponse.json({ ok: true }, { status: 201 });
  } catch (e) {
    console.error("[create-dir]", e);
    const msg = e instanceof Error ? e.message : "Server error";

    if (msg.includes("Unauthorized")) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    return NextResponse.json(msg, { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const POST = withCors(postHandler);
