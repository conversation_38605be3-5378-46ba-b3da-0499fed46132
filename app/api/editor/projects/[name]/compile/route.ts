import { randomUUID } from "crypto";
import fs from "fs";
import { mkdir, writeFile, readFile, rm } from "fs/promises";
import { tmpdir } from "os";
import { join, dirname } from "path";

import type { FileObject } from "@supabase/storage-js";
import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";
import rehypeCitation from "rehype-citation";
import rehypeHighlight from "rehype-highlight";
import rehypeMathjax from "rehype-mathjax";
import rehypeRaw from "rehype-raw";
import rehypeStringify from "rehype-stringify";
import rehypeLineNumbers from "@/components/rehypeLineNumbers";
import remarkCaptions from "remark-captions";
import remarkFrontmatter from "remark-frontmatter";
import remarkGfm from "remark-gfm";
import remarkHeadingId from "remark-heading-id";
import remarkMath from "remark-math";
import remarkParse from "remark-parse";
import remarkRehype from "remark-rehype";
import { unified } from "unified";

import {
  isTimestampFile,
  findLatestFile,
  findFileOfType,
} from "@/app/editor/[name]/utils/filenameUtils";

/* —— 你的自定义插件 —— */
import remarkAnonymize from "@/components/remarkAnonymize";
import remarkCrossRef from "@/components/remarkCrossRef";
import remarkEmbedMedia from "@/components/remarkEmbedMedia";
import remarkPaperHeader from "@/components/remarkPaperHeader";
import remarkPathResolver from "@/components/remarkPathResolver";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

// 编译状态跟踪机制 - 使用内存Map来跟踪正在处理的编译请求
// 但不会拒绝请求，只用于日志和调试目的
// 格式：{userId}_{projectName} -> 锁定时间戳
const compileLocksMap = new Map<string, number>();

// 锁定过期时间（30秒），避免死锁
const LOCK_TIMEOUT_MS = 30000;

/* ---------- 小工具：递归列出所有文件 ---------- */
async function listRecursive(prefix: string): Promise<FileObject[]> {
  const stack = [prefix];
  const out: FileObject[] = [];

  while (stack.length) {
    const path = stack.pop()!;
    const { data, error } = await serverSupabase.storage.from(BUCKET).list(path, { limit: 1000 });
    if (error) throw error;
    for (const obj of data!) {
      const full = `${path}/${obj.name}`;
      const isDir = obj.metadata?.is_directory || obj.id === null;
      if (isDir) stack.push(full);
      else out.push({ ...obj, name: full });
    }
  }
  return out;
}

/* ---------- 创建临时目录 ---------- */
async function createTempDir(): Promise<string> {
  const tempDir = join(tmpdir(), randomUUID());
  await mkdir(tempDir, { recursive: true });
  return tempDir;
}

/* ---------- Markdown转HTML处理 ---------- */
async function markdownToHtml(
  content: string,
  tempDir: string,
  hasBib: boolean,
  userId: string,
  projectName: string
): Promise<string> {
  return String(
    await unified()
      .use(remarkParse)
      .use(remarkPathResolver, {
        userId,
        projectName,
        apiBasePath: "/api/editor/projects",
      })
      .use(remarkEmbedMedia)
      .use(remarkMath)
      .use(remarkFrontmatter, ["yaml"])
      .use(remarkAnonymize)
      .use(remarkPaperHeader)
      .use(remarkGfm)
      .use(remarkCaptions, {
        internal: { image: "Figure:" },
        external: { table: "Table:" },
      })
      .use(remarkCrossRef)
      .use(remarkHeadingId)
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeHighlight)
      .use(rehypeMathjax)
      .use(
        rehypeCitation,
        hasBib
          ? {
            path: tempDir,
            bibliography: ["references.bib"], // 使用固定名称，因为rehypeCitation会在tempDir中查找
            linkCitations: true,
            csl: "apa",
          }
          : {}
      )
      .use(rehypeLineNumbers)
      .use(rehypeStringify)
      .process(content)
  );
}

/* ---------- 下载编译所需的文件 ---------- */
async function downloadAllFiles(
  tempDir: string,
  userId: string,
  projectName: string,
  forceRefresh: boolean = false
) {
  // 构建存储路径
  const storagePath = `${userId}/paperEditor/${projectName}`;
  const historyPath = `${storagePath}/history`;

  // 使用Set来跟踪已下载的文件，避免重复下载
  const downloadedFiles = new Set<string>();

  // 列出根目录文件 - 不需要递归列出所有文件
  const { data, error } = await serverSupabase.storage.from(BUCKET).list(storagePath);
  if (error) {
    throw new Error(`Cannot list project files: ${error.message}`);
  }

  if (!data?.length) {
    throw new Error("Project is empty, no compile content");
  }

  console.log(`[compile.POST] Found ${data.length} files`);

  // 首先处理历史文件夹中的最新版本的paper.md和references.bib
  try {
    // 1. 只获取历史文件目录列表，不下载文件内容
    const { data: historyData, error: historyError } = await serverSupabase.storage
      .from(BUCKET)
      .list(historyPath);

    if (!historyError && historyData && historyData.length > 0) {
      console.log(`[compile.POST] History directory has ${historyData.length} files`);

      // 获取历史文件列表
      const historyFiles = historyData.map(file => file.name);

      // 只找到最新的 paper.md 和 references.bib 文件
      const latestPaper = findLatestFile(historyFiles, "paper");
      const latestRef = findLatestFile(historyFiles, "ref");

      // 下载最新的 paper.md
      if (latestPaper) {
        console.log(`[compile.POST] Use latest paper file in history directory: ${latestPaper}`);

        const { data: paperData, error: paperError } = await serverSupabase.storage
          .from(BUCKET)
          .download(`${historyPath}/${latestPaper}`);

        if (!paperError && paperData) {
          // 写入到临时目录中的 paper.md
          await writeFile(join(tempDir, "paper.md"), Buffer.from(await paperData.arrayBuffer()));
          console.log(`[compile.POST] Downloaded latest paper file to: ${join(tempDir, "paper.md")}`);

          // 标记为已下载，避免后面重复下载
          downloadedFiles.add(`${storagePath}/paper.md`);
        }
      }

      // 下载最新的 references.bib
      if (latestRef) {
        console.log(`[compile.POST] Use latest reference file in history directory: ${latestRef}`);

        const { data: refData, error: refError } = await serverSupabase.storage
          .from(BUCKET)
          .download(`${historyPath}/${latestRef}`);

        if (!refError && refData) {
          // 写入到临时目录中的 references.bib
          await writeFile(
            join(tempDir, "references.bib"),
            Buffer.from(await refData.arrayBuffer())
          );
          console.log(`[compile.POST] Downloaded latest reference file to: ${join(tempDir, "references.bib")}`);

          // 标记为已下载，避免后面重复下载
          downloadedFiles.add(`${storagePath}/references.bib`);
        }
      }
    }
  } catch (historyErr) {
    console.warn(`[compile.POST] Error processing history file, will use project root file:`, historyErr);
  }

  // 只处理根目录中的常规文件，不递归处理子目录
  // 对于图片和其他资源文件，只在paper.md中引用时按需下载
  for (const item of data) {
    // 如果是"history"目录，跳过处理（因为已经单独处理了最新版本）
    if (item.id === null && item.name === "history") {
      console.log(`[compile.POST] Skip history directory, avoid downloading all history files`);
      continue;
    }

    // 如果是其他目录，创建目录结构但不下载文件内容
    // 这些文件将在需要时根据引用按需下载
    if (item.id === null) {
      // 创建目录结构
      const dirPath = join(tempDir, item.name);
      await mkdir(dirPath, { recursive: true });
      console.log(`[compile.POST] Created directory structure: ${item.name}`);
      continue;
    }

    // 跳过已下载的文件
    const filePath = join(tempDir, item.name);
    const fileStoragePath = `${userId}/paperEditor/${projectName}/${item.name}`;

    // 处理特殊文件
    if (item.name === ".init") {
      console.log(`[compile.POST] Skip special configuration file: ${item.name}`);
      // 创建一个空的 .init 文件以保持结构完整
      try {
        const dir = dirname(filePath);
        await mkdir(dir, { recursive: true });
        await writeFile(filePath, "");
        console.log(`[compile.POST] Created empty placeholder file: ${item.name}`);
        continue;
      } catch (err) {
        console.warn(`[compile.POST] Cannot create placeholder file ${item.name}, but will continue compiling`, err);
        continue;
      }
    }

    // 只下载主要文件：paper.md 和 references.bib (如果还没被下载)
    // 其他文件如图片将在需要时才下载
    const isMainFile = ["paper.md", "references.bib"].includes(item.name);

    if (!isMainFile) {
      // 跳过非主要文件，这些文件会在paper.md引用时按需下载
      continue;
    }

    if (downloadedFiles.has(fileStoragePath)) {
      console.log(`[compile.POST] Skip downloaded file: ${item.name}`);
      continue;
    }

    // 对于强制刷新，添加缓存破坏参数
    const options: any = {};
    if (forceRefresh) {
      options.cacheControl = "no-cache";
    }

    try {
      // 确保fileStoragePath没有多余的斜杠
      const { data: fileData, error: fileError } = await serverSupabase.storage
        .from(BUCKET)
        .download(fileStoragePath.replace(/\/\//g, "/"), options);

      if (fileError) {
        console.warn(`[compile.POST] Download file failed ${item.name}: ${JSON.stringify(fileError)}`);
        // 不抛出错误，继续处理其他文件
        continue;
      }

      // 创建目录（如果需要）
      const dir = dirname(filePath);
      await mkdir(dir, { recursive: true });

      // 写入文件
      await writeFile(filePath, Buffer.from(await fileData.arrayBuffer()));
      console.log(`[compile.POST] Downloaded file: ${item.name}`);

      // 标记为已下载
      downloadedFiles.add(fileStoragePath);
    } catch (err) {
      console.warn(`[compile.POST] Error processing file ${item.name}:`, err);
      // 继续处理其他文件，而不是中断整个编译
      continue;
    }
  }
}

/* ---------- /compile ---------- */
async function compileHandler(req: NextRequest) {
  // 定义锁定相关变量，使其在finally中可访问
  let lockKey: string | null = null;
  let lockAcquired = false;

  try {
    /* ── 0. 路由参数 ─────────────────────────────── */
    // 从URL中获取动态路由参数
    const { pathname } = req.nextUrl;
    const segments = pathname.split("/");
    const name = segments[segments.length - 2]; // [name]/compile 格式获取name参数

    if (!name) {
      return NextResponse.json("Missing project name", { status: 400 });
    }

    /* 1) Auth ----------------------------------------------------------- */
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session) return NextResponse.json("Unauthorized", { status: 401 });
    const userId = session.user.id;

    // 创建锁定键 - 基于用户ID和项目名称
    lockKey = `${userId}_${name}`;
    const now = Date.now();

    // 检查是否已经有正在运行的相同编译任务
    const existingLock = compileLocksMap.get(lockKey);
    if (existingLock) {
      // 我们不再拒绝请求，而是只记录并继续处理
      // 这样可以避免在页面加载和刷新时出现编译错误
      if (now - existingLock < LOCK_TIMEOUT_MS) {
        console.log(`[compile.POST] ℹ️ Detected concurrent compile request, but allowed to continue: ${lockKey}`);
        // 不再拒绝请求，而是继续处理
      } else {
        // 锁定已过期，清除并继续
        console.log(`[compile.POST] Lock expired, re-acquire lock: ${lockKey}`);
        compileLocksMap.delete(lockKey);
      }
    }

    // 设置锁定
    compileLocksMap.set(lockKey, now);
    lockAcquired = true;
    console.log(`[compile.POST] Get compile lock: ${lockKey}`);

    /* 2) 获取请求参数 --------------------------------------------------- */
    const requestBody = await req.json();
    const { entry = "paper.md", forceRefresh = false, compileId, timestamp } = requestBody;
    const forcedRefresh = forceRefresh || req.headers.get("X-Force-Refresh") === "true";

    // 生成一个唯一请求ID用于日志追踪，如果没有提供compileId
    const requestId =
      compileId || `compile-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    console.log(
      `[compile.POST] Compile project: ${name}, user ID: ${userId}, entry file: ${entry}, compile ID: ${requestId}, timestamp: ${timestamp || Date.now()}, forced refresh: ${forcedRefresh}`
    );

    /* 3) 创建临时目录 --------------------------------------------------- */
    const projectName = decodeURIComponent(name);
    const tempDir = await createTempDir();
    console.log(`[compile.POST] Create temporary directory: ${tempDir}`);

    /* 4) 清除潜在的缓存 ------------------------------------------------- */
    if (forcedRefresh) {
      // 手动触发对storage API的请求，确保获取最新文件
      console.log(`[compile.POST] Perform forced refresh, ensure using latest files`);

      try {
        // 特别检查paper.md，确保它被刷新
        const paperPath = `${userId}/paperEditor/${projectName}/${entry}`;
        try {
          // 简单下载以触发缓存刷新
          await serverSupabase.storage.from(BUCKET).download(paperPath);
          console.log(`[compile.POST] ${entry} cache refresh successful`);
        } catch (paperErr) {
          console.warn(`[compile.POST] ${entry} does not exist or refresh failed`, paperErr);
        }

        console.log(`[compile.POST] Forced refresh completed, start downloading project files`);
      } catch (refreshErr) {
        console.warn(`[compile.POST] Forced refresh process error (continue compiling):`, refreshErr);
      }
    }

    /* 5) 下载项目文件到临时目录 ----------------------------------------- */
    await downloadAllFiles(tempDir, userId, projectName, forcedRefresh);

    /* 6) 验证下载的文件 ------------------------------------------------- */
    // 验证入口文件是否存在
    let entryPath;
    let foundEntryFile = false;

    try {
      // 1. 尝试直接使用指定的入口文件
      entryPath = join(tempDir, entry);
      await fs.promises.access(entryPath);
      foundEntryFile = true;
    } catch (err) {
      // 2. 如果指定的入口文件不存在，尝试在目录中查找带时间戳的paper文件或普通paper.md
      console.log(`[compile.POST] Specified entry file ${entry} does not exist, try to find paper file`);

      try {
        const tempFiles = await fs.promises.readdir(tempDir);
        const paperFile = findFileOfType(tempFiles, "paper.md", "paper");

        if (paperFile) {
          entryPath = join(tempDir, paperFile);
          foundEntryFile = true;
          console.log(`[compile.POST] Found paper file: ${paperFile}`);
        } else {
          // 3. 如果找不到任何paper文件，创建一个基本的paper.md
          console.error(`[compile.POST] No paper file found, try to create a basic paper.md file`);
          entryPath = join(tempDir, "paper.md");
          const basicContent = `# Project Document\n\nThis is a basic project document.`;
          await writeFile(entryPath, basicContent);
          foundEntryFile = true;
          console.log(`[compile.POST] Created basic paper.md file`);
        }
      } catch (createErr) {
        console.error(`[compile.POST] File lookup or creation error:`, createErr);
        throw new Error(`No usable paper file found, cannot create basic file`);
      }
    }

    if (!foundEntryFile) {
      throw new Error(`No entry file found: ${entry} or paper.md`);
    }

    // 获取文件大小进行日志记录
    const entryStats = await fs.promises.stat(entryPath);
    console.log(`[compile.POST] Entry file size: ${entryStats.size} bytes`);

    console.log(`[compile.POST] Reading entry file: ${entryPath}`);
    const content = await readFile(entryPath, "utf8");
    console.log(`[compile.POST] Entry file content size: ${content.length} bytes`);

    // 检查是否有参考文献文件（支持带时间戳的文件名）
    let bibPath;
    let hasBib = false;

    // 查找项目文件夹下的所有文件
    try {
      const tempFiles = await fs.promises.readdir(tempDir);

      // 查找带时间戳的references.bib或传统命名的references.bib
      const refFile = findFileOfType(tempFiles, "references.bib", "ref");

      if (refFile) {
        bibPath = join(tempDir, refFile);
        hasBib = true;
        console.log(`[compile.POST] Found reference file: ${refFile}`);

        // 检查参考文献文件内容
        const bibStats = await fs.promises.stat(bibPath);
        if (bibStats.size === 0) {
          await writeFile(bibPath, `@misc{empty2025,\n  title={},\n  author={},\n  year={2025}\n}`);
          console.log(`[compile.POST] Fixed empty reference file`);
        }
      } else {
        console.log(`[compile.POST] No reference file found`);
      }
    } catch (bibErr) {
      console.warn(`[compile.POST] Error checking reference file:`, bibErr);
      hasBib = false;
    }

    // 处理Markdown，传递用户ID和项目名称
    console.log(`[compile.POST] Start Markdown to HTML processing`);
    const projectNameDecoded = decodeURIComponent(projectName); // 解码项目名称以避免双重编码
    const html = await markdownToHtml(content, tempDir, hasBib, userId, projectNameDecoded);

    console.log(`[compile.POST] HTML processing completed, size: ${html.length} bytes`);

    /* 7) 清理临时目录 --------------------------------------------------- */
    try {
      await rm(tempDir, { recursive: true, force: true });
    } catch (e) {
      console.error("Failed to clean up temporary directory:", e);
    }

    /* 8) 返回HTML ------------------------------------------------------- */
    return new NextResponse(html, {
      headers: {
        "Content-Type": "text/html; charset=utf-8",
        "X-Compile-ID": requestId,
        "X-Compile-Time": Date.now().toString(),
        "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
        Pragma: "no-cache",
        Expires: "0",
        ETag: `"${Buffer.from(requestId).toString("base64")}"`, // 添加唯一ETag防止缓存
      },
    });
  } catch (err: unknown) {
    console.error("[compile]", err);
    const message = err instanceof Error ? err.message : "Compile error";
    return NextResponse.json(message, { status: 500 });
  } finally {
    // 无论成功或失败，都释放锁定
    if (lockKey && lockAcquired) {
      console.log(`[compile.POST] Release compile lock: ${lockKey}`);
      compileLocksMap.delete(lockKey);
    }
  }
}

// 使用CORS中间件包装处理器
export const POST = withCors(compileHandler);
