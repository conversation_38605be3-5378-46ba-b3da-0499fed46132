import type { FileObject } from "@supabase/storage-js";
import { createClient } from "@supabase/supabase-js";
import { NextResponse, NextRequest } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* -------- 递归列目录 -------- */
async function listRecursive(prefix: string): Promise<FileObject[]> {
  const stack = [prefix];
  const out: FileObject[] = [];

  while (stack.length) {
    const path = stack.pop()!;
    const { data, error } = await serverSupabase.storage.from(BUCKET).list(path, { limit: 1000 });
    if (error) throw error;

    for (const obj of data!) {
      const full = `${path}/${obj.name}`;
      out.push({ ...obj, name: full });
      if (obj.metadata?.is_directory || obj.id === null) stack.push(full);
    }
  }
  return out;
}

// 修改API路由处理函数，使其符合NextRequest格式
async function getHandler(req: NextRequest) {
  try {
    // 从URL中获取动态路由参数
    const { pathname } = req.nextUrl;
    const segments = pathname.split("/");
    const name = segments[segments.length - 2]; // [name]/tree 格式获取name参数

    if (!name) {
      return NextResponse.json("Missing project name", { status: 400 });
    }

    /* 1) Auth ----------------------------------------------------------- */
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session) return NextResponse.json("Unauthorized", { status: 401 });

    const uid = session.user.id;
    const proj = decodeURIComponent(name);
    const prefix = `${uid}/paperEditor/${proj}`;

    console.log(`[tree.GET] 获取项目文件树: ${proj}, 用户ID: ${uid}`);

    const objs = await listRecursive(prefix);
    const root: Record<string, any> = {};

    /* ---------- 构建嵌套树 ---------- */
    for (const obj of objs) {
      const rel = obj.name.replace(`${prefix}/`, "");
      if (!rel || rel.endsWith("/.init") || rel === ".init") continue;

      const segs = rel.split("/");
      let cur = root;

      segs.forEach((seg, i) => {
        const isLast = i === segs.length - 1;
        const isDir = obj.metadata?.is_directory || obj.id === null;

        if (isLast) {
          if (!cur[seg]) cur[seg] = { isDir, ...(isDir ? { children: {} } : {}) };
          else if (isDir && !cur[seg].isDir) {
            cur[seg].isDir = true;
            cur[seg].children = cur[seg].children || {};
          }
        } else {
          if (!cur[seg] || !cur[seg].isDir)
            cur[seg] = { isDir: true, children: cur[seg]?.children || {} };
          cur[seg].children ??= {};
          cur = cur[seg].children!;
        }
      });
    }

    /* ---------- 转数组返回 ---------- */
    const toArray = (node: any, base = ""): any[] =>
      Object.entries(node).map(([k, info]: any) => ({
        name: k,
        path: `${base}${k}`,
        isDir: info.isDir,
        children: info.isDir ? toArray(info.children || {}, `${base}${k}/`) : [],
      }));

    return NextResponse.json(toArray(root));
  } catch (err: unknown) {
    console.error("[tree]", err);
    const msg = err instanceof Error ? err.message : "Server Error";
    return NextResponse.json(msg, { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const GET = withCors(getHandler);
