import type { FileObject } from "@supabase/storage-js";
import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const BUCKET = "user-temp";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

async function getPrefix(req: NextRequest): Promise<string> {
  // 从URL中获取动态路由参数
  const { pathname } = req.nextUrl;
  const segments = pathname.split("/");
  const name = segments[segments.length - 2]; // [name]/delete 格式获取name参数

  if (!name) {
    throw new Error("Missing project name");
  }

  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) throw new Error("Unauthorized");
  return `${session.user.id}/paperEditor/${decodeURIComponent(name)}`;
}

/**
 * 递归列出目录下所有文件
 */
async function listFilesRecursively(prefix: string): Promise<string[]> {
  const result: string[] = [];
  const stack: string[] = [prefix];

  while (stack.length > 0) {
    const currentPath = stack.pop()!;

    // 列出当前路径下的所有文件和目录
    const { data, error } = await serverSupabase.storage
      .from(BUCKET)
      .list(currentPath, { sortBy: { column: "name", order: "asc" } });

    if (error) {
      console.error(`列出目录失败: ${currentPath}`, error);
      continue;
    }

    if (!data || data.length === 0) {
      // 如果是空目录，也要记录该目录的 .init 文件
      if (currentPath !== prefix) {
        result.push(`${currentPath}/.init`);
      }
      continue;
    }

    for (const item of data) {
      const fullPath = `${currentPath}/${item.name}`;

      if (item.id === null || (item.metadata && item.metadata.is_directory)) {
        // 目录: 加入栈中以便后续处理
        stack.push(fullPath);
      } else {
        // 文件: 直接加入结果
        result.push(fullPath);
      }
    }
  }

  return result;
}

async function deleteHandler(req: NextRequest) {
  try {
    const { path, isDir } = await req.json();
    if (!path) return NextResponse.json("缺少 path", { status: 400 });

    const pfx = await getPrefix(req);
    const fullPath = `${pfx}/${path}`;

    console.log(`[delete.DELETE] 准备删除: ${fullPath}`);

    // 如果前端明确指明文件/目录类型，则直接使用
    let isDirectory: boolean;
    if (typeof isDir === "boolean") {
      isDirectory = isDir;
    } else {
      // 否则自动检测: Supabase 对文件 path 调用 list 时返回空数组且无错误，
      // 因此仅当返回项数量 > 0 时才认定为目录；否则先按文件处理。
      const { data, error: listError } = await serverSupabase.storage.from(BUCKET).list(fullPath);
      isDirectory = !listError && data && data.length > 0;
    }

    if (isDirectory) {
      // 是目录: 递归删除目录下所有文件
      console.log(`[delete.DELETE] 检测到目录, 执行递归删除: ${fullPath}`);

      // 1. 列出所有文件
      const allFiles = await listFilesRecursively(fullPath);
      console.log(`[delete.DELETE] 目录下找到 ${allFiles.length} 个文件需要删除`);

      if (allFiles.length === 0) {
        // 空目录，只删除可能存在的 .init 文件
        const { error } = await serverSupabase.storage.from(BUCKET).remove([`${fullPath}/.init`]);

        if (error && !error.message.includes("not found")) {
          throw error;
        }
      } else {
        // 批量删除文件 (Supabase 限制每次最多删除1000个)
        for (let i = 0; i < allFiles.length; i += 1000) {
          const batch = allFiles.slice(i, i + 1000);
          const { error } = await serverSupabase.storage.from(BUCKET).remove(batch);

          if (error) throw error;
        }
      }

      console.log(`[delete.DELETE] 递归删除完成: ${fullPath}`);
    } else {
      // 是单个文件: 直接删除
      console.log(`[delete.DELETE] 删除单个文件: ${fullPath}`);
      const { error } = await serverSupabase.storage.from(BUCKET).remove([fullPath]);

      if (error) throw error;
    }

    return NextResponse.json({ ok: true });
  } catch (err) {
    console.error("[delete]", err);

    const message = err instanceof Error ? err.message : String(err);

    if (message.includes("Unauthorized")) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    return NextResponse.json("服务器错误", { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const DELETE = withCors(deleteHandler);
