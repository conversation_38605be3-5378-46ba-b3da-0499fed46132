import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { withCors } from "@/utils/cors";
import { getSessionCompat } from "@/utils/supabase/compat";

const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!, // server-only
  { auth: { persistSession: false } }
);

const BUCKET = "user-temp";
const TEMPLATE_BUCKET = "web-resource"; // 模板存储桶
const TEMPLATE_PATH = "editorTemplate"; // 模板文件夹路径

async function getHandler() {
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) return NextResponse.json("Unauthorized", { status: 401 });

  const uid = session.user.id;

  /* 列出 userId/paperEditor/ 下所有文件夹 */
  const { data, error } = await serverSupabase.storage
    .from(BUCKET)
    .list(`${uid}/paperEditor`, { limit: 1000 }); // ← 无 offset

  // —— 简单日志 ——（在本地 dev 或 serverless log 中查看）
  console.log("[projects.GET] list result:", { data, error });

  if (error) return NextResponse.json(error.message, { status: 500 });

  /* 🔑 判断"是不是目录" */
  const folderNames = data
    .filter((item: any) => item.id === null) // 👈 只保留目录
    .map(({ name }: { name: string }) => name);

  // 为每个文件夹查询其下的文件，获取时间信息
  const rows = await Promise.all(
    folderNames.map(async (folderName: string) => {
      try {
        // 使用storage.list()方法列出项目文件夹中的文件
        const { data: filesData, error: filesError } = await serverSupabase.storage
          .from(BUCKET)
          .list(`${uid}/paperEditor/${folderName}`, {
            limit: 1000,
            sortBy: { column: "name", order: "asc" }
          });

        if (filesError) {
          console.error(`[projects.GET] Query project ${folderName} file failed:`, filesError);
          return {
            name: folderName,
            createdAt: null,
            updatedAt: null,
            lastAccessedAt: null,
          };
        }

        if (!filesData || filesData.length === 0) {
          // 如果没有找到任何文件，返回null时间
          return {
            name: folderName,
            createdAt: null,
            updatedAt: null,
            lastAccessedAt: null,
          };
        }

        // 寻找paper.md和references.bib文件，或使用所有文件
        const targetFileNames = ['paper.md', 'references.bib'];
        const targetFiles = filesData.filter(file =>
          file.id !== null && // 只要文件，不要目录
          targetFileNames.includes(file.name)
        );

        // 如果没有找到目标文件，使用所有文件
        const filesToAnalyze = targetFiles.length > 0 ? targetFiles : filesData.filter(f => f.id !== null);

        if (filesToAnalyze.length === 0) {
          return {
            name: folderName,
            createdAt: null,
            updatedAt: null,
            lastAccessedAt: null,
          };
        }

        // 筛选有有效时间信息的文件
        const validTimes = filesToAnalyze.filter(file => file.created_at && file.updated_at);

        if (validTimes.length === 0) {
          return {
            name: folderName,
            createdAt: null,
            updatedAt: null,
            lastAccessedAt: null,
          };
        }

        // 项目创建时间 = 最早的文件创建时间
        const createdAt = validTimes.reduce((earliest, file) => {
          return !earliest || new Date(file.created_at) < new Date(earliest)
            ? file.created_at
            : earliest;
        }, null as string | null);

        // 项目修改时间 = 最新的文件修改时间
        const updatedAt = validTimes.reduce((latest, file) => {
          return !latest || new Date(file.updated_at) > new Date(latest)
            ? file.updated_at
            : latest;
        }, null as string | null);

        // 项目最后访问时间 = 最新的文件访问时间
        const lastAccessedAt = filesToAnalyze
          .filter(file => file.last_accessed_at)
          .reduce((latest, file) => {
            return !latest || new Date(file.last_accessed_at) > new Date(latest)
              ? file.last_accessed_at
              : latest;
          }, null as string | null);

        console.log(`[projects.GET] Project ${folderName} time information:`, {
          totalFiles: filesData.length,
          targetFiles: targetFiles.length,
          filesAnalyzed: filesToAnalyze.length,
          validTimes: validTimes.length,
          createdAt,
          updatedAt,
          lastAccessedAt
        });

        return {
          name: folderName,
          createdAt,
          updatedAt,
          lastAccessedAt,
        };
      } catch (error) {
        console.error(`[projects.GET] Error processing project ${folderName}:`, error);
        return {
          name: folderName,
          createdAt: null,
          updatedAt: null,
          lastAccessedAt: null,
        };
      }
    })
  );

  return NextResponse.json(rows);
}

async function postHandler(req: NextRequest) {
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session) return NextResponse.json("Unauthorized", { status: 401 });

  const { name } = await req.json();
  if (!name) return NextResponse.json("Missing project name", { status: 400 });
  const uid = session.user.id;
  const prefix = `${uid}/paperEditor/${name}`;

  console.log(`[projects.POST] Create project: ${name}, user ID: ${uid}`);

  try {
    /* 检查是否已存在 */
    const { data: ex, error: listError } = await serverSupabase.storage
      .from(BUCKET)
      .list(`${uid}/paperEditor`, { search: name });

    if (listError) {
      console.error("[projects.POST] List directory error:", listError);
      return NextResponse.json(listError.message, { status: 500 });
    }

    if (ex?.some((i: any) => i.name === name)) {
      return NextResponse.json("Project name already exists", { status: 400 });
    }

    // 创建项目基本结构 - 创建一个初始化标记文件
    const empty = new Blob([""]);
    const { error: initError } = await serverSupabase.storage
      .from(BUCKET)
      .upload(`${prefix}/.init`, empty, { contentType: "text/plain", upsert: true });

    if (initError) {
      console.error("[projects.POST] Initialize project structure failed:", initError);
      return NextResponse.json(initError.message, { status: 500 });
    }

    // 列出模板文件夹中的所有文件
    console.log(`[projects.POST] Listing files in template folder ${TEMPLATE_PATH}`);

    // 递归获取模板文件夹中的所有文件
    async function listAllTemplateFiles(path: string): Promise<string[]> {
      const allFiles: string[] = [];
      const { data, error } = await serverSupabase.storage
        .from(TEMPLATE_BUCKET)
        .list(path, { sortBy: { column: "name", order: "asc" } });

      if (error) {
        console.error(`[projects.POST] List template file failed ${path}:`, error);
        throw new Error(`List template file failed: ${error.message}`);
      }

      if (!data || data.length === 0) return allFiles;

      for (const item of data) {
        const itemPath = `${path}/${item.name}`;

        if (item.id === null) {
          // 这是一个目录，递归获取子文件
          const subFiles = await listAllTemplateFiles(itemPath);
          allFiles.push(...subFiles);
        } else {
          // 这是一个文件
          allFiles.push(itemPath);
        }
      }

      return allFiles;
    }

    try {
      // 获取模板文件夹中的所有文件
      const templateFiles = await listAllTemplateFiles(TEMPLATE_PATH);
      console.log(`[projects.POST] Found ${templateFiles.length} template files to copy`);

      // 复制所有文件到目标项目
      for (const templatePath of templateFiles) {
        // 计算相对路径，去掉模板前缀
        const relativePath = templatePath.substring(TEMPLATE_PATH.length + 1);
        const targetPath = `${prefix}/${relativePath}`;

        console.log(`[projects.POST] Copy file: ${templatePath} -> ${targetPath}`);

        // 先获取模板文件内容
        const { data, error: downloadError } = await serverSupabase.storage
          .from(TEMPLATE_BUCKET)
          .download(templatePath);

        if (downloadError) {
          console.error(`[projects.POST] Download template file failed ${templatePath}:`, downloadError);
          throw new Error(`Download template file failed: ${downloadError.message}`);
        }

        // 上传到目标位置
        const { error: uploadError } = await serverSupabase.storage
          .from(BUCKET)
          .upload(targetPath, data, {
            contentType: data.type,
            upsert: true,
          });

        if (uploadError) {
          console.error(`[projects.POST] Upload file failed ${targetPath}:`, uploadError);
          throw new Error(`Upload file failed: ${uploadError.message}`);
        }
      }

      console.log(`[projects.POST] All template files copied`);
      return NextResponse.json({ ok: true });
    } catch (error) {
      console.error("[projects.POST] Copy template file failed:", error);

      // 尝试清理已创建的目录结构
      try {
        const { data: createdFiles } = await serverSupabase.storage.from(BUCKET).list(prefix);

        if (createdFiles && createdFiles.length > 0) {
          await serverSupabase.storage
            .from(BUCKET)
            .remove(createdFiles.map(f => `${prefix}/${f.name}`));
        }

        // 删除初始化标记
        await serverSupabase.storage.from(BUCKET).remove([`${prefix}/.init`]);
      } catch (cleanupError) {
        console.error("[projects.POST] Clean up failed:", cleanupError);
      }

      return NextResponse.json(
        `Create project failed: ${error instanceof Error ? error.message : String(error)}`,
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("[projects.POST] Unexpected error:", error);
    return NextResponse.json("Server internal error", { status: 500 });
  }
}

// 使用CORS中间件包装处理器
export const GET = withCors(getHandler);
export const POST = withCors(postHandler);
