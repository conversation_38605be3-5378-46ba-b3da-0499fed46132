// app/api/users/route.ts
import { Role } from "@prisma/client";
import { NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";

/** 允许访问该接口的"编辑层"角色集合 */
const EDITOR_ROLES: Role[] = [Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR, Role.PAPER_EDITOR];

/**
 * GET /api/users
 * 返回全站用户的基本信息（id / name / email）。
 * 仅允许 *编辑层* 角色访问。
 */
export async function GET() {
  /* 1) Session 与 RBAC ------------------------------------------------------ */
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // JWT 中已写入 roles（数组），形如：[{ role, scope, scopeId? }, …]
  const roles = session.user.roles as { role: Role }[] | undefined;
  const isEditor = roles?.some(r => EDITOR_ROLES.includes(r.role));
  if (!isEditor) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  /* 2) 查询用户列表 --------------------------------------------------------- */
  const users = await prisma.user.findMany({
    select: { id: true, name: true, email: true },
    orderBy: { email: "asc" },
  });

  return NextResponse.json(users);
}
