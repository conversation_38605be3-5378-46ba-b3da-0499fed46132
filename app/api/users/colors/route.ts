/**
 * 批量获取用户颜色API
 * 支持高效的多用户颜色查询，优化数据库性能
 *
 * 特性：
 * - 批量查询减少数据库连接
 * - 自动去重避免重复查询
 * - 智能回退确保系统可用性
 * - 输入验证保证数据安全
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { DEFAULT_AVATAR_COLOR } from '@/lib/userColorManager';

// 请求体验证模式
const requestSchema = z.object({
    userIds: z.array(z.string().uuid()).min(1).max(100), // 最多支持100个UUID格式的用户ID
});

export async function POST(request: NextRequest) {
    try {
        // 验证请求体
        const body = await request.json();
        const { userIds } = requestSchema.parse(body);

        // 去重用户ID并过滤空值
        const uniqueUserIds = Array.from(new Set(userIds.filter(Boolean)));

        if (uniqueUserIds.length === 0) {
            return NextResponse.json({
                success: true,
                userColors: {},
                count: 0,
            });
        }

        // 批量查询用户颜色
        const users = await prisma.user.findMany({
            where: {
                id: {
                    in: uniqueUserIds,
                },
            },
            select: {
                id: true,
                avatarColor: true,
            },
        });

        // 构建用户颜色映射
        const userColors: Record<string, string> = {};

        // 为查询到的用户设置颜色
        users.forEach(user => {
            userColors[user.id] = user.avatarColor || DEFAULT_AVATAR_COLOR;
        });

        // 为未找到的用户使用默认颜色
        const foundUserIds = new Set(users.map(u => u.id));
        uniqueUserIds.forEach(userId => {
            if (!foundUserIds.has(userId)) {
                userColors[userId] = DEFAULT_AVATAR_COLOR;
            }
        });

        return NextResponse.json({
            success: true,
            userColors,
            count: Object.keys(userColors).length,
        });

    } catch (error) {
        // 在开发环境记录详细错误，生产环境记录简化信息
        if (process.env.NODE_ENV === 'development') {
            console.error('批量获取用户颜色失败:', error);
        }

        if (error instanceof z.ZodError) {
            return NextResponse.json(
                {
                    error: '请求参数无效',
                    details: process.env.NODE_ENV === 'development' ? error.errors : undefined,
                },
                { status: 400 }
            );
        }

        return NextResponse.json(
            {
                error: '服务器内部错误',
                details: process.env.NODE_ENV === 'development' ?
                    (error instanceof Error ? error.message : '未知错误') : undefined
            },
            { status: 500 }
        );
    }
}
