/**
 * 安全状态检查API
 * 提供系统安全状态和审计信息
 */

import { NextRequest, NextResponse } from "next/server";
import { Role, ScopeType } from "@prisma/client";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";
import { auditLog } from "@/lib/security/audit";
import { getRateLimitStatus, createSecureResponse } from "@/lib/security/middleware";
import { getClientIP, getUserAgent } from "@/lib/security/utils";

/**
 * 检查用户是否有管理员权限
 */
function hasAdminPermission(user: any): boolean {
  if (!user?.user_metadata?.roles || !Array.isArray(user.user_metadata.roles)) {
    return false;
  }

  return user.user_metadata.roles.some(
    (r: any) => r.role === Role.ADMIN && r.scope === ScopeType.GLOBAL
  );
}

/**
 * GET /api/security/status
 * 获取安全状态信息
 */
export async function GET(req: NextRequest) {
  try {
    // 1. 身份验证
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);

    if (!session?.user) {
      return createSecureResponse(
        { error: "Unauthorized" },
        401
      );
    }

    // 2. 权限检查 - 只有管理员可以查看安全状态
    if (!hasAdminPermission(session.user)) {
      return createSecureResponse(
        { error: "Forbidden - Admin access required" },
        403
      );
    }

    const clientIP = await getClientIP(req);
    const userAgent = await getUserAgent(req);

    // 3. 记录访问审计
    await auditLog.create({
      userId: session.user.id,
      action: 'SECURITY_STATUS_ACCESS',
      resourceType: 'SECURITY',
      resourceId: 'status',
      reason: 'Admin accessed security status',
      ipAddress: clientIP,
      userAgent: userAgent,
    });

    // 4. 获取安全统计信息
    const [
      auditStats,
      rateLimitStatus,
      recentSecurityEvents
    ] = await Promise.all([
      auditLog.getStats('day'),
      Promise.resolve(getRateLimitStatus(clientIP)),
      auditLog.query({
        action: 'SECURITY_%',
        startDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // 最近24小时
        limit: 50,
      })
    ]);

    // 5. 分析安全事件
    const securityAnalysis = analyzeSecurityEvents(recentSecurityEvents);

    return createSecureResponse({
      success: true,
      timestamp: new Date().toISOString(),
      auditStats,
      rateLimitStatus,
      securityAnalysis,
      recentEvents: recentSecurityEvents.slice(0, 10), // 只返回最近10条
    });

  } catch (error) {
    console.error("Security status API error:", error);
    
    return createSecureResponse(
      { error: "Internal server error" },
      500
    );
  }
}

/**
 * POST /api/security/status
 * 执行安全检查和清理操作
 */
export async function POST(req: NextRequest) {
  try {
    // 1. 身份验证
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);

    if (!session?.user) {
      return createSecureResponse(
        { error: "Unauthorized" },
        401
      );
    }

    // 2. 权限检查
    if (!hasAdminPermission(session.user)) {
      return createSecureResponse(
        { error: "Forbidden - Admin access required" },
        403
      );
    }

    const clientIP = await getClientIP(req);
    const userAgent = await getUserAgent(req);

    // 3. 解析请求体
    const body = await req.json();
    const { action } = body;

    let result: any = {};

    switch (action) {
      case 'cleanup_logs':
        // 清理过期审计日志
        const deletedCount = await auditLog.cleanup();
        result = { deletedLogs: deletedCount };
        break;

      case 'security_scan':
        // 执行安全扫描
        result = await performSecurityScan();
        break;

      case 'reset_rate_limits':
        // 重置速率限制（实际实现需要访问速率限制器实例）
        result = { message: "Rate limits reset requested" };
        break;

      default:
        return createSecureResponse(
          { error: "Invalid action" },
          400
        );
    }

    // 4. 记录管理操作
    await auditLog.create({
      userId: session.user.id,
      action: `SECURITY_ADMIN_${action.toUpperCase()}`,
      resourceType: 'SECURITY',
      resourceId: 'admin',
      reason: `Admin executed security action: ${action}`,
      metadata: result,
      ipAddress: clientIP,
      userAgent: userAgent,
    });

    return createSecureResponse({
      success: true,
      action,
      result,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Security admin action error:", error);
    
    return createSecureResponse(
      { error: "Internal server error" },
      500
    );
  }
}

/**
 * 分析安全事件
 */
function analyzeSecurityEvents(events: any[]): {
  totalEvents: number;
  eventsByType: Record<string, number>;
  suspiciousIPs: string[];
  threatLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  recommendations: string[];
} {
  const eventsByType: Record<string, number> = {};
  const ipCounts: Record<string, number> = {};
  
  events.forEach(event => {
    // 统计事件类型
    eventsByType[event.action] = (eventsByType[event.action] || 0) + 1;
    
    // 统计IP地址
    if (event.ipAddress) {
      ipCounts[event.ipAddress] = (ipCounts[event.ipAddress] || 0) + 1;
    }
  });

  // 识别可疑IP（事件数量异常高的IP）
  const suspiciousIPs = Object.entries(ipCounts)
    .filter(([_, count]) => count > 10) // 24小时内超过10次安全事件
    .map(([ip, _]) => ip);

  // 评估威胁级别
  let threatLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
  const criticalEvents = events.filter(e => e.severity === 'CRITICAL').length;
  const highEvents = events.filter(e => e.severity === 'HIGH').length;

  if (criticalEvents > 0) {
    threatLevel = 'CRITICAL';
  } else if (highEvents > 5) {
    threatLevel = 'HIGH';
  } else if (events.length > 50) {
    threatLevel = 'MEDIUM';
  }

  // 生成建议
  const recommendations: string[] = [];
  
  if (suspiciousIPs.length > 0) {
    recommendations.push(`Consider blocking suspicious IPs: ${suspiciousIPs.slice(0, 3).join(', ')}`);
  }
  
  if (eventsByType['SECURITY_RATE_LIMIT_EXCEEDED'] > 20) {
    recommendations.push('High rate limit violations detected - consider tightening limits');
  }
  
  if (eventsByType['SECURITY_SUSPICIOUS_ACTIVITY'] > 10) {
    recommendations.push('Multiple suspicious activities detected - review security rules');
  }

  if (recommendations.length === 0) {
    recommendations.push('No immediate security concerns detected');
  }

  return {
    totalEvents: events.length,
    eventsByType,
    suspiciousIPs,
    threatLevel,
    recommendations,
  };
}

/**
 * 执行安全扫描
 */
async function performSecurityScan(): Promise<{
  scanTime: string;
  checks: Array<{
    name: string;
    status: 'PASS' | 'WARN' | 'FAIL';
    message: string;
  }>;
}> {
  const checks: Array<{
    name: string;
    status: 'PASS' | 'WARN' | 'FAIL';
    message: string;
  }> = [];

  // 检查环境变量
  checks.push({
    name: 'Environment Variables',
    status: process.env.NODE_ENV === 'production' ? 'PASS' : 'WARN',
    message: process.env.NODE_ENV === 'production' 
      ? 'Running in production mode' 
      : 'Not running in production mode',
  });

  // 检查数据库连接
  try {
    // 这里可以添加数据库连接检查
    checks.push({
      name: 'Database Connection',
      status: 'PASS',
      message: 'Database connection healthy',
    });
  } catch {
    checks.push({
      name: 'Database Connection',
      status: 'FAIL',
      message: 'Database connection failed',
    });
  }

  // 检查审计日志系统
  try {
    await auditLog.getStats('day');
    checks.push({
      name: 'Audit Logging',
      status: 'PASS',
      message: 'Audit logging system operational',
    });
  } catch {
    checks.push({
      name: 'Audit Logging',
      status: 'FAIL',
      message: 'Audit logging system error',
    });
  }

  return {
    scanTime: new Date().toISOString(),
    checks,
  };
}
