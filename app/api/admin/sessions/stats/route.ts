import { Role, ScopeType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";

/**
 * 检查用户是否具有全局管理员权限
 */
function hasGlobalAdminPermission(user: any): boolean {
  if (!user?.user_metadata?.roles || !Array.isArray(user.user_metadata.roles)) {
    console.log("权限检查：用户没有角色列表或格式错误");
    return false;
  }

  const isGlobalAdmin = user.user_metadata.roles.some(
    (r: any) => r.role === Role.ADMIN && r.scope === ScopeType.GLOBAL
  );

  console.log(
    "权限检查：用户角色",
    JSON.stringify(user.user_metadata.roles),
    "是否全局管理员:",
    isGlobalAdmin
  );
  return isGlobalAdmin;
}

/**
 * GET /api/admin/sessions/stats
 * 获取会话统计数据
 */
export async function GET(req: NextRequest) {
  try {
    // 检查权限
    const supabase = await getSupabaseRoute();
    // 先验证用户身份
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: "未授权" }, { status: 401 });
    }

    console.log("会话统计API访问，用户:", user.id);

    // 检查全局管理员权限，使用验证过的用户
    if (!hasGlobalAdminPermission(user)) {
      return NextResponse.json({ error: "没有权限访问管理功能" }, { status: 403 });
    }

    // 获取活跃会话总数
    const activeSessions = await prisma.$queryRaw<{ count: bigint }[]>`
            SELECT COUNT(*) as count FROM "Session" WHERE expires > NOW()
        `;
    const activeSessionCount = Number(activeSessions[0].count);

    // 获取活跃用户总数（有活跃会话的不同用户数）
    const activeUsers = await prisma.$queryRaw<{ count: bigint }[]>`
            SELECT COUNT(DISTINCT "userId") as count 
            FROM "Session" 
            WHERE expires > NOW()
        `;
    const activeUserCount = Number(activeUsers[0].count);

    // 获取今日登录用户数（根据会话创建时间）
    const todayUsers = await prisma.$queryRaw<{ count: bigint }[]>`
            SELECT COUNT(DISTINCT "userId") as count 
            FROM "Session" 
            WHERE expires > NOW() 
            AND "sessionToken" IN (
                SELECT "sessionToken" FROM "Session" 
                WHERE DATE("expires") = CURRENT_DATE + INTERVAL '1 day'
            )
        `;
    const todayUserCount = Number(todayUsers[0].count);

    // 按角色统计活跃用户
    const roleStats = await prisma.$queryRaw<{ role: string; count: bigint }[]>`
            SELECT r.role, COUNT(DISTINCT s."userId") as count
            FROM "Session" s
            JOIN "RoleBinding" r ON s."userId" = r."principalId"
            WHERE s.expires > NOW()
            GROUP BY r.role
        `;

    // 将 BigInt 转换为 Number
    const usersByRole = roleStats.map(item => ({
      role: item.role,
      count: Number(item.count),
    }));

    // 获取按IP分组的会话数量
    const ipStats = await prisma.$queryRaw<{ ipAddress: string; count: bigint }[]>`
            SELECT "ipAddress", COUNT(*) as count
            FROM "Session"
            WHERE expires > NOW() AND "ipAddress" IS NOT NULL
            GROUP BY "ipAddress"
            ORDER BY count DESC
            LIMIT 10
        `;

    const sessionsByIp = ipStats.map(item => ({
      ipAddress: item.ipAddress || "unknown",
      count: Number(item.count),
    }));

    // 获取最近1小时活跃用户数
    const recentActiveUsers = await prisma.$queryRaw<{ count: bigint }[]>`
            SELECT COUNT(DISTINCT "userId") as count
            FROM "Session"
            WHERE "lastActiveAt" > NOW() - INTERVAL '1 hour'
        `;
    const recentActiveUserCount = Number(recentActiveUsers[0]?.count || 0);

    return NextResponse.json({
      activeSessionCount,
      activeUserCount,
      todayUserCount,
      recentActiveUserCount,
      usersByRole,
      sessionsByIp,
    });
  } catch (error) {
    console.error("获取会话统计失败:", error);
    return NextResponse.json({ error: "获取会话统计失败" }, { status: 500 });
  }
}
