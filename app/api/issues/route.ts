// app/api/issues/route.ts
import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";

/**
 * GET /api/issues?fields=id,name&includeUnpublished=true
 *
 * 支持通过 fields 选择要返回的字段，字段名用逗号分隔。
 * 若未传 fields，则默认返回 id 和 name。
 * 默认仅返回已经 "上线"(publishedAt 非 null) 的期刊；
 * 如果提供了 includeUnpublished=true 参数，则返回所有期次。
 * 结果按发布时间倒序，然后按创建时间倒序。
 */
export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);

  /* ────── 解析 ?fields=... ────── */
  const fieldsParam = searchParams.get("fields");
  const wanted = (fieldsParam ?? "id,name")
    .split(",")
    .map(s => s.trim())
    .filter(Boolean);

  /* ────── 解析 ?includeUnpublished=... ────── */
  const includeUnpublished = searchParams.get("includeUnpublished") === "true";

  // prisma 的 select 形如 { id: true, name: true }
  const select = Object.fromEntries(wanted.map(f => [f, true]));

  try {
    const issues = await prisma.issue.findMany({
      where: includeUnpublished ? {} : { publishedAt: { not: null } },
      orderBy: [{ publishedAt: "desc" }, { createdAt: "desc" }],
      select,
    });

    return NextResponse.json(issues, { status: 200 });
  } catch (err) {
    console.error("[api/issues] ", err);
    return NextResponse.json({ message: "Internal Server Error" }, { status: 500 });
  }
}

export const revalidate = 60;
