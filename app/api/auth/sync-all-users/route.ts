import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";
import { syncUserToPrisma, syncRolesToAuthUser } from "@/lib/syncUser";
import { createRouteHandlerClient } from "@/utils/supabase/server";

// 服务端管理员客户端
const adminClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/**
 * 同步所有用户 - 仅管理员可用
 */
export async function GET(req: NextRequest) {
  try {
    // 1. 验证当前用户是否是管理员
    const supabase = await createRouteHandlerClient();
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "未授权" }, { status: 401 });
    }

    // 检查用户是否是管理员
    const isAdmin = await prisma.roleBinding.findFirst({
      where: {
        principalId: user.id,
        role: "ADMIN",
        scopeType: "GLOBAL",
      },
    });

    if (!isAdmin) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    // 2. 获取所有Supabase Auth用户
    const { data: authUsers, error: listError } = await adminClient.auth.admin.listUsers();

    if (listError) {
      return NextResponse.json({ error: listError.message }, { status: 500 });
    }

    // 3. 同步每个用户
    const results = [];

    for (const authUser of authUsers.users) {
      try {
        // 同步到Prisma
        const syncToPrismaResult = await syncUserToPrisma(authUser);

        // 同步角色到Auth
        const syncToAuthResult = await syncRolesToAuthUser(authUser.id, adminClient);

        results.push({
          userId: authUser.id,
          email: authUser.email,
          syncToPrisma: syncToPrismaResult,
          syncToAuth: syncToAuthResult,
        });
      } catch (error) {
        console.error(`同步用户 ${authUser.id} 失败:`, error);
        results.push({
          userId: authUser.id,
          email: authUser.email,
          error: error instanceof Error ? error.message : "未知错误",
        });
      }
    }

    // 4. 返回结果
    return NextResponse.json({
      success: true,
      totalUsers: authUsers.users.length,
      results,
    });
  } catch (error) {
    console.error("同步所有用户失败:", error);
    return NextResponse.json(
      {
        error: "同步所有用户失败",
        details: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
