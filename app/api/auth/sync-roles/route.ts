import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";
import { syncUserToPrisma, syncRolesToAuthUser, syncAvatarColorToAuth } from "@/lib/syncUser";
import { createRouteHandlerClient } from "@/utils/supabase/server";

// 使用服务角色的Supabase客户端
const adminClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

export async function GET(req: NextRequest) {
  try {
    // 1. 先验证当前用户身份
    const supabase = await createRouteHandlerClient();
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "未授权" }, { status: 401 });
    }

    // 2. 第一步：确保用户在Prisma中存在并有角色
    const syncToPrismaResult = await syncUserToPrisma(user);

    // 3. 第二步：从Prisma同步角色到Supabase Auth
    const syncToAuthResult = await syncRolesToAuthUser(user.id, adminClient);

    // 4. 第三步：同步头像颜色到Supabase Auth（确保数据一致性）
    const syncAvatarResult = await syncAvatarColorToAuth(user.id, adminClient);

    // 5. 获取最终的角色信息
    const roleBindings = await prisma.roleBinding.findMany({
      where: { principalId: user.id, principalType: "USER" },
      select: { role: true, scopeType: true, scopeId: true },
    });

    return NextResponse.json({
      success: syncToPrismaResult && syncToAuthResult && syncAvatarResult,
      userId: user.id,
      roleCount: roleBindings.length,
      roles: roleBindings,
      syncToPrisma: syncToPrismaResult,
      syncToAuth: syncToAuthResult,
      syncAvatar: syncAvatarResult,
    });
  } catch (error) {
    console.error("同步角色失败:", error);
    return NextResponse.json({ error: "同步角色失败" }, { status: 500 });
  }
}
