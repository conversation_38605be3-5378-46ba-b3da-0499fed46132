import { NextRequest, NextResponse } from "next/server";

import { createRouteHandlerClient } from "@/utils/supabase/server";

export async function POST(req: NextRequest) {
  try {
    // 使用路由处理程序创建supabase客户端
    const supabase = await createRouteHandlerClient();

    // 执行登出操作
    const { error } = await supabase.auth.signOut();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (err) {
    return NextResponse.json(
      { error: err instanceof Error ? err.message : "未知错误" },
      { status: 500 }
    );
  }
}
