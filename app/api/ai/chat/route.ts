/**
 * 统一AI聊天API路由
 * 
 * 功能特性：
 * 1. 支持访客免费体验（固定次数 + 速率限制）
 * 2. 支持已注册用户（按剩余额度与请求频率受控）
 * 3. 同一路由同时支持流式问答和Function Calling
 * 4. 使用Edge Runtime，低延迟响应
 */

import { NextRequest } from 'next/server';
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';
import { createRouteHandlerClient } from '@/utils/supabase/server';
import { prisma } from '@/lib/prisma';

// 注意：使用标准Node.js Runtime以支持Prisma数据库访问
// 如需Edge Runtime性能优势，需要重构为Supabase Data API + RLS策略

// 初始化Upstash Redis和限流器
const redis = new Redis({
    url: process.env.UPSTASH_REDIS_REST_URL!,
    token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

// 访客速率限制：每分钟最多3次请求
const guestRateLimit = new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(3, '1 m'),
    analytics: true,
});

// 注册用户速率限制：每分钟最多10次请求
const userRateLimit = new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(10, '1 m'),
    analytics: true,
});

// 访客每日配额限制
const GUEST_DAILY_LIMIT = 10;

interface ChatRequest {
    messages: Array<{ role: string; content?: string; tool_calls?: any[]; tool_call_id?: string }>;
    stream?: boolean;
    model?: string;
    temperature?: number;
    max_tokens?: number;
}

export async function POST(request: NextRequest) {
    try {
        const body: ChatRequest = await request.json();

        if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
            return new Response(
                JSON.stringify({ error: '消息列表不能为空' }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 获取访客ID和IP地址
        const visitorId = request.cookies.get('visitor_id')?.value;
        const ipAddress = request.headers.get('x-forwarded-for')?.split(',')[0]?.trim()
            || request.headers.get('x-real-ip')
            || 'unknown';

        // 创建Supabase客户端检查用户状态
        const supabase = await createRouteHandlerClient();
        const { data: { user } } = await supabase.auth.getUser();

        let rateLimitIdentifier: string;
        let rateLimiter: Ratelimit;
        let quotaCheckResult: { allowed: boolean; remaining: number; error?: string };

        if (user) {
            // 已登录用户逻辑
            rateLimitIdentifier = `user:${user.id}`;
            rateLimiter = userRateLimit;
            quotaCheckResult = await checkUserQuota(user.id);
        } else {
            // 访客逻辑
            rateLimitIdentifier = visitorId ? `visitor:${visitorId}` : `ip:${ipAddress}`;
            rateLimiter = guestRateLimit;
            quotaCheckResult = await checkGuestQuota(visitorId, ipAddress);
        }

        // 检查速率限制
        const rateLimit = await rateLimiter.limit(rateLimitIdentifier);

        if (!rateLimit.success) {
            return new Response(
                JSON.stringify({
                    error: '请求过于频繁，请稍后再试',
                    retryAfter: Math.ceil(rateLimit.reset / 1000)
                }),
                {
                    status: 429,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-RateLimit-Limit': rateLimit.limit.toString(),
                        'X-RateLimit-Remaining': rateLimit.remaining.toString(),
                        'X-RateLimit-Reset': rateLimit.reset.toString(),
                    }
                }
            );
        }

        // 检查配额
        if (!quotaCheckResult.allowed) {
            const errorMessage = user
                ? '您的AI使用额度已用完，请联系管理员获取更多额度'
                : '今日免费体验次数已用完，请注册账号以获得更多使用机会';

            return new Response(
                JSON.stringify({
                    error: errorMessage,
                    remaining: quotaCheckResult.remaining,
                    upgrade: !user
                }),
                {
                    status: 403,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Quota-Remaining': quotaCheckResult.remaining.toString(),
                    }
                }
            );
        }

        // 更新用量
        if (user) {
            await updateUserUsage(user.id);
        } else {
            await updateGuestUsage(visitorId, ipAddress);
        }

        // 获取用户模型配置
        const aiModel = user ? await getUserAIModel(user.id) : 'deepseek-chat';

        // 直接使用前端传入的消息历史
        const messages = body.messages;

        // 调用AI API（流式转发模式）
        const aiResponse = await callAIAPI({
            messages,
            model: body.model || aiModel,
            stream: body.stream ?? true,
            temperature: body.temperature,
            max_tokens: body.max_tokens,
        });

        // 设置响应头
        const responseHeaders = {
            'X-RateLimit-Limit': rateLimit.limit.toString(),
            'X-RateLimit-Remaining': rateLimit.remaining.toString(),
            'X-Quota-Remaining': (quotaCheckResult.remaining - 1).toString(),
        };

        if (body.stream !== false) {
            // 流式响应
            return new Response(aiResponse.body, {
                headers: {
                    'Content-Type': 'text/stream; charset=utf-8',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    ...responseHeaders,
                },
            });
        } else {
            // 非流式响应
            return new Response(aiResponse.body, {
                headers: {
                    'Content-Type': 'application/json',
                    ...responseHeaders,
                },
            });
        }

    } catch (error) {
        console.error('AI API处理失败:', error);

        return new Response(
            JSON.stringify({
                error: '服务器内部错误，请稍后重试',
                details: process.env.NODE_ENV === 'development' ?
                    (error instanceof Error ? error.message : '未知错误') : undefined
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

// 检查用户配额
async function checkUserQuota(userId: string): Promise<{ allowed: boolean; remaining: number; error?: string }> {
    try {
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { aiReqRest: true, aiLastReqTime: true }
        });

        if (!user || user.aiReqRest <= 0) {
            return { allowed: false, remaining: 0 };
        }

        // 检查冷却时间（可选：避免过于频繁的请求）
        const lastReqTime = user.aiLastReqTime;
        const now = new Date();
        const cooldownMs = 1000; // 1秒冷却

        if (lastReqTime && (now.getTime() - lastReqTime.getTime()) < cooldownMs) {
            return { allowed: false, remaining: user.aiReqRest, error: '请求过于频繁' };
        }

        return { allowed: true, remaining: user.aiReqRest };
    } catch (error) {
        console.error('检查用户配额异常:', error);
        return { allowed: false, remaining: 0, error: '配额检查失败' };
    }
}

// 检查访客配额
async function checkGuestQuota(visitorId: string | undefined, ipAddress: string): Promise<{ allowed: boolean; remaining: number }> {
    try {
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
        const key = visitorId ? `quota:${visitorId}:${today}` : `quota:ip:${ipAddress}:${today}`;

        const current = await redis.get(key) as number || 0;

        if (current >= GUEST_DAILY_LIMIT) {
            return { allowed: false, remaining: 0 };
        }

        return { allowed: true, remaining: GUEST_DAILY_LIMIT - current };
    } catch (error) {
        console.error('检查访客配额失败:', error);
        // 出错时允许访问，但记录日志
        return { allowed: true, remaining: GUEST_DAILY_LIMIT };
    }
}

// 更新用户用量
async function updateUserUsage(userId: string): Promise<void> {
    try {
        // 先获取当前值
        const currentUser = await prisma.user.findUnique({
            where: { id: userId },
            select: { aiReqRest: true, aiReqTotal: true }
        });

        if (!currentUser) {
            console.error('获取用户当前用量失败: 用户不存在');
            return;
        }

        // 更新用量
        await prisma.user.update({
            where: { id: userId },
            data: {
                aiReqRest: Math.max(0, currentUser.aiReqRest - 1),
                aiReqTotal: currentUser.aiReqTotal + 1,
                aiLastReqTime: new Date(),
            }
        });
    } catch (error) {
        console.error('更新用户用量异常:', error);
    }
}

// 更新访客用量
async function updateGuestUsage(visitorId: string | undefined, ipAddress: string): Promise<void> {
    try {
        const today = new Date().toISOString().split('T')[0];
        const key = visitorId ? `quota:${visitorId}:${today}` : `quota:ip:${ipAddress}:${today}`;

        await redis.incr(key);
        await redis.expire(key, 86400); // 24小时过期
    } catch (error) {
        console.error('更新访客用量失败:', error);
    }
}

// 获取用户AI模型配置
async function getUserAIModel(userId: string): Promise<string> {
    try {
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { aiModel: true }
        });

        if (!user) {
            return 'deepseek-chat'; // 默认模型
        }

        const rawModel = user.aiModel || 'deepseek-chat';

        // 🔧 修复模型名称格式问题
        // 如果包含冒号，只取冒号后面的部分
        const cleanModel = rawModel.includes(':') ? rawModel.split(':')[1] : rawModel;

        console.log('🔧 模型名称处理:', { rawModel, cleanModel });

        return cleanModel;
    } catch (error) {
        console.error('获取用户AI模型配置失败:', error);
        return 'deepseek-chat';
    }
}

// 智能AI API代理 - 流式转发模式
async function callAIAPI({
    messages,
    model = 'deepseek-chat',
    stream = true,
    temperature,
    max_tokens,
}: {
    messages: any[];
    model?: string;
    stream?: boolean;
    temperature?: number;
    max_tokens?: number;
}) {
    const apiKey = process.env.DEEPSEEK_API_KEY || process.env.OPENAI_API_KEY;
    const baseURL = process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1';

    if (!apiKey) {
        throw new Error('AI API密钥未配置');
    }

    // 获取Function Calling schema（用于前端执行）
    let functions = [];
    try {
        const { ensureCommandsInitialized } = await import('@/lib/commands/init');
        ensureCommandsInitialized();

        const { getAIFunctionSchema } = await import('@/lib/commands');
        functions = getAIFunctionSchema();
        console.log('✅ 加载Function Schema供前端使用，函数数量:', functions.length);
    } catch (error) {
        console.error('❌ 加载Function Schema失败:', error);
        functions = [];
    }

    const requestBody: any = {
        model,
        messages,
        stream,
        temperature: temperature ?? 0.7,
        max_tokens: max_tokens ?? 2000,
        ...(functions.length > 0 ? {
            tools: functions,
            tool_choice: 'auto',
        } : {})
    };

    console.log('🌐 代理AI API请求:', {
        url: `${baseURL}/chat/completions`,
        model,
        messageCount: messages.length,
        stream,
        functionsCount: functions.length
    });

    // 直接流式转发DeepSeek API响应
    const response = await fetch(`${baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
        },
        body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`AI API调用失败: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return response;
}

