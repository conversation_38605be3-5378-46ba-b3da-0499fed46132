import { Prisma, PaperType, Category, PaperStatus } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "@/lib/prisma";

/* ── 1. 查询参数校验 ─────────────────────────────── */
const Query = z.object({
    // 分页
    skip: z.coerce.number().int().min(0).default(0),
    take: z.coerce.number().int().positive().max(100).default(20),

    // 排序
    sortBy: z.enum(['publishedAt', 'submittedAt', 'acceptedAt', 'readNumber', 'downloads', 'citation', 'title']).default('publishedAt'),
    sortOrder: z.enum(['desc', 'asc']).default('desc'),

    // 筛选
    status: z.string().optional(),
    category: z.string().optional(),
    type: z.string().optional(),
    tags: z.string().optional(), // 逗号分隔的标签列表
    author: z.string().optional(),
    search: z.string().optional(),
    issue: z.string().optional(),

    // 日期范围
    dateFrom: z.string().optional(), // YYYY-MM-DD
    dateTo: z.string().optional(),   // YYYY-MM-DD
    dateField: z.enum(['publishedAt', 'submittedAt', 'acceptedAt']).default('publishedAt'),
});

type QueryType = z.infer<typeof Query>;

/* ── 2. API handler (public, 无鉴权) ───────────────── */
export async function GET(req: NextRequest) {
    /* 2-1 解析 & 校验 query string */
    let q: QueryType;
    try {
        const searchParams = Object.fromEntries(req.nextUrl.searchParams);
        q = Query.parse(searchParams);
    } catch (err) {
        return NextResponse.json({ error: "Invalid query parameters" }, { status: 400 });
    }

    try {
        /* 2-2 组合 where 条件 */
        const where: any = {};

        // 状态筛选
        if (q.status) {
            if (q.status === 'ALL') {
                // 不设置status条件，显示所有状态
            } else {
                where.status = q.status;
            }
        } else {
            // 默认只显示已发布的论文
            where.status = 'PUBLISHED';
        }

        // 分类筛选
        if (q.category && q.category !== 'ALL') {
            where.category = q.category as Category;
        }

        // 类型筛选
        if (q.type && q.type !== 'ALL') {
            where.type = q.type as PaperType;
        }

        // 期刊期次筛选
        if (q.issue && q.issue !== 'ALL') {
            where.issueId = q.issue;
        }

        // 标签筛选
        if (q.tags) {
            const tagList = q.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
            if (tagList.length > 0) {
                where.paperTags = {
                    hasSome: tagList
                };
            }
        }

        // 作者筛选
        if (q.author) {
            where.authors = {
                hasSome: [q.author]
            };
        }

        // 全文搜索
        if (q.search) {
            where.OR = [
                { title: { contains: q.search, mode: "insensitive" } },
                { abstract: { contains: q.search, mode: "insensitive" } },
                { authors: { hasSome: [q.search] } },
                { paperTags: { hasSome: [q.search] } },
                { affiliations: { hasSome: [q.search] } },
            ];
        }

        // 日期范围筛选
        if (q.dateFrom || q.dateTo) {
            const dateFilter: any = {};

            if (q.dateFrom) {
                dateFilter.gte = new Date(q.dateFrom + 'T00:00:00Z');
            }

            if (q.dateTo) {
                dateFilter.lte = new Date(q.dateTo + 'T23:59:59Z');
            }

            // 根据指定的日期字段进行筛选
            if (q.dateField === 'submittedAt') {
                where.submittedAt = dateFilter;
            } else if (q.dateField === 'acceptedAt') {
                where.acceptedAt = dateFilter;
            } else {
                where.publishedAt = dateFilter;
            }
        }

        /* 2-3 组合 orderBy 条件 */
        let orderBy: Prisma.PaperOrderByWithRelationInput;

        switch (q.sortBy) {
            case 'readNumber':
                orderBy = { readNumber: q.sortOrder };
                break;
            case 'downloads':
                orderBy = { downloads: q.sortOrder };
                break;
            case 'citation':
                orderBy = { citation: q.sortOrder };
                break;
            case 'title':
                orderBy = { title: q.sortOrder };
                break;
            case 'submittedAt':
                orderBy = { submittedAt: q.sortOrder };
                break;
            case 'acceptedAt':
                orderBy = { acceptedAt: q.sortOrder };
                break;
            case 'publishedAt':
            default:
                orderBy = { publishedAt: q.sortOrder };
                break;
        }

        /* 2-4 查询数据 */
        const papers = await prisma.paper.findMany({
            where,
            orderBy,
            skip: q.skip,
            take: q.take,
            select: {
                id: true,
                title: true,
                abstract: true,
                authors: true,
                paperTags: true,
                category: true,
                type: true,
                status: true,
                publishedAt: true,
                acceptedAt: true,
                submittedAt: true,
                readNumber: true,
                downloads: true,
                citation: true,
                authorId: true,
                issueId: true,
                // 可以根据需要添加更多字段
                // doi: true,
                // affiliations: true,
                // videoUrl: true,
            },
        });

        /* 2-5 返回结果 */
        return NextResponse.json({
            papers,
            query: q, // 返回查询参数，方便调试
            count: papers.length
        });

    } catch (error) {
        console.error('T_Papers API Error:', error);
        return NextResponse.json({
            error: "Internal server error",
            details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
    }
} 