import { Role, ScopeType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";

/** ───────────────────────────────────────────────────────────
 * 判断当前用户是否有权修改 needsAttention
 * 允许角色：
 *   • ADMIN / CHIEF_EDITOR （GLOBAL）
 *   • ISSUE_EDITOR         （ISSUE = 论文所在期次）
 *   • PAPER_EDITOR         （PAPER = 当前论文）
 * ───────────────────────────────────────────────────────────*/
function canEditAttention(
  binds: { role: Role; scopeType: ScopeType; scopeId: string | null }[],
  paper: { id: string; issueId: string | null }
) {
  return (
    /* 全局 */
    binds.some(
      b =>
        (b.role === Role.ADMIN || b.role === Role.CHIEF_EDITOR) && b.scopeType === ScopeType.GLOBAL
    ) ||
    /* 期刊期次 */
    (paper.issueId &&
      binds.some(
        b =>
          b.role === Role.ISSUE_EDITOR &&
          b.scopeType === ScopeType.ISSUE &&
          b.scopeId === paper.issueId
      )) ||
    /* 稿件级 */
    binds.some(
      b => b.role === Role.PAPER_EDITOR && b.scopeType === ScopeType.PAPER && b.scopeId === paper.id
    )
  );
}

export async function PATCH(req: NextRequest, ctx: { params: Promise<{ id: string }> }) {
  /* 0. 会话 ---------------------------------------------------- */
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session?.user) return NextResponse.json({ error: "Unauthorized" }, { status: 401 });

  const { id } = await ctx.params; // ← await Promise 再解构
  const paperId = id.trim();

  /* 1. 拉取 Paper & 用户绑定 ---------------------------------- */
  const [paper, bindings] = await Promise.all([
    prisma.paper.findUnique({
      where: { id: paperId },
      select: { id: true, issueId: true },
    }),
    prisma.roleBinding.findMany({
      where: { principalId: session.user.id, principalType: "USER" },
      select: { role: true, scopeType: true, scopeId: true },
    }),
  ]);

  if (!paper) return NextResponse.json({ error: "Paper not found" }, { status: 404 });

  if (!canEditAttention(bindings, paper))
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });

  /* 2. 解析负载 ------------------------------------------------ */
  let body: { value: boolean };
  try {
    body = await req.json();
  } catch {
    return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
  }

  /* 3. 更新并返回 --------------------------------------------- */
  await prisma.paper.update({
    where: { id: paperId },
    data: { needsAttention: body.value },
  });

  return NextResponse.json({ ok: true });
}
