/**
 * 安全的论文版本获取API
 * 替代直接在客户端使用服务端密钥
 */

import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { z } from "zod";

import { checkPaperAccess } from "@/lib/auth/permissions";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";
import { getClientIP, getUserAgent, inputValidation } from "@/lib/security/utils";
import { Role } from "@prisma/client";

// 服务端存储客户端（仅在服务端使用）
const storage = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

// 参数验证
const paramsSchema = z.object({
  id: z.string().cuid(),
});

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 1. 参数验证
    const { id } = await params;
    const { id: paperId } = paramsSchema.parse({ id });

    // 2. 身份验证
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;
    const clientIP = await getClientIP(req);
    const userAgent = await getUserAgent(req);

    // 3. 权限验证
    const permissionResult = await checkPaperAccess(
      userId,
      paperId,
      [Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR, Role.PAPER_EDITOR, Role.REVIEWER, Role.AUTHOR],
      {
        requireAudit: true,
        ipAddress: clientIP,
        userAgent: userAgent,
      }
    );

    if (!permissionResult.allowed) {
      return NextResponse.json(
        { error: "Access denied", reason: permissionResult.reason },
        { status: 403 }
      );
    }

    // 4. 获取版本列表
    const { data: versionObjs, error } = await storage.storage
      .from("submitted")
      .list(paperId);

    if (error) {
      console.error("Failed to fetch versions:", error);
      return NextResponse.json(
        { error: "Failed to fetch versions" },
        { status: 500 }
      );
    }

    // 5. 处理版本元数据
    const folderMetadataPromises = (versionObjs ?? [])
      .filter(v => v.id === null) // 只筛选文件夹
      .map(async folder => {
        try {
          // 基于文件夹名称确定优先级和创建时间
          let createdAt = new Date(0);
          let priority = 0;

          if (/^v\d+$/.test(folder.name)) {
            const vNum = parseInt(folder.name.slice(1));
            createdAt = new Date(2000 + vNum);
            priority = 1000 + vNum;
          } else if (folder.name.startsWith("revision_V")) {
            const vNum = parseInt(folder.name.match(/revision_V(\d+)/)?.[1] || "0");
            createdAt = new Date(2200, 0, 1 + vNum);
            priority = 3000 + vNum; // 最高优先级
          } else if (folder.name === "revision") {
            createdAt = new Date(2100, 0, 1);
            priority = 2000;
          } else if (folder.name === "draft") {
            createdAt = new Date(1900, 0, 1);
            priority = 100; // 最低优先级
          } else {
            createdAt = new Date(2000, 0, 1);
            priority = 500;
          }

          return {
            name: folder.name,
            createdAt: createdAt,
            priority: priority,
          };
        } catch (error) {
          return {
            name: folder.name,
            createdAt: new Date(0),
            priority: 0,
          };
        }
      });

    // 6. 等待所有元数据获取完成并排序
    const folderMetadata = await Promise.all(folderMetadataPromises);
    
    const versions = folderMetadata
      .sort((a, b) => b.priority - a.priority) // 按优先级降序排序
      .map(folder => ({
        name: folder.name,
        createdAt: folder.createdAt.toISOString(),
        priority: folder.priority,
      }));

    // 7. 确保至少有一个版本
    if (versions.length === 0) {
      versions.push({
        name: "draft",
        createdAt: new Date().toISOString(),
        priority: 100,
      });
    }

    return NextResponse.json({
      success: true,
      versions: versions,
      totalCount: versions.length,
    });

  } catch (error) {
    console.error("Error in versions API:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * 获取特定版本的文件列表
 */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 1. 参数验证
    const { id } = await params;
    const { id: paperId } = paramsSchema.parse({ id });

    const body = await req.json();
    const { version } = z.object({
      version: z.string().min(1).max(100),
    }).parse(body);

    // 2. 身份验证
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;
    const clientIP = await getClientIP(req);
    const userAgent = await getUserAgent(req);

    // 3. 权限验证
    const permissionResult = await checkPaperAccess(
      userId,
      paperId,
      [Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR, Role.PAPER_EDITOR, Role.REVIEWER, Role.AUTHOR],
      {
        requireAudit: true,
        ipAddress: clientIP,
        userAgent: userAgent,
      }
    );

    if (!permissionResult.allowed) {
      return NextResponse.json(
        { error: "Access denied", reason: permissionResult.reason },
        { status: 403 }
      );
    }

    // 4. 获取版本文件列表
    const { data: files, error } = await storage.storage
      .from("submitted")
      .list(`${paperId}/${version}`);

    if (error) {
      console.error("Failed to fetch version files:", error);
      return NextResponse.json(
        { error: "Failed to fetch version files" },
        { status: 500 }
      );
    }

    // 5. 过滤和格式化文件信息
    const fileList = (files ?? [])
      .filter(file => file.id !== null) // 只返回文件，不返回文件夹
      .map(file => ({
        name: file.name,
        size: file.metadata?.size || 0,
        lastModified: file.updated_at || file.created_at,
        contentType: file.metadata?.mimetype || 'application/octet-stream',
      }));

    return NextResponse.json({
      success: true,
      version: version,
      files: fileList,
      totalFiles: fileList.length,
    });

  } catch (error) {
    console.error("Error in version files API:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
