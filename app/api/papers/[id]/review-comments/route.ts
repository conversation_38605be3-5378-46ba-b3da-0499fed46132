import { Role, ScopeType, CommentType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";
import { checkCommentPermission } from "@/lib/auth/permissions";
import { getClientIP, getUserAgent, inputValidation, RateLimiter, createSecureHeaders } from "@/lib/security/utils";

// 速率限制器 - 每15分钟最多50条评论
const rateLimiter = new RateLimiter(15 * 60 * 1000, 50);

// 输入验证 Schema
const commentSchema = z.object({
  body: z.string()
    .min(1, "Comment body cannot be empty")
    .max(10000, "Comment body too long")
    .transform(body => inputValidation.cleanUserInput(body, 10000)),
});

const paramsSchema = z.object({
  id: z.string().cuid("Invalid paper ID format"),
});

/**
 * POST /api/papers/[id]/review-comments
 * 企业级安全的评论创建API
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const headers = createSecureHeaders();

  try {
    /* 1. 参数验证 -------------------------------------------------- */
    const { id } = await params;
    const { id: paperId } = paramsSchema.parse({ id });

    /* 2. 身份验证 --------------------------------------------------- */
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401, headers }
      );
    }

    const userId = session.user.id;
    const clientIP = await getClientIP(req);
    const userAgent = await getUserAgent(req);

    /* 3. 速率限制检查 --------------------------------------------- */
    const rateLimitKey = `comment:${userId}:${clientIP}`;
    if (rateLimiter.isRateLimited(rateLimitKey)) {
      return NextResponse.json(
        {
          error: "Rate limit exceeded",
          retryAfter: 900 // 15分钟
        },
        { status: 429, headers }
      );
    }

    /* 4. 输入验证和清理 ------------------------------------------- */
    const body = await req.json();
    const { body: commentBody } = commentSchema.parse(body);

    /* 5. 权限验证 ------------------------------------------------- */
    const commentPermission = await checkCommentPermission(userId, paperId, {
      requireAudit: true,
      ipAddress: clientIP,
      userAgent: userAgent,
    });

    if (!commentPermission.allowed || !commentPermission.canReply) {
      return NextResponse.json(
        {
          error: "Permission denied",
          reason: commentPermission.reason
        },
        { status: 403, headers }
      );
    }

    /* 6. 获取论文信息 ------------------------------------------- */
    const paper = await prisma.paper.findUnique({
      where: { id: paperId },
      select: {
        id: true,
        status: true,
        authorCanReply: true,
        reviewersCanReply: true,
        authorId: true,
        issueId: true,
      },
    });

    if (!paper) {
      return NextResponse.json(
        { error: "Paper not found" },
        { status: 404, headers }
      );
    }

    /* 7. 确定评论可见性设置（默认策略：编辑集中控制） ----------- */
    const userRole = commentPermission.userRole!;
    const roleLabel = commentPermission.roleLabel;

    // 🔒 新的安全策略：所有新评论默认只对编辑可见
    // 由编辑手动控制每条评论的可见性，确保内容审核
    let visibleToAuthor = false;
    let visibleToReviewers = false;

    // 所有角色的评论都采用相同的默认策略
    if (userRole === Role.REVIEWER) {
      // 审稿人评论：默认对作者和其他审稿人都不可见
      visibleToAuthor = false;
      visibleToReviewers = false;
    } else if (userRole === Role.AUTHOR) {
      // 作者评论：默认对审稿人不可见
      visibleToAuthor = true; // 作者可以看到自己的评论
      visibleToReviewers = false;
    } else if ([Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR, Role.PAPER_EDITOR].includes(userRole)) {
      // 编辑评论：默认对作者和审稿人都不可见，由编辑发布后手动控制
      visibleToAuthor = false;
      visibleToReviewers = false;
    }

    /* 8. 创建评论 ---------------------------------------- */
    const newComment = await prisma.reviewComment.create({
      data: {
        paperId,
        authorId: userId,
        roleLabel,
        body: commentBody,
        type: CommentType.MESSAGE,
        visibleToAuthor,
        visibleToReviewers,
      },
      select: {
        id: true,
        body: true,
        roleLabel: true,
        createdAt: true,
      }
    });

    /* 9. 更新论文时间戳 ----------------------------- */
    await prisma.paper.update({
      where: { id: paperId },
      data: { lastUpdatedAt: new Date() },
    });

    return NextResponse.json({
      success: true,
      comment: {
        id: newComment.id,
        body: newComment.body,
        roleLabel: newComment.roleLabel,
        createdAt: newComment.createdAt,
      }
    }, { headers });

  } catch (error) {
    console.error("Error creating review comment:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Validation failed",
          details: error.errors
        },
        { status: 400, headers }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500, headers }
    );
  }
}
