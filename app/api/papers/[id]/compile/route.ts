import { randomUUID } from "crypto";
import { mkdir, rm, writeFile, readFile } from "fs/promises";
import { tmpdir } from "os";
import { join, dirname } from "path";

import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

/* remark / rehype pipeline */
import rehypeHighlight from "rehype-highlight";
import rehypeMathjax from "rehype-mathjax";
import rehypeRaw from "rehype-raw";
import rehypeStringify from "rehype-stringify";
import rehypeLineNumbers from "@/components/rehypeLineNumbers";
import remarkFrontmatter from "remark-frontmatter";
import remarkGfm from "remark-gfm";
import remarkHeadingId from "remark-heading-id";
import remarkMath from "remark-math";
import remarkParse from "remark-parse";
import remarkRehype from "remark-rehype";
import { unified } from "unified";

/* 自定义插件 */
import remarkAnonymize from "@/components/remarkAnonymize";
import remarkCrossRef from "@/components/remarkCrossRef";
import remarkPaperHeader from "@/components/remarkPaperHeader";
import { remarkPathResolverPaper } from "@/components/remarkPathResolverPaper";

const BUCKET = "submitted";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* ---------- 工具：创建临时目录 ---------- */
async function createTempDir(): Promise<string> {
  const dir = join(tmpdir(), randomUUID());
  await mkdir(dir, { recursive: true });
  return dir;
}

/* ---------- 工具：递归下载版本文件 ---------- */
async function downloadAllFiles(prefix: string, destDir: string) {
  // 列出 prefix 下所有对象（非递归）
  const stack: string[] = [prefix.replace(/\/$/, "")];
  while (stack.length) {
    const p = stack.pop()!;
    const { data, error } = await serverSupabase.storage.from(BUCKET).list(p, { limit: 1000 });
    if (error) throw error;
    for (const obj of data ?? []) {
      const full = p ? `${p}/${obj.name}` : obj.name;
      const isDir = obj.metadata?.is_directory || obj.id === null;
      if (isDir) {
        stack.push(full);
      } else {
        const { data: fileData } = await serverSupabase.storage.from(BUCKET).download(full);
        if (!fileData) continue;
        const outPath = join(destDir, full.substring(prefix.length + 1));
        await mkdir(dirname(outPath), { recursive: true });
        await writeFile(outPath, Buffer.from(await fileData.arrayBuffer()));
      }
    }
  }
}

/* ---------- Markdown → HTML ---------- */
async function md2html(md: string, opts: { paperId: string; version: string; tempDir: string }) {
  const { paperId, version, tempDir } = opts;
  return String(
    await unified()
      .use(remarkParse)
      .use(remarkFrontmatter, ["yaml"]) // 保留 YAML
      .use(remarkAnonymize) // 添加匿名化插件
      .use(remarkMath)
      .use(remarkGfm)
      .use(remarkPaperHeader)
      .use(remarkCrossRef)
      .use(remarkHeadingId)
      .use(remarkPathResolverPaper, { paperId, version })
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeHighlight)
      .use(rehypeMathjax)
      .use(rehypeLineNumbers)
      .use(rehypeStringify)
      .process(md)
  );
}

/* ---------- POST /api/papers/[id]/compile ---------- */
export async function POST(req: NextRequest) {
  try {
    /* ---- 1. 路由参数 ---- */
    const encodedId = req.nextUrl.pathname.split("/").slice(3, 4)[0]; // papers/{id}/compile
    const decodedId = decodeURIComponent(encodedId);
    const [paperId] = decodedId.split("/"); // 仅 paperId

    if (!paperId) {
      return NextResponse.json("Missing paperId", { status: 400 });
    }

    /* ---- 2. Body 参数 ---- */
    const { version = "draft", entry = "paper.md" } = await req.json().catch(() => ({}));

    /* ---- 3. 创建临时目录并下载完整版本 ---- */
    const tempDir = await createTempDir();
    const prefix = `${paperId}/${version}`;
    await downloadAllFiles(prefix, tempDir);

    /* ---- 4. 读取入口文件 ---- */
    const entryPath = join(tempDir, entry);
    const md = await readFile(entryPath, "utf8");

    /* ---- 5. 编译 ---- */
    const html = await md2html(md, { paperId, version, tempDir });

    /* ---- 6. 清理临时目录 ---- */
    await rm(tempDir, { recursive: true, force: true });

    /* ---- 7. 返回 ---- */
    return new NextResponse(html, {
      headers: {
        "content-type": "text/html; charset=utf-8",
        "cache-control": "no-store",
      },
    });
  } catch (err) {
    console.error("[compile-paper]", err);
    const msg = err instanceof Error ? err.message : String(err);
    return NextResponse.json(msg, { status: 500 });
  }
}
