import { Prisma } from "@prisma/client"; // ← 修正
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "@/lib/prisma";

/* ---------- 校验 ---------- */
const bodySchema = z
  .object({
    authorCanReply: z.boolean().optional(),
    reviewersCanReply: z.boolean().optional(),
  })
  .refine(d => d.authorCanReply !== undefined || d.reviewersCanReply !== undefined, {
    message: "payload must include authorCanReply or reviewersCanReply",
  });

export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const paperId = id.trim();
  if (!paperId) return NextResponse.json({ error: "paperId required" }, { status: 400 });

  /* ① JSON 校验 */
  let payload: z.infer<typeof bodySchema>;
  try {
    payload = bodySchema.parse(await req.json());
  } catch {
    return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
  }

  /* ② 组装更新字段 */
  const data: Record<string, boolean> = {};
  if (payload.authorCanReply !== undefined) data.authorCanReply = payload.authorCanReply;
  if (payload.reviewersCanReply !== undefined) data.reviewersCanReply = payload.reviewersCanReply;

  /* ③ 更新数据库 */
  try {
    await prisma.paper.update({ where: { id: paperId }, data });
    return new NextResponse(null, { status: 204 });
  } catch (err) {
    if (err instanceof Prisma.PrismaClientKnownRequestError && err.code === "P2025")
      return NextResponse.json({ error: "Paper not found" }, { status: 404 });

    console.error("[reply-perms]", err);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}
