import { randomUUID } from "crypto";
import { mkdir, rm, writeFile, readFile } from "fs/promises";
import { tmpdir } from "os";
import { join, dirname } from "path";

import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

/* remark / rehype pipeline */
import rehypeHighlight from "rehype-highlight";
import rehypeMathjax from "rehype-mathjax";
import rehypeRaw from "rehype-raw";
import rehypeStringify from "rehype-stringify";
import rehypeLineNumbers from "@/components/rehypeLineNumbers";
import remarkFrontmatter from "remark-frontmatter";
import remarkGfm from "remark-gfm";
import remarkHeadingId from "remark-heading-id";
import remarkMath from "remark-math";
import remarkParse from "remark-parse";
import remarkRehype from "remark-rehype";
import { unified } from "unified";

/* 自定义插件 */
import remarkAnonymize from "@/components/remarkAnonymize";
import remarkCrossRef from "@/components/remarkCrossRef";
import remarkPaperHeader from "@/components/remarkPaperHeader";
import { remarkPathResolverPaper } from "@/components/remarkPathResolverPaper";

const BUCKET = "submitted";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/**
 * 创建临时目录
 */
async function createTempDir(): Promise<string> {
  const dir = join(tmpdir(), randomUUID());
  await mkdir(dir, { recursive: true });
  return dir;
}

/**
 * 下载文件
 * 优化: 仅下载必要的文件 (paper.md 和 references.bib)
 */
async function downloadRequiredFiles(
  paperId: string,
  version: string,
  destDir: string
): Promise<boolean> {
  try {
    // 构建文件路径
    const prefix = `${paperId}/${version}`;

    // 必要文件列表
    const requiredFiles = ["paper.md", "references.bib"];

    // 下载必要文件
    for (const fileName of requiredFiles) {
      const filePath = `${prefix}/${fileName}`;
      const { data, error } = await serverSupabase.storage.from(BUCKET).download(filePath);

      if (error || !data) {
        // 如果 paper.md 不存在，下载失败
        if (fileName === "paper.md") {
          console.warn(`必要文件不存在: ${filePath}`);
          return false;
        }
        continue; // references.bib 可选
      }

      // 保存文件
      const outPath = join(destDir, fileName);
      await writeFile(outPath, Buffer.from(await data.arrayBuffer()));
      console.log(`已下载: ${filePath}`);
    }

    // 下载图片和其他引用的资源会在编译时按需处理

    return true;
  } catch (err) {
    console.error("下载文件错误:", err);
    return false;
  }
}

/**
 * Markdown 转 HTML
 */
async function md2html(md: string, opts: { paperId: string; version: string; tempDir: string }) {
  const { paperId, version, tempDir } = opts;
  return String(
    await unified()
      .use(remarkParse)
      .use(remarkFrontmatter, ["yaml"])
      .use(remarkAnonymize)
      .use(remarkMath)
      .use(remarkGfm)
      .use(remarkPaperHeader)
      .use(remarkCrossRef)
      .use(remarkHeadingId)
      .use(remarkPathResolverPaper, { paperId, version })
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeHighlight)
      .use(rehypeMathjax)
      .use(rehypeLineNumbers)
      .use(rehypeStringify)
      .process(md)
  );
}

/**
 * POST /api/papers/[id]/preview
 * 统一的论文预览API: 先检查预编译HTML是否存在，不存在则实时编译
 * Body: { version: string }
 */
export async function POST(req: NextRequest) {
  try {
    /* ---- 1. 获取路由参数 ---- */
    const encodedId = req.nextUrl.pathname.split("/").slice(3, 4)[0]; // papers/{id}/preview
    const decodedId = decodeURIComponent(encodedId);
    const [paperId] = decodedId.split("/"); // 仅 paperId

    if (!paperId) {
      return NextResponse.json("论文ID不能为空", { status: 400 });
    }

    /* ---- 2. 获取请求体参数 ---- */
    const { version = "draft" } = await req.json().catch(() => ({}));

    /* ---- 3. 首先尝试获取预编译HTML ---- */
    const htmlPath = `${paperId}/${version}/compiled.html`;
    const { data: htmlData, error: htmlError } = await serverSupabase.storage
      .from(BUCKET)
      .download(htmlPath);

    // 如果预编译HTML存在，直接返回
    if (htmlData && !htmlError) {
      console.log(`[Preview API] 使用预编译HTML: ${htmlPath}`);
      const htmlContent = await htmlData.text();

      return new NextResponse(htmlContent, {
        headers: {
          "content-type": "text/html; charset=utf-8",
        },
      });
    }

    console.log(`[Preview API] 预编译HTML不存在，准备实时编译: ${htmlPath}`);

    /* ---- 4. 预编译HTML不存在，进行实时编译 ---- */
    // 创建临时目录
    const tempDir = await createTempDir();

    try {
      // 下载必要文件 (paper.md, references.bib)
      const filesDownloaded = await downloadRequiredFiles(paperId, version, tempDir);
      if (!filesDownloaded) {
        return NextResponse.json(`无法找到必要的文件 (${paperId}/${version}/paper.md)`, {
          status: 404,
        });
      }

      // 读取入口文件
      const entryPath = join(tempDir, "paper.md");
      const md = await readFile(entryPath, "utf8");

      // 编译 Markdown 为 HTML
      const html = await md2html(md, { paperId, version, tempDir });

      console.log(`[Preview API] 实时编译完成: ${paperId}/${version}`);

      return new NextResponse(html, {
        headers: {
          "content-type": "text/html; charset=utf-8",
        },
      });
    } finally {
      // 清理临时目录
      await rm(tempDir, { recursive: true, force: true });
    }
  } catch (err) {
    console.error("[Preview API] 错误:", err);
    const msg = err instanceof Error ? err.message : String(err);
    return NextResponse.json(msg, { status: 500 });
  }
}
