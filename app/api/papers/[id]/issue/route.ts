import { Role, ScopeType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat, getUserRoles, UserRole } from "@/utils/supabase/compat";

// 请求体验证
const bodySchema = z.object({
  issueId: z.string().nullable(), // 期次ID，null表示取消分配
});

// 判断是否有全局管理员权限（仅ADMIN和CHIEF_EDITOR）
function isGlobalAdmin(rb: UserRole): boolean {
  const role = rb.role || rb;
  const scope = rb.scope || rb.scopeType || ScopeType.GLOBAL;
  return (
    (role === Role.ADMIN || role === Role.CHIEF_EDITOR) &&
    (scope === ScopeType.GLOBAL || scope === "GLOBAL")
  );
}

// PATCH: 更新论文的期次分配
export async function PATCH(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const paperId = id.trim();
    if (!paperId) {
      return NextResponse.json({ error: "论文ID不能为空" }, { status: 400 });
    }

    // 解析请求体
    let body;
    try {
      body = bodySchema.parse(await req.json());
      console.log("请求参数:", JSON.stringify(body));
    } catch (e) {
      console.error("请求参数无效:", e);
      return NextResponse.json({ error: "无效的请求参数" }, { status: 400 });
    }

    // 获取当前用户
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session?.user) {
      return NextResponse.json({ error: "未授权" }, { status: 401 });
    }

    // 获取用户角色
    const roles = getUserRoles(session.user);

    // 鉴权: 只有全局管理员可以分配期次
    const hasPermission = roles.some(rb => isGlobalAdmin(rb));

    if (!hasPermission) {
      return NextResponse.json({ error: "权限不足，只有全局管理员可以分配期次" }, { status: 403 });
    }

    // 获取论文信息
    const paper = await prisma.paper.findUnique({
      where: { id: paperId },
      select: { id: true, issueId: true },
    });

    if (!paper) {
      return NextResponse.json({ error: "论文不存在" }, { status: 404 });
    }

    // 如果设置了issueId，检查期次是否存在
    if (body.issueId) {
      const issue = await prisma.issue.findUnique({
        where: { id: body.issueId },
        select: { id: true },
      });

      if (!issue) {
        return NextResponse.json({ error: "期次不存在" }, { status: 404 });
      }
    }

    // 更新论文的期次
    const updatedPaper = await prisma.paper.update({
      where: { id: paperId },
      data: { issueId: body.issueId },
      select: { id: true, issueId: true },
    });

    return NextResponse.json({
      message: body.issueId ? "成功分配期次" : "成功取消期次分配",
      paper: updatedPaper,
    });
  } catch (error) {
    console.error("更新论文期次失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
