import type { FileObject } from "@supabase/storage-js";
import { createClient } from "@supabase/supabase-js";
import J<PERSON><PERSON><PERSON> from "jszip";
import { NextRequest, NextResponse } from "next/server";

const BUCKET = "submitted";
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

/* 递归列出文件 */
async function listRecursive(prefix: string): Promise<FileObject[]> {
  const stack = [prefix.replace(/\/$/, "")];
  const out: FileObject[] = [];
  while (stack.length) {
    const p = stack.pop()!;
    const { data, error } = await serverSupabase.storage.from(BUCKET).list(p, { limit: 1000 });
    if (error) throw error;
    for (const obj of data ?? []) {
      const full = p ? `${p}/${obj.name}` : obj.name;
      const isDir = obj.metadata?.is_directory || obj.id === null;
      if (isDir) stack.push(full);
      else out.push({ ...obj, name: full });
    }
  }
  return out;
}

function guessMime(path: string): string {
  const ext = path.split(".").pop()?.toLowerCase();
  switch (ext) {
    case "md":
    case "txt":
      return "text/plain;charset=utf-8";
    case "png":
      return "image/png";
    case "jpg":
    case "jpeg":
      return "image/jpeg";
    case "gif":
      return "image/gif";
    default:
      return "application/octet-stream";
  }
}

export async function GET(req: NextRequest) {
  try {
    const segments = req.nextUrl.pathname.split("/"); // ['', 'api','papers',id,'download-tree']
    const encoded = segments[3];
    const decoded = decodeURIComponent(encoded); // e.g. 123/v1 or 123
    const parts = decoded.split("/");
    const paperId = parts[0];
    const version = parts[1] ?? "v1";

    const relPath = req.nextUrl.searchParams.get("path") ?? "";
    const rootPrefix = `${paperId}/${version}`;

    const targetPath = relPath ? `${rootPrefix}/${relPath}` : rootPrefix;

    // 判断是文件还是目录
    let isFile = false;
    if (relPath) {
      const dir = relPath.includes("/") ? relPath.replace(/\/[^/]+$/, "") : "";
      const base = relPath.split("/").pop()!;
      const parentPrefix = dir ? `${rootPrefix}/${dir}` : rootPrefix;
      const { data } = await serverSupabase.storage
        .from(BUCKET)
        .list(parentPrefix, { limit: 1000 });
      isFile = (data ?? []).some(
        o => o.name === base && !(o.metadata?.is_directory || o.id === null)
      );
    }

    /* 文件 */
    if (isFile) {
      const { data, error } = await serverSupabase.storage.from(BUCKET).download(targetPath);
      if (error || !data) throw error || new Error("file not found");
      const buf = await data.arrayBuffer();
      return new NextResponse(new Uint8Array(buf), {
        headers: {
          "content-type": guessMime(relPath),
          "content-disposition": `inline; filename=${relPath.split("/").pop()}`,
          "cache-control": "public, max-age=31536000, immutable",
        },
      });
    }

    /* 目录 zip */
    const objs = await listRecursive(targetPath.replace(/\/$/, ""));
    const zip = new JSZip();
    const baseLen = targetPath.length + 1;
    for (const obj of objs) {
      const { data } = await serverSupabase.storage.from(BUCKET).download(obj.name);
      if (!data) continue;
      zip.file(obj.name.slice(baseLen), await data.arrayBuffer());
    }
    const blob = await zip.generateAsync({ type: "uint8array" });
    const zipName = (relPath || `${paperId}-${version}`) + ".zip";
    return new NextResponse(blob, {
      headers: {
        "content-type": "application/zip",
        "content-disposition": `attachment; filename=${zipName}`,
      },
    });
  } catch (err) {
    console.error("[download-tree-paper]", err);
    const msg = err instanceof Error ? err.message : String(err);
    return NextResponse.json(msg, { status: 500 });
  }
}
