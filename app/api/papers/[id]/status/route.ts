import { Role, ScopeType, PaperStatus, CommentType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";

/**
 * 允许的角色：
 *   • ADMIN / CHIEF_EDITOR（全局）
 *   • ISSUE_EDITOR        （针对该期 Issue）
 *   • PAPER_EDITOR        （针对该 Paper）
 */
function canChangeStatus(
  bindings: { role: Role; scopeType: ScopeType; scopeId: string | null }[],
  paper: { id: string; issueId: string | null }
): boolean {
  return (
    // 全局管理员
    bindings.some(
      b =>
        (b.role === Role.ADMIN || b.role === Role.CHIEF_EDITOR) && b.scopeType === ScopeType.GLOBAL
    ) ||
    // 该期刊期次的编辑
    (paper.issueId &&
      bindings.some(
        b =>
          b.role === Role.ISSUE_EDITOR &&
          b.scopeType === ScopeType.ISSUE &&
          b.scopeId === paper.issueId
      )) ||
    // 该稿件的编辑
    bindings.some(
      b => b.role === Role.PAPER_EDITOR && b.scopeType === ScopeType.PAPER && b.scopeId === paper.id
    )
  );
}

export async function PATCH(req: NextRequest, ctx: { params: Promise<{ id: string }> }) {
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session?.user) return NextResponse.json({ error: "Unauthorized" }, { status: 401 });

  /* 0. 解析 payload ------------------------------------------------- */
  const { id: rawId } = await ctx.params;
  const paperId = rawId.trim();
  let payload: { status: PaperStatus };
  try {
    payload = await req.json();
  } catch {
    return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
  }

  /* 1. 拉取 paper & 用户绑定 ---------------------------------------- */
  const [paper, bindings] = await Promise.all([
    prisma.paper.findUnique({
      where: { id: paperId },
      select: { id: true, issueId: true, status: true },
    }),
    prisma.roleBinding.findMany({
      where: {
        principalId: session.user.id,
        principalType: "USER",
      },
      select: { role: true, scopeType: true, scopeId: true },
    }),
  ]);

  if (!paper) return NextResponse.json({ error: "Paper not found" }, { status: 404 });

  /* 2. 权限校验 ------------------------------------------------------ */
  if (!canChangeStatus(bindings, paper)) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  /* 3. 更新状态 ------------------------------------------------------ */
  await prisma.paper.update({
    where: { id: paper.id },
    // @ts-ignore - 使用新的lastUpdatedAt字段
    data: {
      status: payload.status,
      lastUpdatedAt: new Date()
    } as any,
  });

  /* 4. 写入时间线 (ReviewComment, 类型: STATUS_CHANGE) -------------- */
  const statusComment = await prisma.reviewComment.create({
    data: {
      paperId: paper.id,
      authorId: session.user.id,
      roleLabel: "Editor",
      body: `Status changed to **${payload.status}**`,
      type: CommentType.STATUS_CHANGE,
      visibleToAuthor: true,
      visibleToReviewers: true,
    },
  });

  /* 5. 自动发送通知给管理员（特别是新paper提交时） ---------------- */
  try {
    // 获取paper基本信息用于通知
    const paperForNotification = await prisma.paper.findUnique({
      where: { id: paperId },
      select: {
        title: true,
        authorId: true,
        issueId: true
      }
    });

    if (paperForNotification) {
      // 获取发送者信息
      const sender = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { name: true }
      });

      // 获取全局管理员
      const adminBindings = await prisma.roleBinding.findMany({
        where: {
          role: { in: [Role.ADMIN, Role.CHIEF_EDITOR] },
          scopeType: "GLOBAL"
        },
        select: {
          principalId: true
        }
      });

      let recipients: string[] = [];

      // 获取paper编辑和issue编辑（如果已分配）
      const paperEditors = await prisma.roleBinding.findMany({
        where: {
          scopeId: paperId,
          role: Role.PAPER_EDITOR
        },
        select: {
          principalId: true
        }
      });

      let issueEditors: { principalId: string }[] = [];
      if (paperForNotification.issueId) {
        issueEditors = await prisma.roleBinding.findMany({
          where: {
            scopeId: paperForNotification.issueId,
            role: Role.ISSUE_EDITOR
          },
          select: {
            principalId: true
          }
        });
      }

      // 确定通知接收者
      recipients.push(...adminBindings.map(ab => ab.principalId));
      recipients.push(...paperEditors.map(pe => pe.principalId));
      recipients.push(...issueEditors.map(ie => ie.principalId));

      // 去重并排除发送者自己
      recipients = [...new Set(recipients)].filter(id => id !== session.user.id);

      if (recipients.length > 0) {
        // 生成通知预览文本
        const preview = `Status changed to ${payload.status}`;

        // 批量创建通知
        await Promise.all(
          recipients.map(async (recipientId) => {
            // 创建通知记录
            const notification = await prisma.notification.create({
              data: {
                type: 'PAPER_STATUS_CHANGE',
                paperId: paperId,
                commentId: statusComment.id,
                preview: preview,
                paperTitle: paperForNotification.title,
                actorId: session.user.id,
                actorName: sender?.name || 'Unknown User'
              }
            });

            // 创建接收者记录
            await prisma.notificationRecipient.create({
              data: {
                notificationId: notification.id,
                userId: recipientId
              }
            });
          })
        );
      }
    }
  } catch (notificationError) {
    console.warn('自动通知发送失败:', notificationError);
    // 不阻止状态更新的成功响应
  }

  return NextResponse.json({ ok: true });
}
