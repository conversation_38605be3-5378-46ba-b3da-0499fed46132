import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const paperId = id;

    // 创建Supabase Admin客户端，使用服务端角色密钥访问私有桶
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!, // 使用服务端密钥
      {
        auth: {
          persistSession: false,
        },
      }
    );

    // 从私有桶获取compiled.html
    const { data, error } = await supabase.storage
      .from("published")
      .download(`${paperId}/compiled.html`);

    if (error) {
      console.error("获取文件出错：", error);
      return NextResponse.json({ error: "无法获取文档", details: error.message }, { status: 404 });
    }

    if (!data) {
      return NextResponse.json({ error: "文档不存在" }, { status: 404 });
    }

    // 将文件内容转换为文本
    let htmlContent = await data.text();

    // 替换所有 editor projects download-tree 的图片链接为新的 assets API
    // 替换 /api/editor/projects/one/download-tree?path=xxx 格式的链接
    htmlContent = htmlContent.replace(
      /\/api\/editor\/projects\/[^\/]+\/download-tree\?path=([^"'&]+)/g,
      `/api/papers/${paperId}/assets?path=$1`
    );

    // 替换相对路径的图片引用，如 figures/image.png、./figures/image.png、../figures/image.png
    htmlContent = htmlContent.replace(
      /src\s*=\s*["'](?:\.{0,2}\/)?figures\/([^"']+)["']/g,
      `src="/api/papers/${paperId}/assets?path=figures/$1"`
    );

    // 替换可能的其他相对路径资源
    htmlContent = htmlContent.replace(
      /href\s*=\s*["'](?:\.{0,2}\/)?figures\/([^"']+)["']/g,
      `href="/api/papers/${paperId}/assets?path=figures/$1"`
    );

    // 返回HTML内容，设置适当的Content-Type
    return new NextResponse(htmlContent, {
      headers: {
        "Content-Type": "text/html; charset=utf-8",
      },
    });
  } catch (error) {
    console.error("API错误：", error);
    return NextResponse.json(
      { error: "服务器错误", details: error instanceof Error ? error.message : "未知错误" },
      { status: 500 }
    );
  }
}
