import { Role, ScopeType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat, getUserRoles, UserRole } from "@/utils/supabase/compat";

// 判断是否有全局编辑权限
function isGlobalEditor(rb: UserRole): boolean {
  const role = rb.role || rb;
  const scope = rb.scope || rb.scopeType || ScopeType.GLOBAL;
  return (
    (role === Role.ADMIN || role === Role.CHIEF_EDITOR || role === Role.ISSUE_EDITOR) &&
    (scope === ScopeType.GLOBAL || scope === "GLOBAL")
  );
}

// 判断是否有期次编辑权限
function isIssueEditor(rb: UserRole, issueId: string | null): boolean {
  if (!issueId) return false;

  const role = rb.role || rb;
  const scope = rb.scope || rb.scopeType || null;
  const scopeId = rb.scopeId || null;

  return (
    role === Role.ISSUE_EDITOR &&
    (scope === ScopeType.ISSUE || scope === "ISSUE") &&
    scopeId === issueId
  );
}

// DELETE: 删除论文编辑
export async function DELETE(
  _req: NextRequest,
  { params }: { params: Promise<{ id: string; bindingId: string }> }
) {
  try {
    const { id, bindingId } = await params;
    const paperId = id.trim();
    const bindingIdTrimmed = bindingId.trim();

    if (!paperId || !bindingIdTrimmed) {
      return NextResponse.json({ error: "参数不完整" }, { status: 400 });
    }

    // 获取当前用户
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session?.user) {
      return NextResponse.json({ error: "未授权" }, { status: 401 });
    }

    // 获取用户角色
    const roles = getUserRoles(session.user);

    // 获取论文信息
    const paper = await prisma.paper.findUnique({
      where: { id: paperId },
      select: { id: true, issueId: true },
    });

    if (!paper) {
      return NextResponse.json({ error: "论文不存在" }, { status: 404 });
    }

    // 获取角色绑定信息
    const binding = await prisma.roleBinding.findUnique({
      where: { id: bindingIdTrimmed },
      select: { id: true, role: true, scopeType: true, scopeId: true },
    });

    if (!binding) {
      return NextResponse.json({ error: "角色绑定不存在" }, { status: 404 });
    }

    // 确认角色绑定是否与论文相关
    if (
      binding.role !== Role.PAPER_EDITOR ||
      binding.scopeType !== ScopeType.PAPER ||
      binding.scopeId !== paperId
    ) {
      return NextResponse.json({ error: "角色绑定与论文不匹配" }, { status: 400 });
    }

    // 鉴权: 只有全局管理员或期刊编辑可以删除编辑
    const hasPermission =
      roles.some(rb => isGlobalEditor(rb)) || roles.some(rb => isIssueEditor(rb, paper.issueId));

    if (!hasPermission) {
      return NextResponse.json({ error: "权限不足" }, { status: 403 });
    }

    // 删除角色绑定
    await prisma.roleBinding.delete({
      where: { id: bindingIdTrimmed },
    });

    return NextResponse.json({ message: "成功移除编辑" });
  } catch (error) {
    console.error("删除论文编辑失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
