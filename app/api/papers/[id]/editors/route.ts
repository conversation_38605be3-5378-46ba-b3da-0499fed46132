import { Role, ScopeType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat, getUserRoles, UserRole } from "@/utils/supabase/compat";

// 请求体验证
const bodySchema = z.object({
  userId: z.string().min(1), // 用户ID
});

// 判断是否有全局编辑权限
function isGlobalEditor(rb: UserRole): boolean {
  const role = rb.role || rb;
  const scope = rb.scope || rb.scopeType || ScopeType.GLOBAL;
  return (
    (role === Role.ADMIN || role === Role.CHIEF_EDITOR || role === Role.ISSUE_EDITOR) &&
    (scope === ScopeType.GLOBAL || scope === "GLOBAL")
  );
}

// 判断是否有期次编辑权限
function isIssueEditor(rb: UserRole, issueId: string | null): boolean {
  if (!issueId) return false;

  const role = rb.role || rb;
  const scope = rb.scope || rb.scopeType || null;
  const scopeId = rb.scopeId || null;

  return (
    role === Role.ISSUE_EDITOR &&
    (scope === ScopeType.ISSUE || scope === "ISSUE") &&
    scopeId === issueId
  );
}

// GET: 获取论文编辑
export async function GET(_req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const paperId = id.trim();
    if (!paperId) {
      return NextResponse.json({ error: "论文ID不能为空" }, { status: 400 });
    }

    // 获取当前用户
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session?.user) {
      return NextResponse.json({ error: "未授权" }, { status: 401 });
    }

    // 获取用户角色
    const roles = getUserRoles(session.user);

    // 获取论文信息
    const paper = await prisma.paper.findUnique({
      where: { id: paperId },
      select: { id: true, issueId: true },
    });

    if (!paper) {
      return NextResponse.json({ error: "论文不存在" }, { status: 404 });
    }

    // 鉴权: 只有全局管理员或期刊编辑可以查看编辑分配
    const hasPermission =
      roles.some(rb => isGlobalEditor(rb)) ||
      (paper.issueId && roles.some(rb => isIssueEditor(rb, paper.issueId)));

    if (!hasPermission) {
      return NextResponse.json({ error: "权限不足" }, { status: 403 });
    }

    // 查询论文的编辑
    const editors = await prisma.roleBinding.findMany({
      where: {
        role: Role.PAPER_EDITOR,
        scopeType: ScopeType.PAPER,
        scopeId: paperId,
      },
      include: {
        principal: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // 格式化结果
    const formattedEditors = editors.map(editor => ({
      roleBindingId: editor.id,
      userId: editor.principalId,
      userName: editor.principal.name,
      userEmail: editor.principal.email,
    }));

    return NextResponse.json(formattedEditors);
  } catch (error) {
    console.error("获取论文编辑失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

// POST: 添加论文编辑
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const paperId = id.trim();
    if (!paperId) {
      return NextResponse.json({ error: "论文ID不能为空" }, { status: 400 });
    }

    // 解析请求体
    let body;
    try {
      body = bodySchema.parse(await req.json());
      console.log("请求参数:", JSON.stringify(body));
    } catch (e) {
      console.error("请求参数无效:", e);
      return NextResponse.json({ error: "无效的请求参数" }, { status: 400 });
    }

    // 获取当前用户
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session?.user) {
      return NextResponse.json({ error: "未授权" }, { status: 401 });
    }

    // 获取用户角色
    const roles = getUserRoles(session.user);

    // 获取论文信息
    const paper = await prisma.paper.findUnique({
      where: { id: paperId },
      select: { id: true, issueId: true },
    });

    if (!paper) {
      return NextResponse.json({ error: "论文不存在" }, { status: 404 });
    }

    // 鉴权: 只有全局管理员或期刊编辑可以分配编辑
    const hasPermission =
      roles.some(rb => isGlobalEditor(rb)) ||
      (paper.issueId && roles.some(rb => isIssueEditor(rb, paper.issueId)));

    if (!hasPermission) {
      return NextResponse.json({ error: "权限不足" }, { status: 403 });
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: body.userId },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 检查是否已经存在相同的分配
    const existingBinding = await prisma.roleBinding.findFirst({
      where: {
        principalId: body.userId,
        role: Role.PAPER_EDITOR,
        scopeType: ScopeType.PAPER,
        scopeId: paperId,
      },
    });

    if (existingBinding) {
      return NextResponse.json({
        message: "用户已经是该论文的编辑",
        roleBindingId: existingBinding.id,
      });
    }

    // 创建新的角色绑定
    const roleBinding = await prisma.roleBinding.create({
      data: {
        principalId: body.userId,
        principalType: "USER",
        role: Role.PAPER_EDITOR,
        scopeType: ScopeType.PAPER,
        scopeId: paperId,
        alias: "Paper Editor",
      },
    });

    return NextResponse.json({
      message: "成功分配编辑",
      roleBindingId: roleBinding.id,
    });
  } catch (error) {
    console.error("分配论文编辑失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
