import { Role, ScopeType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat, getUserRoles, UserRole } from "@/utils/supabase/compat";

/* ------------ schema ------------ */
const bodySchema = z.object({
  alias: z.string().min(1),
  userId: z.string().min(1), // 放宽验证条件，不强制要求cuid格式
});

/* ------------ helper ------------ */
const GLOBAL_ROLES: Role[] = [Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR];

/** 是否拥有 *全局* 编辑权限 */
function isGlobalEditor(rb: UserRole): boolean {
  // 兼容不同的角色格式
  const role = rb.role || rb;
  const scope = rb.scope || rb.scopeType || ScopeType.GLOBAL;
  return GLOBAL_ROLES.includes(role as Role) && (scope === ScopeType.GLOBAL || scope === "GLOBAL");
}

/** 稿件级（作者 / reviewer / paper-editor） */
function isLocalPaperRole(rb: UserRole, pid: string): boolean {
  const LOCAL: Role[] = [Role.AUTHOR, Role.REVIEWER, Role.PAPER_EDITOR];
  const role = rb.role || rb;
  const scope = rb.scope || rb.scopeType || null;
  const scopeId = rb.scopeId || null;

  return (
    LOCAL.includes(role as Role) &&
    (scope === ScopeType.PAPER || scope === "PAPER") &&
    scopeId === pid
  );
}

/* ------------ GET ------------ */
export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ id?: string; paperId?: string }> }
) {
  try {
    const { paperId: pid, id } = await params;
    const paperId = (pid ?? id ?? "").trim();
    if (!paperId) return NextResponse.json({ error: "paperId required" }, { status: 400 });

    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session?.user) return NextResponse.json([], { status: 200 });

    // 使用getUserRoles获取标准化的角色数组
    const roles = getUserRoles(session.user);
    // console.log("用户角色:", JSON.stringify(roles));

    const hasPrivilege =
      roles.some(rb => isGlobalEditor(rb)) || roles.some(rb => isLocalPaperRole(rb, paperId));

    if (!hasPrivilege) {
      // console.log("权限不足:", session.user.id);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const list = await prisma.reviewAssignment.findMany({
      where: { paperId },
      orderBy: { alias: "asc" },
      include: { reviewer: { select: { id: true, name: true, email: true } } },
    });

    return NextResponse.json(
      list.map(a => ({
        alias: a.alias,
        assignmentId: a.id,
        userId: a.reviewer.id,
        userName: a.reviewer.name,
        userEmail: a.reviewer.email,
      }))
    );
  } catch (error) {
    console.error("get assign reviewers error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/* ------------ POST ------------ */
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id?: string; paperId?: string }> }
) {
  try {
    const { id, paperId: pid } = await params; // ← await 之后再解构
    const paperId = (pid ?? id ?? "").trim();
    if (!paperId) return NextResponse.json({ error: "paperId required" }, { status: 400 });

    let body;
    try {
      body = bodySchema.parse(await req.json());
      // console.log("请求参数:", JSON.stringify(body));
    } catch (e) {
      // console.error("请求参数无效:", e);
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }

    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 使用getUserRoles获取标准化的角色数组
    const roles = getUserRoles(session.user);
    // console.log("用户角色:", JSON.stringify(roles));

    if (!roles.some(rb => isGlobalEditor(rb))) {
      // console.log("权限不足:", session.user.id);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // 先检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: body.userId },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const dup = await prisma.reviewAssignment.findFirst({
      where: { paperId, alias: body.alias },
    });
    if (dup) return NextResponse.json({ error: "Alias already in use" }, { status: 409 });

    const assn = await prisma.$transaction(async tx => {
      const a = await tx.reviewAssignment.create({
        data: { paperId, alias: body.alias, reviewerId: body.userId },
      });

      // 先检查是否已有相同的角色绑定
      const existingBinding = await tx.roleBinding.findFirst({
        where: {
          principalId: body.userId,
          principalType: "USER",
          role: Role.REVIEWER,
          scopeType: ScopeType.PAPER,
          scopeId: paperId,
        },
      });

      // 如果没有，才创建新的
      if (!existingBinding) {
        await tx.roleBinding.create({
          data: {
            principalId: body.userId,
            principalType: "USER",
            role: Role.REVIEWER,
            scopeType: ScopeType.PAPER,
            scopeId: paperId,
            alias: body.alias,
          },
        });
      }
      return a;
    });

    // console.log("分配成功:", assn);
    return NextResponse.json({ assignmentId: assn.id, alias: body.alias });
  } catch (error) {
    // console.error("分配审稿人出错:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
