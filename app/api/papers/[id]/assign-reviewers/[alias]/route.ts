import { Role, ScopeType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat, getUserRoles, UserRole } from "@/utils/supabase/compat";

export async function DELETE(
  _req: NextRequest,
  { params }: { params: Promise<{ paperId?: string; id?: string; alias?: string }> }
) {
  try {
    const { paperId: pid, id, alias: raw } = await params;

    const paperId = (pid ?? id ?? "").trim();
    const alias = decodeURIComponent(raw ?? "").trim();
    if (!paperId || !alias) {
      console.log("Request parameter missing:", { paperId, alias });
      return NextResponse.json({ error: "paperId & alias required" }, { status: 400 });
    }

    /* ---------- 鉴权 ---------- */
    const supabase = await getSupabaseRoute();
    const {
      data: { session },
    } = await getSessionCompat(supabase);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 使用getUserRoles获取标准化的角色数组
    const roles = getUserRoles(session.user);
    console.log("User roles:", JSON.stringify(roles));

    const allowed: Role[] = [Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR];
    const ok = roles.some((rb: UserRole) => {
      const role = rb.role || rb;
      const scope = rb.scope || rb.scopeType || null;
      return allowed.includes(role as Role) && (scope === ScopeType.GLOBAL || scope === "GLOBAL");
    });

    if (!ok) {
      console.log("Insufficient permissions:", session.user.id);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    /* ---------- 删除 ---------- */
    console.log(`Deleting reviewer: ${alias}, paper ID: ${paperId}`);

    const assignment = await prisma.reviewAssignment.findFirst({
      where: { paperId, alias },
      select: { id: true, reviewerId: true },
    });

    if (!assignment) {
      console.log("No matching assignment found:", { paperId, alias });
      return NextResponse.json({ error: "Assignment not found" }, { status: 404 });
    }

    console.log(`Found assignment: ${assignment.id}, reviewer ID: ${assignment.reviewerId}`);

    await prisma.$transaction([
      prisma.reviewAssignment.delete({ where: { id: assignment.id } }),
      prisma.roleBinding.deleteMany({
        where: {
          principalId: assignment.reviewerId,
          role: "REVIEWER",
          scopeType: "PAPER",
          scopeId: paperId,
        },
      }),
    ]);

    console.log("Delete successful");
    return NextResponse.json({ ok: true });
  } catch (e) {
    console.error("Delete reviewer error:", e);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}
