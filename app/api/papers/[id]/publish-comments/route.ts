import { NextRequest, NextResponse } from "next/server";

import { prisma } from "@/lib/prisma"; // Prisma client instance
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const paperId = id;

  if (!paperId) {
    return NextResponse.json({ error: "Invalid paper ID" }, { status: 400 });
  }
  // Parse pagination query parameters
  const { searchParams } = new URL(request.url);
  const skipParam = searchParams.get("skip") || "0";
  const takeParam = searchParams.get("take") || "10";
  const skip = Number(skipParam);
  const take = Number(takeParam);

  // Fetch comments for the given paper, with user name, newest first
  const comments = await prisma.publishComment.findMany({
    where: { paperId },
    include: {
      user: { select: { name: true } }, // include only the user's name
    },
    orderBy: { createdAt: "desc" },
    skip: skip,
    take: take,
  });

  return NextResponse.json(comments);
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const paperId = id;

  console.log("[POST /publish-comments] paperId:", paperId);
  if (!paperId) {
    return NextResponse.json({ error: "Invalid paper ID" }, { status: 400 });
  }
  // Check user session (must be logged in)
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  console.log("[POST /publish-comments] session:", session);
  if (!session || !session.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const userId = session.user.id;

  const body = await request.json();
  console.log("[POST /publish-comments] body:", body);
  const text: string = body.text?.trim();
  const parentId: string | undefined = body.parentId; // 用于回复
  if (!text) {
    return NextResponse.json({ error: "Comment text is required" }, { status: 400 });
  }

  // Create new comment associated with this paper and user
  const newComment = await prisma.publishComment.create({
    data: {
      text: text,
      paperId: paperId,
      userId: userId,
      parentId: parentId || undefined, // 如果有parentId则是回复
    },
    include: {
      user: { select: { name: true } }, // include user's name in the returned object
    },
  });

  // 为publish评论自动发送通知
  try {
    // 获取论文信息
    const paper = await prisma.paper.findUnique({
      where: { id: paperId },
      select: {
        title: true,
        authorId: true
      }
    });

    if (paper) {
      // 获取评论者信息
      const commenter = await prisma.user.findUnique({
        where: { id: userId },
        select: { name: true }
      });

      let recipients: string[] = [];
      let notificationType: 'COMMENT_ON_PAPER' | 'REPLY_ON_COMMENT' = 'COMMENT_ON_PAPER';

      if (parentId) {
        // 这是回复，通知被回复的人
        const parentComment = await prisma.publishComment.findUnique({
          where: { id: parentId },
          select: { userId: true }
        });

        if (parentComment && parentComment.userId !== userId) {
          recipients.push(parentComment.userId);
          notificationType = 'REPLY_ON_COMMENT';
        }
      } else {
        // 这是新评论，通知论文作者
        if (paper.authorId !== userId) {
          recipients.push(paper.authorId);
        }
      }

      // 发送通知
      if (recipients.length > 0) {
        const notifications = await Promise.all(
          recipients.map(async (recipientId) => {
            const notification = await prisma.notification.create({
              data: {
                type: notificationType,
                paperId: paperId,
                commentId: newComment.id,
                preview: text.length > 100 ? text.substring(0, 100) + "..." : text,
                paperTitle: paper.title,
                actorId: userId,
                actorName: commenter?.name || 'Unknown User'
              }
            });

            await prisma.notificationRecipient.create({
              data: {
                notificationId: notification.id,
                userId: recipientId
              }
            });

            return notification;
          })
        );
      }
    }
  } catch (notifyError) {
    console.warn('发布评论通知发送失败:', notifyError);
    // 不阻止评论创建的成功响应
  }

  return NextResponse.json(newComment);
}
