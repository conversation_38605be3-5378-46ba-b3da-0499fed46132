import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

/* ---------- mime helper ---------- */
function guessMime(path: string): string {
  const ext = path.split(".").pop()?.toLowerCase();
  switch (ext) {
    case "md":
    case "txt":
    case "bib":
      return "text/plain;charset=utf-8";
    case "png":
      return "image/png";
    case "jpg":
    case "jpeg":
      return "image/jpeg";
    case "gif":
      return "image/gif";
    case "pdf":
      return "application/pdf";
    case "svg":
      return "image/svg+xml";
    default:
      return "application/octet-stream";
  }
}

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const paperId = id;

    // 获取请求的路径参数
    const path = request.nextUrl.searchParams.get("path");

    if (!path) {
      return NextResponse.json({ error: "缺少path参数" }, { status: 400 });
    }

    // 创建Supabase Admin客户端，使用服务端角色密钥访问私有桶
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!, // 使用服务端密钥
      {
        auth: {
          persistSession: false,
        },
      }
    );

    // 构建资源路径
    const assetPath = `${paperId}/${path}`;
    console.log(`[assets] 获取资源: ${assetPath}`);

    // 从published桶获取资源文件
    const { data, error } = await supabase.storage.from("published").download(assetPath);

    if (error) {
      console.error("获取资源文件出错：", error);

      // 如果是404错误并且是promo.png，尝试获取默认图片
      if (error.message?.includes("Not Found") && path === "promo.png") {
        console.log("[assets] 尝试获取默认promo图片");

        // 可以从默认位置获取通用promo图片
        const { data: defaultData, error: defaultError } = await supabase.storage
          .from("published")
          .download("default/promo.png");

        if (!defaultError && defaultData) {
          const buffer = await defaultData.arrayBuffer();
          return new NextResponse(new Uint8Array(buffer), {
            headers: {
              "Content-Type": "image/png",
              "Cache-Control": "public, max-age=86400", // 缓存1天
            },
          });
        }
      }

      return NextResponse.json({ error: "无法获取资源", details: error.message }, { status: 404 });
    }

    if (!data) {
      return NextResponse.json({ error: "资源不存在" }, { status: 404 });
    }

    // 将文件内容转换为ArrayBuffer
    const buffer = await data.arrayBuffer();

    // 返回文件内容，设置适当的Content-Type
    return new NextResponse(new Uint8Array(buffer), {
      headers: {
        "Content-Type": guessMime(path),
        "Cache-Control": "public, max-age=3600", // 缓存1小时
      },
    });
  } catch (error) {
    console.error("API错误：", error);
    return NextResponse.json(
      { error: "服务器错误", details: error instanceof Error ? error.message : "未知错误" },
      { status: 500 }
    );
  }
}
