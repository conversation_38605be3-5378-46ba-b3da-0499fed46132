// app/api/papers/route.ts
import { Prisma, PaperType } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "@/lib/prisma";

/* ── 1. 查询参数校验 ─────────────────────────────── */
const Query = z.object({
  skip: z.coerce.number().int().min(0).default(0),
  take: z.coerce.number().int().positive().max(50).default(10),
  sort: z.enum(["new", "views", "comments"]).default("new"),
  issue: z.string().default("ALL"),
  search: z.string().trim().default(""),
  paperType: z.enum(["ALL", "FULL", "GALLERY", "PREPRINT"]).default("FULL"), // 默认改为FULL，支持PREPRINT
});
type QueryType = z.infer<typeof Query>;

/* ── 2. API handler (public, 无鉴权) ───────────────── */
export async function GET(req: NextRequest) {
  /* 2-1 解析 & 校验 query string */
  let q: QueryType;
  try {
    q = Query.parse(Object.fromEntries(req.nextUrl.searchParams));
  } catch (err) {
    return NextResponse.json({ error: "Invalid query" }, { status: 400 });
  }

  /* 2-2 组合 where / orderBy 条件 */
  const where: any = { status: "PUBLISHED" };

  if (q.issue !== "ALL") where.issueId = q.issue;

  if (q.search) {
    where.OR = [
      { title: { contains: q.search, mode: "insensitive" } },
      { authors: { hasSome: [q.search] } },
      { paperTags: { hasSome: [q.search] } },
      { affiliations: { hasSome: [q.search] } },
    ];
  }

  if (q.paperType !== "ALL") {
    where.type = q.paperType as PaperType;
  }

  /* ① 计算排序条件 —— 返回值显式断言成正确类型 */
  const orderBy: Prisma.PaperOrderByWithRelationInput =
    q.sort === "views"
      ? { readNumber: "desc" } // 浏览量
      : q.sort === "comments"
        ? { publishComments: { _count: "desc" } } // 评论数
        : { publishedAt: "desc" }; // 最新

  /* 2-3 查询 */
  const papers = await prisma.paper.findMany({
    where,
    orderBy,
    skip: q.skip,
    take: q.take,
    select: {
      id: true,
      title: true,
      abstract: true,
      publishedAt: true,
      paperTags: true,
      readNumber: true,
      type: true,
    },
  });

  /* 2-4 返回 JSON */
  return NextResponse.json({ papers });
}
