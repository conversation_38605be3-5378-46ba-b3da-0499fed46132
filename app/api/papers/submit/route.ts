import { Role, ScopeType, CommentType, PaperStatus, Category } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "@/lib/prisma";
import { supabase, getSupabaseRoute } from "@/lib/supabaseServer";

/* ---------- 元信息校验 ---------- */
const metaSchema = z.object({
  title: z.string().min(2),
  abstract: z.string().min(10),
  tags: z.array(z.string()).min(1),
  videoUrl: z.string().url().optional(),
  authors: z.array(z.object({ name: z.string() })).min(1),
  affiliations: z.array(z.object({ name: z.string() })).optional(),
});

const MAX_PDF_SIZE = 25 * 1024 * 1024; // 25 MB
const MAX_PNG_SIZE = 1 * 1024 * 1024; // 1 MB

export async function POST(req: NextRequest) {
  /* ---------- 1. Session & 角色 ---------- */
  const supabase = await getSupabaseRoute();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  const roles = user?.user_metadata?.roles as
    | { role: Role; scope: ScopeType; scopeId?: string | null }[]
    | undefined;

  const isAuthor = roles?.some(r => r.role === Role.AUTHOR) ?? false;
  if (!isAuthor) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const userId = user!.id;

  /* ---------- 2. 解析 multipart ---------- */
  const form = await req.formData();
  const pdf = form.get("file") as File | null;
  const png = form.get("promo") as File | null;
  const metaRaw = form.get("meta") as string | null;

  if (!pdf || !pdf.name.endsWith(".pdf"))
    return NextResponse.json({ error: "PDF required" }, { status: 400 });
  if (pdf.size > MAX_PDF_SIZE)
    return NextResponse.json({ error: "PDF too large" }, { status: 400 });

  if (!png || !png.name.endsWith(".png"))
    return NextResponse.json({ error: "PNG promo_pic required" }, { status: 400 });
  if (png.size > MAX_PNG_SIZE)
    return NextResponse.json({ error: "PNG too large" }, { status: 400 });

  if (!metaRaw) return NextResponse.json({ error: "Missing meta" }, { status: 400 });

  /* ---------- 3. 校验 meta ---------- */
  let meta: z.infer<typeof metaSchema>;
  try {
    meta = metaSchema.parse(JSON.parse(metaRaw));
  } catch {
    return NextResponse.json({ error: "Meta validation failed" }, { status: 400 });
  }

  /* ---------- 4. 创建 Paper（URL 先留空） ---------- */
  const paper = await prisma.paper.create({
    data: {
      title: meta.title,
      abstract: meta.abstract,
      authors: meta.authors.map(a => a.name),
      affiliations: meta.affiliations?.map(a => a.name) ?? [],
      videoUrl: meta.videoUrl ?? null,
      paperTags: meta.tags,
      category: Category.UNKNOWN,
      status: "SUBMITTED" as any,
      authorCanReply: true, // 让作者可立即回复
      authorId: userId,
    },
  });

  /* —— 给作者写入 RoleBinding: AUTHOR@PAPER —— */
  await prisma.roleBinding.create({
    data: {
      principalId: userId,
      principalType: "USER",
      role: Role.AUTHOR,
      scopeType: ScopeType.PAPER,
      scopeId: paper.id,
    },
  });

  /* ---------- 5. 上传文件到 Supabase ---------- */
  const pdfKey = `${paper.id}/paper.pdf`;
  const pngKey = `${paper.id}/promo_pic.png`;

  const upPdf = await supabase.storage
    .from("submissions")
    .upload(pdfKey, Buffer.from(await pdf.arrayBuffer()), {
      contentType: "application/pdf",
      upsert: true,
    });
  if (upPdf.error) return NextResponse.json({ error: upPdf.error.message }, { status: 500 });

  const upPng = await supabase.storage
    .from("submissions")
    .upload(pngKey, Buffer.from(await png.arrayBuffer()), {
      contentType: "image/png",
      upsert: true,
    });
  if (upPng.error) return NextResponse.json({ error: upPng.error.message }, { status: 500 });

  const pdfUrl = supabase.storage.from("submissions").getPublicUrl(pdfKey).data.publicUrl;
  const promoUrl = supabase.storage.from("submissions").getPublicUrl(pngKey).data.publicUrl;

  /* ---------- 6. 创建时间线（ReviewComment） ---------- */
  await prisma.reviewComment.create({
    data: {
      paperId: paper.id,
      authorId: userId,
      roleLabel: "Author",
      body: `[📄 PDF](${pdfUrl}) ｜ ![promo](${promoUrl})`,
      type: CommentType.MESSAGE,
      visibleToAuthor: true,
      visibleToReviewers: true,
    },
  });

  return NextResponse.json({ ok: true, id: paper.id });
}
