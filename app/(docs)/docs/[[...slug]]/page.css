@import "tailwindcss";
@import "fumadocs-ui/css/shadcn.css";
@import "fumadocs-ui/css/preset.css";

/* 自定义粗体样式 - 让fumadocs中的粗体更粗 */
@layer components {
  /* 针对fumadocs文档内容区域的粗体样式 */
  [data-docs-body] strong,
  [data-docs-body] b,
  .fd-docs strong,
  .fd-docs b,
  .prose strong,
  .prose b {
    font-weight: 700 !important; /* 使用最粗的字重 */
  }

  /* 针对标题中的粗体 */
  [data-docs-body] h1 strong,
  [data-docs-body] h2 strong,
  [data-docs-body] h3 strong,
  [data-docs-body] h4 strong,
  [data-docs-body] h5 strong,
  [data-docs-body] h6 strong,
  .fd-docs h1 strong,
  .fd-docs h2 strong,
  .fd-docs h3 strong,
  .fd-docs h4 strong,
  .fd-docs h5 strong,
  .fd-docs h6 strong {
    font-weight: 700 !important;
  }

  /* 针对Tailwind的font-bold类 */
  [data-docs-body] .font-bold,
  .fd-docs .font-bold {
    font-weight: 700 !important;
  }

  /* 针对MDX组件中的粗体 */
  .fd-content strong,
  .fd-content b,
  .fd-content .font-bold {
    font-weight: 700 !important;
  }

  /* 确保在列表、表格等元素中的粗体也生效 */
  [data-docs-body] li strong,
  [data-docs-body] li b,
  [data-docs-body] td strong,
  [data-docs-body] td b,
  [data-docs-body] th strong,
  [data-docs-body] th b,
  .fd-docs li strong,
  .fd-docs li b,
  .fd-docs td strong,
  .fd-docs td b,
  .fd-docs th strong,
  .fd-docs th b {
    font-weight: 700 !important;
  }

  /* 去掉所有标题的下划线和边框 */
  [data-docs-body] h1,
  [data-docs-body] h2,
  [data-docs-body] h3,
  [data-docs-body] h4,
  [data-docs-body] h5,
  [data-docs-body] h6,
  .fd-docs h1,
  .fd-docs h2,
  .fd-docs h3,
  .fd-docs h4,
  .fd-docs h5,
  .fd-docs h6,
  .fd-content h1,
  .fd-content h2,
  .fd-content h3,
  .fd-content h4,
  .fd-content h5,
  .fd-content h6,
  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    border-bottom: none !important;
    text-decoration: none !important;
    border: none !important;
  }

  /* 去掉标题中链接的下划线 */
  [data-docs-body] h1 a,
  [data-docs-body] h2 a,
  [data-docs-body] h3 a,
  [data-docs-body] h4 a,
  [data-docs-body] h5 a,
  [data-docs-body] h6 a,
  .fd-docs h1 a,
  .fd-docs h2 a,
  .fd-docs h3 a,
  .fd-docs h4 a,
  .fd-docs h5 a,
  .fd-docs h6 a,
  .fd-content h1 a,
  .fd-content h2 a,
  .fd-content h3 a,
  .fd-content h4 a,
  .fd-content h5 a,
  .fd-content h6 a,
  .prose h1 a,
  .prose h2 a,
  .prose h3 a,
  .prose h4 a,
  .prose h5 a,
  .prose h6 a {
    text-decoration: none !important;
    border-bottom: none !important;
    border: none !important;
  }

  /* 确保标题的伪元素也没有下划线 */
  [data-docs-body] h1::after,
  [data-docs-body] h2::after,
  [data-docs-body] h3::after,
  [data-docs-body] h4::after,
  [data-docs-body] h5::after,
  [data-docs-body] h6::after,
  .fd-docs h1::after,
  .fd-docs h2::after,
  .fd-docs h3::after,
  .fd-docs h4::after,
  .fd-docs h5::after,
  .fd-docs h6::after,
  .fd-content h1::after,
  .fd-content h2::after,
  .fd-content h3::after,
  .fd-content h4::after,
  .fd-content h5::after,
  .fd-content h6::after {
    border-bottom: none !important;
    border: none !important;
    content: none !important;
  }

  /* 去掉标题的锚点链接下划线 */
  [data-docs-body] .heading-anchor,
  .fd-docs .heading-anchor,
  .fd-content .heading-anchor {
    text-decoration: none !important;
    border-bottom: none !important;
    border: none !important;
  }

  /* 增加h1标题的上边距 - 让h1和上一段之间有更大的空间 */
  [data-docs-body] h1,
  .fd-docs h1,
  .fd-content h1,
  .prose h1 {
    margin-top: 4rem !important; /* 48px - 比默认更大的上边距 */
  }

  /* 确保第一个h1标题不会有过大的上边距 */
  [data-docs-body] h1:first-child,
  .fd-docs h1:first-child,
  .fd-content h1:first-child,
  .prose h1:first-child {
    margin-top: 0 !important;
  }

  /* 如果h1前面紧跟着另一个标题，减少一些间距 */
  [data-docs-body] h1 + h1,
  [data-docs-body] h2 + h1,
  [data-docs-body] h3 + h1,
  [data-docs-body] h4 + h1,
  [data-docs-body] h5 + h1,
  [data-docs-body] h6 + h1,
  .fd-docs h1 + h1,
  .fd-docs h2 + h1,
  .fd-docs h3 + h1,
  .fd-docs h4 + h1,
  .fd-docs h5 + h1,
  .fd-docs h6 + h1,
  .fd-content h1 + h1,
  .fd-content h2 + h1,
  .fd-content h3 + h1,
  .fd-content h4 + h1,
  .fd-content h5 + h1,
  .fd-content h6 + h1 {
    margin-top: 2rem !important; /* 32px - 标题之间的间距稍小一些 */
  }
}
