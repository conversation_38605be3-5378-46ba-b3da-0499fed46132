import { DocsLayout } from "fumadocs-ui/layouts/docs";
import type { ReactNode } from "react";
import { source } from "@/lib/source";

export default function DocsRootLayout({ children }: { children: ReactNode }) {
  return (
    <div>
      <DocsLayout
        tree={source.pageTree}
        themeSwitch={{ enabled: false }}
        nav={{
          title: (
            <span className={`inline-block font-logo text-2xl font-black text-brand-600`}>
              interactives
            </span>
          ),
        }}
      >
        {children}
      </DocsLayout>
    </div>
  );
}
