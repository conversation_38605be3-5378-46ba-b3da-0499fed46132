/* 导入共享的页面排版样式 */
@import "../shared/page-layout.css";
@import "tailwindcss";

/* paged-preview页面特有的主题设置 */


@layer utilities {
  /* paged-preview特有的全局样式 */
  html,
  body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  .paged-preview {
    background: #f0f0f0;
    height: 100%;
    width: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 20px 0;
    box-sizing: border-box;
  }

  .pages-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: fit-content;
    min-height: 100%;
    margin: 0 auto;
  }
}
