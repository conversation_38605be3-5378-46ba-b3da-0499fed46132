/* app/paged-preview/page.tsx */
"use client";
import dynamic from "next/dynamic";
import { useEffect, useState, useRef } from "react";
import "./page.css";

/* Dynamic loading of paged components to avoid SSR errors */
const PagedPreview = dynamic(() => import("@/components/PagedPreview"), { ssr: false });
const StaticPagedPreview = dynamic(() => import("@/components/StaticPagedPreview"), { ssr: false });

export default function PagedPreviewPage() {
  const [html, setHtml] = useState<string>("");
  const [htmlReceived, setHtmlReceived] = useState<boolean>(false);
  const [lastReceived, setLastReceived] = useState<Date | null>(null);
  const [isPrePaged, setIsPrePaged] = useState<boolean>(false);
  const currentProjectRef = useRef<string | null>(null);

  // Get current project name from URL
  const getProjectNameFromUrl = (): string | null => {
    try {
      const parentUrl = window.parent?.location.pathname || "";
      const match = parentUrl.match(/\/editor\/([^\/]+)/);
      return match ? match[1] : null;
    } catch (e) {
      return null;
    }
  };

  // Detect if HTML content is already paginated
  const detectPrePagedContent = (htmlContent: string): boolean => {
    if (!htmlContent) return false;

    const hasPageElements = htmlContent.includes('class="page"') || htmlContent.includes('class="pages-container"');
    const hasPageHeaders = htmlContent.includes('class="page-header"') || htmlContent.includes('class="page-footer"');
    const hasMultiplePages = (htmlContent.match(/class="page"/g) || []).length > 1;

    return hasPageElements && (hasPageHeaders || hasMultiplePages);
  };

  // Check if project context has changed and handle accordingly
  const checkAndHandleProjectChange = (): boolean => {
    const projectName = getProjectNameFromUrl();
    if (projectName !== currentProjectRef.current) {
      currentProjectRef.current = projectName;

      // Clear current content for new project
      setHtml("");
      setHtmlReceived(false);
      setLastReceived(null);
      return true;
    }
    return false;
  };

  // Initialize project name and setup event listeners
  useEffect(() => {
    currentProjectRef.current = getProjectNameFromUrl();

    const handlePopState = () => {
      checkAndHandleProjectChange();
    };

    // Listen to parent window popstate events if in iframe
    if (window.parent !== window) {
      try {
        window.parent.addEventListener("popstate", handlePopState);
      } catch (e) {
        // Silently handle cross-origin errors
      }
    }

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
      if (window.parent !== window) {
        try {
          window.parent.removeEventListener("popstate", handlePopState);
        } catch (e) {
          // Silently handle cleanup errors
        }
      }
    };
  }, []);

  // Initialize project context
  useEffect(() => {
    checkAndHandleProjectChange();
  }, []);

  // Send ready message when component mounts
  useEffect(() => {
    checkAndHandleProjectChange();

    const sendReadyMessage = () => {
      if (window.parent === window) return;

      try {
        window.parent.postMessage(
          {
            type: "preview-ready",
            projectName: currentProjectRef.current,
          },
          "*"
        );
      } catch (err) {
        // Silently handle send errors
      }
    };

    sendReadyMessage();
    const timer1 = setTimeout(sendReadyMessage, 600);
    const timer2 = setTimeout(() => {
      if (!htmlReceived) {
        sendReadyMessage();
      }
    }, 2000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  // Listen for HTML content messages from parent window
  useEffect(() => {
    function handler(e: MessageEvent) {
      checkAndHandleProjectChange();

      // Handle HTML update messages
      if (e.data?.type === "html" || e.data?.type === "set-html") {
        const messageId = e.data.messageId;

        // Validate project name matches current project
        if (e.data.projectName && e.data.projectName !== currentProjectRef.current) {
          return;
        }

        const htmlContent = e.data.content;
        if (htmlContent === undefined) {
          return;
        }

        // Send confirmation message
        if (messageId && window.parent !== window) {
          try {
            window.parent.postMessage(
              {
                type: "message-received",
                messageId: messageId,
                timestamp: Date.now(),
                projectName: currentProjectRef.current,
              },
              "*"
            );
          } catch (err) {
            // Silently handle send errors
          }
        }

        if (typeof htmlContent !== "string" || htmlContent === html) {
          return;
        }

        // Detect if content is already paginated and update state
        const prePaged = detectPrePagedContent(htmlContent);
        setHtml(htmlContent);
        setIsPrePaged(prePaged);
        setHtmlReceived(true);
        setLastReceived(new Date());
      }
      // Handle reset scale messages
      else if (e.data?.type === "reset-scale") {
        if (e.data.messageId && window.parent !== window) {
          try {
            window.parent.postMessage(
              {
                type: "message-received",
                messageId: e.data.messageId,
                timestamp: Date.now(),
                projectName: currentProjectRef.current,
              },
              "*"
            );
          } catch (err) {
            // Silently handle send errors
          }
        }

        try {
          window.dispatchEvent(new CustomEvent("reset-preview-scale"));
        } catch (err) {
          // Silently handle event dispatch errors
        }
      }
      // Handle print commands
      else if (e.data?.action === "print" && e.origin === window.location.origin) {
        try {
          window.print();
        } catch (printErr) {
          // Silently handle print errors
        }
      }
      // Handle scroll position requests
      else if (e.data?.type === "get-scroll-position") {
        const messageId = e.data.messageId;
        const previewContainer = document.querySelector('.paged-preview') as HTMLElement;

        if (previewContainer && window.parent !== window) {
          const scrollTop = previewContainer.scrollTop;
          const scrollLeft = previewContainer.scrollLeft;

          try {
            window.parent.postMessage(
              {
                type: "scroll-position-response",
                messageId,
                scrollTop,
                scrollLeft,
                timestamp: Date.now(),
              },
              "*"
            );
          } catch (err) {
            // Silently handle send errors
          }
        }
      }
      // Handle scroll position restoration
      else if (e.data?.type === "restore-scroll-position") {
        const { scrollTop, scrollLeft } = e.data;
        const previewContainer = document.querySelector('.paged-preview') as HTMLElement;

        if (previewContainer) {
          previewContainer.scrollTo({
            top: scrollTop,
            left: scrollLeft || 0,
            behavior: 'smooth'
          });
        }
      }
      // Handle request for paged HTML content
      else if (e.data?.type === "get-paged-html") {
        const messageId = e.data.messageId;

        try {
          // 获取已分页的HTML内容
          const pagesContainer = document.querySelector('.pages-container');
          let pagedHtml = '';

          if (pagesContainer) {
            // 如果有分页容器，获取完整的分页HTML
            pagedHtml = pagesContainer.outerHTML;
          } else {
            // 如果没有分页容器，可能是StaticPagedPreview，获取整个内容
            const contentContainer = document.querySelector('.scaled-container') ||
                                   document.querySelector('.preview-wrapper') ||
                                   document.body;
            pagedHtml = contentContainer ? contentContainer.innerHTML : '';
          }

          // 发送已分页的HTML回父窗口
          if (window.parent !== window) {
            window.parent.postMessage({
              type: "paged-html-response",
              messageId: messageId,
              html: pagedHtml,
              timestamp: Date.now(),
            }, "*");
          }
        } catch (error) {
          // 发送错误响应
          if (window.parent !== window) {
            window.parent.postMessage({
              type: "paged-html-response",
              messageId: messageId,
              error: error instanceof Error ? error.message : "Unknown error",
              timestamp: Date.now(),
            }, "*");
          }
        }
      }
    }

    window.addEventListener("message", handler);
    return () => {
      window.removeEventListener("message", handler);
    };
  }, []);

  if (!html) {
    return (
      <div className="p-4 text-gray-400 flex flex-col items-center justify-center h-full">
        <p className="text-sm mt-2">
          {htmlReceived
            ? `Last received: ${lastReceived?.toLocaleTimeString()}`
            : "Waiting for compile..."}
        </p>
      </div>
    );
  }

  return (
    <div className="h-full w-full overflow-hidden flex flex-col">
      {isPrePaged ? (
        <StaticPagedPreview content={html} debug={false} />
      ) : (
        <PagedPreview content={html} debug={false} />
      )}
    </div>
  );
}
