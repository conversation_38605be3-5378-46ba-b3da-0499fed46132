// app/layout.tsx
import "./globals.css";
import { ReactNode } from "react";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { Analytics } from "@vercel/analytics/next";
import { proseFont, monoFont } from "./fonts";
import { GeistSans } from "geist/font/sans";
import { RootProvider } from "fumadocs-ui/provider";
import { Toaster } from "sonner";

import SupabaseProvider from "@/components/SupabaseProvider";
import { applyWebSocketPatch } from "@/utils/fixWebSocketPattern";
import { getSessionCompat } from "@/utils/supabase/compat";
import { createClient } from "@/utils/supabase/server";

// 应用WebSocket错误修复（开发环境）
if (process.env.NODE_ENV === "development") {
  try {
    // 这里不会在服务器端执行applyWebSocketPatch
    // 但会在客户端执行，以防止pattern.split错误
    if (typeof window !== "undefined") {
      console.log("注入WebSocket错误修复...");
      setTimeout(() => {
        // 使用setTimeout确保在客户端环境中执行
        applyWebSocketPatch();
      }, 0);
    }
  } catch (e) {
    console.warn("应用WebSocket错误修复失败", e);
  }
}

export const metadata = {
  title: "interactives",
  description: "Supabase Auth Demo",
};

export default async function RootLayout({ children }: { children: ReactNode }) {
  // 创建服务端 Supabase 客户端
  const supabase = await createClient();

  // 先验证用户身份
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // 然后获取完整会话（使用安全方法）
  const {
    data: { session },
  } = await getSessionCompat(supabase);

  return (
    <html
      lang="en"
      suppressHydrationWarning
      className={`${proseFont.variable} ${monoFont.variable} monoFont.className ${GeistSans.variable}`}
    >
      <body className="min-h-screen flex flex-col">
        <RootProvider
          theme={{
            enabled: false,
            defaultTheme: "light",
            forcedTheme: "light",
            enableSystem: false,
          }}
        >
          <SupabaseProvider initialSession={session as any}>
            {children}
          </SupabaseProvider>
        </RootProvider>
        <SpeedInsights />
        <Analytics />
        <Toaster />
      </body>
    </html>
  );
}
