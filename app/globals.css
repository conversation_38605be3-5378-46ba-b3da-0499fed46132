/* === imports === */
@import "tailwindcss";
@import "tw-animate-css";

@plugin "@tailwindcss/typography";

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /*custom theme*/
  --color-brand-950: oklch(23.18% 0.0994 267.08);
  --color-brand-900: oklch(34.56% 0.1328 267.63);
  --color-brand-800: oklch(39.62% 0.2068 265.17);
  --color-brand-700: oklch(47.65% 0.3102 264.11);
  --color-brand-600: oklch(50.58% 0.2886 264.84);
  --color-brand-500: oklch(57.66% 0.2443 267.62);
  --color-brand-400: oklch(63.79% 0.1899 269.89);
  --color-brand-300: oklch(76.12% 0.1218 272.4);
  --color-brand-200: oklch(83.76% 0.0774 273.32);
  --color-brand-100: oklch(94.22% 0.0275 274.66);
  --color-brand-50: oklch(97.05% 0.0054 274.97);

  --font-code: var(--font-geist-mono);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 89.876);
  --foreground: oklch(0.13 0.028 261.692);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.13 0.028 261.692);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.13 0.028 261.692);
  --primary: oklch(0.21 0.034 264.665);
  --primary-foreground: oklch(0.985 0.002 247.839);
  --secondary: oklch(0.967 0.003 264.542);
  --secondary-foreground: oklch(0.21 0.034 264.665);
  --muted: oklch(0.967 0.003 264.542);
  --muted-foreground: oklch(0.551 0.027 264.364);
  --accent: oklch(0.967 0.003 264.542);
  --accent-foreground: oklch(0.21 0.034 264.665);
  --destructive: oklch(0.577 0.245 27.325);
  --border: var(--color-gray-300);
  --input: oklch(0.928 0.006 264.531);
  --ring: oklch(0.707 0.022 261.325);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0.002 247.839);
  --sidebar-foreground: oklch(0.13 0.028 261.692);
  --sidebar-primary: oklch(0.21 0.034 264.665);
  --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
  --sidebar-accent: oklch(0.967 0.003 264.542);
  --sidebar-accent-foreground: oklch(0.21 0.034 264.665);
  --sidebar-border: oklch(0.928 0.006 264.531);
  --sidebar-ring: oklch(0.707 0.022 261.325);
}

/* ===== 基础层 ===== */
@layer base {
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-geist-sans), sans-serif;
  }

  /* 设置默认border颜色，这样border类就会自动使用全局的border颜色 */
  * {
    border-color: var(--color-gray-300);
  }

  /* 全局粗体样式增强 - 让所有粗体更粗 */
  strong, b {
    font-weight: 700;
  }

  /* 确保在使用自定义字体时粗体效果明显 */
  .font-prose strong,
  .font-prose b {
    font-weight: 700;
  }

  /* 全局去除标题下划线 */
  h1, h2, h3, h4, h5, h6 {
    border-bottom: none;
    text-decoration: none;
  }

  /* 去除标题中链接的下划线 */
  h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
    text-decoration: none;
    border-bottom: none;
  }

  /* 全局h1标题间距设置 */
  h1 {
    margin-top: 3rem; /* 48px - 增加h1和上一段之间的间距 */
  }

  /* 第一个h1不需要上边距 */
  h1:first-child {
    margin-top: 0;
  }
}

/* ===== 组件层 ===== */
@layer components {
  /* Paper卡片自定义样式 - 响应式设计 */
  .card-custom {
    @apply bg-background;

    /* 取消圆角 */
    border-radius: 0;

    /* 响应式阴影：根据屏幕大小调整阴影强度 */
    box-shadow: 0 clamp(1px, 0.2vw, 2px) clamp(3px, 0.8vw, 8px) rgba(0, 0, 0, 0.1);

    /* 平滑过渡动画 */
    transition: box-shadow 0.2s ease, transform 0.2s ease;

    /* 细边框 */
    border: 1px solid var(--color-gray-200);
  }

  /* 卡片悬停效果：增强阴影和轻微上移 */
  .card-custom:hover {
    box-shadow: 0 clamp(2px, 0.5vw, 4px) clamp(8px, 2vw, 16px) rgba(0, 0, 0, 0.15);
    transform: translateY(clamp(-1px, -0.2vw, -2px));
  }

  /* 评论回复区域的自定义滚动条样式 */
  .replies-container .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
  }

  .replies-container .overflow-y-auto::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 3px;
  }

  .replies-container .overflow-y-auto::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .replies-container .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Firefox 滚动条样式已通过内联样式处理 */

  /* 浮动窗口动画样式 */
  .floating-window-enter {
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .floating-window-enter-active {
    opacity: 1;
    transform: scale(1);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .floating-window-exit {
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 悬停时的微妙动画效果 */
  .floating-window-header:hover {
    transition: background-color 0.2s ease;
  }

  /* 图片浮窗专用动画 */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .image-drag-handle {
    cursor: move;
  }

  /* 浮窗性能优化样式 */
  .floating-window-header {
    /* 确保拖拽手柄始终可交互 */
    pointer-events: auto;
    /* 禁用文本选择 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    /* 启用硬件加速 */
    transform: translateZ(0);
  }

  /* 确保头部按钮始终可点击 */
  .floating-window-header button {
    pointer-events: auto;
    position: relative;
    z-index: 10;
  }

  /* react-rnd 组件优化 */
  .react-rnd {
    /* 启用硬件加速 */
    transform: translateZ(0);
  }

  /* 全屏状态样式 */
  .floating-window-maximized {
    /* 全屏时保留边框颜色，只移除圆角 */
    border-radius: 0 !important;
    /* 可选：调整阴影效果，但保留边框 */
    box-shadow: 0 0 0 2px var(--color-brand-500, #3b82f6) !important;
  }

  .floating-window-maximized .floating-window-header {
    /* 全屏时调整标题栏样式，移除圆角 */
    border-radius: 0;
  }

  /* 双击提示样式 */
  .floating-window-header {
    /* 添加双击提示 */
    position: relative;
  }

  .floating-window-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    transition: background-color 0.2s ease;
  }

  /* 确保iframe容器的相对定位 */
  .floating-window-content {
    position: relative;
  }

  /* 优化iframe的交互体验 */
  .floating-window-content iframe {
    pointer-events: auto;
  }

  /* 移动端触控优化 */
  @media (max-width: 768px) {
    .floating-window-header {
      /* 移动端增加标题栏高度 */
      padding: 10px 8px;
    }

    /* 移动端按钮尺寸现在通过动态类名控制 */

    /* 移动端拖拽优化 */
    .react-rnd {
      /* 禁用移动端的默认触摸行为 */
      touch-action: none;
    }

    /* 移动端浮窗最小尺寸调整 */
    .floating-window-maximized {
      /* 移动端全屏时完全占满屏幕 */
      border: none !important;
      box-shadow: none !important;
    }
  }

  /* 触控设备通用优化 */
  @media (pointer: coarse) {
    /* 触控设备按钮尺寸现在通过动态类名控制 */

    /* 触控设备的拖拽手柄优化 */
    .floating-window-header {
      cursor: grab;
    }

    .floating-window-header:active {
      cursor: grabbing;
    }
  }

  /* 拖拽冲突解决方案 - 拖拽时禁用iframe的pointer-events */
  .floating-window-dragging iframe {
    pointer-events: none !important;
  }

  /* 拖拽时禁用所有可能干扰的元素 */
  .floating-window-dragging .floating-window-content {
    pointer-events: none !important;
  }

  /* 确保拖拽手柄在拖拽时仍然可用 */
  .floating-window-dragging .floating-window-header {
    pointer-events: auto !important;
  }

  /* 关闭按钮图标线条粗细优化 */
  .floating-window-header button svg {
    stroke-width: 3 !important;
  }

  /* 图片浮窗关闭按钮图标线条粗细 */
  .image-floating-window-close-btn svg {
    stroke-width: 3 !important;
  }

  /* XTerm.js 透明背景支持 */
  #terminal, .xterm {
    background: transparent !important;
  }

  /* 确保 xterm 相关元素都是透明的 */
  .xterm .xterm-viewport {
    background: transparent !important;
  }

  .xterm .xterm-screen {
    background: transparent !important;
  }

  /* 确保 xterm 容器本身也是透明的 */
  .xterm-helper-textarea {
    background: transparent !important;
  }

  /* 修复ANSI转义序列格式化字符的背景色问题 */
  /* 特别针对斜体和下划线文本的背景色问题 */
  .xterm .xterm-rows .xterm-row span[style*="font-style: italic"] {
    background: transparent !important;
    background-color: transparent !important;
  }

  .xterm .xterm-rows .xterm-row span[style*="text-decoration: underline"] {
    background: transparent !important;
    background-color: transparent !important;
  }

  /* 处理组合样式：斜体+下划线 */
  .xterm .xterm-rows .xterm-row span[style*="font-style: italic"][style*="text-decoration: underline"] {
    background: transparent !important;
    background-color: transparent !important;
  }

  /* 确保其他格式化文本也保持透明背景 */
  .xterm .xterm-rows .xterm-row span[style*="font-weight: bold"],
  .xterm .xterm-rows .xterm-row span[style*="text-decoration: line-through"],
  .xterm .xterm-rows .xterm-row span[style*="color:"] {
    background: transparent !important;
  }

  /* 更具体地处理所有可能的ANSI格式化元素 */
  .xterm .xterm-rows .xterm-row span {
    background: transparent !important;
  }

  /* 处理WebGL渲染器的背景色问题 */
  .xterm .xterm-decoration-container,
  .xterm .xterm-decoration-container > * {
    background: transparent !important;
  }

  /* 确保所有xterm内部元素都是透明的，除了特殊功能元素 */
  .xterm .xterm-rows,
  .xterm .xterm-rows > *,
  .xterm .xterm-char-measure-element {
    background: transparent !important;
  }

  /* 确保光标和选择区域的背景色正常工作 */
  .xterm .xterm-cursor-layer .xterm-cursor,
  .xterm .xterm-selection {
    background: initial !important;
  }

  /* 特别处理Canvas渲染器的背景 */
  .xterm canvas {
    background: transparent !important;
  }

  /* 针对WebGL渲染器的特殊处理 */
  .xterm .xterm-screen canvas {
    background: transparent !important;
  }

  /* 确保所有渲染层都是透明的 */
  .xterm .xterm-screen,
  .xterm .xterm-screen > *,
  .xterm .xterm-text-layer,
  .xterm .xterm-link-layer,
  .xterm .xterm-cursor-layer {
    background: transparent !important;
  }

  /* 修复ANSI背景色渲染问题 - 针对WebGL渲染器 */
  .xterm .xterm-screen .xterm-bg-color-0,
  .xterm .xterm-screen .xterm-bg-color-1,
  .xterm .xterm-screen .xterm-bg-color-2,
  .xterm .xterm-screen .xterm-bg-color-3,
  .xterm .xterm-screen .xterm-bg-color-4,
  .xterm .xterm-screen .xterm-bg-color-5,
  .xterm .xterm-screen .xterm-bg-color-6,
  .xterm .xterm-screen .xterm-bg-color-7,
  .xterm .xterm-screen .xterm-bg-color-8,
  .xterm .xterm-screen .xterm-bg-color-9,
  .xterm .xterm-screen .xterm-bg-color-10,
  .xterm .xterm-screen .xterm-bg-color-11,
  .xterm .xterm-screen .xterm-bg-color-12,
  .xterm .xterm-screen .xterm-bg-color-13,
  .xterm .xterm-screen .xterm-bg-color-14,
  .xterm .xterm-screen .xterm-bg-color-15 {
    background: transparent !important;
  }

  /* 特别针对xterm.js的斜体和下划线CSS类 */
  .xterm .xterm-italic,
  .xterm .xterm-underline,
  .xterm .xterm-italic.xterm-underline,
  .xterm span.xterm-italic,
  .xterm span.xterm-underline,
  .xterm span.xterm-italic.xterm-underline {
    background: transparent !important;
    background-color: transparent !important;
  }

  /* 处理Canvas渲染器中的斜体和下划线 */
  .xterm .xterm-rows .xterm-row .xterm-italic,
  .xterm .xterm-rows .xterm-row .xterm-underline,
  .xterm .xterm-rows .xterm-row .xterm-italic.xterm-underline {
    background: transparent !important;
    background-color: transparent !important;
  }

  /* 最强力的修复：覆盖所有可能的斜体和下划线背景 */
  .xterm *[style*="font-style: italic"],
  .xterm *[style*="text-decoration: underline"],
  .xterm *[style*="font-style:italic"],
  .xterm *[style*="text-decoration:underline"] {
    background: transparent !important;
    background-color: transparent !important;
  }

  /* 针对特定的xterm内部类名 */
  .xterm .xterm-decoration-italic,
  .xterm .xterm-decoration-underline,
  .xterm .xterm-char-italic,
  .xterm .xterm-char-underline {
    background: transparent !important;
    background-color: transparent !important;
  }
}

/* ===== 工具层 ===== */
@layer utilities {
  .font-menu {
    font-family: var(--font-geist-sans), sans-serif;
  }

  /* 统一表格行高度 */
  .table-row-uniform {
    height: 50px; /* 设置固定行高 */
  }
  
  /* 确保表格单元格内容垂直居中 */
  .table-row-uniform td {
    vertical-align: middle;
  }
}
