"use client";

import { PaperType, Category } from "@prisma/client";
import { Eye, Download, Quote } from "lucide-react";
import { useState } from "react";
import { globalMessageManager } from "@/lib/floating-window-messages";

// Image component with fallback for loading and error states (adapted from paper-card.tsx)
function ImageWithFallback({ src, alt, paperId }: { src?: string; alt: string; paperId: string }) {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    // 使用新的assets API路径获取promo.png图片
    const imageSrc = paperId ? `/api/papers/${paperId}/assets?path=promo.png` : src || "";

    return (
        <div className="relative h-full w-full rounded overflow-hidden">
            {/* Brand-colored placeholder shown during loading or on error */}
            <div className={`absolute inset-0 bg-brand-800 ${!isLoading && !hasError ? "hidden" : ""}`} />

            {/* Actual image, hidden if error occurred */}
            <img
                src={imageSrc}
                alt={alt}
                className={`h-full w-full object-cover ${hasError ? "hidden" : ""}`}
                onLoad={() => setIsLoading(false)}
                onError={() => {
                    setIsLoading(false);
                    setHasError(true);
                }}
            />
        </div>
    );
}

interface TPaper {
    id: string;
    title: string;
    abstract: string;
    authors: string[];
    paperTags: string[];
    category: Category;
    type: PaperType;
    status: string;
    publishedAt: string | null;
    acceptedAt: string | null;
    submittedAt: string;
    readNumber: number;
    downloads: number;
    citation: number;
    authorId: string;
    issueId: string | null;
}

interface PaperRowProps {
    paper: TPaper;
    index: number;
}

const CATEGORY_COLORS = {
    AI: 'bg-blue-100 text-blue-700',
    HCI: 'bg-green-100 text-green-700',
    DESIGN: 'bg-purple-100 text-purple-700',
    SYSTEMS: 'bg-orange-100 text-orange-700',
    THEORY: 'bg-red-100 text-red-700',
    UNKNOWN: 'bg-gray-100 text-gray-600'
};

const TYPE_COLORS = {
    FULL: 'bg-indigo-100 text-indigo-700',
    GALLERY: 'bg-pink-100 text-pink-700',
    PREPRINT: 'bg-yellow-100 text-yellow-700'
};



export default function PaperRow({ paper, index }: PaperRowProps) {
    const formatDate = (dateString: string | null) => {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    };

    const formatNumber = (num: number) => {
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'k';
        }
        return num.toString();
    };



    // 生成消息ID
    const generateMessageId = () => {
        return 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    };

    const sendTerminalCommand = (command: string) => {
        // console.log(`🚀 发送terminal命令: ${command}`);
        // console.log(`📍 当前环境检测:`, {
        //     isInIframe: window.parent !== window,
        //     hasParent: !!window.parent,
        //     origin: window.location.origin
        // });

        // 尝试多种通信方式
        let messageSent = false;

        // 方式1: 使用最新的postMessage协议格式
        try {
            if (window.parent && window.parent !== window) {
                // console.log(`📤 使用最新postMessage协议发送到父窗口`);
                const messageId = generateMessageId();
                window.parent.postMessage({
                    id: messageId,              // 🆕 消息ID
                    type: 'TERMINAL_COMMAND',
                    timestamp: Date.now(),
                    windowId: undefined,        // 当前窗口不是浮窗，所以windowId为undefined
                    source: 'window',           // 🆕 消息来源
                    payload: {
                        command: command,
                        params: undefined,      // 🆕 命令参数
                        expectResponse: true
                    }
                }, '*');
                messageSent = true;
            }
        } catch (error) {
            console.error('❌ postMessage failed:', error);
        }

        // 方式2: 如果在主窗口中，使用globalMessageManager
        if (!messageSent) {
            try {
                // console.log(`📤 使用globalMessageManager发送`);
                globalMessageManager.sendTerminalCommand(command);
                messageSent = true;
            } catch (error) {
                console.error('❌ GlobalMessageManager failed to send command:', error);
            }
        }

        // 方式3: 降级到传统方式
        if (!messageSent) {
            // console.log(`📤 降级到传统新窗口方式`);
            const url = command.replace('open ', '');
            window.open(url, '_blank');
        }
    };

    const handleRowClick = () => {
        const command = `create_window type=path target=/paper/${paper.id} x=0.2 y=0.2 width=0.8 height=0.8`;
        sendTerminalCommand(command);
    };

    const handleTitleClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        const command = `create_window type=path target=/paper/${paper.id} x=0.2 y=0.2 width=0.8 height=0.8`;
        sendTerminalCommand(command);
    };

    return (
        <div
            id={`paper-row-${paper.id}`}  // 🆕 添加ID供DOM操作测试使用
            className={`
        border-b border-gray-200 py-1 cursor-pointer transition-colors
        ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
        hover:bg-blue-50
      `}
            onClick={handleRowClick}
        >
            <div className="flex items-center whitespace-nowrap overflow-hidden text-xs">
                {/* PromoImage */}
                <div className="w-16 h-16 shrink-0 mr-2">
                    <ImageWithFallback paperId={paper.id} src="" alt={paper.title} />
                </div>

                {/* 标题 */}
                <div className="w-80 shrink-0 px-2">
                    <div
                        className="text-gray-900 hover:text-blue-600 transition-colors cursor-pointer"
                        onClick={handleTitleClick}
                        title={paper.title}
                    >
                        <span className="truncate font-medium">
                            {paper.title}
                        </span>
                    </div>
                    <div className="text-xs text-gray-500 truncate mt-0.5" title={paper.abstract}>
                        {paper.abstract.slice(0, 100)}...
                    </div>
                </div>

                {/* 作者 */}
                <div className="w-32 shrink-0 px-2">
                    <div className="truncate text-gray-700" title={paper.authors.join(', ')}>
                        {paper.authors.length > 0 ? paper.authors[0] : '-'}
                        {paper.authors.length > 1 && (
                            <span className="text-gray-500 ml-1">+{paper.authors.length - 1}</span>
                        )}
                    </div>
                </div>

                {/* 分类 */}
                <div className="w-24 shrink-0 px-2">
                    <span className={`
            inline-block px-2 py-0.5 rounded-full text-xs font-medium
            ${CATEGORY_COLORS[paper.category] || CATEGORY_COLORS.UNKNOWN}
          `}>
                        {paper.category}
                    </span>
                </div>

                {/* 类型 */}
                <div className="w-20 shrink-0 px-2">
                    <span className={`
            inline-block px-2 py-0.5 rounded-full text-xs font-medium
            ${TYPE_COLORS[paper.type]}
          `}>
                        {paper.type}
                    </span>
                </div>



                {/* 发布时间 */}
                <div className="w-28 shrink-0 px-2 text-gray-600">
                    {formatDate(paper.publishedAt)}
                </div>

                {/* 阅读数 */}
                <div className="w-16 shrink-0 px-2 text-right">
                    <div className="flex items-center justify-end gap-1">
                        <Eye className="w-3 h-3 text-gray-400" />
                        <span className="text-gray-600">{formatNumber(paper.readNumber)}</span>
                    </div>
                </div>

                {/* 下载数 */}
                <div className="w-16 shrink-0 px-2 text-right">
                    <div className="flex items-center justify-end gap-1">
                        <Download className="w-3 h-3 text-gray-400" />
                        <span className="text-gray-600">{formatNumber(paper.downloads)}</span>
                    </div>
                </div>

                {/* 引用数 */}
                <div className="w-16 shrink-0 px-2 text-right">
                    <div className="flex items-center justify-end gap-1">
                        <Quote className="w-3 h-3 text-gray-400" />
                        <span className="text-gray-600">{formatNumber(paper.citation)}</span>
                    </div>
                </div>

                {/* 标签 */}
                <div className="w-40 shrink-0 px-2">
                    <div className="flex items-center gap-1 overflow-hidden">
                        {paper.paperTags.slice(0, 3).map((tag, index) => (
                            <span
                                key={index}
                                className="inline-block px-1.5 py-0.5 bg-gray-200 text-gray-700 rounded text-xs font-medium"
                                title={tag}
                            >
                                {tag.length > 8 ? tag.slice(0, 8) + '...' : tag}
                            </span>
                        ))}
                        {paper.paperTags.length > 3 && (
                            <span className="text-gray-500 text-xs">
                                +{paper.paperTags.length - 3}
                            </span>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
} 