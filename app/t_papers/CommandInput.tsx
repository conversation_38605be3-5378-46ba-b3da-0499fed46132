"use client";

import { useState, useEffect } from 'react';
import { Command } from 'cmdk';
import { Search, RotateCcw, Clock, Tag, User, Calendar, Filter, SortAsc } from 'lucide-react';

interface CommandInputProps {
    value: string;
    onChange: (value: string) => void;
    onSubmit: (value: string) => void;
    placeholder?: string;
}

// 命令选项定义
const COMMAND_OPTIONS = [
    // 排序选项
    {
        group: 'Sort',
        icon: SortAsc,
        items: [
            { value: 'sort:publishedAt-desc', label: 'Published time descending', description: 'Latest published papers first' },
            { value: 'sort:publishedAt-asc', label: 'Published time ascending', description: 'Earliest published papers first' },
            { value: 'sort:readNumber-desc', label: 'Read number descending', description: 'Most popular papers first' },
            { value: 'sort:readNumber-asc', label: 'Read number ascending', description: 'Least popular papers first' },
            { value: 'sort:downloads-desc', label: 'Download number descending', description: 'Most downloaded papers first' },
            { value: 'sort:citation-desc', label: 'Citation number descending', description: 'Most cited papers first' },
            { value: 'sort:submittedAt-desc', label: 'Submitted time descending', description: 'Latest submitted papers first' },
            { value: 'sort:title-asc', label: 'Title alphabetically', description: 'Title A-Z order' },
        ]
    },
    // 分类筛选
    {
        group: 'Research category',
        icon: Filter,
        items: [
            { value: 'category:AI', label: 'Artificial Intelligence', description: 'AI, machine learning, deep learning, etc.' },
            { value: 'category:HCI', label: 'Human-Computer Interaction', description: 'UI/UX, interaction design, user research, etc.' },
            { value: 'category:DESIGN', label: 'Design', description: 'Visual design, product design, etc.' },
            { value: 'category:SYSTEMS', label: 'Systems', description: 'Operating systems, distributed systems, etc.' },
            { value: 'category:THEORY', label: 'Theory', description: 'Computer theory, algorithm theory, etc.' },
            { value: 'category:ALL', label: 'All categories', description: 'No restriction on research categories' },
        ]
    },
    // 论文类型
    {
        group: 'Paper type',
        icon: Tag,
        items: [
            { value: 'type:FULL', label: 'Full paper', description: 'Full research paper' },
            { value: 'type:GALLERY', label: 'Gallery', description: 'Design works, project presentations' },
            { value: 'type:ALL', label: 'All types', description: 'No restriction on paper types' },
        ]
    },
    // 状态筛选
    {
        group: 'Paper status',
        icon: Clock,
        items: [
            { value: 'status:PUBLISHED', label: 'Published', description: 'Published papers' },
            { value: 'status:SUBMITTED', label: 'Submitted', description: 'Submitted papers' },
            { value: 'status:UNDER_REVIEW', label: 'Under review', description: 'Under review papers' },
            { value: 'status:ACCEPTED', label: 'Accepted', description: 'Accepted papers' },
            { value: 'status:ALL', label: 'All status', description: 'No restriction on paper status' },
        ]
    },
    // 日期范围
    {
        group: 'Date range',
        icon: Calendar,
        items: [
            { value: 'from:2024-01-01', label: 'Since 2024', description: 'Papers since 2024' },
            { value: 'from:2024-06-01', label: '2024下半年', description: 'Papers since 2024' },
            { value: 'to:2024-12-31', label: 'Before 2024', description: 'Papers before 2024' },
            { value: 'dateField:submittedAt', label: 'By submitted time', description: 'Filter by submitted time' },
            { value: 'dateField:acceptedAt', label: 'By accepted time', description: 'Filter by accepted time' },
        ]
    },
    // 其他选项
    {
        group: 'Other',
        icon: User,
        items: [
            { value: 'limit:30', label: '30 per page', description: 'Increase the number of papers per page' },
            { value: 'limit:50', label: '50 per page', description: 'Display more papers' },
            { value: 'limit:10', label: '10 per page', description: 'Decrease the number of papers per page' },
        ]
    }
];

// 快速示例 - 基于实际使用场景
const QUICK_EXAMPLES = [
    { value: 'machine learning', label: 'Machine learning papers', description: 'Search machine learning related papers' },
    { value: 'deep learning', label: 'Deep learning research', description: 'Search deep learning related research' },
    { value: 'sort:readNumber-desc', label: 'Popular papers', description: 'Sort by read number descending' },
    { value: 'sort:publishedAt-desc', label: 'Latest published', description: 'Sort by published time descending' },
    { value: 'sort:citation-desc', label: 'Highly cited papers', description: 'Sort by citation number descending' },
    { value: 'category:AI', label: 'AI papers', description: 'AI related papers' },
    { value: 'category:HCI', label: 'HCI papers', description: 'HCI related papers' },
    { value: 'type:GALLERY', label: 'Gallery', description: 'Design works, project presentations' },
    { value: 'status:PUBLISHED', label: 'Published papers', description: 'Published papers' },
    { value: 'from:2024-01-01', label: '2024 papers', description: 'Papers since 2024' },
];

export default function CommandInput({ value, onChange, onSubmit, placeholder }: CommandInputProps) {
    const [open, setOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState(''); // 用于搜索的内部状态

    // 确保面板关闭时清空搜索查询
    useEffect(() => {
        if (!open) {
            setSearchQuery('');
        }
    }, [open]);

    // 处理键盘事件 - 仅保留基本功能
    const handleInputKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleSubmit();
        } else if (e.key === 'Escape') {
            setOpen(false);
        }
    };

    // 解析当前已设置的条件
    const parseCurrentFilters = (command: string) => {
        const filters: string[] = [];
        const parts = command.match(/(\w+):"?([^"\s]+)"?/g) || [];

        parts.forEach(part => {
            const match = part.match(/(\w+):"?([^"]+)"?/);
            if (match) {
                const [, key, value] = match;
                filters.push(`${key}:${value}`);
            }
        });

        return filters;
    };

    const currentFilters = parseCurrentFilters(value);

    // 处理选项选择
    const handleSelect = (selectedValue: string) => {
        // 检查是否已存在相同类型的筛选条件
        const [newKey] = selectedValue.split(':');
        const existingFilterIndex = currentFilters.findIndex(filter => filter.startsWith(newKey + ':'));

        let newFilters = [...currentFilters];

        // 检查是否选择了完全相同的条件
        if (currentFilters.includes(selectedValue)) {
            setSearchQuery('');
            setOpen(false);
            return;
        }

        if (existingFilterIndex >= 0) {
            // 替换现有条件
            newFilters[existingFilterIndex] = selectedValue;
        } else {
            // 添加新条件
            newFilters.push(selectedValue);
        }

        const newCommand = newFilters.join(' ');
        onChange(newCommand);
        // 立即执行搜索，不等待用户手动提交
        onSubmit(newCommand);
        setSearchQuery(''); // 清空搜索查询
        setOpen(false);
    };

    // 处理重置
    const handleReset = () => {
        onChange('');
        onSubmit(''); // 立即执行搜索以恢复默认列表
        setSearchQuery('');
        setOpen(false);
    };

    // 处理提交
    const handleSubmit = () => {
        // 如果有搜索查询，将其作为search条件添加到现有命令中
        if (searchQuery.trim()) {
            const query = searchQuery.trim();
            // 检查是否已经是命令格式（包含冒号），如果不是则作为search条件
            const isCommand = query.includes(':');
            const searchValue = isCommand ? query : `search:"${query}"`;

            const newCommand = currentFilters.length > 0
                ? `${currentFilters.join(' ')} ${searchValue}`
                : searchValue;
            onChange(newCommand);
            onSubmit(newCommand);
        } else {
            onSubmit(value);
        }
        setSearchQuery('');
        setOpen(false);
    };

    // 移除单个筛选条件
    const removeFilter = (filterToRemove: string) => {
        const newFilters = currentFilters.filter(filter => filter !== filterToRemove);
        const newCommand = newFilters.join(' ');
        onChange(newCommand);
        onSubmit(newCommand); // 立即执行搜索
    };

    return (
        <div className="relative">
            {/* 主输入区域 */}
            <div className="flex items-center bg-white border-b border-gray-200 px-3 py-2">
                <Search className="w-4 h-4 text-gray-400 mr-2 shrink-0" />

                {/* 已选择的筛选条件标签 */}
                <div className="flex items-center gap-2 flex-wrap mr-2">
                    {currentFilters.map((filter, index) => {
                        const [key, value] = filter.split(':');
                        return (
                            <span
                                key={index}
                                className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md"
                            >
                                <span className="font-medium">{key}</span>
                                <span>:</span>
                                <span>{value}</span>
                                <button
                                    onClick={() => removeFilter(filter)}
                                    className="ml-1 hover:bg-blue-200 rounded"
                                    title="Remove this condition"
                                >
                                    ×
                                </button>
                            </span>
                        );
                    })}
                </div>

                {/* 输入框 */}
                <div className="flex-1">
                    <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onFocus={() => setOpen(true)}
                        onKeyDown={handleInputKeyDown}
                        placeholder={currentFilters.length > 0 ? "Search keywords or add more conditions..." : (placeholder || "Enter keywords directly, or select filters...")}
                        className="w-full bg-transparent outline-none text-sm text-gray-700 placeholder-gray-400"
                        autoComplete="off"
                    />
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center gap-1 ml-2">
                    {(currentFilters.length > 0 || searchQuery) && (
                        <button
                            onClick={handleReset}
                            className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
                            title="Clear all conditions"
                        >
                            <RotateCcw className="w-4 h-4" />
                        </button>
                    )}
                </div>
            </div>

            {/* 命令面板 */}
            {open && (
                <div className="absolute bottom-full left-0 right-0 z-20 bg-white border border-gray-200 shadow-lg">
                    <Command className="w-full" value={searchQuery} onValueChange={setSearchQuery} shouldFilter={true}>
                        <Command.List className="max-h-80 overflow-y-auto">
                            <Command.Empty className="p-4 text-center text-gray-500">
                                No matching filters
                            </Command.Empty>

                            {/* 快速示例 */}
                            <Command.Group heading="🚀 Quick examples">
                                {QUICK_EXAMPLES.map((example) => (
                                    <Command.Item
                                        key={example.value}
                                        value={example.value}
                                        onSelect={handleSelect}
                                        className="flex items-center gap-3 px-3 py-2 cursor-pointer hover:bg-gray-100 data-[selected=true]:bg-gray-100"
                                    >
                                        <div className="flex-1">
                                            <div className="font-medium text-sm">{example.label}</div>
                                            <div className="text-xs text-gray-500">{example.description}</div>
                                            <div className="text-xs text-blue-600 mt-1">{example.value}</div>
                                        </div>
                                    </Command.Item>
                                ))}
                            </Command.Group>

                            {/* 各种筛选选项 */}
                            {COMMAND_OPTIONS.map((group) => {
                                const IconComponent = group.icon;
                                return (
                                    <Command.Group key={group.group} heading={`${group.group}`}>
                                        {group.items.map((item) => (
                                            <Command.Item
                                                key={item.value}
                                                value={item.value}
                                                onSelect={handleSelect}
                                                className="flex items-center gap-3 px-3 py-2 cursor-pointer hover:bg-gray-100 data-[selected=true]:bg-gray-100"
                                            >
                                                <IconComponent className="w-4 h-4 text-gray-400 shrink-0" />
                                                <div className="flex-1">
                                                    <div className="font-medium text-sm">{item.label}</div>
                                                    <div className="text-xs text-gray-500">{item.description}</div>
                                                </div>
                                                <div className="text-xs text-blue-600">
                                                    {item.value}
                                                </div>
                                            </Command.Item>
                                        ))}
                                    </Command.Group>
                                );
                            })}
                        </Command.List>
                    </Command>
                </div>
            )}

            {/* 点击外部关闭面板 */}
            {open && (
                <div
                    className="fixed inset-0 z-10"
                    onClick={() => setOpen(false)}
                />
            )}
        </div>
    );
} 