"use client";

import { PaperType, Category } from "@prisma/client";
import { useSearchParams, useRouter } from "next/navigation";
import { useEffect, useState, useCallback, useRef } from "react";
import qs from "qs";

import CommandInput from "./CommandInput";
import PaperRow from "./PaperRow";
import { Skeleton } from "@/components/ui/skeleton";
import { globalMessageManager } from "@/lib/floating-window-messages";

/* ─────────── 类型定义 ─────────── */
type TPaper = {
    id: string;
    title: string;
    abstract: string;
    authors: string[];
    paperTags: string[];
    category: Category;
    type: PaperType;
    status: string;
    publishedAt: string | null;
    acceptedAt: string | null;
    submittedAt: string;
    readNumber: number;
    downloads: number;
    citation: number;
    authorId: string;
    issueId: string | null;
};

/* ─────────── 筛选参数类型 ─────────── */
interface FilterParams {
    // 分页
    skip?: number;
    take?: number;

    // 排序
    sortBy?: 'publishedAt' | 'submittedAt' | 'acceptedAt' | 'readNumber' | 'downloads' | 'citation' | 'title';
    sortOrder?: 'desc' | 'asc';

    // 筛选
    status?: string;
    category?: Category | 'ALL';
    type?: PaperType | 'ALL';
    tags?: string[];
    author?: string;
    search?: string;
    issue?: string;

    // 日期范围 (YYYY-MM-DD格式)
    dateFrom?: string;
    dateTo?: string;
    dateField?: 'publishedAt' | 'submittedAt' | 'acceptedAt';
}

const PAGE_SIZE = 20; // 终端环境适合更多行数

const DEFAULT_PARAMS: FilterParams = {
    skip: 0,
    take: PAGE_SIZE,
    sortBy: 'publishedAt',
    sortOrder: 'desc',
    status: 'PUBLISHED',
    category: 'ALL',
    type: 'ALL',
    dateField: 'publishedAt'
};

export default function TerminalPapersPage() {
    const searchParams = useSearchParams();
    const router = useRouter();

    /* ─────────── 状态管理 ─────────── */
    const [papers, setPapers] = useState<TPaper[]>([]);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [currentParams, setCurrentParams] = useState<FilterParams>(DEFAULT_PARAMS);
    const [commandValue, setCommandValue] = useState("");

    /* ─────────── 引用 ─────────── */
    const isLoadingRef = useRef(false);
    const sentinelRef = useRef<HTMLDivElement>(null);
    const observerRef = useRef<IntersectionObserver | null>(null);

    /* ─────────── 解析URL参数 ─────────── */
    const parseUrlParams = useCallback((): FilterParams => {
        const params: FilterParams = { ...DEFAULT_PARAMS };

        // 基础分页参数
        if (searchParams.get('skip')) params.skip = parseInt(searchParams.get('skip')!);
        if (searchParams.get('take')) params.take = parseInt(searchParams.get('take')!);

        // 排序参数
        if (searchParams.get('sortBy')) params.sortBy = searchParams.get('sortBy') as any;
        if (searchParams.get('sortOrder')) params.sortOrder = searchParams.get('sortOrder') as any;

        // 筛选参数
        if (searchParams.get('status')) params.status = searchParams.get('status')!;
        if (searchParams.get('category')) params.category = searchParams.get('category') as any;
        if (searchParams.get('type')) params.type = searchParams.get('type') as any;
        if (searchParams.get('tags')) params.tags = searchParams.get('tags')!.split(',');
        if (searchParams.get('author')) params.author = searchParams.get('author')!;
        if (searchParams.get('search')) params.search = searchParams.get('search')!;
        if (searchParams.get('issue')) params.issue = searchParams.get('issue')!;

        // 日期参数
        if (searchParams.get('dateFrom')) params.dateFrom = searchParams.get('dateFrom')!;
        if (searchParams.get('dateTo')) params.dateTo = searchParams.get('dateTo')!;
        if (searchParams.get('dateField')) params.dateField = searchParams.get('dateField') as any;

        return params;
    }, [searchParams]);

    /* ─────────── 生成命令字符串 ─────────── */
    const generateCommand = useCallback((params: FilterParams): string => {
        const parts: string[] = [];

        // 排序
        if (params.sortBy !== DEFAULT_PARAMS.sortBy || params.sortOrder !== DEFAULT_PARAMS.sortOrder) {
            parts.push(`sort:${params.sortBy}-${params.sortOrder}`);
        }

        // 筛选
        if (params.status && params.status !== DEFAULT_PARAMS.status) {
            parts.push(`status:${params.status}`);
        }
        if (params.category && params.category !== DEFAULT_PARAMS.category) {
            parts.push(`category:${params.category}`);
        }
        if (params.type && params.type !== DEFAULT_PARAMS.type) {
            parts.push(`type:${params.type}`);
        }
        if (params.tags && params.tags.length > 0) {
            parts.push(`tags:${params.tags.join(',')}`);
        }
        if (params.author) {
            parts.push(`author:${params.author}`);
        }
        if (params.search) {
            parts.push(`search:"${params.search}"`);
        }
        if (params.issue) {
            parts.push(`issue:${params.issue}`);
        }

        // 日期范围
        if (params.dateFrom) {
            parts.push(`from:${params.dateFrom}`);
        }
        if (params.dateTo) {
            parts.push(`to:${params.dateTo}`);
        }
        if (params.dateField && params.dateField !== DEFAULT_PARAMS.dateField) {
            parts.push(`dateField:${params.dateField}`);
        }

        // 分页（通常不显示在命令中，除非用户明确设置）
        if (params.take && params.take !== DEFAULT_PARAMS.take) {
            parts.push(`limit:${params.take}`);
        }

        return parts.join(' ');
    }, []);

    /* ─────────── 解析命令字符串 ─────────── */
    const parseCommand = useCallback((command: string): FilterParams => {
        const params: FilterParams = { ...DEFAULT_PARAMS, skip: 0 }; // 重置分页

        // 简单的命令解析器
        const parts = command.match(/(\w+):"?([^"\s]+)"?/g) || [];

        parts.forEach(part => {
            const match = part.match(/(\w+):"?([^"]+)"?/);
            if (!match) return;

            const [, key, value] = match;

            switch (key) {
                case 'sort':
                    const [sortBy, sortOrder] = value.split('-');
                    if (sortBy) params.sortBy = sortBy as any;
                    if (sortOrder) params.sortOrder = sortOrder as any;
                    break;
                case 'status':
                    params.status = value;
                    break;
                case 'category':
                    params.category = value as any;
                    break;
                case 'type':
                    params.type = value as any;
                    break;
                case 'tags':
                    params.tags = value.split(',').filter(tag => tag.trim().length > 0);
                    break;
                case 'author':
                    params.author = value;
                    break;
                case 'search':
                    params.search = value;
                    break;
                case 'issue':
                    params.issue = value;
                    break;
                case 'from':
                    params.dateFrom = value;
                    break;
                case 'to':
                    params.dateTo = value;
                    break;
                case 'dateField':
                    params.dateField = value as any;
                    break;
                case 'limit':
                    params.take = parseInt(value);
                    break;
            }
        });

        return params;
    }, []);

    /* ─────────── 更新URL ─────────── */
    const updateUrl = useCallback((params: FilterParams) => {
        const query = qs.stringify(
            params,
            {
                addQueryPrefix: true,
                encodeValuesOnly: true,
                arrayFormat: 'comma',
                skipNulls: true
            }
        );
        router.replace(`/t_papers${query}`, { scroll: false });
    }, [router]);

    /* ─────────── 数据加载 ─────────── */
    const loadPapers = useCallback(async (params: FilterParams, isLoadMore = false) => {
        if (isLoadingRef.current) return;

        isLoadingRef.current = true;
        setLoading(true);

        try {
            // 使用扩展的API调用
            const query = qs.stringify(params, {
                addQueryPrefix: true,
                encodeValuesOnly: true,
                arrayFormat: 'comma',
                skipNulls: true
            });

            const res = await fetch(`/api/t_papers${query}`);
            if (!res.ok) throw new Error('API调用失败');

            const data = await res.json();
            const newPapers = data.papers as TPaper[];

            if (isLoadMore) {
                setPapers(prev => [...prev, ...newPapers]);
            } else {
                setPapers(newPapers);
            }

            setHasMore(newPapers.length === params.take);

        } catch (error) {
            console.error('加载论文失败:', error);
            setHasMore(false);
        } finally {
            isLoadingRef.current = false;
            setLoading(false);
        }
    }, []);

    /* ─────────── 重置并加载 ─────────── */
    const resetAndLoad = useCallback((params: FilterParams) => {
        const resetParams = { ...params, skip: 0 };
        setCurrentParams(resetParams);
        loadPapers(resetParams, false);
    }, [loadPapers]);

    /* ─────────── 加载更多 ─────────── */
    const loadMore = useCallback(() => {
        if (!hasMore || isLoadingRef.current) return;

        const nextParams = {
            ...currentParams,
            skip: papers.length
        };

        loadPapers(nextParams, true);
    }, [currentParams, papers.length, hasMore, loadPapers]);

    /* ─────────── 无限滚动设置 ─────────── */
    useEffect(() => {
        const sentinel = sentinelRef.current;
        if (!sentinel) return;

        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting) {
                    loadMore();
                }
            },
            { threshold: 0.1 }
        );

        observer.observe(sentinel);
        observerRef.current = observer;

        return () => {
            observer.disconnect();
        };
    }, [loadMore]);

    /* ─────────── 初始化 ─────────── */
    useEffect(() => {
        const params = parseUrlParams();
        setCurrentParams(params);
        setCommandValue(generateCommand(params));
        loadPapers(params, false);
    }, [parseUrlParams, generateCommand, loadPapers]);

    /* ─────────── 消息监听器 ─────────── */
    useEffect(() => {
        // 监听postMessage并转发给globalMessageManager
        const messageListener = (event: MessageEvent) => {
            console.log(`📬 收到消息:`, event.data);

            if (event.data?.type === 'TERMINAL_COMMAND') {
                console.log(`🚀 处理TERMINAL_COMMAND:`, event.data);

                // 按照最新的协议格式转发给globalMessageManager
                try {
                    // 构造符合新格式的消息
                    const message = {
                        id: event.data.id || `forwarded-${Date.now()}`,
                        type: 'TERMINAL_COMMAND' as const,
                        timestamp: event.data.timestamp || Date.now(),
                        windowId: event.data.windowId,
                        source: event.data.source || 'window',
                        payload: {
                            command: event.data.payload.command,
                            params: event.data.payload.params,
                            expectResponse: event.data.payload.expectResponse || true
                        }
                    };

                    // 直接分发到globalMessageManager
                    globalMessageManager.dispatchMessage(message);
                    console.log(`✅ 命令已转发给globalMessageManager`);
                } catch (error) {
                    console.error(`❌ 转发命令失败:`, error);

                    // 降级处理：使用旧方法
                    try {
                        globalMessageManager.sendTerminalCommand(
                            event.data.payload.command,
                            event.data.payload.params
                        );
                        console.log(`✅ 使用降级方法转发成功`);
                    } catch (fallbackError) {
                        console.error(`❌ 降级方法也失败:`, fallbackError);
                    }
                }
            } else if (event.data?.type === 'TERMINAL_COMMAND_RESPONSE') {
                const { success, command, result, error } = event.data.payload;
                console.log(`📨 Terminal命令响应:`, { success, command, result, error });

                if (success) {
                    console.log(`✅ 命令执行成功: ${command}`);
                    if (result) {
                        console.log(`📊 执行结果:`, result);
                    }
                } else {
                    console.error(`❌ 命令执行失败: ${command} - ${error}`);
                }
            } else if (event.data?.type === 'WINDOW_STATE') {
                console.log(`🪟 窗口状态更新:`, event.data.payload);
            }
        };

        window.addEventListener('message', messageListener);

        return () => {
            window.removeEventListener('message', messageListener);
        };
    }, []);

    /* ─────────── 命令处理 ─────────── */
    const handleCommandSubmit = useCallback((command: string) => {
        const params = parseCommand(command);
        setCurrentParams(params);
        updateUrl(params);
        resetAndLoad(params);
    }, [parseCommand, updateUrl, resetAndLoad]);

    const handleCommandChange = useCallback((command: string) => {
        setCommandValue(command);
    }, []);

    /* ─────────── 渲染 ─────────── */
    return (
        <div className="min-h-screen bg-slate-50 text-sm pb-16">
            {/* 表头 */}
            <div id="papers-header" className="sticky top-0 bg-gray-100 border-b border-gray-300 px-2 py-1 text-xs font-bold text-gray-700 overflow-hidden z-10">
                <div className="flex items-center whitespace-nowrap">
                    <div className="w-16 shrink-0 mr-2">图片</div>
                    <div className="w-80 shrink-0 px-2">标题</div>
                    <div className="w-32 shrink-0 px-2">作者</div>
                    <div className="w-24 shrink-0 px-2">分类</div>
                    <div className="w-20 shrink-0 px-2">类型</div>
                    <div className="w-28 shrink-0 px-2">发布时间</div>
                    <div className="w-16 shrink-0 px-2 text-right">阅读</div>
                    <div className="w-16 shrink-0 px-2 text-right">下载</div>
                    <div className="w-16 shrink-0 px-2 text-right">引用</div>
                    <div className="w-40 shrink-0 px-2">标签</div>
                </div>
            </div>

            {/* 论文列表 */}
            <div id="papers-list" className="px-2">
                {papers.length > 0 ? (
                    papers.map((paper, index) => (
                        <PaperRow
                            key={paper.id}
                            paper={paper}
                            index={index}
                        />
                    ))
                ) : !loading ? (
                    <div className="text-center py-8 text-gray-500">
                        暂无符合条件的论文
                    </div>
                ) : null}

                {/* 加载中骨架屏 */}
                {loading && (
                    <div className="space-y-px">
                        {Array.from({ length: 5 }).map((_, i) => (
                            <div key={i} className="py-1">
                                <div className="flex items-center whitespace-nowrap">
                                    <div className="w-16 h-16 shrink-0 mr-2"><Skeleton className="h-16 w-16" /></div>
                                    <div className="w-80 shrink-0 px-2"><Skeleton className="h-4 w-60" /></div>
                                    <div className="w-32 shrink-0 px-2"><Skeleton className="h-4 w-24" /></div>
                                    <div className="w-24 shrink-0 px-2"><Skeleton className="h-4 w-16" /></div>
                                    <div className="w-20 shrink-0 px-2"><Skeleton className="h-4 w-12" /></div>
                                    <div className="w-28 shrink-0 px-2"><Skeleton className="h-4 w-20" /></div>
                                    <div className="w-16 shrink-0 px-2"><Skeleton className="h-4 w-8" /></div>
                                    <div className="w-16 shrink-0 px-2"><Skeleton className="h-4 w-8" /></div>
                                    <div className="w-16 shrink-0 px-2"><Skeleton className="h-4 w-8" /></div>
                                    <div className="w-40 shrink-0 px-2"><Skeleton className="h-4 w-32" /></div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}

                {/* 无限滚动哨兵 */}
                <div ref={sentinelRef} className="h-4" />

                {/* 底部状态 */}
                {!hasMore && papers.length > 0 && (
                    <div className="text-center py-4 text-xs text-gray-400">
                        — 已显示全部 {papers.length} 篇论文 —
                    </div>
                )}
            </div>

            {/* 底部固定搜索框 */}
            <div id="papers-search-box" className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
                <CommandInput
                    value={commandValue}
                    onChange={handleCommandChange}
                    onSubmit={handleCommandSubmit}
                    placeholder="输入关键字搜索或选择筛选条件..."
                />
            </div>
        </div>
    );
} 