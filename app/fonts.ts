// app/fonts.ts
import localFont from "next/font/local";
import { Courier_Prime } from 'next/font/google';


export const monoFont = Courier_Prime({
  subsets: ['latin'],          // 根据需要选择 latin / latin-ext / cyrillic …
  weight: ['400', '700'],      // 想要哪些粗细就列哪些
  variable: '--font-mono',     // (可选) 生成 CSS 变量
  display: 'swap',             // 避免 FOIT
});

export const logoFont = localFont({
  src: [{ path: "./fonts/epicene-display-black.woff2", weight: "900", style: "normal" }],
  variable: "--font-logo",
  display: "swap",
});

/** UI/正文字体：Martina Plantijn */
export const proseFont = localFont({
  src: [
    // Light weight (300)
    { path: "./fonts/martina-plantijn-light.woff2", weight: "300", style: "normal" },
    { path: "./fonts/martina-plantijn-light-italic.woff2", weight: "300", style: "italic" },

    // Regular weight (400)
    { path: "./fonts/martina-plantijn-regular.woff2", weight: "400", style: "normal" },
    { path: "./fonts/martina-plantijn-italic.woff2", weight: "400", style: "italic" },

    // Medium weight (500)
    { path: "./fonts/martina-plantijn-medium.woff2", weight: "500", style: "normal" },
    { path: "./fonts/martina-plantijn-medium-italic.woff2", weight: "500", style: "italic" },

    // Bold weight (700)
    { path: "./fonts/martina-plantijn-bold.woff2", weight: "700", style: "normal" },
    { path: "./fonts/martina-plantijn-bold-italic.woff2", weight: "700", style: "italic" },

    // Black weight (900)
    { path: "./fonts/martina-plantijn-black.woff2", weight: "900", style: "normal" },
    { path: "./fonts/martina-plantijn-black-italic.woff2", weight: "900", style: "italic" },
  ],
  variable: "--font-prose",
  display: "swap",
});

/** 衬线体：Fraunces */
export const serifFont = localFont({
  src: [
    { path: "./fonts/Fraunces.woff2", weight: "400", style: "normal" },
    { path: "./fonts/Fraunces-Italic.woff2", weight: "400", style: "italic" },
  ],
  variable: "--font-serif",
  display: "swap",
});

/** 等宽字体：JetBrains Mono */
// export const monoFont = localFont({
//   src: [
//     // Thin (100)
//     { path: "./fonts/JetBrainsMono-Thin.woff2", weight: "100", style: "normal" },
//     { path: "./fonts/JetBrainsMono-ThinItalic.woff2", weight: "100", style: "italic" },

//     // Extra Light (200)
//     { path: "./fonts/JetBrainsMono-ExtraLight.woff2", weight: "200", style: "normal" },
//     { path: "./fonts/JetBrainsMono-ExtraLightItalic.woff2", weight: "200", style: "italic" },

//     // Light (300)
//     { path: "./fonts/JetBrainsMono-Light.woff2", weight: "300", style: "normal" },
//     { path: "./fonts/JetBrainsMono-LightItalic.woff2", weight: "300", style: "italic" },

//     // Regular (400)
//     { path: "./fonts/JetBrainsMono-Regular.woff2", weight: "400", style: "normal" },
//     { path: "./fonts/JetBrainsMono-Italic.woff2", weight: "400", style: "italic" },

//     // Medium (500)
//     { path: "./fonts/JetBrainsMono-Medium.woff2", weight: "500", style: "normal" },
//     { path: "./fonts/JetBrainsMono-MediumItalic.woff2", weight: "500", style: "italic" },

//     // Semi Bold (600)
//     { path: "./fonts/JetBrainsMono-SemiBold.woff2", weight: "600", style: "normal" },
//     { path: "./fonts/JetBrainsMono-SemiBoldItalic.woff2", weight: "600", style: "italic" },

//     // Bold (700)
//     { path: "./fonts/JetBrainsMono-Bold.woff2", weight: "700", style: "normal" },
//     { path: "./fonts/JetBrainsMono-BoldItalic.woff2", weight: "700", style: "italic" },

//     // Extra Bold (800)
//     { path: "./fonts/JetBrainsMono-ExtraBold.woff2", weight: "800", style: "normal" },
//     { path: "./fonts/JetBrainsMono-ExtraBoldItalic.woff2", weight: "800", style: "italic" },
//   ],
//   variable: "--font-mono",
//   display: "swap",
// });

// /** 代码字体：也使用Martina Plantijn */
// export const codeFont = localFont({
//   src: [
//     // Light weight (300)
//     { path: "./fonts/martina-plantijn-light.woff2", weight: "300", style: "normal" },
//     { path: "./fonts/martina-plantijn-light-italic.woff2", weight: "300", style: "italic" },

//     // Regular weight (400)
//     { path: "./fonts/martina-plantijn-regular.woff2", weight: "400", style: "normal" },
//     { path: "./fonts/martina-plantijn-italic.woff2", weight: "400", style: "italic" },

//     // Medium weight (500)
//     { path: "./fonts/martina-plantijn-medium.woff2", weight: "500", style: "normal" },
//     { path: "./fonts/martina-plantijn-medium-italic.woff2", weight: "500", style: "italic" },

//     // Bold weight (700)
//     { path: "./fonts/martina-plantijn-bold.woff2", weight: "700", style: "normal" },
//     { path: "./fonts/martina-plantijn-bold-italic.woff2", weight: "700", style: "italic" },

//     // Black weight (900)
//     { path: "./fonts/martina-plantijn-black.woff2", weight: "900", style: "normal" },
//     { path: "./fonts/martina-plantijn-black-italic.woff2", weight: "900", style: "italic" },
//   ],
//   variable: "--font-code",
//   display: "swap",
// });
