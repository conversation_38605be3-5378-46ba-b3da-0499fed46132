import { MetadataRoute } from "next";
import { BlogPost } from "@/app/(site)/news/utils";
import { getBlogPosts } from "@/app/(site)/news/utils/server";

// 根据环境变量设置baseUrl
export const baseUrl =
  process.env.NEXT_PUBLIC_SITE_URL ||
  (process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : "http://localhost:3000");

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // 定义基本路由
  const routes = [
    {
      url: `${baseUrl}`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/news`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
    },
  ];

  // 获取博客文章并添加到站点地图
  try {
    const posts = await getBlogPosts();
    const blogRoutes = posts.map(post => ({
      url: `${baseUrl}/news/${post.slug}`,
      lastModified: new Date(post.metadata.publishedAt),
    }));

    return [...routes, ...blogRoutes];
  } catch (error) {
    console.error("Error generating sitemap:", error);
    return routes;
  }
}
