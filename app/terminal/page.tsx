'use client';
import dynamic from 'next/dynamic';
import '@xterm/xterm/css/xterm.css';
import FloatingWindowManager from '@/components/FloatingWindowManager';
import { TerminalMenuManager } from '@/components/TerminalMenuManager';

// 关闭 SSR，防止构建阶段加载 xterm
const XTermClient = dynamic(() => import('@/components/XTermClient'), {
    ssr: false,
});

// 动态加载骰子模拟器，避免SSR问题
const DiceSimulation = dynamic(() => import('@/components/DiceSimulation'), {
    ssr: false,
    loading: () => null, // 静默加载
});

import { useEffect, useRef, useCallback, useState } from 'react';

export default function TerminalPage() {
    const terminalInstanceRef = useRef<any>(null);
    const executeCommandRef = useRef<((command: string, source?: 'user' | 'window' | 'menu') => Promise<any>) | null>(null);
    const keyboardUnsubscribeRef = useRef<(() => void) | null>(null);
    const pendingKeyboardCallbackRef = useRef<((key: string) => void) | null>(null);
    const [isTerminalReady, setIsTerminalReady] = useState(false);

    useEffect(() => {
        // 在组件挂载时设置全局过度滚动防护
        const originalBodyOverscroll = document.body.style.overscrollBehavior;
        const originalHtmlOverscroll = document.documentElement.style.overscrollBehavior;
        const originalBodyOverflow = document.body.style.overflow;

        document.body.style.overscrollBehavior = 'none';
        document.documentElement.style.overscrollBehavior = 'none';
        document.body.style.overflow = 'hidden';

        return () => {
            // 组件卸载时恢复原始样式
            document.body.style.overscrollBehavior = originalBodyOverscroll;
            document.documentElement.style.overscrollBehavior = originalHtmlOverscroll;
            document.body.style.overflow = originalBodyOverflow;
        };
    }, []);

    // 监听来自菜单的命令事件
    useEffect(() => {
        const handleMenuCommand = (event: CustomEvent) => {
            const { command, source } = event.detail;

            if (executeCommandRef.current) {
                // 🚀 智能源识别：菜单命令统一标识为'menu'，其他保持原有逻辑
                let commandSource: 'user' | 'window' | 'menu' = 'menu';

                // 如果明确标识为window源，则保持
                if (source === 'window') {
                    commandSource = 'window';
                } else if (source && source.startsWith('menu:')) {
                    // 来自菜单的命令，确保标识为menu
                    commandSource = 'menu';
                }

                executeCommandRef.current(command, commandSource);
            }
        };

        document.addEventListener('terminal-menu-command', handleMenuCommand as EventListener);

        return () => {
            document.removeEventListener('terminal-menu-command', handleMenuCommand as EventListener);
        };
    }, []);

    // 🆕 监听来自浮窗的postMessage（包括下载请求）
    useEffect(() => {
        const handlePostMessage = (event: MessageEvent) => {
            // 验证消息格式
            if (!event.data || typeof event.data !== 'object') return;

            const { type } = event.data;

            // 处理下载文件请求
            if (type === 'DOWNLOAD_FILE') {
                try {
                    const { filename, data, mimeType } = event.data;
                    if (filename && data) {
                        // 创建下载链接
                        const a = document.createElement('a');
                        a.href = data;
                        a.download = filename;
                        a.style.display = 'none';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    }
                } catch (error) {
                    // 静默处理下载失败
                }
                return;
            }

            // 处理其他类型的消息...
        };

        window.addEventListener('message', handlePostMessage);

        return () => {
            window.removeEventListener('message', handlePostMessage);
        };
    }, []);

    // 处理命令执行的回调
    const handleCommandExecute = useCallback((command: string, source?: string) => {
        // 尝试多种方式执行命令
        if (executeCommandRef.current) {
            // 🚀 智能源识别：菜单命令默认为'menu'，支持其他源类型
            let commandSource: 'user' | 'window' | 'menu' = 'menu';

            if (source === 'window') {
                commandSource = 'window';
            } else if (source === 'user') {
                commandSource = 'user';
            }

            executeCommandRef.current(command, commandSource);
        } else if (terminalInstanceRef.current && typeof terminalInstanceRef.current.write === 'function') {
            // 直接写入terminal
            terminalInstanceRef.current.write(`${command}\r\n`);
        } else {
            // 广播事件，让XTermClient处理
            const event = new CustomEvent('terminal-execute-command', {
                detail: { command, source }
            });
            document.dispatchEvent(event);
        }
    }, []);

    // 🎲 处理骰子模拟器键盘回调注册
    const handleDiceKeyboardCallback = useCallback((callback: (key: string) => void) => {
        // console.log('[TerminalPage] 骰子组件请求注册键盘回调');

        if (terminalInstanceRef.current && typeof terminalInstanceRef.current.addKeyboardCallback === 'function') {
            // Terminal已准备好，立即注册
            // console.log('[TerminalPage] Terminal已准备好，立即注册键盘回调');
            const unsubscribe = terminalInstanceRef.current.addKeyboardCallback(callback);
            keyboardUnsubscribeRef.current = unsubscribe;
            return unsubscribe;
        } else {
            // Terminal还没准备好，保存回调等待后续注册
            // console.log('[TerminalPage] Terminal未准备好，保存回调等待注册');
            pendingKeyboardCallbackRef.current = callback;
            return undefined;
        }
    }, []);

    // 从XTermClient获取terminal实例和执行函数的引用
    const handleTerminalReady = useCallback((terminalInstance: any, executeCommand: (command: string, source?: 'user' | 'window' | 'menu') => Promise<any>) => {
        // console.log('[TerminalPage] Terminal实例准备完成');
        terminalInstanceRef.current = terminalInstance;
        executeCommandRef.current = executeCommand;

        // 设置全局引用，供菜单使用
        if (typeof window !== 'undefined') {
            (window as any).terminalInstance = terminalInstance;
            (window as any).executeCommand = executeCommand;
        }

        // 如果有等待的键盘回调，现在注册它
        if (pendingKeyboardCallbackRef.current && typeof terminalInstance.addKeyboardCallback === 'function') {
            // console.log('[TerminalPage] 注册等待中的键盘回调');
            const unsubscribe = terminalInstance.addKeyboardCallback(pendingKeyboardCallbackRef.current);
            keyboardUnsubscribeRef.current = unsubscribe;
            pendingKeyboardCallbackRef.current = null; // 清除等待的回调
        }

        // 设置terminal准备完成状态，触发骰子组件加载
        setIsTerminalReady(true);
        // console.log('[TerminalPage] Terminal完全准备完成，可以加载骰子组件');
    }, []);

    // 清理函数
    useEffect(() => {
        return () => {
            if (keyboardUnsubscribeRef.current) {
                keyboardUnsubscribeRef.current();
                keyboardUnsubscribeRef.current = null;
            }
        };
    }, []);

    return (
        <main className="h-screen w-screen overflow-hidden lg:pl-2 md:pl-1 sm:pl-0 relative overscroll-none">
            {/* Terminal组件 - 优先加载，确保快速可用 */}
            <XTermClient onTerminalReady={handleTerminalReady} />

            {/* 🎲 骰子模拟器 - 等待Terminal完全准备后再懒加载 */}
            {isTerminalReady && (
                <DiceSimulation
                    className="dice-background"
                    enableTextures={true}
                    autoStart={true}
                    onKeyboardCallbackReady={handleDiceKeyboardCallback}
                />
            )}

            {/* 浮窗管理器 */}
            <FloatingWindowManager />

            {/* 菜单管理器 */}
            <TerminalMenuManager
                onCommandExecute={handleCommandExecute}
                terminalInstance={terminalInstanceRef.current}
            />

            <style jsx>{`
                @keyframes gradientShift {
                    0% { background-position: 0% 50%; }
                    50% { background-position: 100% 50%; }
                    100% { background-position: 0% 50%; }
                }

                .dice-background {
                    z-index: -10 !important;
                }
            `}</style>
        </main>
    );
}