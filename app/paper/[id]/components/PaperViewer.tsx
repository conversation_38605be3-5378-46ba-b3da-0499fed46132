"use client";

import { useRef, useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import DocumentRenderer from "./DocumentRenderer";
import Toolbar from "./Toolbar";
import CommentSystem from "./CommentSystem";
import { useDocumentLoader } from "../hooks/useDocumentLoader";
import { useZoomControl } from "../hooks/useZoomControl";
import { PaperViewerProps } from "../types";

import "../page.css";

export default function PaperViewer({ paperId }: PaperViewerProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const previewContainerRef = useRef<HTMLElement | null>(null);

  // State
  const [showComments, setShowComments] = useState(true);
  const [showHighlights, setShowHighlights] = useState(true);
  const [targetCommentId, setTargetCommentId] = useState<string | null>(null);

  // Hooks
  const htmlUrl = `/api/papers/${paperId}/html`;

  const {
    isLoading,
    isContentVisible,
    isToolbarVisible,
    error,
    retry
  } = useDocumentLoader({
    htmlUrl,
    containerRef,
    onContentLoaded: () => {
      // 获取预览容器引用
      if (containerRef.current && !previewContainerRef.current) {
        const previewContainer = containerRef.current.closest(".paged-preview") as HTMLElement;
        previewContainerRef.current = previewContainer;

      }
    }
  });

  const {
    scale,
    zoomIn,
    zoomOut,
    resetZoom
  } = useZoomControl({
    containerRef,
    wrapperRef,
    previewContainerRef,
    isLoading
  });



  // 处理URL锚点和查询参数
  useEffect(() => {
    const commentIdFromQuery = searchParams.get('commentId');
    if (commentIdFromQuery) {
      setTargetCommentId(commentIdFromQuery);
      setShowComments(true);
      setShowHighlights(true);
      return;
    }

    const hash = window.location.hash;
    if (hash.startsWith("#comment-")) {
      const commentId = hash.replace("#comment-", "");
      setTargetCommentId(commentId);
      setShowComments(true);
      setShowHighlights(true);
    }
  }, [searchParams]);

  // 初始化预览容器引用
  useEffect(() => {
    if (containerRef.current) {
      const previewContainer = containerRef.current.closest(".paged-preview") as HTMLElement;
      previewContainerRef.current = previewContainer;

    }
  }, []);

  // 事件处理器
  const handleGoBack = () => {
    router.push("/papers");
  };

  const handlePrint = () => {
    window.print();
  };

  const toggleAnnotations = () => {
    setShowComments(prev => !prev);
    setShowHighlights(prev => !prev);
  };

  const handleCommentFocused = () => {
    setTargetCommentId(null);
  };

  // 错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="error-message p-8 text-red-500 bg-red-50 rounded-lg border border-red-200 max-w-md text-center">
          <h3 className="text-lg font-semibold mb-2">Load paper failed</h3>
          <p className="text-sm text-red-600 mb-4">{error}</p>
          <button
            onClick={retry}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            Reload
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* 加载状态 */}
      {isLoading && (
        <div className="loading-overlay">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-500 mb-3"></div>
            <div className="text-lg">Loading paper preview...</div>
            <div className="text-sm text-gray-500 mt-1">Loading paged HTML content</div>
          </div>
        </div>
      )}

      {/* 文档容器 */}
      <div
        ref={wrapperRef}
        className={`relative w-full flex justify-center paper-container transition-all duration-700 ease-out transform ${isContentVisible
            ? 'opacity-100 translate-y-0 scale-100'
            : 'opacity-0 translate-y-4 scale-98'
          }`}
      >
        {/* 文档渲染器 */}
        <DocumentRenderer
          containerRef={containerRef}
          scale={scale}
        />

        {/* 评论系统 */}
        {paperId && (
          <CommentSystem
            paperId={paperId}
            containerRef={containerRef}
            scale={scale}
            showComments={showComments}
            showHighlights={showHighlights}
            targetCommentId={targetCommentId}
            onCommentFocused={handleCommentFocused}
          />
        )}
      </div>

      {/* 工具栏 */}
      <Toolbar
        scale={scale}
        showComments={showComments}
        showHighlights={showHighlights}
        isVisible={isToolbarVisible}
        onZoomIn={zoomIn}
        onZoomOut={zoomOut}
        onResetZoom={resetZoom}
        onToggleAnnotations={toggleAnnotations}
        onGoBack={handleGoBack}
        onPrint={handlePrint}
      />
    </>
  );
}
