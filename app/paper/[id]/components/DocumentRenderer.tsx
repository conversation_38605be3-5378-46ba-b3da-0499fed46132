"use client";

import { forwardRef } from "react";

interface DocumentRendererProps {
  containerRef: React.RefObject<HTMLDivElement | null>;
  scale: number;
}

const DocumentRenderer = forwardRef<HTMLDivElement, DocumentRendererProps>(
  ({ containerRef, scale }, ref) => {
    return (
      <div
        ref={containerRef}
        className="pages-container"
        style={{
          transform: `scale(${scale})`,
          transformOrigin: "top center", // 默认居中，会被JavaScript动态调整
        }}
        data-scale={scale} // 添加数据属性便于调试
      />
    );
  }
);

DocumentRenderer.displayName = "DocumentRenderer";

export default DocumentRenderer;
