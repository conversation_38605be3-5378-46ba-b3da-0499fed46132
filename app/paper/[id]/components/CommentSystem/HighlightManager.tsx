"use client";

import { useEffect, useRef } from "react";
import { ExtendedCommentData } from "../../types";

interface HighlightManagerProps {
  comments: ExtendedCommentData[];
  containerRef: React.RefObject<HTMLDivElement | null>;
  scale: number;
  showHighlights: boolean;
  visibleHighlights: Set<string>;
  activeHighlightId: string | null;
  isTextSelecting: boolean;
  onHighlightHover: (commentId: string) => void;
  onHighlightLeave: (commentId: string) => void;
}

export default function HighlightManager({
  comments,
  containerRef,
  scale,
  showHighlights,
  visibleHighlights,
  activeHighlightId,
  isTextSelecting,
  onHighlightHover,
  onHighlightLeave,
}: HighlightManagerProps) {
  const highlightsRestored = useRef(false);

  // 恢复高亮 - 使用与原始代码相同的逻辑
  const restoreHighlight = (comment: ExtendedCommentData) => {
    if (!containerRef.current || !showHighlights) return;

    const { highlightId, pageIndex, x, y, width, height } = comment;
    const actualPageIndex = pageIndex === null || pageIndex === undefined ? 0 : pageIndex;

    if (
      !highlightId ||
      x === undefined ||
      y === undefined ||
      width === undefined ||
      height === undefined
    ) {
      return;
    }

    const pages = containerRef.current.querySelectorAll(".page");
    if (!pages || pages.length === 0 || actualPageIndex >= pages.length) {
      return;
    }

    const page = pages[actualPageIndex];
    if (!page) return;

    // 检查是否已存在此高亮
    const existingHighlight = document.querySelector(`[data-highlight-id="${highlightId}"]`);
    if (existingHighlight) return;

    // 创建高亮层
    const highlight = document.createElement("div");
    highlight.className = "highlight-overlay";
    if (!showHighlights) {
      highlight.classList.add("hidden");
    }
    highlight.dataset.highlightId = highlightId;
    highlight.dataset.pageIndex = String(actualPageIndex);
    highlight.dataset.commentId = comment.id;

    // 设置高亮层的位置和尺寸

    highlight.style.position = "absolute";
    highlight.style.top = `${y}px`;
    highlight.style.left = `${x}px`;
    highlight.style.width = `${width}px`;
    highlight.style.height = `${height}px`;

    // 设置交互属性
    highlight.style.pointerEvents = isTextSelecting ? 'none' : 'auto';

    // 设置初始状态和动画类
    const isHighlightVisible = visibleHighlights.has(highlightId);
    if (isHighlightVisible) {
      highlight.classList.add('visible', 'animate-in');
    } else {
      highlight.classList.add('hidden');
    }

    // 鼠标事件
    highlight.addEventListener("mouseenter", () => {
      highlight.classList.add("active");
      if (comment.id) {
        onHighlightHover(comment.id);
      }
    });

    highlight.addEventListener("mouseleave", () => {
      highlight.classList.remove("active");
      if (comment.id) {
        onHighlightLeave(comment.id);
      }
    });

    // 将高亮层添加到页面中
    page.appendChild(highlight);
  };

  // 恢复所有高亮
  const restoreAllHighlights = () => {
    if (!containerRef.current) return;

    setTimeout(() => {
      comments.forEach((comment) => {
        const actualPageIndex = comment.pageIndex === null || comment.pageIndex === undefined ? 0 : comment.pageIndex;

        if (
          comment.highlightId &&
          comment.x !== undefined &&
          comment.y !== undefined &&
          comment.width !== undefined &&
          comment.height !== undefined
        ) {
          // 检查是否已存在此高亮，避免重复创建
          const existingHighlight = document.querySelector(`[data-highlight-id="${comment.highlightId}"]`);
          if (!existingHighlight) {
            restoreHighlight(comment);
          }
        }
      });

      highlightsRestored.current = true;
    }, 500);
  };

  // 更新高亮显示状态
  useEffect(() => {
    const allHighlights = document.querySelectorAll('[data-highlight-id]') as NodeListOf<HTMLElement>;

    allHighlights.forEach(highlight => {
      const highlightId = highlight.dataset.highlightId;
      if (highlightId) {
        const isVisible = visibleHighlights.has(highlightId);
        const wasVisible = highlight.classList.contains('visible');

        if (isVisible && !wasVisible) {
          // 显示动画
          highlight.classList.remove('hidden', 'animate-out');
          highlight.classList.add('visible', 'animate-in');
        } else if (!isVisible && wasVisible) {
          // 隐藏动画
          highlight.classList.remove('visible', 'animate-in');
          highlight.classList.add('hidden', 'animate-out');
        }
      }
    });
  }, [visibleHighlights]);

  // 更新激活状态
  useEffect(() => {
    const allHighlights = document.querySelectorAll('.highlight-overlay');
    
    allHighlights.forEach(highlight => {
      const highlightId = highlight.getAttribute('data-highlight-id');
      if (highlightId === activeHighlightId) {
        highlight.classList.add('active');
      } else {
        highlight.classList.remove('active');
      }
    });
  }, [activeHighlightId]);

  // 更新文本选择模式下的交互状态
  useEffect(() => {
    const allHighlights = document.querySelectorAll('.highlight-overlay') as NodeListOf<HTMLElement>;
    
    allHighlights.forEach(highlight => {
      if (isTextSelecting) {
        highlight.style.pointerEvents = 'none';
        highlight.style.opacity = '0.3';
        highlight.setAttribute('data-selection-mode', 'true');
      } else {
        highlight.style.pointerEvents = 'auto';
        const highlightId = highlight.dataset.highlightId;
        const isVisible = highlightId && visibleHighlights.has(highlightId);
        highlight.style.opacity = isVisible ? '1' : '0';
        highlight.removeAttribute('data-selection-mode');
      }
    });
  }, [isTextSelecting, visibleHighlights]);

  // 监听评论变化，恢复高亮
  useEffect(() => {
    let observer: MutationObserver | null = null;

    const setupHighlightRestoration = () => {
      if (!containerRef.current) return;

      // 检查页面元素是否已加载
      const pages = containerRef.current.querySelectorAll(".page");
      if (pages.length > 0) {
        restoreAllHighlights();
        return;
      }

      // 如果页面还没加载，设置观察器等待
      observer = new MutationObserver(mutations => {
        for (const mutation of mutations) {
          if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
            const hasPageElement = (containerRef.current?.querySelectorAll(".page").length ?? 0) > 0;
            if (hasPageElement) {
              restoreAllHighlights();
              break;
            }
          }
        }
      });

      observer.observe(containerRef.current, {
        childList: true,
        subtree: true,
      });
    };

    if (comments.length > 0 && showHighlights) {
      setupHighlightRestoration();
    }

    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  }, [comments, showHighlights, containerRef]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (containerRef.current) {
        const highlights = containerRef.current.querySelectorAll(".highlight-overlay");
        highlights.forEach(highlight => highlight.remove());
      }
    };
  }, [containerRef]);

  // 重置高亮恢复状态当显示状态改变时
  useEffect(() => {
    if (!showHighlights) {
      highlightsRestored.current = false;
      // 隐藏所有高亮 - 使用动画
      const allHighlights = document.querySelectorAll('.highlight-overlay') as NodeListOf<HTMLElement>;
      allHighlights.forEach((highlight, index) => {
        setTimeout(() => {
          highlight.classList.remove('visible', 'animate-in');
          highlight.classList.add('hidden', 'animate-out');
        }, index * 50); // 错开动画时间
      });
    }
  }, [showHighlights]);

  return null; // 这个组件不渲染任何内容，只管理高亮
}
