"use client";

import { InputPosition } from "../../types";
import { buildCallbackUrl } from "../../utils";
import { openAccountWindow, isInFloatingWindow } from "../../utils/floatingWindowCommunication";

interface TextSelectionProps {
  containerRef: React.RefObject<HTMLDivElement | null>;
  scale: number;
  showInput: boolean;
  inputPos: InputPosition | null;
  inputValue: string;
  isSubmitting: boolean;
  needLogin: boolean;
  temporaryHighlight: string | null;
  paperId: string;
  onInputChange: (value: string) => void;
  onSubmit: () => void;
  onCancel: () => void;
}

export default function TextSelection({
  containerRef,
  scale,
  showInput,
  inputPos,
  inputValue,
  isSubmitting,
  needLogin,
  temporaryHighlight,
  paperId,
  onInputChange,
  onSubmit,
  onCancel,
}: TextSelectionProps) {
  if (!showInput || !inputPos) return null;

  // 处理登录按钮点击
  const handleLoginClick = async () => {
    if (isInFloatingWindow()) {
      // 在浮窗环境中，使用浮窗通信打开账户窗口
      try {
        const success = await openAccountWindow();
        if (success) {
          console.log('[TextSelection] 已打开账户登录窗口');
        } else {
          console.warn('[TextSelection] 打开账户登录窗口失败，回退到直接跳转');
          // 回退到直接跳转
          window.open(buildCallbackUrl(paperId), '_blank');
        }
      } catch (error) {
        console.error('[TextSelection] 打开账户登录窗口时出错:', error);
        // 回退到直接跳转
        window.open(buildCallbackUrl(paperId), '_blank');
      }
    } else {
      // 不在浮窗环境中，直接跳转
      window.location.href = buildCallbackUrl(paperId);
    }
  };

  // 计算输入框位置
  const topPosition = inputPos.pageTop && inputPos.y
    ? (inputPos.pageTop + inputPos.y) * scale
    : 0;

  return (
    <div
      className="selection-comment-input absolute pointer-events-auto z-50"
      style={{
        right: "20px",
        top: `${topPosition}px`,
        transform: `scale(${scale})`,
        transformOrigin: "top right",
      }}
      onClick={e => e.stopPropagation()}
    >
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden min-w-[280px]">
        {/* 输入区域 */}
        <textarea
          value={inputValue}
          onChange={e => onInputChange(e.target.value)}
          placeholder={needLogin ? "请先登录后发表评论" : "添加评论..."}
          className="w-full p-3 focus:outline-none resize-none text-sm"
          rows={3}
          disabled={isSubmitting || needLogin}
          autoFocus={!needLogin}
        />

        {/* 按钮区域 */}
        <div className="flex justify-end bg-gray-50 px-3 py-2 space-x-2">
          <button
            onClick={onCancel}
            className="text-xs text-gray-500 px-3 py-1 rounded hover:bg-gray-200"
            disabled={isSubmitting}
          >
            取消
          </button>
          
          {needLogin ? (
            <button
              onClick={handleLoginClick}
              className="text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
            >
              去登录
            </button>
          ) : (
            <button
              onClick={onSubmit}
              className="text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 disabled:opacity-50"
              disabled={!inputValue.trim() || isSubmitting}
            >
              {isSubmitting ? "提交中..." : "发表"}
            </button>
          )}
        </div>
      </div>

      {/* 临时高亮指示器 */}
      {temporaryHighlight && (
        <div className="absolute -left-2 top-1/2 transform -translate-y-1/2">
          <div className="w-1 h-8 bg-blue-500 rounded-full opacity-60"></div>
        </div>
      )}
    </div>
  );
}
