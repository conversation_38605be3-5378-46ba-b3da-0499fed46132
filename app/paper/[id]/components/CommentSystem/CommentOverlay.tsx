"use client";

import { useRef, useEffect } from "react";
import { CommentBox } from "./CommentBox";
import { ExtendedCommentData, CommentEventHandlers, User } from "../../types";
import { getCommentUserColor } from "../../hooks/useUserColors";

interface CommentOverlayProps {
  comments: ExtendedCommentData[];
  scale: number;
  expandedComments: Record<string, boolean>;
  visibleComments: Set<string>;
  activeHighlightId: string | null;
  currentlyReplyingId: string | null;
  eventHandlers: CommentEventHandlers;
  user: User | null;
  paperId: string;
  onCommentHover: (commentId: string) => void;
  onCommentLeave: (commentId: string) => void;
  onCloseAllComments: (exceptId?: string) => void;
  isCommentReplying: (commentId: string) => boolean;
}

export default function CommentOverlay({
  comments,
  scale,
  expandedComments,
  visibleComments,
  activeHighlightId,
  currentlyReplyingId,
  eventHandlers,
  user,
  paperId,
  onCommentHover,
  onCommentLeave,
  onCloseAllComments,
  isCommentReplying,
}: CommentOverlayProps) {
  const commentRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  // 获取评论圆点颜色
  const getCommentDotColor = (commentUser: User) => {
    return getCommentUserColor(commentUser);
  };

  // 圆点动画管理
  useEffect(() => {
    const allDots = document.querySelectorAll('.comment-dot') as NodeListOf<HTMLElement>;

    allDots.forEach((dot, index) => {
      const commentId = dot.closest('.comment-container')?.getAttribute('data-comment-id');
      if (commentId) {
        const isVisible = visibleComments.has(commentId);
        const wasVisible = dot.classList.contains('visible');

        if (isVisible && !wasVisible) {
          // 显示动画 - 错开时间
          setTimeout(() => {
            dot.classList.remove('hidden', 'animate-out');
            dot.classList.add('visible', 'animate-in');
          }, index * 100);
        } else if (!isVisible && wasVisible) {
          // 隐藏动画
          dot.classList.remove('visible', 'animate-in');
          dot.classList.add('hidden', 'animate-out');
        }
      }
    });
  }, [visibleComments]);

  // 全局点击事件处理
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      const commentElements = document.querySelectorAll(".comment-dot, .comment-box");
      const highlightElements = document.querySelectorAll(".highlight-overlay");
      let clickedOnCommentOrHighlight = false;

      commentElements.forEach(el => {
        if (el.contains(e.target as Node)) {
          clickedOnCommentOrHighlight = true;
        }
      });

      highlightElements.forEach(el => {
        if (el.contains(e.target as Node)) {
          clickedOnCommentOrHighlight = true;
        }
      });

      if (!clickedOnCommentOrHighlight) {
        onCloseAllComments();
      }
    };

    document.addEventListener("click", handleGlobalClick);
    return () => document.removeEventListener("click", handleGlobalClick);
  }, [onCloseAllComments]);

  return (
    <>
      {comments.map(comment => {
        // 如果是回复则不单独显示
        if (comment.parentId) return null;

        const commentHighlightId = comment.highlightId;
        if (!commentHighlightId) return null;

        const isExpanded = !!expandedComments[comment.id];
        const topPosition = comment.pageTop && comment.yPosition
          ? (comment.pageTop + comment.yPosition) * scale
          : 0;
        const isCommentVisible = visibleComments.has(comment.id);

        return (
          <div
            key={comment.id}
            ref={el => {
              if (el) commentRefs.current.set(comment.id, el);
              return undefined;
            }}
            className={`comment-container absolute pointer-events-auto transition-all duration-500 ease-out transform ${
              isCommentVisible
                ? 'opacity-100 translate-x-0 scale-100'
                : 'opacity-0 translate-x-4 scale-95'
            }`}
            style={{
              right: comment.groupIndex ? `${20 + comment.groupIndex * 20}px` : "20px",
              top: `${topPosition}px`,
              zIndex: isExpanded ? 60 : 50,
              transform: `scale(${scale}) ${isCommentVisible ? 'translateX(0)' : 'translateX(16px)'}`,
              transformOrigin: "top right",
            }}
            data-comment-id={comment.id}
            data-group-id={comment.groupId}
            data-group-index={comment.groupIndex}
            data-group-total={comment.groupTotal}
            onMouseEnter={() => {
              commentHighlightId && eventHandlers.onHighlight(commentHighlightId);
              onCommentHover(comment.id);
            }}
            onMouseLeave={() => {
              eventHandlers.onRemoveHighlight();
              onCommentLeave(comment.id);
            }}
          >
            {/* 评论框主体 */}
            <div
              className={`comment-box-wrapper ${isExpanded ? "expanded" : ""}`}
            >
              <div
                className={`comment-box ${activeHighlightId === commentHighlightId ? "active" : ""}`}
                onClick={e => e.stopPropagation()}
                style={{
                  fontSize: `${14 / scale}px`,
                }}
              >
                <CommentBox
                  comment={comment}
                  eventHandlers={eventHandlers}
                  scale={scale}
                  isReplyingTo={currentlyReplyingId}
                  isCommentReplying={isCommentReplying}
                  paperId={paperId}
                  currentUser={user}
                />
              </div>
            </div>

            {/* 评论圆点 */}
            <div
              className={`comment-dot ${isExpanded ? "expanded" : ""} ${
                visibleComments.has(comment.id) ? "visible animate-in" : "hidden"
              }`}
              style={{
                backgroundColor: getCommentDotColor(comment.user),
              }}
              onClick={e => {
                e.stopPropagation();
                e.preventDefault();

                if (expandedComments[comment.id]) {
                  onCloseAllComments();
                } else {
                  onCloseAllComments(comment.id);
                }

                // 切换高亮状态
                if (commentHighlightId) {
                  const highlight = document.querySelector(
                    `[data-highlight-id="${commentHighlightId}"]`
                  );
                  if (highlight) {
                    if (expandedComments[comment.id]) {
                      highlight.classList.remove("active");
                    } else {
                      highlight.classList.add("active");
                    }
                  }
                }
              }}
            >
              {/* 分组指示器 */}
              {comment.groupTotal && comment.groupTotal > 1 && (
                <div className="comment-group-indicator">
                  {comment.groupIndex! + 1}/{comment.groupTotal}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </>
  );
}
