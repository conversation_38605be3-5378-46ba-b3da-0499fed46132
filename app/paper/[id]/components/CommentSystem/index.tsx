"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { useSupabase } from "@/components/SupabaseProvider";

import CommentOverlay from "./CommentOverlay";
import TextSelection from "./TextSelection";
import HighlightManager from "./HighlightManager";
import { useCommentSystem } from "../../hooks/useCommentSystem";
import { useTextSelection } from "../../hooks/useTextSelection";
import { CommentSystemProps, CommentEventHandlers } from "../../types";
import { adaptSupabaseUser } from "../../utils";

export default function CommentSystem({
  paperId,
  containerRef,
  scale,
  showComments,
  showHighlights,
  targetCommentId,
  onCommentFocused,
}: CommentSystemProps) {
  const { user: supabaseUser, isLoading: isAuthLoading } = useSupabase();
  const user = adaptSupabaseUser(supabaseUser);


  
  // 评论系统状态和方法
  const {
    comments,
    isLoading,
    error,
    expandedComments,
    visibleComments,
    visibleHighlights,
    activeHighlightId,
    currentlyReplyingId,
    replyingComments,
    handleReply,
    handleDeleteComment,
    startReply,
    finishReply,
    highlightComment,
    removeHighlight,
    handleCommentHover,
    handleCommentLeave,
    handleHighlightHover,
    handleHighlightLeave,
    closeAllComments,
    isCommentReplying,
    refreshComments,
  } = useCommentSystem({
    paperId,
    containerRef,
    showComments,
    showHighlights,
    targetCommentId,
    onCommentFocused,
  });

  // 文本选择状态和方法
  const {
    isTextSelecting,
    showInput,
    inputPos,
    inputValue,
    isSubmitting,
    needLogin,
    temporaryHighlight,
    setInputValue,
    handleSubmitComment,
    cancelInput,
  } = useTextSelection({
    paperId,
    containerRef,
    scale,
    showHighlights,
    user,
    isAuthLoading,
    onCommentCreated: (newComment) => {
      // 新评论创建后的处理逻辑
    },
    onRefreshComments: refreshComments,
  });

  // 事件处理器
  const eventHandlers: CommentEventHandlers = {
    onReply: handleReply,
    onDelete: handleDeleteComment,
    onStartReply: startReply,
    onFinishReply: finishReply,
    onHighlight: highlightComment,
    onRemoveHighlight: removeHighlight,
  };

  // 如果系统不可见且没有在动画中，则不渲染
  if (!showComments && !showHighlights) return null;

  return (
    <>
      {/* 高亮管理器 */}
      <HighlightManager
        comments={comments}
        containerRef={containerRef}
        scale={scale}
        showHighlights={showHighlights}
        visibleHighlights={visibleHighlights}
        activeHighlightId={activeHighlightId}
        isTextSelecting={isTextSelecting}
        onHighlightHover={handleHighlightHover}
        onHighlightLeave={handleHighlightLeave}
      />

      {/* 评论覆盖层 */}
      {showComments && (
        <CommentOverlay
          comments={comments}
          scale={scale}
          expandedComments={expandedComments}
          visibleComments={visibleComments}
          activeHighlightId={activeHighlightId}
          currentlyReplyingId={currentlyReplyingId}
          eventHandlers={eventHandlers}
          user={user}
          paperId={paperId}
          onCommentHover={handleCommentHover}
          onCommentLeave={handleCommentLeave}
          onCloseAllComments={closeAllComments}
          isCommentReplying={isCommentReplying}
        />
      )}

      {/* 文本选择处理 */}
      <TextSelection
        containerRef={containerRef}
        scale={scale}
        showInput={showInput}
        inputPos={inputPos}
        inputValue={inputValue}
        isSubmitting={isSubmitting}
        needLogin={needLogin}
        temporaryHighlight={temporaryHighlight}
        paperId={paperId}
        onInputChange={setInputValue}
        onSubmit={handleSubmitComment}
        onCancel={cancelInput}
      />
    </>
  );
}
