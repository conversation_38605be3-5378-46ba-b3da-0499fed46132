import { useState } from "react";
import { formatDistanceToNow } from "date-fns";
import { enUS } from "date-fns/locale";
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import { ConfirmDialog } from "../../ConfirmDialog";
import { getCommentUserColor } from "../../hooks/useUserColors";
import { CommentData, User, CommentEventHandlers } from "../../types";
import { getInitials, parseCommentText, buildCallbackUrl } from "../../utils";

interface CommentBoxProps {
  comment: CommentData;
  eventHandlers: CommentEventHandlers;
  scale: number;
  isReply?: boolean;
  currentUser: User | null;
  depth?: number;
  isReplyingTo?: string | null;
  isCommentReplying?: (commentId: string) => boolean;
  paperId?: string;
}

export function CommentBox({
  comment,
  eventHandlers,
  scale,
  isReply = false,
  currentUser,
  depth = 0,
  isReplyingTo,
  isCommentReplying,
  paperId,
}: CommentBoxProps) {
  const [replyText, setReplyText] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [notifyAuthor, setNotifyAuthor] = useState(false);
  const [needLogin, setNeedLogin] = useState(false);

  // 检查当前用户是否是评论作者
  const isCommentOwner = currentUser?.id === comment.user.id;

  // 使用上层状态管理判断是否正在回复
  const isReplying = isCommentReplying ? isCommentReplying(comment.id) : false;

  // 获取头像颜色
  const getAvatarColor = (user: User) => {
    return getCommentUserColor(user);
  };

  // 处理提交回复
  const handleSubmitReply = async () => {
    if (!replyText.trim()) return;

    if (!currentUser) {
      setNeedLogin(true);
      return;
    }

    setIsSubmitting(true);
    try {
      const parentCommentId = comment.parentId || comment.id;
      await eventHandlers.onReply(
        parentCommentId,
        replyText,
        comment.user.id,
        comment.user.name || "Anonymous user",
        notifyAuthor
      );

      setReplyText("");
      setNotifyAuthor(false);
      eventHandlers.onFinishReply(comment.id);
    } catch (error) {
      console.error("Reply failed:", error);
      eventHandlers.onFinishReply(comment.id);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理删除评论
  const handleDelete = async () => {
    try {
      await eventHandlers.onDelete(comment.id);
      setIsConfirmOpen(false);
    } catch (error) {
      console.error("Delete comment failed:", error);
    }
  };

  // 格式化时间
  const formatCommentTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: enUS });
    } catch (error) {
      return "Unknown time";
    }
  };

  const parsedComment = parseCommentText(comment.text);

  return (
    <div
      className={`bg-white rounded-md ${depth > 0 ? 'ml-4 mt-2 border-l-2 border-gray-100 pl-3' : ''}`}
      onMouseEnter={() => {
        comment.highlightId && eventHandlers.onHighlight(comment.highlightId);
        // 评论框内部悬停时，保持评论框打开
      }}
      onMouseLeave={() => {
        eventHandlers.onRemoveHighlight();
        // 离开评论框时，不立即关闭，由外层容器处理
      }}
    >
      <div className="flex items-start gap-3 p-3">
        {/* 用户头像 */}
        <Avatar className={`${isReply ? "h-6 w-6" : "h-8 w-8"} ${depth > 1 ? "h-5 w-5" : ""}`}>
          <AvatarImage src={comment.user.image || undefined} alt={comment.user.name || "用户"} />
          <AvatarFallback
            style={{
              backgroundColor: getAvatarColor(comment.user),
              color: '#ffffff'
            }}
          >
            {getInitials(comment.user.name)}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          {/* 头部信息 */}
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center">
              <span className={`font-medium ${depth > 0 ? "text-xs" : "text-sm"}`}>
                {comment.user.name || "Anonymous user"}
              </span>
              <span className={`text-gray-500 ml-2 ${depth > 0 ? "text-xs" : "text-xs"}`}>
                {formatCommentTime(comment.createdAt)}
              </span>
            </div>

            {/* 删除按钮 */}
            {isCommentOwner && (
              <button
                onClick={() => setIsConfirmOpen(true)}
                className="text-gray-400 hover:text-gray-600 text-xs"
                aria-label="Delete comment"
              >
                ×
              </button>
            )}
          </div>

          {/* 评论内容 */}
          <div className={`text-gray-700 whitespace-pre-wrap break-words ${depth > 0 ? "text-xs" : "text-sm"}`}>
            {parsedComment.isReply ? (
              <>
                <span className="text-blue-600 font-medium">Reply to {parsedComment.replyToName}</span>
                <span className="text-gray-500">: </span>
                <span>{parsedComment.content}</span>
              </>
            ) : (
              parsedComment.content
            )}
          </div>

          {/* 回复按钮 */}
          {depth < 3 && (
            <button
              onClick={() => {
                if (isReplying) {
                  eventHandlers.onFinishReply(comment.id);
                  setReplyText("");
                  setNotifyAuthor(false);
                  setNeedLogin(false);
                } else {
                  if (!currentUser) {
                    setNeedLogin(true);
                  } else {
                    setNeedLogin(false);
                  }
                  eventHandlers.onStartReply(comment.id);
                }
              }}
              className={`text-blue-500 mt-2 hover:underline ${depth > 0 ? "text-xs" : "text-xs"}`}
            >
              {isReplying ? "Cancel reply" : "Reply"}
            </button>
          )}
        </div>
      </div>

      {/* 回复框 */}
      {isReplying && (
        <div className={`${depth > 0 ? "pl-6" : "pl-11"} pr-3 pb-3 reply-container`}>
          <div className="border rounded-md overflow-hidden">
            <textarea
              value={replyText}
              onChange={e => setReplyText(e.target.value)}
              placeholder={needLogin ? "Please login first to reply" : `Reply to ${comment.user.name || "Anonymous user"}...`}
              className={`w-full p-2 focus:outline-none resize-none ${depth > 0 ? "text-xs" : "text-sm"}`}
              rows={2}
              disabled={isSubmitting || needLogin}
            />
            <div className="flex justify-end bg-gray-50 px-2 py-1">
              {!needLogin && (
                <div className="flex items-center mr-auto">
                  <Checkbox
                    id={`notify-author-${comment.id}`}
                    checked={notifyAuthor}
                    onCheckedChange={(checked) => setNotifyAuthor(checked === true)}
                    disabled={isSubmitting}
                    className="mr-2"
                  />
                  <label
                    htmlFor={`notify-author-${comment.id}`}
                    className={`select-none cursor-pointer ${depth > 0 ? "text-xs" : "text-xs"} text-gray-600`}
                  >
                    Notify Author
                  </label>
                </div>
              )}
              <button
                onClick={() => {
                  setReplyText("");
                  setNotifyAuthor(false);
                  setNeedLogin(false);
                  eventHandlers.onFinishReply(comment.id);
                }}
                className="text-xs text-gray-500 px-2 py-1 rounded hover:bg-gray-200 mr-2"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              {needLogin ? (
                <a
                  href={buildCallbackUrl(paperId || '', comment.id)}
                  className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 inline-block"
                >
                  Go to login
                </a>
              ) : (
                <button
                  onClick={handleSubmitReply}
                  className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
                  disabled={!replyText.trim() || isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Reply"}
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 显示子回复 */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="replies-container">
          <div
            className={comment.replies.length > 5 ? 'overflow-y-auto border border-gray-200 rounded-md p-2' : ''}
            style={comment.replies.length > 5 ? {
              maxHeight: '30rem',
              scrollbarWidth: 'thin',
              scrollbarColor: '#cbd5e1 #f8fafc'
            } : {}}
          >
            {comment.replies.map(reply => (
              <CommentBox
                key={reply.id}
                comment={reply}
                eventHandlers={eventHandlers}
                scale={scale}
                isReply={true}
                currentUser={currentUser}
                depth={depth + 1}
                isReplyingTo={isReplyingTo}
                isCommentReplying={isCommentReplying}
                paperId={paperId}
              />
            ))}
          </div>
        </div>
      )}

      {/* 确认删除对话框 */}
      <ConfirmDialog
        isOpen={isConfirmOpen}
        title="Delete comment"
        message={
          comment.replies && comment.replies.length > 0
            ? "Deleting this comment will also delete all replies. Are you sure you want to continue?"
            : "Are you sure you want to delete this comment?"
        }
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDelete}
        onCancel={() => setIsConfirmOpen(false)}
      />
    </div>
  );
}
