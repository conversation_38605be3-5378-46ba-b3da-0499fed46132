"use client";

import { <PERSON><PERSON><PERSON><PERSON>, ZoomIn, ZoomOut, Maximize, Printer, MessageSquareText } from "lucide-react";
import { ToolbarProps } from "../types";

export default function Toolbar({
  scale,
  showComments,
  showHighlights,
  isVisible,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onToggleAnnotations,
  onGoBack,
  onPrint,
}: ToolbarProps) {
  return (
    <div
      className={`preview-toolbar transition-all duration-500 ease-out transform ${isVisible
        ? 'opacity-100 translate-y-0 scale-100'
        : 'opacity-0 translate-y-2 scale-95'
        }`}
    >
      {/* 返回按钮 */}
      {/* <button 
        className="toolbar-button" 
        onClick={onGoBack} 
        title="Back to paper list"
      >
        <ArrowLeft size={16} />
      </button> */}

      {/* 分隔线 */}
      {/* <div className="toolbar-divider"></div> */}

      {/* 缩放控制 */}
      <button
        className="toolbar-button"
        onClick={onZoomIn}
        title="Zoom in"
      >
        <ZoomIn size={16} />
      </button>
      <button
        className="toolbar-button"
        onClick={onZoomOut}
        title="Zoom out"
      >
        <ZoomOut size={16} />
      </button>
      <button
        className="toolbar-button"
        onClick={onResetZoom}
        title="Reset zoom"
      >
        <Maximize size={16} />
      </button>

      {/* 打印功能 */}
      <button
        className="toolbar-button"
        onClick={onPrint}
        title="Print"
      >
        <Printer size={16} />
      </button>

      {/* 分隔线 */}
      <div className="toolbar-divider"></div>

      {/* 注释控制按钮（合并高亮和留言功能） */}
      <button
        className={`toolbar-button ${showComments && showHighlights ? "active" : ""}`}
        onClick={onToggleAnnotations}
        title={showComments && showHighlights ? "Hide annotations" : "Show annotations"}
      >
        <MessageSquareText size={16} />
      </button>
    </div>
  );
}
