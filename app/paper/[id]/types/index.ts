// 用户类型定义 - 匹配 Supabase 数据库字段
export interface User {
  id: string;
  name: string | null;
  email: string;
  emailVerified: Date | null;
  password: string | null;
  image: string | null;
  createdAt: Date;
  avatarColor: string | null;
  aiLastReqTime: Date | null;
  aiReqRest: number;
  aiReqTotal: number;
  aiModel: string | null;
}

// 评论数据类型
export interface CommentData {
  id: string;
  text: string;
  createdAt: string;
  highlightId: string | null;
  pageTop?: number;
  yPosition?: number;
  pageIndex?: number;
  parentId?: string;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  user: User;
  replies?: CommentData[];
  replyToUser?: User;
}

// 扩展评论数据，包含分组信息
export interface ExtendedCommentData extends CommentData {
  groupIndex?: number;
  groupTotal?: number;
  groupId?: string;
}

// 高亮信息类型
export interface HighlightInfo {
  highlightId: string;
  pageIndex: number;
  pageTop: number;
  rect: DOMRect | null;
  y: number;
}

// 输入位置类型
export interface InputPosition {
  pageIndex: number;
  y: number;
  pageTop: number;
}

// 缩放控制类型
export interface ZoomState {
  scale: number;
  isOverflow: boolean;
  layoutMode: 'center' | 'left';
}

// 文档状态类型
export interface DocumentState {
  isLoading: boolean;
  isContentVisible: boolean;
  isToolbarVisible: boolean;
  visiblePages: Set<number>;
  error: string | null;
}

// 评论系统状态类型
export interface CommentSystemState {
  comments: ExtendedCommentData[];
  isLoading: boolean;
  error: string | null;
  showComments: boolean;
  showHighlights: boolean;
  expandedComments: Record<string, boolean>;
  visibleComments: Set<string>;
  visibleHighlights: Set<string>;
  activeHighlightId: string | null;
  currentlyReplyingId: string | null;
  replyingComments: Set<string>;
}

// 文本选择状态类型
export interface TextSelectionState {
  isTextSelecting: boolean;
  showInput: boolean;
  inputPos: InputPosition | null;
  inputValue: string;
  isSubmitting: boolean;
  needLogin: boolean;
  temporaryHighlight: string | null;
  currentSelection: HighlightInfo | null;
}

// 组件Props类型
export interface PaperViewerProps {
  paperId: string;
}

export interface DocumentRendererProps {
  htmlUrl: string;
  containerRef: React.RefObject<HTMLDivElement>;
  onContentLoaded: () => void;
  onError: (error: string) => void;
}

export interface ZoomControllerProps {
  scale: number;
  onZoomChange: (newScale: number) => void;
  containerRef: React.RefObject<HTMLDivElement>;
}

export interface ToolbarProps {
  scale: number;
  showComments: boolean;
  showHighlights: boolean;
  isVisible: boolean;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onToggleAnnotations: () => void;
  onGoBack: () => void;
  onPrint: () => void;
}

export interface CommentSystemProps {
  paperId: string;
  containerRef: React.RefObject<HTMLDivElement | null>;
  scale: number;
  showComments: boolean;
  showHighlights: boolean;
  targetCommentId?: string | null;
  onCommentFocused?: () => void;
}

// API响应类型
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  status: number;
}

// 事件处理器类型
export type CommentEventHandlers = {
  onReply: (parentId: string, text: string, replyToUserId?: string, replyToUserName?: string, notifyAuthor?: boolean) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  onStartReply: (commentId: string) => void;
  onFinishReply: (commentId: string) => void;
  onHighlight: (highlightId: string) => void;
  onRemoveHighlight: () => void;
};
