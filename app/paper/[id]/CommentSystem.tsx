import { useEffect, useRef, useState, useCallback, useMemo } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar";
import { CommentBox, CommentData } from "./CommentBox";
import { useSupabase } from "@/components/SupabaseProvider";
import { User } from "./types";
import { adaptSupabaseUser, getInitials } from "./utils";
import { useUserColors, getCommentUserColor, preloadCommentUserColors } from "./hooks/useUserColors";

// 扩展CommentData接口添加分组信息
interface ExtendedCommentData extends CommentData {
  groupIndex?: number; // 在组内的索引
  groupTotal?: number; // 组内评论总数
  groupId?: string; // 组的唯一标识
}

interface HighlightInfo {
  highlightId: string;
  pageIndex: number;
  pageTop: number;
  rect: DOMRect | null;
  y: number;
}

interface CommentSystemProps {
  paperId: string;
  containerRef: React.RefObject<HTMLDivElement>;
  scale: number;
  showComments: boolean;
  showHighlights: boolean;
  targetCommentId?: string | null;
  onCommentFocused?: () => void;
}

export default function CommentSystem({
  paperId,
  containerRef,
  scale,
  showComments,
  showHighlights,
  targetCommentId,
  onCommentFocused,
}: CommentSystemProps) {
  // 使用Supabase认证
  const { user: supabaseUser, isLoading: isAuthLoading } = useSupabase();
  const user = adaptSupabaseUser(supabaseUser);
  const [comments, setComments] = useState<ExtendedCommentData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showInput, setShowInput] = useState(false);
  const [inputPos, setInputPos] = useState<{
    pageIndex: number;
    y: number;
    pageTop: number;
  } | null>(null);
  const [inputValue, setInputValue] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeHighlightId, setActiveHighlightId] = useState<string | null>(null);
  const commentRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const currentSelectionRef = useRef<HighlightInfo | null>(null);
  const [needLogin, setNeedLogin] = useState(false);

  // 新增：管理评论展开状态
  const [expandedComments, setExpandedComments] = useState<Record<string, boolean>>({});

  // 延迟关闭定时器
  const closeTimerRef = useRef<Record<string, NodeJS.Timeout>>({});

  // 回复保护状态 - 防止用户回复时评论框消失
  const [replyingComments, setReplyingComments] = useState<Set<string>>(new Set());

  // 新增：统一的回复状态管理 - 追踪当前正在回复的评论ID
  const [currentlyReplyingId, setCurrentlyReplyingId] = useState<string | null>(null);

  // 添加评论浮现效果状态
  const [visibleComments, setVisibleComments] = useState<Set<string>>(new Set());
  // 添加高亮浮现效果状态
  const [visibleHighlights, setVisibleHighlights] = useState<Set<string>>(new Set());
  // 添加系统级别的显示/隐藏动画状态
  const [isSystemVisible, setIsSystemVisible] = useState(showComments);
  const [isAnimating, setIsAnimating] = useState(false);

  // 新增：文本选择状态管理
  const [isTextSelecting, setIsTextSelecting] = useState(false);
  const textSelectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 新增：临时高亮管理
  const [temporaryHighlight, setTemporaryHighlight] = useState<string | null>(null);
  const temporaryHighlightRef = useRef<HTMLElement | null>(null);

  // 新增：创建临时高亮
  const createTemporaryHighlight = useCallback((
    rangeRect: DOMRect,
    highlightId: string,
    pageIndex: number
  ) => {
    // 清理之前的临时高亮
    if (temporaryHighlightRef.current) {
      temporaryHighlightRef.current.remove();
      temporaryHighlightRef.current = null;
    }

    if (!containerRef.current || !showHighlights) return;

    const actualPageIndex = pageIndex < 0 || pageIndex === null ? 0 : pageIndex;
    const pages = containerRef.current.querySelectorAll(".page");
    if (!pages || !pages[actualPageIndex]) return;

    const page = pages[actualPageIndex];
    const pageRect = page.getBoundingClientRect();

    // 创建临时高亮层
    const highlight = document.createElement("div");
    highlight.className = "highlight-overlay temporary";
    highlight.dataset.highlightId = highlightId;
    highlight.dataset.pageIndex = String(actualPageIndex);
    highlight.dataset.temporary = "true";

    // 设置位置和尺寸
    highlight.style.position = "absolute";
    highlight.style.top = `${(rangeRect.top - pageRect.top) / scale}px`;
    highlight.style.left = `${(rangeRect.left - pageRect.left) / scale}px`;
    highlight.style.width = `${rangeRect.width / scale}px`;
    highlight.style.height = `${rangeRect.height / scale}px`;
    highlight.style.pointerEvents = 'none'; // 临时高亮不需要交互

    // 添加到页面中
    page.appendChild(highlight);
    
    // 保存引用
    temporaryHighlightRef.current = highlight;
    setTemporaryHighlight(highlightId);

    console.log('[TemporaryHighlight] 创建临时高亮:', highlightId);
  }, [containerRef, showHighlights, scale]);

  // 新增：清理临时高亮
  const clearTemporaryHighlight = useCallback(() => {
    if (temporaryHighlightRef.current) {
      temporaryHighlightRef.current.remove();
      temporaryHighlightRef.current = null;
      console.log('[TemporaryHighlight] 清理临时高亮');
    }
    setTemporaryHighlight(null);
  }, []);

  // 新增：临时禁用所有高亮层的交互
  const disableHighlightInteraction = useCallback(() => {
    const highlights = document.querySelectorAll('.highlight-overlay');
    highlights.forEach(highlight => {
      const el = highlight as HTMLElement;
      el.style.pointerEvents = 'none';
      // 添加视觉反馈：降低透明度，提示用户可以穿透选择
      el.style.opacity = '0.3';
      el.setAttribute('data-selection-mode', 'true');
    });
  }, []);

  // 新增：重新启用所有高亮层的交互
  const enableHighlightInteraction = useCallback(() => {
    const highlights = document.querySelectorAll('.highlight-overlay');
    highlights.forEach(highlight => {
      const el = highlight as HTMLElement;
      el.style.pointerEvents = 'auto';
      // 恢复正常透明度
      const highlightId = el.dataset.highlightId;
      const isVisible = highlightId && visibleHighlights.has(highlightId);
      el.style.opacity = isVisible ? '1' : '0';
      el.removeAttribute('data-selection-mode');
    });
  }, [visibleHighlights]);

  // 新增：开始文本选择模式
  const startTextSelection = useCallback(() => {
    setIsTextSelecting(true);
    disableHighlightInteraction();
    console.log('[TextSelection] 进入文本选择模式，已禁用高亮层交互');
  }, [disableHighlightInteraction]);

  // 新增：结束文本选择模式
  const endTextSelection = useCallback(() => {
    // 清除之前的定时器
    if (textSelectionTimeoutRef.current) {
      clearTimeout(textSelectionTimeoutRef.current);
    }

    // 设置一个短暂延迟后恢复高亮交互，避免与点击事件冲突
    textSelectionTimeoutRef.current = setTimeout(() => {
      setIsTextSelecting(false);
      enableHighlightInteraction();
      console.log('[TextSelection] 退出文本选择模式，已恢复高亮层交互');
    }, 50); // 减少延迟时间，让交互更快恢复
  }, [enableHighlightInteraction]);

  // 新增：切换评论展开状态
  const toggleCommentExpand = (commentId: string) => {
    setExpandedComments(prev => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  };

  // 新增：关闭所有评论
  const closeAllComments = (exceptId?: string) => {
    const newState: Record<string, boolean> = {};
    if (exceptId) {
      newState[exceptId] = true;
    }
    setExpandedComments(newState);
  };

  // 新增：检查评论树中是否有任何回复正在进行中
  const isCommentTreeReplying = (topLevelCommentId: string) => {
    // 检查顶级评论本身是否在回复中
    if (replyingComments.has(topLevelCommentId)) {
      return true;
    }

    // 检查所有子评论是否在回复中
    const comment = comments.find(c => c.id === topLevelCommentId);
    if (comment?.replies) {
      return comment.replies.some(reply => replyingComments.has(reply.id));
    }

    return false;
  };

  // 新增：延迟关闭评论
  const delayCloseComment = (commentId: string) => {
    // 检查整个评论树是否有回复正在进行中
    if (isCommentTreeReplying(commentId)) {
      return;
    }

    // 如果有之前的定时器，先清除
    if (closeTimerRef.current[commentId]) {
      clearTimeout(closeTimerRef.current[commentId]);
    }

    // 设置新的定时器
    closeTimerRef.current[commentId] = setTimeout(() => {
      // 再次检查是否还在回复中（双重保护）
      if (isCommentTreeReplying(commentId)) {
        return;
      }

      setExpandedComments(prev => {
        const newState = { ...prev };
        delete newState[commentId];
        return newState;
      });
      delete closeTimerRef.current[commentId];
    }, 800); // 延长到800ms延迟，让用户有更多时间操作
  };

  // 取消延迟关闭
  const cancelDelayClose = (commentId: string) => {
    if (closeTimerRef.current[commentId]) {
      clearTimeout(closeTimerRef.current[commentId]);
      delete closeTimerRef.current[commentId];
    }
  };

  // 新增：根据评论ID找到顶级评论ID
  const getTopLevelCommentId = (commentId: string): string => {
    // 先找到这个评论
    const findInComments = (comments: ExtendedCommentData[], targetId: string): ExtendedCommentData | null => {
      for (const comment of comments) {
        if (comment.id === targetId) {
          return comment;
        }
        if (comment.replies) {
          const found = findInComments(comment.replies, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    const comment = findInComments(comments, commentId);
    if (comment?.parentId) {
      // 如果有parentId，递归查找顶级评论
      return getTopLevelCommentId(comment.parentId);
    }
    return commentId; // 没有parentId，说明就是顶级评论
  };

  // 开始回复 - 设置保护状态
  const startReply = (commentId: string) => {
    // 如果已经有其他评论在回复中，先结束它
    if (currentlyReplyingId && currentlyReplyingId !== commentId) {
      finishReply(currentlyReplyingId);
    }

    setReplyingComments(prev => new Set([...prev, commentId]));
    setCurrentlyReplyingId(commentId);

    // 取消当前评论的延迟关闭
    cancelDelayClose(commentId);

    // 取消顶级评论的延迟关闭（重要：这样可以保护整个评论框不消失）
    const topLevelCommentId = getTopLevelCommentId(commentId);
    if (topLevelCommentId !== commentId) {
      cancelDelayClose(topLevelCommentId);
    }
  };

  // 完成回复 - 清除保护状态并恢复正常关闭逻辑
  const finishReply = (commentId: string) => {
    setReplyingComments(prev => {
      const newSet = new Set(prev);
      newSet.delete(commentId);
      return newSet;
    });

    // 如果结束的是当前正在回复的评论，清除状态
    if (currentlyReplyingId === commentId) {
      setCurrentlyReplyingId(null);
    }

    // 获取顶级评论ID
    const topLevelCommentId = getTopLevelCommentId(commentId);

    // 完成回复后，如果整个评论树中没有其他回复正在进行，恢复延迟关闭逻辑
    setTimeout(() => {
      if (!isCommentTreeReplying(topLevelCommentId)) {
        delayCloseComment(topLevelCommentId);
      }
    }, 100); // 稍微延迟一下，确保状态更新完成
  };

  // 新增：检查指定评论是否正在回复中
  const isCommentReplying = (commentId: string) => {
    return currentlyReplyingId === commentId;
  };

  // 处理同一行的多条评论，进行分组和位置调整
  const processCommentGroups = useCallback(() => {
    if (!comments.length || !containerRef.current) return;

    // 仅在需要时执行，防止无限循环
    let needsUpdate = false;

    // 先找出所有顶级评论（非回复）
    const topComments = comments.filter(c => !c.parentId);

    // 按照pageIndex和y位置进行分组
    const commentGroups: Record<string, ExtendedCommentData[]> = {};

    topComments.forEach(comment => {
      // 如果没有位置信息，跳过
      if (comment.pageIndex === undefined || comment.yPosition === undefined) return;

      // 创建分组键：页码_Y坐标（取整到5像素）
      // 这样相近位置的评论会被分到同一组
      const groupKey = `${comment.pageIndex}_${Math.round(comment.yPosition / 5) * 5}`;

      if (!commentGroups[groupKey]) {
        commentGroups[groupKey] = [];
      }

      commentGroups[groupKey].push(comment);
    });

    // 处理每个分组
    Object.entries(commentGroups).forEach(([groupKey, groupComments]) => {
      // 如果只有一条评论，不需要特殊处理
      if (groupComments.length <= 1) return;

      // 按创建时间排序，较早的评论在前
      groupComments.sort((a, b) => {
        const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return timeA - timeB;
      });

      // 更新每条评论的分组信息
      groupComments.forEach((comment, index) => {
        // 检查是否需要更新
        if (
          comment.groupIndex !== index ||
          comment.groupTotal !== groupComments.length ||
          comment.groupId !== groupKey
        ) {
          needsUpdate = true;
          // 添加分组信息到评论数据中
          comment.groupIndex = index;
          comment.groupTotal = groupComments.length;
          comment.groupId = groupKey;
        }
      });
    });

    // 只有在实际发生更改时才更新状态
    if (needsUpdate) {
      console.log("评论分组数据已更新，共", Object.keys(commentGroups).length, "个分组");
      setComments(prev => [...prev]);
    }
  }, [comments, containerRef]);

  // 在评论加载完成后处理分组，添加isLoading防止无限循环
  useEffect(() => {
    if (!isLoading && comments.length > 0) {
      processCommentGroups();
    }
  }, [processCommentGroups, isLoading]);

  // 监听visibleHighlights变化，更新高亮显示状态
  useEffect(() => {
    // 获取所有高亮元素
    const allHighlights = document.querySelectorAll('[data-highlight-id]') as NodeListOf<HTMLElement>;

    allHighlights.forEach(highlight => {
      const highlightId = highlight.dataset.highlightId;
      if (highlightId) {
        const isVisible = visibleHighlights.has(highlightId);
        if (isVisible) {
          highlight.style.opacity = '1';
          highlight.style.transform = 'scale(1)';
        } else {
          highlight.style.opacity = '0';
          highlight.style.transform = 'scale(0.8)';
        }
      }
    });
  }, [visibleHighlights]);

  // 监听showComments变化，处理系统级别的显示/隐藏动画
  useEffect(() => {
    if (showComments) {
      // 显示评论系统
      setIsSystemVisible(true);
      setIsAnimating(true);

      // 稍等一下让DOM更新，然后触发浮现动画
      setTimeout(() => {
        if (comments.length > 0) {
          const topComments = comments.filter(c => !c.parentId);
          topComments.forEach((comment, index) => {
            setTimeout(() => {
              setVisibleComments(prev => new Set([...prev, comment.id]));
              if (comment.highlightId) {
                setVisibleHighlights(prev => new Set([...prev, comment.highlightId as string]));
              }
            }, index * 100); // 比初始加载稍快一些
          });
        }

        setTimeout(() => {
          setIsAnimating(false);
        }, comments.length * 100 + 200);
      }, 50);
    } else {
      // 隐藏评论系统
      setIsAnimating(true);

      // 立即清空可见状态，触发隐去动画
      setVisibleComments(new Set());
      setVisibleHighlights(new Set());

      // 等动画完成后隐藏组件
      setTimeout(() => {
        setIsSystemVisible(false);
        setIsAnimating(false);
      }, 400); // 等待动画完成
    }
  }, [showComments, comments]);

  // 获取评论并创建高亮
  useEffect(() => {
    let observer: MutationObserver | null = null;
    let commentsData: CommentData[] = [];
    let highlightsRestored = false;

    async function fetchComments() {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/comments?paperId=${paperId}`);

        if (!response.ok) {
          throw new Error(`获取评论失败: ${response.status}`);
        }

        const data = await response.json();

        // 预加载所有评论用户的颜色
        await preloadCommentUserColors(data);

        setComments(data);
        commentsData = data;

        // 触发评论浮现效果 - 只在初始加载且showComments为true时执行
        if (data.length > 0 && showComments && !isAnimating) {
          const topComments = data.filter((c: CommentData) => !c.parentId);
          topComments.forEach((comment: CommentData, index: number) => {
            setTimeout(() => {
              setVisibleComments(prev => new Set([...prev, comment.id]));
              // 同时让对应的高亮浮现
              if (comment.highlightId && showHighlights) {
                setVisibleHighlights(prev => new Set([...prev, comment.highlightId as string]));
              }
            }, index * 150); // 每个评论延迟150ms显示
          });
        }

        // 如果DOM已经准备好，马上恢复高亮
        if (containerRef.current && showHighlights) {
          setupHighlightRestoration();
        }
      } catch (error) {
        console.error("获取评论失败:", error);
        setError("无法加载评论");
      } finally {
        setIsLoading(false);
      }
    }

    // 使用MutationObserver监视DOM变化，确保页面元素加载完毕
    const setupHighlightRestoration = () => {
      if (!containerRef.current || highlightsRestored) return;

      // 检查页面元素是否已加载
      const pages = containerRef.current.querySelectorAll(".page");
      if (pages.length > 0) {
        // 页面已经加载，直接恢复高亮
        restoreAllHighlights();
        return;
      }

      // 如果页面还没加载，设置观察器等待
      observer = new MutationObserver(mutations => {
        // 检查是否有.page元素被添加
        for (const mutation of mutations) {
          if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
            const hasPageElement = containerRef.current?.querySelectorAll(".page").length > 0;
            if (hasPageElement && !highlightsRestored) {
              // 有.page元素被添加，恢复高亮并断开观察器
              restoreAllHighlights();
              break;
            }
          }
        }
      });

      // 开始观察DOM变化
      observer.observe(containerRef.current, {
        childList: true,
        subtree: true,
      });
    };

    // 恢复所有高亮
    const restoreAllHighlights = () => {
      if (highlightsRestored || !containerRef.current) return;

      console.log("开始恢复高亮，评论总数:", commentsData.length);
      let firstPageHighlightsCount = 0;

      // 先等待一小段时间确保DOM完全稳定
      setTimeout(() => {
        commentsData.forEach((comment: CommentData) => {
          // 处理pageIndex为null的情况，认为是第一页(索引0)
          const actualPageIndex =
            comment.pageIndex === null || comment.pageIndex === undefined ? 0 : comment.pageIndex;

          // 详细记录每条评论的高亮信息，特别关注第一页
          const isFirstPage = actualPageIndex === 0;
          if (isFirstPage) {
            console.log(
              "第一页评论:",
              comment.id,
              "pageIndex:",
              actualPageIndex,
              "highlightId:",
              comment.highlightId,
              "x,y,w,h:",
              comment.x,
              comment.y,
              comment.width,
              comment.height
            );
            firstPageHighlightsCount++;
          }

          // 如果评论的pageIndex为null，创建一个修正后的评论对象
          const fixedComment = {
            ...comment,
            pageIndex: actualPageIndex,
          };

          if (
            comment.highlightId &&
            comment.x !== undefined &&
            comment.y !== undefined &&
            comment.width !== undefined &&
            comment.height !== undefined
          ) {
            restoreHighlight(fixedComment);
          }
        });

        console.log("第一页评论数:", firstPageHighlightsCount);
        highlightsRestored = true;

        // 断开观察器
        if (observer) {
          observer.disconnect();
          observer = null;
        }
      }, 500);
    };

    if (paperId) {
      fetchComments();
    }

    // 清理函数
    return () => {
      if (observer) {
        observer.disconnect();
      }
      if (containerRef.current) {
        const highlights = containerRef.current.querySelectorAll(".highlight-overlay");
        highlights.forEach(highlight => highlight.remove());
      }
      // 清理临时高亮
      if (temporaryHighlightRef.current) {
        temporaryHighlightRef.current.remove();
        temporaryHighlightRef.current = null;
      }
    };
  }, [paperId, containerRef, showHighlights]);

  // 从数据库恢复高亮区域
  const restoreHighlight = (comment: CommentData) => {
    if (!containerRef.current || !showHighlights) return;

    const { highlightId, pageIndex, x, y, width, height } = comment;

    // 更精确的检查，特别是对pageIndex为0的情况
    // 如果pageIndex为null或undefined，默认使用第一页(0)
    const actualPageIndex = pageIndex === null || pageIndex === undefined ? 0 : pageIndex;

    if (
      !highlightId ||
      x === undefined ||
      y === undefined ||
      width === undefined ||
      height === undefined
    ) {
      console.log("无效的高亮数据:", comment.id, actualPageIndex, x, y, width, height);
      return;
    }

    const pages = containerRef.current.querySelectorAll(".page");
    if (!pages || pages.length === 0) {
      console.log("未找到页面元素");
      return;
    }

    // 确保页面索引有效
    if (actualPageIndex >= pages.length) {
      console.log("页面索引超出范围:", actualPageIndex, "页面总数:", pages.length);
      return;
    }

    const page = pages[actualPageIndex];
    if (!page) {
      console.log("无法获取页面元素, 索引:", actualPageIndex);
      return;
    }

    // 检查是否已存在此高亮
    const existingHighlight = document.querySelector(`[data-highlight-id="${highlightId}"]`);
    if (existingHighlight) {
      console.log("高亮已存在:", highlightId);
      return;
    }

    // 创建高亮层
    const highlight = document.createElement("div");
    highlight.className = "highlight-overlay";
    if (!showHighlights) {
      highlight.classList.add("hidden");
    }
    highlight.dataset.highlightId = highlightId;
    highlight.dataset.pageIndex = String(actualPageIndex);
    // 存储评论ID，用于鼠标悬停时的关联
    if (comment.id) {
      highlight.dataset.commentId = comment.id;
    }

    // 设置高亮层的位置和尺寸
    highlight.style.position = "absolute";
    highlight.style.top = `${y}px`;
    highlight.style.left = `${x}px`;
    highlight.style.width = `${width}px`;
    highlight.style.height = `${height}px`;

    // 设置交互属性 - 默认启用交互，但在文本选择模式时禁用
    highlight.style.pointerEvents = isTextSelecting ? 'none' : 'auto';

    // 添加浮现效果的初始状态和过渡
    highlight.style.transition = 'all 0.4s ease-out';
    const isHighlightVisible = visibleHighlights.has(highlightId);
    highlight.style.opacity = isHighlightVisible ? '1' : '0';
    highlight.style.transform = isHighlightVisible ? 'scale(1)' : 'scale(0.8)';

    // 鼠标悬停在高亮上时激活对应的评论
    highlight.addEventListener("mouseenter", () => {
      // 设置激活ID并添加活动类
      setActiveHighlightId(highlightId);
      highlight.classList.add("active");
      console.log("恢复的高亮激活:", highlightId);

      // 通过highlightId找到对应的评论ID
      if (comment.id) {
        handleCommentHover(comment.id);
      }
    });

    highlight.addEventListener("mouseleave", () => {
      // 直接移除active类，不依赖activeHighlightId状态
      highlight.classList.remove("active");
      setActiveHighlightId(null);
      console.log("恢复的高亮取消激活:", highlightId);

      // 通过评论ID延迟关闭评论框
      if (comment.id) {
        delayCloseComment(comment.id);
      }
    });

    // 将高亮层添加到页面中
    page.appendChild(highlight);
    console.log("已添加高亮:", highlightId, "到页面:", actualPageIndex);
  };

  // 获取当前选中文本所在的页面索引和页面位置
  const getPageInfo = (node: Node | null): { pageIndex: number; pageTop: number } => {
    if (!node || !containerRef.current) return { pageIndex: -1, pageTop: 0 };

    let element = node.nodeType === Node.TEXT_NODE ? node.parentElement : (node as Element);

    // 向上查找.page元素
    while (element && !element.classList.contains("page")) {
      element = element.parentElement;
    }

    if (!element) return { pageIndex: -1, pageTop: 0 };

    // 获取所有页面并找出当前页面的索引
    const pages = containerRef.current.querySelectorAll(".page");
    const pageIndex = Array.from(pages).indexOf(element as Element);

    // 计算页面顶部相对于容器的位置
    const containerRect = containerRef.current.getBoundingClientRect();
    const pageRect = element.getBoundingClientRect();
    const pageTop = (pageRect.top - containerRect.top) / scale;

    return { pageIndex, pageTop };
  };

  // 处理文本选择
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let isMouseDown = false;

    const handleMouseDown = (e: MouseEvent) => {
      // 检查是否点击在评论相关元素上，如果是则不禁用高亮交互
      const target = e.target as HTMLElement;
      if (target.closest('.comment-box') || 
          target.closest('.comment-dot') || 
          target.closest('.selection-comment-input') ||
          target.closest('button') ||
          target.closest('input') ||
          target.closest('textarea')) {
        console.log('[TextSelection] 点击在UI元素上，跳过文本选择模式');
        return;
      }

      isMouseDown = true;
      
      // 立即禁用所有高亮层交互，让用户能够穿透选择文本
      console.log('[TextSelection] mousedown detected, 立即禁用高亮层交互');
      startTextSelection();
    };

    const handleMouseUp = () => {
      if (!isMouseDown) return;
      
      isMouseDown = false;
      
      // 延迟处理文本选择，确保选择操作完全完成
      setTimeout(() => {
        processTextSelection();
        // 延迟恢复高亮交互，避免与其他事件冲突
        setTimeout(() => {
          endTextSelection();
        }, 100); // 优化延迟时间
      }, 30); // 减少初始延迟
    };

    // 处理鼠标离开容器的情况
    const handleMouseLeave = () => {
      if (isMouseDown) {
        isMouseDown = false;
        // 如果鼠标离开容器时还在按下状态，延迟恢复高亮交互
        setTimeout(() => {
          endTextSelection();
        }, 100); // 优化延迟时间
      }
    };

    const processTextSelection = () => {
      const sel = window.getSelection();
      if (!sel || sel.isCollapsed) {
        console.log('[TextSelection] 没有选中文本或选区已折叠');
        return;
      }

      if (!container.contains(sel.anchorNode) || !container.contains(sel.focusNode)) {
        console.log('[TextSelection] 选区不在容器内');
        return;
      }

      const range = sel.getRangeAt(0);
      const { pageIndex, pageTop } = getPageInfo(range.commonAncestorContainer);

      if (pageIndex === -1) {
        console.log('[TextSelection] 无法确定页面索引');
        return;
      }

      // 获取页面元素
      const pages = container.querySelectorAll(".page");
      const page = pages[pageIndex];
      const pageRect = page.getBoundingClientRect();
      const rangeRect = range.getBoundingClientRect();

      // 生成唯一的高亮ID
      const highlightId = `highlight-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // 计算选区相对于页面的位置
      const rect = {
        top: (rangeRect.top - pageRect.top) / scale,
        left: (rangeRect.left - pageRect.left) / scale,
        width: rangeRect.width / scale,
        height: rangeRect.height / scale,
      };

      // 保存当前选区信息
      currentSelectionRef.current = {
        highlightId,
        pageIndex,
        pageTop,
        rect: rangeRect,
        y: rect.top,
      };

      // 创建临时高亮显示当前选择的区域
      createTemporaryHighlight(rangeRect, highlightId, pageIndex);

      // 显示输入框在页面右侧
      setInputPos({
        pageIndex,
        y: rect.top,
        pageTop,
      });

      // 检查用户是否登录
      if (!user && !isAuthLoading) {
        setNeedLogin(true);
      } else {
        setNeedLogin(false);
      }

      setShowInput(true);
      setInputValue("");

      console.log('[TextSelection] 文本选择处理完成，已创建临时高亮');
    };

    // 在容器上监听所有相关事件
    container.addEventListener("mousedown", handleMouseDown, true); // 使用捕获阶段确保优先执行
    container.addEventListener("mouseup", handleMouseUp);
    container.addEventListener("mouseleave", handleMouseLeave);

    return () => {
      // 清理事件监听器
      container.removeEventListener("mousedown", handleMouseDown, true);
      container.removeEventListener("mouseup", handleMouseUp);
      container.removeEventListener("mouseleave", handleMouseLeave);
      
      // 清理定时器
      if (textSelectionTimeoutRef.current) {
        clearTimeout(textSelectionTimeoutRef.current);
      }
    };
  }, [containerRef, scale, user, isAuthLoading, startTextSelection, endTextSelection]);

  // 创建高亮覆盖层
  const createHighlightOverlay = (
    rangeRect: DOMRect,
    highlightId: string,
    pageIndex: number,
    commentId?: string
  ) => {
    if (!containerRef.current || !showHighlights) return;

    // 确保pageIndex至少为0
    const actualPageIndex = pageIndex < 0 || pageIndex === null ? 0 : pageIndex;

    const pages = containerRef.current.querySelectorAll(".page");
    if (!pages || !pages[actualPageIndex]) {
      console.log("创建高亮失败: 页面索引无效", actualPageIndex);
      return;
    }

    const page = pages[actualPageIndex];
    const pageRect = page.getBoundingClientRect();

    // 先清理可能存在的同ID临时高亮
    const existingTemporary = document.querySelector(`[data-highlight-id="${highlightId}"][data-temporary="true"]`);
    if (existingTemporary) {
      existingTemporary.remove();
      console.log('[HighlightOverlay] 清理了同ID的临时高亮:', highlightId);
    }

    // 检查是否已存在正式高亮
    const existingHighlight = document.querySelector(`[data-highlight-id="${highlightId}"]:not([data-temporary="true"])`);
    if (existingHighlight) {
      console.log('[HighlightOverlay] 正式高亮已存在，跳过创建:', highlightId);
      return;
    }

    // 创建高亮层
    const highlight = document.createElement("div");
    highlight.className = "highlight-overlay";
    if (!showHighlights) {
      highlight.classList.add("hidden");
    }
    highlight.dataset.highlightId = highlightId;
    highlight.dataset.pageIndex = String(actualPageIndex);

    // 如果提供了commentId，存储在data属性中
    if (commentId) {
      highlight.dataset.commentId = commentId;
    }

    // 设置高亮层的位置和尺寸
    highlight.style.position = "absolute";
    highlight.style.top = `${(rangeRect.top - pageRect.top) / scale}px`;
    highlight.style.left = `${(rangeRect.left - pageRect.left) / scale}px`;
    highlight.style.width = `${rangeRect.width / scale}px`;
    highlight.style.height = `${rangeRect.height / scale}px`;

    // 设置交互属性 - 默认启用交互，但在文本选择模式时禁用
    highlight.style.pointerEvents = isTextSelecting ? 'none' : 'auto';

    // 添加浮现效果的初始状态和过渡
    highlight.style.transition = 'all 0.4s ease-out';
    const isHighlightVisible = visibleHighlights.has(highlightId);
    highlight.style.opacity = isHighlightVisible ? '1' : '0';
    highlight.style.transform = isHighlightVisible ? 'scale(1)' : 'scale(0.8)';

    // 鼠标悬停在高亮上时激活对应的评论
    highlight.addEventListener("mouseenter", () => {
      // 设置激活ID并添加活动类
      setActiveHighlightId(highlightId);
      highlight.classList.add("active");
      console.log("高亮激活:", highlightId);

      // 优先使用data-comment-id属性
      const storedCommentId = highlight.dataset.commentId;
      if (storedCommentId) {
        handleCommentHover(storedCommentId);
        return;
      }

      // 回退到通过highlightId在comments中查找
      const matchingComment = comments.find(c => c.highlightId === highlightId);
      if (matchingComment) {
        // 找到后，将commentId存储到data属性中以便将来使用
        highlight.dataset.commentId = matchingComment.id;
        handleCommentHover(matchingComment.id);
      }
    });

    highlight.addEventListener("mouseleave", () => {
      // 直接移除active类，不依赖activeHighlightId状态
      highlight.classList.remove("active");
      setActiveHighlightId(null);
      console.log("高亮取消激活:", highlightId);

      // 优先使用data-comment-id属性
      const storedCommentId = highlight.dataset.commentId;
      if (storedCommentId) {
        delayCloseComment(storedCommentId);
        return;
      }

      // 回退到通过highlightId在comments中查找
      const matchingComment = comments.find(c => c.highlightId === highlightId);
      if (matchingComment) {
        // 找到后，将commentId存储到data属性中以便将来使用
        highlight.dataset.commentId = matchingComment.id;
        delayCloseComment(matchingComment.id);
      }
    });

    // 将高亮层添加到页面中
    page.appendChild(highlight);
    console.log("创建新高亮:", highlightId, "页面:", actualPageIndex);

    return highlight;
  };

  // 添加评论
  const handleSubmitComment = async () => {
    if (!inputValue.trim() || !currentSelectionRef.current || !user) {
      setShowInput(false);
      clearTemporaryHighlight(); // 清理临时高亮
      return;
    }

    setIsSubmitting(true);

    try {
      const { highlightId, pageIndex, pageTop, y, rect } = currentSelectionRef.current;

      // 计算高亮区域的位置和尺寸
      const pages = containerRef.current?.querySelectorAll(".page");
      const page = pages?.[pageIndex];
      const pageRect = page?.getBoundingClientRect();

      // 计算相对于页面的位置
      const x = pageRect && rect ? (rect.left - pageRect.left) / scale : 0;
      const width = rect ? rect.width / scale : 0;
      const height = rect ? rect.height / scale : 0;

      // 确保pageIndex至少为0（对第一页特别处理）
      const actualPageIndex = pageIndex < 0 ? 0 : pageIndex;

      const response = await fetch("/api/comments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: inputValue.trim(),
          paperId,
          highlightId,
          pageIndex: actualPageIndex, // 使用处理后的索引
          yPosition: y,
          pageTop,
          x,
          y,
          width,
          height,
        }),
      });

      if (!response.ok) {
        throw new Error(`创建评论失败: ${response.status}`);
      }

      const newComment = await response.json();

      // 添加到评论列表
      setComments(prev => [newComment, ...prev]);

      // 保存选区信息，避免后续清理导致丢失
      const savedRect = currentSelectionRef.current.rect;
      const savedHighlightId = highlightId;
      const savedPageIndex = actualPageIndex;

      // 清理临时高亮
      clearTemporaryHighlight();

      // 立即显示新评论和对应的高亮
      setTimeout(() => {
        setVisibleComments(prev => new Set([...prev, newComment.id]));
        if (savedHighlightId) {
          setVisibleHighlights(prev => new Set([...prev, savedHighlightId]));
        }
      }, 100);

      // 创建正式高亮（使用保存的数据）
      setTimeout(() => {
        if (savedRect) {
          createHighlightOverlay(
            savedRect,
            savedHighlightId,
            savedPageIndex,
            newComment.id // 传递新评论的ID
          );
          console.log('[Comment] 正式高亮已创建:', savedHighlightId);
        }
      }, 50);

      // 重置状态
      setShowInput(false);
      setInputValue("");
      currentSelectionRef.current = null;

      // 清除选区
      window.getSelection()?.removeAllRanges();

      console.log('[Comment] 评论创建成功，临时高亮已清理，正式高亮已创建');
    } catch (error) {
      console.error("提交评论失败:", error);
      // 失败时也要清理临时高亮
      clearTemporaryHighlight();
    } finally {
      setIsSubmitting(false);
    }
  };

  // 添加回复
  const handleReply = async (parentId: string, text: string, replyToUserId?: string, replyToUserName?: string, notifyAuthor?: boolean) => {
    if (!text.trim() || !user) {
      console.warn("handleReply: 缺少必要参数", { text: text.trim(), user: !!user });
      return;
    }

    console.log("handleReply: 开始处理回复", { 
      parentId, 
      text: text.substring(0, 50) + "...", 
      replyToUserId, 
      replyToUserName, 
      notifyAuthor,
      paperId 
    });

    try {
      // 如果是回复特定用户，格式化回复文本
      const formattedText = replyToUserId && replyToUserName
        ? `回复${replyToUserName}: ${text.trim()}`
        : text.trim();

      console.log("handleReply: 准备发送请求", { 
        formattedText: formattedText.substring(0, 50) + "...",
        endpoint: "/api/comments"
      });

      const requestBody = {
        text: formattedText,
        paperId,
        parentId,
        replyToUserId, // 传递被回复人的ID用于通知
        notifyAuthor, // 传递是否通知作者的选项
      };

      console.log("handleReply: 请求体", requestBody);

      const response = await fetch("/api/comments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      console.log("handleReply: 收到响应", { 
        status: response.status, 
        ok: response.ok,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        // 尝试获取错误详情
        let errorDetails = "";
        try {
          const errorText = await response.text();
          console.error("handleReply: 响应错误内容", errorText);
          errorDetails = errorText;
        } catch (e) {
          console.error("handleReply: 无法读取错误响应", e);
        }
        
        throw new Error(`创建回复失败: ${response.status} ${response.statusText}. 详情: ${errorDetails}`);
      }

      const newReply = await response.json();
      console.log("创建回复成功:", newReply);

      // 确保回复对象包含必要的用户信息
      if (!newReply.user && user) {
        newReply.user = {
          id: user.id,
          name: user.name || user.email?.split("@")[0] || "用户",
          email: user.email,
          emailVerified: user.emailVerified,
          password: user.password,
          image: user.image,
          createdAt: user.createdAt,
          avatarColor: user.avatarColor,
          aiLastReqTime: user.aiLastReqTime,
          aiReqRest: user.aiReqRest,
          aiReqTotal: user.aiReqTotal,
          aiModel: user.aiModel,
        };
      }

      // 更新评论列表中的回复
      setComments(prevComments => {
        const updatedComments = prevComments.map(comment => {
          if (comment.id === parentId) {
            // 确保replies字段存在，如果不存在则初始化为空数组
            const updatedReplies = Array.isArray(comment.replies)
              ? [...comment.replies, newReply]
              : [newReply];
            const updatedComment = {
              ...comment,
              replies: updatedReplies,
            };
            console.log("更新后的评论:", updatedComment);
            return updatedComment;
          }
          return comment;
        });
        
        console.log("handleReply: 状态更新完成", { 
          totalComments: updatedComments.length,
          targetParentId: parentId 
        });
        return updatedComments;
      });

      // 强制重新渲染评论部分
      setTimeout(() => {
        const commentElement = document.querySelector(`[data-comment-id="${parentId}"]`);
        if (commentElement) {
          // 触发一个无害的样式更改以强制重新渲染
          commentElement.classList.add("commented");
          setTimeout(() => commentElement.classList.remove("commented"), 10);
          console.log("handleReply: 触发了DOM重新渲染");
        } else {
          console.warn("handleReply: 未找到目标评论元素", parentId);
        }
      }, 100);

      console.log("handleReply: 回复处理完成");
    } catch (error) {
      console.error("提交回复失败:", error);
      console.error("错误堆栈:", error instanceof Error ? error.stack : "无堆栈信息");
      
      // 提供更详细的错误信息
      if (error instanceof TypeError && error.message.includes('fetch')) {
        console.error("handleReply: 网络请求失败，可能是连接问题");
      }
      
      throw error;
    }
  };

  // 删除评论
  const handleDeleteComment = async (commentId: string) => {
    try {
      // 首先检查是否为顶级评论以及是否有子评论
      const comment = comments.find(c => c.id === commentId);
      const isParentComment = comment && !comment.parentId;
      const hasReplies = comment?.replies && comment.replies.length > 0;

      // 如果是父评论并且有回复，构建要删除的所有评论ID数组
      let idsToDelete = [commentId];
      if (isParentComment && hasReplies && comment?.replies) {
        const childIds = comment.replies.map(reply => reply.id);
        idsToDelete = [...idsToDelete, ...childIds];
      }

      // 调用API删除评论
      const response = await fetch(`/api/comments/${commentId}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cascade: isParentComment && hasReplies,
        }),
      });

      if (!response.ok) {
        throw new Error(`删除评论失败: ${response.status}`);
      }

      // 从本地状态中移除评论
      setComments(prevComments => {
        // 如果是子评论，从父评论的replies数组中移除
        if (!isParentComment) {
          return prevComments.map(comment => {
            if (comment.replies) {
              return {
                ...comment,
                replies: comment.replies.filter(reply => reply.id !== commentId),
              };
            }
            return comment;
          });
        }

        // 如果是顶级评论，直接从列表中移除
        return prevComments.filter(c => c.id !== commentId);
      });

      // 移除对应的高亮
      const highlightEl = document.querySelector(`[data-highlight-id="${comment?.highlightId}"]`);
      if (highlightEl) {
        highlightEl.remove();
      }
    } catch (error) {
      console.error("删除评论失败:", error);
      throw error;
    }
  };

  // 高亮评论
  const highlightComment = (highlightId: string) => {
    setActiveHighlightId(highlightId);
    const highlight = document.querySelector(`[data-highlight-id="${highlightId}"]`);
    if (highlight) {
      highlight.classList.add("active");

      // 首先尝试从高亮元素的data-comment-id属性获取评论ID
      const commentId = highlight.getAttribute("data-comment-id");
      if (commentId) {
        // 直接使用关联的评论ID
        cancelDelayClose(commentId);
        setExpandedComments(prev => ({
          ...prev,
          [commentId]: true,
        }));
        return;
      }
    }

    // 回退到comments中查找
    const matchingComment = comments.find(c => c.highlightId === highlightId);
    if (matchingComment) {
      // 找到后，更新高亮元素的data-comment-id属性
      if (highlight) {
        highlight.setAttribute("data-comment-id", matchingComment.id);
      }

      // 取消任何延迟关闭
      cancelDelayClose(matchingComment.id);

      // 展开评论框
      setExpandedComments(prev => ({
        ...prev,
        [matchingComment.id]: true,
      }));
    }
  };

  // 移除高亮
  const removeHighlight = () => {
    // 先获取当前激活的高亮元素
    const activeHighlights = document.querySelectorAll(".highlight-overlay.active");
    activeHighlights.forEach(highlight => {
      highlight.classList.remove("active");
    });

    setActiveHighlightId(null);
  };

  // 获取用户名首字母作为头像Fallback
  const getInitials = (name: string | null) => {
    if (!name) return "?";
    return name.charAt(0).toUpperCase();
  };

  // 收集所有评论中的用户
  const allUsers = useMemo(() => {
    const users: (User | undefined)[] = [];
    const collectUsers = (comments: ExtendedCommentData[]) => {
      comments.forEach(comment => {
        users.push(comment.user);
        if (comment.replies) {
          collectUsers(comment.replies);
        }
      });
    };
    collectUsers(comments);
    return users;
  }, [comments]);

  // 使用统一的用户颜色管理
  const userColors = useUserColors(allUsers);

  // 获取头像颜色（返回Hex颜色值）
  const getAvatarColor = useCallback((user: User | undefined) => {
    if (!user) return "#000000";
    return userColors[user.id] || getCommentUserColor(user);
  }, [userColors]);

  // 获取评论圆点颜色（返回Hex颜色值）
  const getCommentDotColor = useCallback((user: User | undefined) => {
    if (!user) return "#000000";
    return userColors[user.id] || getCommentUserColor(user);
  }, [userColors]);

  // 构建登录后的回调URL
  const buildCallbackUrl = () => {
    const currentUrl = `/paper/${paperId}`;
    const callbackUrl = encodeURIComponent(currentUrl);
    return `/account?callbackUrl=${callbackUrl}`;
  };

  // 优化全局点击事件，考虑高亮区域
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      // 检查点击是否在评论框或高亮区域外
      const commentElements = document.querySelectorAll(".comment-dot, .comment-box");
      const highlightElements = document.querySelectorAll(".highlight-overlay");
      let clickedOnCommentOrHighlight = false;

      // 检查评论元素
      commentElements.forEach(el => {
        if (el.contains(e.target as Node)) {
          clickedOnCommentOrHighlight = true;
        }
      });

      // 检查高亮元素
      highlightElements.forEach(el => {
        if (el.contains(e.target as Node)) {
          clickedOnCommentOrHighlight = true;
        }
      });

      if (!clickedOnCommentOrHighlight) {
        closeAllComments();
      }
    };

    document.addEventListener("click", handleGlobalClick);
    return () => document.removeEventListener("click", handleGlobalClick);
  }, []);

  // 新增：当鼠标悬停在评论框上时也设为展开状态
  const handleCommentHover = useCallback(
    (commentId: string) => {
      // 取消可能存在的延迟关闭
      cancelDelayClose(commentId);

      if (!expandedComments[commentId]) {
        closeAllComments(commentId);
      }
    },
    [expandedComments]
  );

  // 新增：处理目标评论ID，自动展开和滚动
  useEffect(() => {
    if (!targetCommentId || !comments.length || isLoading) return;

    // 查找目标评论
    const findTargetComment = (commentsArray: ExtendedCommentData[]): ExtendedCommentData | null => {
      for (const comment of commentsArray) {
        if (comment.id === targetCommentId) return comment;
        if (comment.replies) {
          const found = findTargetComment(comment.replies);
          if (found) return found;
        }
      }
      return null;
    };

    const targetComment = findTargetComment(comments);
    if (!targetComment) return;

    // 找到顶级评论ID
    const topLevelCommentId = getTopLevelCommentId(targetComment.id);

    // 自动展开评论
    setExpandedComments(prev => ({
      ...prev,
      [topLevelCommentId]: true,
    }));

    // 如果目标评论有高亮，激活高亮
    if (targetComment.highlightId) {
      setActiveHighlightId(targetComment.highlightId);
      const highlight = document.querySelector(`[data-highlight-id="${targetComment.highlightId}"]`);
      if (highlight) {
        highlight.classList.add("active");
      }
    }

    // 滚动到评论位置
    setTimeout(() => {
      const commentElement = document.querySelector(`[data-comment-id="${topLevelCommentId}"]`);
      if (commentElement && containerRef.current) {
        // 计算滚动位置
        const scrollContainer = containerRef.current.closest('.paged-preview') as HTMLElement;
        if (scrollContainer) {
          // 获取可视区域高度
          const viewportHeight = scrollContainer.clientHeight;

          // 获取评论框的位置和高度
          const commentRect = commentElement.getBoundingClientRect();
          const scrollContainerRect = scrollContainer.getBoundingClientRect();

          // 计算评论框相对于滚动容器的位置
          const commentRelativeTop = scrollContainer.scrollTop + (commentRect.top - scrollContainerRect.top);
          const commentHeight = commentRect.height;

          // 计算目标滚动位置：让评论框中心出现在屏幕中央
          // 考虑评论框的高度，让整个评论框都能看到
          const commentCenter = commentRelativeTop + (commentHeight / 2);
          const targetScrollTop = commentCenter - (viewportHeight / 2);

          // 添加一些缓冲区，确保评论框不会贴边显示
          const buffer = 50;
          const finalScrollTop = Math.max(buffer, targetScrollTop);

          console.log('评论框自动滚动:', {
            commentRelativeTop,
            commentHeight,
            viewportHeight,
            commentCenter,
            targetScrollTop,
            finalScrollTop
          });

          scrollContainer.scrollTo({
            top: finalScrollTop,
            behavior: 'smooth'
          });
        }
      }

      // 触发回调
      if (onCommentFocused) {
        onCommentFocused();
      }
    }, 500); // 等待评论系统完全加载后再滚动
  }, [targetCommentId, comments, isLoading, onCommentFocused]);

  // 如果系统不可见且没有在动画中，则不渲染
  if (!isSystemVisible && !isAnimating) return null;

  return (
    <>
      {/* 独立评论框，每条评论单独显示在页面右侧 */}
      {showComments &&
        comments.map(comment => {
          // 如果是回复则不单独显示
          if (comment.parentId) return null;

          // 查找对应的高亮元素以获取位置
          const commentHighlightId = comment.highlightId;
          if (!commentHighlightId) return null;

          // 判断评论是否展开
          const isExpanded = !!expandedComments[comment.id];

          // 使用scale计算正确的位置
          const topPosition =
            comment.pageTop && comment.yPosition
              ? (comment.pageTop + comment.yPosition) * scale
              : 0;

          const isCommentVisible = visibleComments.has(comment.id);

          return (
            <div
              key={comment.id}
              ref={el => {
                if (el) commentRefs.current.set(comment.id, el);
                return undefined;
              }}
              className={`comment-container absolute pointer-events-auto transition-all duration-500 ease-out transform ${isCommentVisible
                ? 'opacity-100 translate-x-0 scale-100'
                : 'opacity-0 translate-x-4 scale-95'
                }`}
              style={{
                right: comment.groupIndex ? `${20 + comment.groupIndex * 20}px` : "20px",
                top: `${topPosition}px`,
                zIndex: isExpanded ? 60 : 50,
                transform: `scale(${scale}) ${isCommentVisible ? 'translateX(0)' : 'translateX(16px)'}`,
                transformOrigin: "top right", // 从右上角开始变换
              }}
              data-comment-id={comment.id}
              data-group-id={comment.groupId}
              data-group-index={comment.groupIndex}
              data-group-total={comment.groupTotal}
              onMouseEnter={() => {
                commentHighlightId && highlightComment(commentHighlightId);
                handleCommentHover(comment.id);
              }}
              onMouseLeave={() => {
                removeHighlight();
                // 使用延迟关闭
                delayCloseComment(comment.id);
              }}
            >
              {/* 评论框主体在左侧 */}
              <div
                className={`comment-box-wrapper ${isExpanded ? "expanded" : ""}`}
                onMouseEnter={() => cancelDelayClose(comment.id)}
              >
                <div
                  className={`comment-box ${activeHighlightId === commentHighlightId ? "active" : ""}`}
                  onClick={e => e.stopPropagation()} // 防止点击评论框时关闭它
                  style={{
                    fontSize: `${14 / scale}px`, // 字体大小反向调整，保持可读性
                  }}
                >

                  <CommentBox
                    comment={comment}
                    highlightComment={highlightComment}
                    removeHighlight={removeHighlight}
                    onReply={handleReply}
                    onDelete={handleDeleteComment}
                    onStartReply={startReply}
                    onFinishReply={finishReply}
                    scale={scale}
                    isReplyingTo={currentlyReplyingId}
                    isCommentReplying={isCommentReplying}
                    paperId={paperId}
                    currentUser={
                      user
                    }
                  />
                </div>
              </div>

              {/* 小圆点在右侧 */}
              <div
                className={`comment-dot ${isExpanded ? "expanded" : ""}`}
                style={{
                  backgroundColor: getCommentDotColor(comment.user),
                }}
                onClick={e => {
                  e.stopPropagation();
                  e.preventDefault();

                  // 切换状态
                  if (expandedComments[comment.id]) {
                    // 如果已经展开，则关闭
                    setExpandedComments(prev => {
                      const newState = { ...prev };
                      delete newState[comment.id];
                      return newState;
                    });
                  } else {
                    // 如果未展开，先关闭所有其他评论，再展开当前评论
                    closeAllComments(comment.id);
                  }

                  // 如果有对应的高亮，也切换高亮状态
                  if (commentHighlightId) {
                    const highlight = document.querySelector(
                      `[data-highlight-id="${commentHighlightId}"]`
                    ) as HTMLElement;
                    if (highlight) {
                      if (expandedComments[comment.id]) {
                        highlight.classList.remove("active");
                        setActiveHighlightId(null);
                      } else {
                        highlight.classList.add("active");
                        setActiveHighlightId(commentHighlightId);
                      }
                    }
                  }
                }}
                onMouseEnter={() => handleCommentHover(comment.id)}
              ></div>
            </div>
          );
        })}

      {/* 评论输入框也应用同样的缩放 */}
      {showInput && inputPos && (
        <div
          className="absolute z-50 pointer-events-auto"
          style={{
            right: "20px",
            top: `${(inputPos.pageTop + inputPos.y) * scale}px`,
            width: "280px",
            transform: `scale(${scale})`,
            transformOrigin: "top right"
          }}
        >
          <div
            className="bg-white p-3 border rounded-lg shadow-lg selection-comment-input"
            style={{
              animation: 'fadeInRight 0.4s ease-out'
            }}
          >
            <div className="flex items-center mb-2">
              {user ? (
                <Avatar className="h-7 w-7 mr-2">
                  <AvatarImage
                    src={user.image || undefined}
                    alt={user.name || "用户"}
                  />
                  <AvatarFallback
                    style={{
                      backgroundColor: getAvatarColor(user),
                      color: '#ffffff'
                    }}
                  >
                    {getInitials(user.name || user.email)}
                  </AvatarFallback>
                </Avatar>
              ) : null}
              <span className="text-sm font-medium">添加留言</span>
            </div>

            <textarea
              className="w-full border rounded-md p-2 text-sm mb-2 focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
              rows={3}
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              placeholder={needLogin ? "请先登录后发表留言" : "在此处输入留言..."}
              disabled={isSubmitting || needLogin}
              autoFocus={!needLogin}
              style={{ fontSize: `${14 / scale}px` }} // 调整字体大小以保持可读性
            />

            <div className="flex justify-end">
              <button
                className="bg-gray-200 text-gray-700 text-xs px-3 py-1.5 rounded-md hover:bg-gray-300 mr-2"
                onClick={() => {
                  setShowInput(false);
                  setNeedLogin(false);
                  clearTemporaryHighlight(); // 清理临时高亮
                  currentSelectionRef.current = null;
                  window.getSelection()?.removeAllRanges();
                  console.log('[Comment] 取消评论，已清理临时高亮');
                }}
                disabled={isSubmitting}
                style={{ fontSize: `${12 / scale}px` }} // 调整按钮字体大小
              >
                取消
              </button>
              {needLogin ? (
                <a
                  href={buildCallbackUrl()}
                  className="bg-blue-500 text-white text-xs px-3 py-1.5 rounded-md hover:bg-blue-600 inline-block"
                  style={{ fontSize: `${12 / scale}px` }} // 调整链接字体大小
                >
                  去登录
                </a>
              ) : (
                <button
                  className="bg-blue-500 text-white text-xs px-3 py-1.5 rounded-md hover:bg-blue-600"
                  onClick={handleSubmitComment}
                  disabled={!inputValue.trim() || isSubmitting}
                  style={{ fontSize: `${12 / scale}px` }} // 调整按钮字体大小
                >
                  {isSubmitting ? "提交中..." : "发布"}
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
