import { useState } from "react";
import { formatDistanceToNow } from "date-fns";
import { enUS } from "date-fns/locale";
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import { ConfirmDialog } from "./ConfirmDialog";
import { getCommentUserColor } from "./hooks/useUserColors";
import { User } from "./types";

export interface CommentData {
  id: string;
  text: string;
  createdAt: string;
  highlightId: string | null;
  pageTop?: number;
  yPosition?: number;
  pageIndex?: number;
  parentId?: string;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  user: User;
  replies?: CommentData[];
  replyToUser?: User;
}

interface CommentBoxProps {
  comment: CommentData;
  highlightComment: (highlightId: string) => void;
  removeHighlight: () => void;
  onReply: (parentId: string, text: string, replyToUserId?: string, replyToUserName?: string, notifyAuthor?: boolean) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  onStartReply?: (commentId: string) => void;
  onFinishReply?: (commentId: string) => void;
  scale: number;
  isReply?: boolean;
  currentUser: User | null;
  depth?: number;
  isReplyingTo?: string | null;
  isCommentReplying?: (commentId: string) => boolean;
  paperId?: string;
}

export function CommentBox({
  comment,
  highlightComment,
  removeHighlight,
  onReply,
  onDelete,
  onStartReply,
  onFinishReply,
  scale,
  isReply = false,
  currentUser,
  depth = 0,
  isReplyingTo,
  isCommentReplying,
  paperId,
}: CommentBoxProps) {
  const [replyText, setReplyText] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [notifyAuthor, setNotifyAuthor] = useState(false);
  const [needLogin, setNeedLogin] = useState(false);

  // 检查当前用户是否是评论作者
  const isCommentOwner = currentUser?.id === comment.user.id;

  // 使用上层状态管理判断是否正在回复
  const isReplying = isCommentReplying ? isCommentReplying(comment.id) : false;

  // 构建登录后的回调URL
  const buildCallbackUrl = () => {
    if (!paperId) return '/account';

    // 构建基础URL，包含评论ID以便精确跳转
    const currentUrl = `/paper/${paperId}?commentId=${comment.id}`;
    const callbackUrl = encodeURIComponent(currentUrl);
    return `/account?callbackUrl=${callbackUrl}`;
  };

  // 获取用户名首字母作为头像Fallback
  const getInitials = (name: string | null) => {
    if (!name) return "?";
    return name.charAt(0).toUpperCase();
  };

  // 获取头像颜色（返回Hex颜色值）
  const getAvatarColor = (user: User) => {
    return getCommentUserColor(user);
  };

  // 处理提交回复
  const handleSubmitReply = async () => {
    if (!replyText.trim()) {
      console.warn("handleSubmitReply: reply text is empty");
      return;
    }

    // 检查登录状态
    if (!currentUser) {
      console.warn("handleSubmitReply: user not logged in");
      setNeedLogin(true);
      return;
    }

    console.log("handleSubmitReply: start submitting reply", {
      commentId: comment.id,
      replyText: replyText.substring(0, 50) + "...",
      parentId: comment.parentId,
      notifyAuthor
    });

    setIsSubmitting(true);
    try {
      console.log(`Submit reply to comment ID: ${comment.id}, content: ${replyText}`);

      // 找到真正的父评论ID（总是回复到顶级评论）
      const parentCommentId = comment.parentId || comment.id;
      console.log("handleSubmitReply: determine parent comment ID", {
        originalCommentId: comment.id,
        parentCommentId,
        commentParentId: comment.parentId
      });

      // 传递被回复人的信息
      console.log("handleSubmitReply: prepare to call onReply", {
        parentCommentId,
        replyText,
        userId: comment.user.id,
        userName: comment.user.name || "Anonymous user",
        notifyAuthor
      });

      await onReply(parentCommentId, replyText, comment.user.id, comment.user.name || "匿名用户", notifyAuthor);

      console.log("handleSubmitReply: onReply called successfully");

      // 清除输入并关闭回复框
      setReplyText("");
      setNotifyAuthor(false);

      // 回复完成，清除保护状态
      if (onFinishReply) {
        onFinishReply(comment.id);
        console.log("handleSubmitReply: cleared protection state");
      }

      // 给用户明确的反馈
      const successFeedback = document.createElement("div");
      successFeedback.textContent = "Reply sent";
      successFeedback.className = "text-green-500 text-xs mb-2 text-center";

      const replyContainer = document.querySelector(
        `[data-comment-id="${comment.id}"] .reply-container`
      );
      if (replyContainer) {
        replyContainer.appendChild(successFeedback);
        setTimeout(() => {
          successFeedback.remove();
        }, 2000);
        console.log("handleSubmitReply: displayed success feedback");
      } else {
        console.warn("handleSubmitReply: reply container not found");
      }

      console.log("handleSubmitReply: reply submission completed");
    } catch (error) {
      console.error("Reply failed:", error);
      console.error("Error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : "No stack trace",
        commentId: comment.id,
        replyText: replyText.substring(0, 100)
      });

      // 显示更详细的错误信息
      const errorMessage = error instanceof Error ? error.message : "Reply failed";
      alert(`Reply failed: ${errorMessage}\n\nPlease check your network connection and try again.`);

      // 即使出错也要清除保护状态
      if (onFinishReply) {
        onFinishReply(comment.id);
      }
    } finally {
      setIsSubmitting(false);
      console.log("handleSubmitReply: set isSubmitting to false");
    }
  };

  // 处理删除评论
  const handleDelete = async () => {
    try {
      await onDelete(comment.id);
      setIsConfirmOpen(false);
    } catch (error) {
      console.error("Delete comment failed:", error);
    }
  };

  // 打开确认对话框
  const openConfirmDialog = () => {
    if (!isCommentOwner) return;
    setIsConfirmOpen(true);
  };

  // 格式化时间
  const formatCommentTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true, locale: enUS });
    } catch (error) {
      return "Unknown time";
    }
  };

  // 解析评论文本，提取回复信息
  const parseCommentText = (text: string) => {
    const replyPattern = /^回复(.+?):\s*([\s\S]*)$/;
    const match = text.match(replyPattern);

    if (match) {
      return {
        isReply: true,
        replyToName: match[1],
        content: match[2]
      };
    }

    return {
      isReply: false,
      content: text
    };
  };

  const parsedComment = parseCommentText(comment.text);

  return (
    <div
      className={`bg-white rounded-md ${depth > 0 ? 'ml-4 mt-2 border-l-2 border-gray-100 pl-3' : ''}`}
      onMouseEnter={() => comment.highlightId && highlightComment(comment.highlightId)}
      onMouseLeave={removeHighlight}
    >
      <div className="flex items-start gap-3 p-3">
        {/* 用户头像 */}
        <Avatar className={`${isReply ? "h-6 w-6" : "h-8 w-8"} ${depth > 1 ? "h-5 w-5" : ""}`}>
          <AvatarImage src={comment.user.image || undefined} alt={comment.user.name || "用户"} />
          <AvatarFallback
            style={{
              backgroundColor: getAvatarColor(comment.user),
              color: '#ffffff'
            }}
          >
            {getInitials(comment.user.name)}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          {/* 头部信息 */}
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center">
              <span className={`font-medium ${depth > 0 ? "text-xs" : "text-sm"}`}>
                {comment.user.name || "Anonymous user"}
              </span>
              <span className={`text-gray-500 ml-2 ${depth > 0 ? "text-xs" : "text-xs"}`}>
                {formatCommentTime(comment.createdAt)}
              </span>
            </div>

            {/* 删除按钮 - 仅对评论作者显示 */}
            {isCommentOwner && (
              <button
                onClick={openConfirmDialog}
                className="text-gray-400 hover:text-gray-600 text-xs"
                aria-label="Delete comment"
              >
                ×
              </button>
            )}
          </div>

          {/* 评论内容 */}
          <div className={`text-gray-700 whitespace-pre-wrap break-words ${depth > 0 ? "text-xs" : "text-sm"}`}>
            {parsedComment.isReply ? (
              <>
                <span className="text-blue-600 font-medium">Reply to {parsedComment.replyToName}</span>
                <span className="text-gray-500">: </span>
                <span>{parsedComment.content}</span>
              </>
            ) : (
              parsedComment.content
            )}
          </div>

          {/* 回复按钮 - 现在所有评论都可以回复，但限制嵌套深度 */}
          {depth < 3 && (
            <button
              onClick={() => {
                if (isReplying) {
                  // 结束回复
                  if (onFinishReply) {
                    onFinishReply(comment.id);
                  }
                  setReplyText("");
                  setNotifyAuthor(false);
                  setNeedLogin(false); // 重置登录提示
                } else {
                  // 检查是否登录
                  if (!currentUser) {
                    console.log("User not logged in, display login prompt");
                    setNeedLogin(true);
                    // 仍然需要开始回复状态以显示回复框
                    if (onStartReply) {
                      onStartReply(comment.id);
                    }
                  } else {
                    // 已登录，正常开始回复
                    setNeedLogin(false);
                    if (onStartReply) {
                      onStartReply(comment.id);
                    }
                  }
                }
              }}
              className={`text-blue-500 mt-2 hover:underline ${depth > 0 ? "text-xs" : "text-xs"}`}
            >
              {isReplying ? "Cancel reply" : "Reply"}
            </button>
          )}
        </div>
      </div>

      {/* 回复框 */}
      {isReplying && (
        <div className={`${depth > 0 ? "pl-6" : "pl-11"} pr-3 pb-3 reply-container`} data-comment-id={comment.id}>
          <div className="border rounded-md overflow-hidden">
            <textarea
              value={replyText}
              onChange={e => setReplyText(e.target.value)}
              placeholder={needLogin ? "Please login first to reply" : `Reply to ${comment.user.name || "Anonymous user"}...`}
              className={`w-full p-2 focus:outline-none resize-none ${depth > 0 ? "text-xs" : "text-sm"}`}
              rows={2}
              disabled={isSubmitting || needLogin}
            />
            <div className="flex justify-end bg-gray-50 px-2 py-1">
              {!needLogin && (
                <div className="flex items-center mr-auto">
                  <Checkbox
                    id={`notify-author-${comment.id}`}
                    checked={notifyAuthor}
                    onCheckedChange={(checked) => setNotifyAuthor(checked === true)}
                    disabled={isSubmitting}
                    className="mr-2"
                  />
                  <label
                    htmlFor={`notify-author-${comment.id}`}
                    className={`select-none cursor-pointer ${depth > 0 ? "text-xs" : "text-xs"} text-gray-600`}
                  >
                    Notify Author
                  </label>
                </div>
              )}
              <button
                onClick={() => {
                  setReplyText("");
                  setNotifyAuthor(false); // 重置状态
                  setNeedLogin(false); // 重置登录状态
                  // 取消回复时，清除保护状态
                  if (onFinishReply) {
                    onFinishReply(comment.id);
                  }
                }}
                className="text-xs text-gray-500 px-2 py-1 rounded hover:bg-gray-200 mr-2"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              {needLogin ? (
                <a
                  href={buildCallbackUrl()}
                  className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 inline-block"
                >
                  Go to login
                </a>
              ) : (
                <button
                  onClick={handleSubmitReply}
                  className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
                  disabled={!replyText.trim() || isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Reply"}
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 显示子回复 */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="replies-container" data-parent-id={comment.id}>
          <div
            className={comment.replies.length > 5 ? 'overflow-y-auto border border-gray-200 rounded-md p-2' : ''}
            style={comment.replies.length > 5 ? {
              maxHeight: '30rem', // 约5个回复的高度
              scrollbarWidth: 'thin',
              scrollbarColor: '#cbd5e1 #f8fafc'
            } : {}}
          >
            {comment.replies.map(reply => (
              <CommentBox
                key={reply.id}
                comment={reply}
                highlightComment={highlightComment}
                removeHighlight={removeHighlight}
                onReply={onReply}
                onDelete={onDelete}
                onStartReply={onStartReply}
                onFinishReply={onFinishReply}
                scale={scale}
                isReply={true}
                currentUser={currentUser}
                depth={depth + 1}
                isReplyingTo={isReplyingTo}
                isCommentReplying={isCommentReplying}
                paperId={paperId}
              />
            ))}
          </div>
        </div>
      )}

      {/* 确认删除对话框 */}
      <ConfirmDialog
        isOpen={isConfirmOpen}
        title="Delete comment"
        message={
          comment.replies && comment.replies.length > 0
            ? "Deleting this comment will also delete all replies. Are you sure you want to continue?"
            : "Are you sure you want to delete this comment?"
        }
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDelete}
        onCancel={() => setIsConfirmOpen(false)}
      />
    </div>
  );
}
