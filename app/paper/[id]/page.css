/* 导入共享的页面排版样式 */
@import "../../shared/page-layout.css";
@import "tailwindcss";

@layer base {
  * {
    border-color: var(--border, oklch(0.928 0.006 264.531));
  }
}

/* paper页面特有的打印样式扩展 */
@media print {
  /* 基本页面设置 */
  body {
    margin: 0 !important;
    padding: 0 !important; /* 消除所有padding */
  }

  /* 隐藏header和工具栏 */
  header,
  nav,
  .preview-toolbar,
  .site-header,
  .nav-container {
    display: none !important;
  }

  /* 将header空间压缩为0 */
  .site-header,
  header,
  nav {
    height: 0 !important;
    min-height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 0 !important;
  }

  /* 处理页面和容器样式 */
  .h-screen,
  .paged-preview {
    height: auto !important;
    overflow: visible !important;
    padding: 0 !important;
    margin: 0 !important;
    background: none !important;
  }

  .pages-container {
    transform: scale(1) !important; /* 强制重置缩放 */
  }

  .page {
    margin: 0 !important;
    border: none !important;
    box-shadow: none !important;
  }

  /* 打印时隐藏UI元素 */
  .preview-toolbar,
  .comment-sidebar,
  .comment-highlight,
  .comment-controls,
  .loading-overlay {
    display: none !important;
  }

  /* 确保打印时高亮区域完全透明，不影响文本显示 */
  .highlight-overlay {
    background: transparent !important;
    box-shadow: none !important;
    opacity: 0 !important;
    display: none !important; /* 完全隐藏高亮元素 */
  }
}

@layer utilities {
  /* paper页面特有的.page样式扩展 */
  .page {
    margin-bottom: 20mm; /* 确保页面间有足够间距 */
    min-height: 297mm; /* 确保即使内容少也保持A4高度 */
  }

  /* paper页面特有的全局样式 */
  html,
  body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: auto;
  }

  .paged-preview {
    background:
      radial-gradient(129% 99% at 112% 85%, rgb(115, 115, 115) 20%, rgb(78, 78, 78) 90%),
      url("https://assets.codepen.io/16327/noise-e82662fe.png");
    background-blend-mode: color-dodge;
    min-height: 100vh;
    width: 100%;
    overflow: auto;
    padding: 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    scroll-behavior: auto; /* 确保滚动立即生效 */
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }

  .pages-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: fit-content;
    min-height: 100%;
    transform-origin: top center;
    margin: 0 auto;
    height: fit-content;
    will-change: transform;
    backface-visibility: hidden;
    -webkit-font-smoothing: subpixel-antialiased;
    transition: none !important; /* 确保没有过渡效果 */
  }

  .html-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: none !important; /* 从opacity 0.3s改为none */
    overflow: visible; /* 消除嵌套滚动 */
  }

  .html-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* 添加加载样式 */
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    font-size: 1.25rem;
    color: #666;
  }

  /* 更新工具栏样式，与review和editor界面一致 */
  .preview-toolbar {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    z-index: 40;
    padding: 4px;
    align-items: center;
  }

  .toolbar-button {
    padding: 8px;
    margin: 0 2px;
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: #666;
    transition: all 0.2s ease;
  }

  .toolbar-button:hover {
    background-color: #f3f4f6;
    color: #333;
  }

  .toolbar-button.active {
    background-color: #ffffff;
    color: var(--color-brand-500);
  }

  .toolbar-button svg {
    width: 18px;
    height: 18px;
  }

  .toolbar-divider {
    width: 1px;
    height: 24px;
    background-color: #e5e7eb;
    margin: 0 6px;
  }

  /* 修改高亮样式 */
  .highlight-overlay {
    position: absolute;
    /* 添加CSS变量用于自定义渐变颜色 */
    --highlight-from: rgba(111, 141, 255, 0.4);
    --highlight-to: rgba(255, 200, 100, 0.2);
    /* 使用从左到右的线性渐变替换单色背景 */
    background: linear-gradient(to right, var(--highlight-from), var(--highlight-to));
    pointer-events: auto;
    z-index: 10;
    cursor: pointer;
    border-radius: 2px;
    /* 只使用 opacity 过渡 */
    transition: opacity 0.3s ease-out;
    /* 为激活状态准备叠加层 */
    position: relative;
    /* 初始状态：隐藏 */
    opacity: 0;
  }

  /* 文本选择模式下的高亮样式 */
  .highlight-overlay[data-selection-mode="true"] {
    /* 在文本选择模式下，高亮层变得半透明且带有虚线边框 */
    opacity: 0.8 !important;
    filter: grayscale(0.5);
    cursor: text; /* 显示文本选择光标 */
    /* 添加缓慢的脉冲动画提示用户可以穿透选择 */
    animation: selectionModePulse 2s ease-in-out infinite;
  }

  /* 临时高亮样式 - 用于显示当前正在评论的文本选择 */
  .highlight-overlay.temporary {
    /* 使用不同的渐变色调以区别于正式高亮 */
    --highlight-from: rgba(255, 193, 7, 0.5);
    --highlight-to: rgba(255, 152, 0, 0.3);
    background: linear-gradient(to right, var(--highlight-from), var(--highlight-to));
    /* 稍微提高z-index确保在其他高亮之上 */
    z-index: 15;
    /* 添加轻微的阴影效果 */
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
    /* 添加脉冲动画 */
    animation: temporaryHighlightPulse 1.5s ease-in-out infinite;
  }

  /* 临时高亮的脉冲动画 */
  @keyframes temporaryHighlightPulse {
    0%, 100% {
      opacity: 0.7;
      transform: scale(1);
    }
    50% {
      opacity: 0.9;
      transform: scale(1.002);
    }
  }

  /* 文本选择模式的脉冲动画 */
  @keyframes selectionModePulse {
    0%, 100% {
      opacity: 0.8;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.005);
    }
  }

  /* 高亮显示状态 */
  .highlight-overlay.visible {
    opacity: 1;
  }

  /* 高亮隐藏状态 */
  .highlight-overlay.hidden {
    opacity: 0;
  }

  .highlight-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, rgba(18, 25, 246, 0.4), rgba(255, 180, 50, 0.4));
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease-out;
    pointer-events: none;
  }

  .highlight-overlay.active {
    box-shadow: 0 0 0 2px rgba(255, 200, 0, 0.3);
    transform: scale(1.01);
    /* 增强基础背景的饱和度 */
    filter: saturate(1.2) brightness(1.1);
  }

  .highlight-overlay.active::before {
    opacity: 1;
  }
}

.comment-highlight {
  /* 修改评论高亮也使用相同的渐变风格 */
  --highlight-from: rgba(255, 255, 150, 0.4);
  --highlight-to: rgba(255, 200, 100, 0.2);
  background: linear-gradient(to right, var(--highlight-from), var(--highlight-to));
  position: relative;
  z-index: 1;
  transition: none !important;
}

.comment-highlight.opacity-0 {
  background: transparent;
}

/* 分页预览样式 */
.paged-preview {
  position: relative;
  overflow: auto;
}

.pages-container {
  padding: 20px;
  width: fit-content;
  margin: 0 auto;
}

/* 评论系统样式 */

.comment-sidebar {
  pointer-events: auto;
}

.comment-controls {
  transition: none !important;
}

/* 高亮互动效果 */
.comment-box {
  width: 350px; /* 固定宽度 */
  box-sizing: border-box;
  white-space: normal;
  overflow-wrap: break-word;
  background-color: white;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease,
    background-color 0.2s ease;
}

.comment-box.active {
  background-color: #fef9c3 !important;
  z-index: 100 !important;
  /* Remove transform to prevent layout shift */
  transform: none !important;
}

/* 新增：评论圆点样式 */
.comment-container {
  position: absolute;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  flex-direction: row;
  height: auto;
  /* 确保容器不会阻挡其他元素的交互 */
  pointer-events: none;
  transition: opacity 0.3s ease-out;
}

.comment-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  cursor: pointer;
  z-index: 50;
  box-shadow:
    0 1px 2px rgba(0, 0, 0, 0.2),
    inset 0 0 0 1px rgba(255, 255, 255, 0.35);
  transition: opacity 0.3s ease-out;
  flex-shrink: 0;
  user-select: none; /* 防止文本选择 */
  -webkit-tap-highlight-color: transparent; /* 移动设备点击不高亮 */
  pointer-events: auto; /* 确保圆点始终可以接收鼠标事件 */
  position: relative; /* 添加相对定位 */
  /* 初始状态：隐藏 */
  opacity: 0;
}

/* 圆点显示状态 */
.comment-dot.visible {
  opacity: 0.95;
}

/* 圆点隐藏状态 */
.comment-dot.hidden {
  opacity: 0;
}

.comment-dot:hover,
.comment-dot.expanded {
  opacity: 1;
}

/* 触摸设备的反馈 */
@media (hover: none) {
  .comment-dot:active {
    opacity: 1;
  }
}

/* 增加圆点的可点击区域 */
.comment-dot:before {
  content: "";
  position: absolute;
  top: -8px;
  right: -8px;
  bottom: -8px;
  left: -8px;
  /* 不显示，只是扩大点击区域 */
  opacity: 0;
}

/* 修改评论框包装器样式，避免文字压缩 */
.comment-box-wrapper {
  position: relative;
  overflow: hidden;
  width: 0;
  margin-right: 8px; /* 右侧间距，和圆点隔开 */
  opacity: 0;
  pointer-events: none; /* 非展开状态不接收鼠标事件 */
  transition:
    width 0.3s ease-out,
    opacity 0.3s ease-out;
}

.comment-box-wrapper.expanded {
  width: 350px;
  opacity: 1;
  pointer-events: auto; /* 展开状态接收鼠标事件 */
}

/* 文档容器样式 */
.paper-container {
  min-height: 100px;
  overflow: visible;
  position: relative;
  will-change: height;
  transition: none !important; /* 确保没有过渡效果 */
  justify-content: center; /* 默认居中，会被JavaScript动态调整 */
}

/* 自适应缩放的媒体查询 */
@media screen and (max-width: 768px) {
  /* 移动设备优化 */
  .preview-toolbar {
    bottom: 10px;
    padding: 2px;
  }

  .toolbar-button {
    padding: 6px;
  }

  .toolbar-button svg {
    width: 16px;
    height: 16px;
  }

  .pages-container {
    padding: 10px;
  }

  .comment-dot {
    width: 12px;
    height: 12px;
  }
}

/* 添加划词评论输入框样式 */
.selection-comment-input {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: white;
  overflow: hidden;
}

.comment-box-wrapper.expanded {
  max-height: 1000px; /* 足够大的值以适应内容 */
  opacity: 1;
}


@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* 批量显示动画类 */
.highlight-overlay.animate-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.comment-dot.animate-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* 批量隐藏动画类 */
.highlight-overlay.animate-out {
  animation: fadeOut 0.3s ease-out forwards;
}

.comment-dot.animate-out {
  animation: fadeOut 0.3s ease-out forwards;
}
