import { CommentData, ExtendedCommentData, User } from '../types';
import type { User as SupabaseUser } from '@supabase/supabase-js';

/**
 * 计算最佳缩放比例
 */
export function calculateOptimalScale(
  containerElement: HTMLElement | null,
  pageElement: HTMLElement | null
): number {
  if (!containerElement || !pageElement) return 1;

  const containerWidth = containerElement.clientWidth - 40; // 减去边距
  const containerHeight = containerElement.clientHeight - 40;
  const pageWidth = pageElement.offsetWidth;
  const pageHeight = pageElement.offsetHeight;

  if (containerWidth <= 0 || containerHeight <= 0 || pageWidth <= 0 || pageHeight <= 0) {
    return 1;
  }

  const widthScale = containerWidth / pageWidth;
  const heightScale = containerHeight / pageHeight;

  // 取较小值确保页面能完整显示，并限制最小缩放比例为0.5，最大为1.0
  return Math.max(0.5, Math.min(Math.min(widthScale, heightScale), 1.0));
}

/**
 * 检查内容是否超出容器宽度
 */
export function checkContentOverflow(
  scale: number,
  containerElement: HTMLElement | null,
  pageElement: HTMLElement | null
): boolean {
  if (!containerElement || !pageElement) return false;

  const pageOriginalWidth = pageElement.offsetWidth;
  const scaledPageWidth = pageOriginalWidth * scale;
  const containerWidth = containerElement.clientWidth - 40; // 40px为左右padding

  return scaledPageWidth > containerWidth;
}

/**
 * 获取页面信息（索引和顶部位置）
 * 与原始实现完全一致
 */
export function getPageInfo(
  node: Node | null,
  containerRef: React.RefObject<HTMLDivElement | null>,
  scale: number
): { pageIndex: number; pageTop: number } {
  if (!node || !containerRef.current) return { pageIndex: -1, pageTop: 0 };

  let element = node.nodeType === Node.TEXT_NODE ? node.parentElement : (node as Element);

  // 向上查找.page元素
  while (element && !element.classList.contains("page")) {
    element = element.parentElement;
  }

  if (!element) return { pageIndex: -1, pageTop: 0 };

  // 获取所有页面并找出当前页面的索引
  const pages = containerRef.current.querySelectorAll(".page");
  const pageIndex = Array.from(pages).indexOf(element as Element);

  // 计算页面顶部相对于容器的位置 - 与原始代码完全一致
  const containerRect = containerRef.current.getBoundingClientRect();
  const pageRect = element.getBoundingClientRect();
  const pageTop = (pageRect.top - containerRect.top) / scale;

  return { pageIndex, pageTop };
}

/**
 * 处理评论分组
 */
export function processCommentGroups(comments: CommentData[]): ExtendedCommentData[] {
  // 先找出所有顶级评论（非回复）
  const topComments = comments.filter(c => !c.parentId);

  // 按照pageIndex和y位置进行分组
  const commentGroups: Record<string, ExtendedCommentData[]> = {};

  topComments.forEach(comment => {
    // 如果没有位置信息，跳过
    if (comment.pageIndex === undefined || comment.yPosition === undefined) return;

    // 创建分组键：页码_Y坐标（取整到5像素）
    const groupKey = `${comment.pageIndex}_${Math.round(comment.yPosition / 5) * 5}`;

    if (!commentGroups[groupKey]) {
      commentGroups[groupKey] = [];
    }

    commentGroups[groupKey].push(comment as ExtendedCommentData);
  });

  // 处理每个分组
  Object.entries(commentGroups).forEach(([groupKey, groupComments]) => {
    if (groupComments.length <= 1) return;

    // 按创建时间排序，较早的评论在前
    groupComments.sort((a, b) => {
      const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return timeA - timeB;
    });

    // 更新每条评论的分组信息
    groupComments.forEach((comment, index) => {
      comment.groupIndex = index;
      comment.groupTotal = groupComments.length;
      comment.groupId = groupKey;
    });
  });

  return comments as ExtendedCommentData[];
}

/**
 * 生成唯一的高亮ID
 */
export function generateHighlightId(): string {
  return `highlight-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
}

/**
 * 将 Supabase Auth User 转换为我们的 User 类型
 */
export function adaptSupabaseUser(supabaseUser: SupabaseUser | null): User | null {
  if (!supabaseUser) return null;

  return {
    id: supabaseUser.id,
    name: supabaseUser.user_metadata?.name || supabaseUser.email?.split("@")[0] || null,
    email: supabaseUser.email || '',
    emailVerified: supabaseUser.email_confirmed_at ? new Date(supabaseUser.email_confirmed_at) : null,
    password: null, // 不从 Auth User 中获取密码
    image: supabaseUser.user_metadata?.avatar_url || null,
    createdAt: new Date(supabaseUser.created_at),
    avatarColor: supabaseUser.user_metadata?.avatarColor || null,
    aiLastReqTime: null, // 这些字段需要从数据库获取
    aiReqRest: 0,
    aiReqTotal: 0,
    aiModel: null,
  };
}

/**
 * 获取用户名首字母
 */
export function getInitials(name: string | null): string {
  if (!name) return "?";
  return name.charAt(0).toUpperCase();
}

/**
 * 解析评论文本，提取回复信息
 */
export function parseCommentText(text: string) {
  const replyPattern = /^回复(.+?):\s*([\s\S]*)$/;
  const match = text.match(replyPattern);

  if (match) {
    return {
      isReply: true,
      replyToName: match[1],
      content: match[2]
    };
  }

  return {
    isReply: false,
    content: text
  };
}

/**
 * 构建登录回调URL
 */
export function buildCallbackUrl(paperId: string, commentId?: string): string {
  const baseUrl = `/paper/${paperId}`;
  const urlWithComment = commentId ? `${baseUrl}?commentId=${commentId}` : baseUrl;
  const callbackUrl = encodeURIComponent(urlWithComment);
  return `/account?callbackUrl=${callbackUrl}`;
}

/**
 * 查找顶级评论ID
 */
export function getTopLevelCommentId(
  commentId: string,
  comments: ExtendedCommentData[]
): string {
  const findInComments = (comments: ExtendedCommentData[], targetId: string): ExtendedCommentData | null => {
    for (const comment of comments) {
      if (comment.id === targetId) {
        return comment;
      }
      if (comment.replies) {
        const found = findInComments(comment.replies, targetId);
        if (found) return found;
      }
    }
    return null;
  };

  const comment = findInComments(comments, commentId);
  if (comment?.parentId) {
    // 如果有parentId，递归查找顶级评论
    return getTopLevelCommentId(comment.parentId, comments);
  }
  return commentId; // 没有parentId，说明就是顶级评论
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
