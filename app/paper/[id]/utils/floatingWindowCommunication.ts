/**
 * 浮窗通信工具 - 用于 paper 页面与 terminal 的通信
 * 遵循现有的浮窗通信协议，不破坏原有的命令管理器和通讯管理器
 */

interface TerminalCommandMessage {
  id: string;
  type: 'TERMINAL_COMMAND';
  timestamp: number;
  windowId?: string;
  source: 'window';
  payload: {
    command: string;
    params?: Record<string, any>;
    expectResponse: boolean;
  };
}

// 全局变量存储当前窗口ID
let currentWindowId: string | null = null;
let windowIdPromise: Promise<string> | null = null;

/**
 * 检查是否在浮窗环境中
 */
export function isInFloatingWindow(): boolean {
  try {
    return window.parent && window.parent !== window;
  } catch {
    return false;
  }
}

/**
 * 获取当前窗口的 windowId
 * 通过监听 WINDOW_INIT 消息来获取
 */
export function getCurrentWindowId(): Promise<string> {
  // 如果已经有 windowId，直接返回
  if (currentWindowId) {
    return Promise.resolve(currentWindowId);
  }

  // 如果正在获取中，返回现有的 Promise
  if (windowIdPromise) {
    return windowIdPromise;
  }

  // 创建新的 Promise 来获取 windowId
  windowIdPromise = new Promise((resolve, reject) => {
    if (!isInFloatingWindow()) {
      reject(new Error('不在浮窗环境中'));
      return;
    }

    // 设置超时
    const timeout = setTimeout(() => {
      cleanup();
      reject(new Error('获取 windowId 超时'));
    }, 5000);

    // 监听 WINDOW_INIT 消息
    const messageHandler = (event: MessageEvent) => {
      if (event.source === window.parent && event.data?.type === 'WINDOW_INIT') {
        const windowId = event.data.windowId || event.data.payload?.windowInfo?.id;
        if (windowId) {
          currentWindowId = windowId;
          cleanup();
          resolve(windowId);
        }
      }
    };

    const cleanup = () => {
      clearTimeout(timeout);
      window.removeEventListener('message', messageHandler);
      windowIdPromise = null;
    };

    window.addEventListener('message', messageHandler);

    // 如果页面已经加载完成，可能已错过 WINDOW_INIT 消息
    // 尝试从 URL 参数或其他方式获取
    const urlParams = new URLSearchParams(window.location.search);
    const urlWindowId = urlParams.get('windowId');
    if (urlWindowId) {
      currentWindowId = urlWindowId;
      cleanup();
      resolve(urlWindowId);
      return;
    }

    // 请求父窗口发送窗口信息
    try {
      window.parent.postMessage({
        type: 'REQUEST_WINDOW_INFO',
        timestamp: Date.now()
      }, '*');
    } catch (error) {
      console.warn('[FloatingWindowCommunication] 无法请求窗口信息:', error);
    }
  });

  return windowIdPromise;
}

/**
 * 向 terminal 发送命令
 * 使用与现有系统一致的 postMessage 协议
 */
export async function sendTerminalCommand(command: string, params?: Record<string, any>): Promise<boolean> {
  if (!isInFloatingWindow()) {
    console.warn('[FloatingWindowCommunication] 不在浮窗环境中，无法发送命令');
    return false;
  }

  try {
    // 获取当前窗口ID
    let windowId: string | undefined;
    try {
      windowId = await getCurrentWindowId();
    } catch (error) {
      console.warn('[FloatingWindowCommunication] 无法获取 windowId，使用 undefined:', error);
    }

    const messageId = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const message: TerminalCommandMessage = {
      id: messageId,
      type: 'TERMINAL_COMMAND',
      timestamp: Date.now(),
      windowId,
      source: 'window',
      payload: {
        command,
        params,
        expectResponse: true
      }
    };

    console.log(`[FloatingWindowCommunication] 发送命令到 terminal: ${command}`, windowId ? `(windowId: ${windowId})` : '(无 windowId)');

    window.parent!.postMessage(message, '*');

    return true;
  } catch (error) {
    console.error('[FloatingWindowCommunication] 发送命令失败:', error);
    return false;
  }
}

/**
 * 打开账户登录浮窗
 * 使用与 terminal menu 中 login 按钮相同的逻辑
 */
export async function openAccountWindow(): Promise<boolean> {
  const command = 'create_window type=path target=/account width=0.5 height=0.5 x=0.25 y=0.25 title=ACCOUNT';
  return await sendTerminalCommand(command);
}

/**
 * 关闭菜单（如果需要的话）
 */
export async function closeMenu(): Promise<boolean> {
  const command = 'menu action=toggle';
  return await sendTerminalCommand(command);
}

/**
 * 关闭当前窗口
 * 使用正确的 close_window 命令格式
 */
export async function closeCurrentWindow(): Promise<boolean> {
  try {
    const windowId = await getCurrentWindowId();
    const command = `close_window windowId=${windowId}`;
    return await sendTerminalCommand(command);
  } catch (error) {
    console.error('[FloatingWindowCommunication] 关闭窗口失败:', error);
    return false;
  }
}

/**
 * 组合操作：打开账户窗口并关闭菜单
 * 模拟 terminal menu 中 login 按钮的完整行为
 */
export async function openAccountWindowAndCloseMenu(): Promise<boolean> {
  try {
    // 先打开账户窗口
    const accountResult = await openAccountWindow();

    if (accountResult) {
      // 如果账户窗口打开成功，则关闭菜单
      await closeMenu();
      return true;
    }

    return false;
  } catch (error) {
    console.error('[FloatingWindowCommunication] 打开账户窗口失败:', error);
    return false;
  }
}

/**
 * 监听来自 terminal 的消息（如果需要的话）
 */
export function listenToTerminalMessages(callback: (message: any) => void): () => void {
  const messageHandler = (event: MessageEvent) => {
    // 只处理来自父窗口的消息
    if (event.source === window.parent) {
      callback(event.data);
    }
  };

  window.addEventListener('message', messageHandler);
  
  // 返回清理函数
  return () => {
    window.removeEventListener('message', messageHandler);
  };
}

/**
 * 检查用户是否已登录
 * 这个函数可以在需要时扩展，目前主要用于判断是否需要打开登录窗口
 */
export function shouldOpenLoginWindow(user: any): boolean {
  return !user || !user.id;
}
