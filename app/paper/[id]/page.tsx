import { Suspense } from "react";

import PaperViewer from "./components/PaperViewer";

// 这个页面是Server Component
export default async function PaperPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const paperId = id;

  return (
    <div className="h-screen w-full flex flex-col paged-preview overflow-auto transition-opacity duration-700 ease-out opacity-100">
      <Suspense
        fallback={
          <div className="text-center p-10 transition-all duration-500 ease-out">
            <div className="flex flex-col items-center opacity-100 translate-y-0">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-500 mb-3"></div>
              <div className="text-lg">Loading...</div>
              <div className="text-sm text-gray-500 mt-1">Preparing paper preview</div>
            </div>
          </div>
        }
      >
        <PaperViewer paperId={paperId} />
      </Suspense>
    </div>
  );
}
