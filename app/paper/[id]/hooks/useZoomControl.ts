import { useState, useEffect, useCallback, useRef } from 'react';
import { ZoomState } from '../types';
import { calculateOptimalScale, checkContentOverflow, debounce } from '../utils';

interface UseZoomControlProps {
  containerRef: React.RefObject<HTMLDivElement | null>;
  wrapperRef: React.RefObject<HTMLDivElement | null>;
  previewContainerRef: React.RefObject<HTMLElement | null>;
  isLoading: boolean;
}

export function useZoomControl({
  containerRef,
  wrapperRef,
  previewContainerRef,
  isLoading
}: UseZoomControlProps) {
  const [zoomState, setZoomState] = useState<ZoomState>({
    scale: 1,
    isOverflow: false,
    layoutMode: 'center'
  });

  // 应用布局模式
  const applyLayoutMode = useCallback((isOverflow: boolean, newScale: number, containerElement?: HTMLElement) => {
    const previewContainer = containerElement || previewContainerRef.current;
    if (!previewContainer || !containerRef.current || !wrapperRef.current) return;

    if (isOverflow) {
      // 内容超出容器：左对齐模式
      wrapperRef.current.style.justifyContent = 'flex-start';
      containerRef.current.style.transformOrigin = 'top left';
    } else {
      // 内容未超出容器：居中模式
      wrapperRef.current.style.justifyContent = 'center';
      containerRef.current.style.transformOrigin = 'top center';
    }

    containerRef.current.style.transform = `scale(${newScale})`;
  }, [containerRef, wrapperRef, previewContainerRef]);

  // 调整容器高度以适应缩放
  const adjustContainerHeight = useCallback((newScale?: number) => {
    const currentScale = newScale || zoomState.scale;

    if (!containerRef.current || !wrapperRef.current) return;

    const pages = containerRef.current.querySelectorAll(".page");
    if (pages.length === 0) return;

    const firstPage = pages[0] as HTMLElement;
    const pageHeight = firstPage.offsetHeight;

    // 计算页面间距总和
    let totalMargin = 0;
    if (pages.length > 1) {
      const style = window.getComputedStyle(firstPage);
      const marginBottom = parseInt(style.marginBottom || "0");
      totalMargin = (pages.length - 1) * marginBottom;
    }

    const totalHeight = pageHeight * pages.length + totalMargin;
    wrapperRef.current.style.height = `${totalHeight * currentScale + 40}px`;
  }, [containerRef, wrapperRef, zoomState.scale]);

  // 智能缩放函数
  const zoomWithFocus = useCallback((newScale: number) => {
    if (newScale === zoomState.scale) {
      return;
    }

    const previewContainer = previewContainerRef.current;
    if (!previewContainer || !containerRef.current) {
      return;
    }

    // 保存当前滚动位置和视口信息
    const viewportWidth = previewContainer.clientWidth;
    const viewportHeight = previewContainer.clientHeight;
    const scrollTop = previewContainer.scrollTop;
    const scrollLeft = previewContainer.scrollLeft;

    // 检查新缩放级别下内容是否会超出容器
    const pageElement = containerRef.current.querySelector(".page") as HTMLElement;
    const willOverflow = checkContentOverflow(newScale, previewContainer, pageElement);

    // 应用新的布局模式
    applyLayoutMode(willOverflow, newScale, previewContainer);

    // 根据布局模式调整滚动位置
    if (!willOverflow) {
      // 居中模式：保持垂直滚动，重置水平滚动
      const centerY = (scrollTop + viewportHeight / 2) / zoomState.scale;
      const newScrollTop = centerY * newScale - viewportHeight / 2;
      previewContainer.scrollTop = Math.max(0, newScrollTop);
      previewContainer.scrollLeft = 0;
    } else {
      // 左对齐模式：保持视觉中心点
      const centerX = (scrollLeft + viewportWidth / 2) / zoomState.scale;
      const centerY = (scrollTop + viewportHeight / 2) / zoomState.scale;

      const newScrollLeft = centerX * newScale - viewportWidth / 2;
      const newScrollTop = centerY * newScale - viewportHeight / 2;

      previewContainer.scrollLeft = Math.max(0, newScrollLeft);
      previewContainer.scrollTop = Math.max(0, newScrollTop);
    }

    // 更新状态
    setZoomState({
      scale: newScale,
      isOverflow: willOverflow,
      layoutMode: willOverflow ? 'left' : 'center'
    });

    // 立即调整容器高度
    adjustContainerHeight(newScale);
  }, [zoomState.scale, previewContainerRef, containerRef, applyLayoutMode, adjustContainerHeight]);

  // 缩放控制函数
  const zoomIn = useCallback(() => {
    const newScale = Math.min(zoomState.scale + 0.1, 2.0);
    zoomWithFocus(newScale);
  }, [zoomState.scale, zoomWithFocus]);

  const zoomOut = useCallback(() => {
    const newScale = Math.max(zoomState.scale - 0.1, 0.5);
    zoomWithFocus(newScale);
  }, [zoomState.scale, zoomWithFocus]);

  const resetZoom = useCallback(() => {
    const previewContainer = previewContainerRef.current;
    const pageElement = containerRef.current?.querySelector(".page") as HTMLElement;
    const newScale = calculateOptimalScale(previewContainer, pageElement);
    zoomWithFocus(newScale);
  }, [previewContainerRef, containerRef, zoomWithFocus]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = debounce(() => {
      const previewContainer = previewContainerRef.current;
      const pageElement = containerRef.current?.querySelector(".page") as HTMLElement;
      const newScale = calculateOptimalScale(previewContainer, pageElement);

      if (newScale > 0) {
        zoomWithFocus(newScale);
      } else {
        // 即使没有缩放变化，也要检查布局模式
        if (previewContainer && pageElement) {
          const isOverflow = checkContentOverflow(zoomState.scale, previewContainer, pageElement);
          applyLayoutMode(isOverflow, zoomState.scale, previewContainer);
        }
      }
    }, 100);

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [zoomState.scale]); // 移除函数依赖项，避免无限循环

  // 监听缩放比例变化，调整容器高度
  useEffect(() => {
    if (isLoading) return;

    const timer = setTimeout(() => {
      adjustContainerHeight();
    }, 150);

    return () => clearTimeout(timer);
  }, [zoomState.scale, isLoading]); // 移除函数依赖项

  // 初始化最佳缩放
  useEffect(() => {
    if (isLoading || !containerRef.current) return;

    const timer = setTimeout(() => {
      const previewContainer = previewContainerRef.current;
      const pageElement = containerRef.current?.querySelector(".page") as HTMLElement;
      const newScale = calculateOptimalScale(previewContainer, pageElement);

      if (newScale > 0) {
        zoomWithFocus(newScale);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [isLoading]); // 只依赖 isLoading，避免无限循环

  return {
    scale: zoomState.scale,
    isOverflow: zoomState.isOverflow,
    layoutMode: zoomState.layoutMode,
    zoomIn,
    zoomOut,
    resetZoom,
    zoomWithFocus,
  };
}
