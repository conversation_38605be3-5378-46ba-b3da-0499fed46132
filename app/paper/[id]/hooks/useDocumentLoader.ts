import { useState, useEffect, useCallback, useRef } from 'react';
import { DocumentState } from '../types';

interface UseDocumentLoaderProps {
  htmlUrl: string;
  containerRef: React.RefObject<HTMLDivElement | null>;
  onContentLoaded?: () => void;
}

export function useDocumentLoader({ 
  htmlUrl, 
  containerRef, 
  onContentLoaded 
}: UseDocumentLoaderProps) {
  const [state, setState] = useState<DocumentState>({
    isLoading: true,
    isContentVisible: false,
    isToolbarVisible: false,
    visiblePages: new Set(),
    error: null,
  });

  const [paperId, setPaperId] = useState<string>("");

  // 使用 useRef 来存储回调函数，避免依赖项变化
  const onContentLoadedRef = useRef(onContentLoaded);
  onContentLoadedRef.current = onContentLoaded;

  const loadDocument = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // 从API获取已分页的HTML内容
      const response = await fetch(htmlUrl);

      if (!response.ok) {
        try {
          const errorData = await response.json();
          throw new Error(errorData.error || `获取失败，状态码: ${response.status}`);
        } catch (parseError) {
          throw new Error(`获取失败，状态码: ${response.status}`);
        }
      }

      const contentType = response.headers.get("content-type") || "";
      if (contentType.includes("application/json")) {
        const errorData = await response.json();
        throw new Error(errorData.error || "未知错误");
      }

      const pagedHtmlContent = await response.text();

      // 安全地注入已分页的HTML内容
      if (containerRef.current) {
        containerRef.current.innerHTML = "";

        try {
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = pagedHtmlContent;

          let pagesToAdd: Element[] = [];

          // 首先尝试查找pages-container
          const pagesContainer = tempDiv.querySelector('.pages-container');
          if (pagesContainer) {
            pagesToAdd = Array.from(pagesContainer.children);
          } else {
            const pages = tempDiv.querySelectorAll('.page');
            if (pages.length > 0) {
              pagesToAdd = Array.from(pages);
            } else {
              containerRef.current.innerHTML = pagedHtmlContent;
              return;
            }
          }

          // 添加页面元素并设置初始动画状态
          pagesToAdd.forEach((page, index) => {
            const clonedPage = page.cloneNode(true) as HTMLElement;
            clonedPage.style.opacity = '0';
            clonedPage.style.transform = 'translateY(20px) scale(0.98)';
            clonedPage.style.transition = 'all 0.6s ease-out';
            clonedPage.setAttribute('data-page-index', index.toString());
            containerRef.current?.appendChild(clonedPage);
          });
        } catch (parseError) {
          containerRef.current.innerHTML = pagedHtmlContent;
        }
      }

      // 从URL获取文章ID
      const urlParts = htmlUrl.split("/");
      const extractedPaperId = urlParts[urlParts.indexOf("papers") + 1];
      if (extractedPaperId) {
        setPaperId(extractedPaperId);
      }

      setState(prev => ({ ...prev, isLoading: false }));

      // 触发内容加载完成回调
      if (onContentLoadedRef.current) {
        onContentLoadedRef.current();
      }

      // 延迟触发浮现效果
      setTimeout(() => {
        setState(prev => ({ ...prev, isContentVisible: true }));

        // 触发每个页面的逐个浮现效果
        const pages = containerRef.current?.querySelectorAll('.page');
        if (pages) {
          pages.forEach((page, index) => {
            setTimeout(() => {
              const pageElement = page as HTMLElement;
              pageElement.style.opacity = '1';
              pageElement.style.transform = 'translateY(0) scale(1)';
              setState(prev => {
                const newVisiblePages = new Set(prev.visiblePages);
                newVisiblePages.add(index);
                return {
                  ...prev,
                  visiblePages: newVisiblePages
                };
              });
            }, index * 150);
          });
        }

        // 延迟显示工具栏
        const totalPageDelay = pages ? pages.length * 150 : 0;
        setTimeout(() => {
          setState(prev => ({ ...prev, isToolbarVisible: true }));
        }, Math.max(300, totalPageDelay + 200));
      }, 200);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "加载文档时出现未知错误";
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }));
    }
  }, [htmlUrl, containerRef]);

  useEffect(() => {
    loadDocument();
  }, [htmlUrl]); // 只依赖 htmlUrl，避免无限循环

  const retry = useCallback(() => {
    loadDocument();
  }, [htmlUrl]); // 只依赖 htmlUrl

  return {
    ...state,
    paperId,
    retry,
  };
}
