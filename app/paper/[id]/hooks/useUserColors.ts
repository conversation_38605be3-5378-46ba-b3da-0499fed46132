/**
 * 统一的用户颜色 Hook
 * 为 paper 页面的评论系统提供高性能的用户颜色获取
 *
 * 特性：
 * - 批量预加载优化性能
 * - 智能缓存减少重复请求
 * - 自动回退确保可用性
 * - React Hook 模式易于使用
 */

import { useState, useEffect, useCallback } from 'react';
import { getUserColorSync, getUserColorAsync, preloadUserColors, DEFAULT_AVATAR_COLOR } from '@/lib/userColorManager';
import { User } from '../types';

/**
 * 单个用户颜色 Hook
 * @param user 用户对象
 * @returns 用户头像颜色
 */
export function useUserColor(user: User | undefined): string {
    const [color, setColor] = useState<string>(DEFAULT_AVATAR_COLOR);

    useEffect(() => {
        if (!user?.id) {
            setColor(DEFAULT_AVATAR_COLOR);
            return;
        }

        // 优先使用用户设置的颜色
        if (user.avatarColor) {
            setColor(user.avatarColor);
            return;
        }

        // 先使用同步方法获取颜色
        const syncColor = getUserColorSync(user.id);
        setColor(syncColor);

        // 异步获取最新颜色
        getUserColorAsync(user.id)
            .then(asyncColor => {
                if (asyncColor !== syncColor) {
                    setColor(asyncColor);
                }
            })
            .catch(error => {
                // 静默处理错误，保持当前颜色
                if (process.env.NODE_ENV === 'development') {
                    console.error('获取用户颜色失败:', error);
                }
            });
    }, [user?.id, user?.avatarColor]);

    return color;
}

/**
 * 多用户颜色 Hook
 * 支持批量预加载和高效缓存
 * @param users 用户数组
 * @returns 用户ID到颜色的映射
 */
export function useUserColors(users: (User | undefined)[]): Record<string, string> {
    const [colors, setColors] = useState<Record<string, string>>({});

    // 获取单个用户颜色的函数
    const getUserColor = useCallback((user: User | undefined): string => {
        if (!user?.id) return DEFAULT_AVATAR_COLOR;

        // 优先使用用户设置的颜色
        if (user.avatarColor) {
            return user.avatarColor;
        }

        // 从缓存获取或返回默认颜色
        return getUserColorSync(user.id);
    }, []);

    useEffect(() => {
        const validUsers = users.filter((user): user is User => !!user);
        
        if (validUsers.length === 0) {
            setColors({});
            return;
        }

        // 初始化颜色映射（使用同步方法）
        const initialColors: Record<string, string> = {};
        const userIdsToFetch: string[] = [];

        validUsers.forEach(user => {
            const color = getUserColor(user);
            initialColors[user.id] = color;
            
            // 如果用户没有设置颜色，需要异步获取最新颜色
            if (!user.avatarColor) {
                userIdsToFetch.push(user.id);
            }
        });

        setColors(initialColors);

        // 异步获取最新颜色
        if (userIdsToFetch.length > 0) {
            preloadUserColors(userIdsToFetch).then(() => {
                // 更新颜色映射
                const updatedColors = { ...initialColors };
                let hasUpdates = false;

                userIdsToFetch.forEach(userId => {
                    const newColor = getUserColorSync(userId);
                    if (newColor !== updatedColors[userId]) {
                        updatedColors[userId] = newColor;
                        hasUpdates = true;
                    }
                });

                if (hasUpdates) {
                    setColors(updatedColors);
                }
            }).catch(error => {
                // 静默处理错误，不影响主要功能
                if (process.env.NODE_ENV === 'development') {
                    console.error('预加载用户颜色失败:', error);
                }
            });
        }
    }, [users, getUserColor]);

    return colors;
}

/**
 * 获取用户颜色的便捷函数（用于组件内部）
 * @param user 用户对象
 * @returns 用户头像颜色
 */
export function getCommentUserColor(user: User | undefined): string {
    if (!user?.id) return DEFAULT_AVATAR_COLOR;

    // 优先使用用户设置的颜色
    if (user.avatarColor) {
        return user.avatarColor;
    }

    // 从缓存获取或返回默认颜色
    return getUserColorSync(user.id);
}

/**
 * 预加载评论用户颜色
 * 用于评论列表加载时的性能优化
 * @param comments 评论数组
 */
export async function preloadCommentUserColors(comments: any[]): Promise<void> {
    if (!comments?.length) return;

    const userIds = new Set<string>();

    // 递归收集所有用户ID
    const collectUserIds = (commentList: any[]) => {
        commentList.forEach(comment => {
            if (comment?.user?.id && !comment.user.avatarColor) {
                userIds.add(comment.user.id);
            }
            if (comment?.replies?.length) {
                collectUserIds(comment.replies);
            }
        });
    };

    collectUserIds(comments);

    if (userIds.size > 0) {
        await preloadUserColors(Array.from(userIds));
    }
}
