import { useState, useEffect, useCallback, useRef } from 'react';
import { TextSelectionState, HighlightInfo, User, CommentData } from '../types';
import { getPageInfo, generateHighlightId } from '../utils';
import { openAccountWindow, shouldOpenLoginWindow } from '../utils/floatingWindowCommunication';

interface UseTextSelectionProps {
  paperId: string;
  containerRef: React.RefObject<HTMLDivElement | null>;
  scale: number;
  showHighlights: boolean;
  user: User | null;
  isAuthLoading: boolean;
  onCommentCreated?: (comment: CommentData) => void;
  onRefreshComments?: () => void;
}

export function useTextSelection({
  paperId,
  containerRef,
  scale,
  showHighlights,
  user,
  isAuthLoading,
  onCommentCreated,
  onRefreshComments,
}: UseTextSelectionProps) {
  const [state, setState] = useState<TextSelectionState>({
    isTextSelecting: false,
    showInput: false,
    inputPos: null,
    inputValue: "",
    isSubmitting: false,
    needLogin: false,
    temporaryHighlight: null,
    currentSelection: null,
  });

  const textSelectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const temporaryHighlightRef = useRef<HTMLElement | null>(null);

  // 使用 useRef 来存储回调函数和最新的 scale 值
  const onCommentCreatedRef = useRef(onCommentCreated);
  onCommentCreatedRef.current = onCommentCreated;

  const onRefreshCommentsRef = useRef(onRefreshComments);
  onRefreshCommentsRef.current = onRefreshComments;

  // 使用 useRef 存储最新的 scale 值，避免闭包陷阱
  const scaleRef = useRef(scale);
  scaleRef.current = scale;

  // 创建临时高亮
  const createTemporaryHighlight = useCallback((
    rangeRect: DOMRect,
    highlightId: string,
    pageIndex: number
  ) => {
    // 清理之前的临时高亮
    if (temporaryHighlightRef.current) {
      temporaryHighlightRef.current.remove();
      temporaryHighlightRef.current = null;
    }

    if (!containerRef.current || !showHighlights) return;

    const actualPageIndex = pageIndex < 0 || pageIndex === null ? 0 : pageIndex;
    const pages = containerRef.current.querySelectorAll(".page");
    if (!pages || !pages[actualPageIndex]) return;

    const page = pages[actualPageIndex];
    const pageRect = page.getBoundingClientRect();

    // 创建临时高亮层
    const highlight = document.createElement("div");
    highlight.className = "highlight-overlay temporary";
    highlight.dataset.highlightId = highlightId;
    highlight.dataset.pageIndex = String(actualPageIndex);
    highlight.dataset.temporary = "true";

    // 设置位置和尺寸 - 使用 scaleRef 获取最新的 scale 值
    const currentScale = scaleRef.current;
    const top = (rangeRect.top - pageRect.top) / currentScale;
    const left = (rangeRect.left - pageRect.left) / currentScale;
    const width = rangeRect.width / currentScale;
    const height = rangeRect.height / currentScale;

    highlight.style.position = "absolute";
    highlight.style.top = `${top}px`;
    highlight.style.left = `${left}px`;
    highlight.style.width = `${width}px`;
    highlight.style.height = `${height}px`;
    highlight.style.pointerEvents = 'none';

    page.appendChild(highlight);
    temporaryHighlightRef.current = highlight;
    setState(prev => ({ ...prev, temporaryHighlight: highlightId }));
  }, [containerRef, showHighlights]); // 移除 scale 依赖项，使用 scaleRef

  // 清理临时高亮
  const clearTemporaryHighlight = useCallback(() => {
    if (temporaryHighlightRef.current) {
      temporaryHighlightRef.current.remove();
      temporaryHighlightRef.current = null;
    }
    setState(prev => ({ ...prev, temporaryHighlight: null }));
  }, []);

  // 禁用高亮交互
  const disableHighlightInteraction = useCallback(() => {
    const highlights = document.querySelectorAll('.highlight-overlay');
    highlights.forEach(highlight => {
      const el = highlight as HTMLElement;
      el.style.pointerEvents = 'none';
      el.style.opacity = '0.3';
      el.setAttribute('data-selection-mode', 'true');
    });
  }, []);

  // 启用高亮交互
  const enableHighlightInteraction = useCallback(() => {
    const highlights = document.querySelectorAll('.highlight-overlay');
    highlights.forEach(highlight => {
      const el = highlight as HTMLElement;
      el.style.pointerEvents = 'auto';
      el.style.opacity = '1';
      el.removeAttribute('data-selection-mode');
    });
  }, []);

  // 开始文本选择模式
  const startTextSelection = useCallback(() => {
    setState(prev => ({ ...prev, isTextSelecting: true }));
    disableHighlightInteraction();
  }, [disableHighlightInteraction]);

  // 结束文本选择模式
  const endTextSelection = useCallback(() => {
    if (textSelectionTimeoutRef.current) {
      clearTimeout(textSelectionTimeoutRef.current);
    }

    textSelectionTimeoutRef.current = setTimeout(() => {
      setState(prev => ({ ...prev, isTextSelecting: false }));
      enableHighlightInteraction();
    }, 50);
  }, [enableHighlightInteraction]);

  // 处理文本选择
  const processTextSelection = useCallback(() => {
    const sel = window.getSelection();
    if (!sel || sel.isCollapsed || !containerRef.current) return;

    if (!containerRef.current.contains(sel.anchorNode) || !containerRef.current.contains(sel.focusNode)) {
      return;
    }

    const range = sel.getRangeAt(0);
    const currentScale = scaleRef.current; // 使用最新的 scale 值
    const { pageIndex, pageTop } = getPageInfo(range.commonAncestorContainer, containerRef, currentScale);

    if (pageIndex === -1) return;

    const pages = containerRef.current.querySelectorAll(".page");
    const page = pages[pageIndex];
    const pageRect = page.getBoundingClientRect();
    const rangeRect = range.getBoundingClientRect();

    const highlightId = generateHighlightId();

    // 计算相对位置 - 使用最新的 scale 值
    const rect = {
      top: (rangeRect.top - pageRect.top) / currentScale,
      left: (rangeRect.left - pageRect.left) / currentScale,
      width: rangeRect.width / currentScale,
      height: rangeRect.height / currentScale,
    };

    // 保存当前选区信息
    const selectionInfo: HighlightInfo = {
      highlightId,
      pageIndex,
      pageTop,
      rect: rangeRect,
      y: rect.top,
    };

    setState(prev => ({ 
      ...prev, 
      currentSelection: selectionInfo,
      inputPos: {
        pageIndex,
        y: rect.top,
        pageTop,
      },
      showInput: true,
      inputValue: "",
      needLogin: !user && !isAuthLoading,
    }));

    // 创建临时高亮
    createTemporaryHighlight(rangeRect, highlightId, pageIndex);
  }, [containerRef, user, isAuthLoading, createTemporaryHighlight]); // 移除 scale 依赖项

  // 提交评论
  const handleSubmitComment = useCallback(async () => {
    if (!state.inputValue.trim() || !state.currentSelection) {
      setState(prev => ({ ...prev, showInput: false }));
      // 清理临时高亮
      if (temporaryHighlightRef.current) {
        temporaryHighlightRef.current.remove();
        temporaryHighlightRef.current = null;
      }
      setState(prev => ({ ...prev, temporaryHighlight: null }));
      return;
    }

    // 检查用户是否已登录，如果未登录则打开登录窗口
    if (shouldOpenLoginWindow(user)) {
      try {
        const success = await openAccountWindow();
        if (success) {
          console.log('[TextSelection] 已打开账户登录窗口');
        } else {
          console.warn('[TextSelection] 打开账户登录窗口失败');
        }
      } catch (error) {
        console.error('[TextSelection] 打开账户登录窗口时出错:', error);
      }

      // 保持输入框状态，让用户登录后可以继续
      return;
    }

    setState(prev => ({ ...prev, isSubmitting: true }));

    try {
      const { highlightId, pageIndex, pageTop, y, rect } = state.currentSelection;

      const pages = containerRef.current?.querySelectorAll(".page");
      const page = pages?.[pageIndex];
      const pageRect = page?.getBoundingClientRect();

      // 计算相对于页面的位置 - 使用最新的 scale 值
      const currentScale = scaleRef.current;
      const x = pageRect && rect ? (rect.left - pageRect.left) / currentScale : 0;
      const width = rect ? rect.width / currentScale : 0;
      const height = rect ? rect.height / currentScale : 0;

      const actualPageIndex = pageIndex < 0 ? 0 : pageIndex;

      const response = await fetch("/api/comments", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          text: state.inputValue.trim(),
          paperId,
          highlightId,
          pageIndex: actualPageIndex,
          yPosition: y,
          pageTop,
          x,
          y,
          width,
          height,
        }),
      });

      if (!response.ok) {
        throw new Error(`创建评论失败: ${response.status}`);
      }

      const newComment = await response.json();

      // 清理临时高亮
      if (temporaryHighlightRef.current) {
        temporaryHighlightRef.current.remove();
        temporaryHighlightRef.current = null;
      }
      setState(prev => ({ ...prev, temporaryHighlight: null }));

      // 重置状态
      setState(prev => ({
        ...prev,
        showInput: false,
        inputValue: "",
        currentSelection: null,
        isSubmitting: false,
      }));

      // 清除选区
      window.getSelection()?.removeAllRanges();

      // 通知父组件
      if (onCommentCreatedRef.current) {
        onCommentCreatedRef.current(newComment);
      }

      // 刷新评论列表
      if (onRefreshCommentsRef.current) {
        onRefreshCommentsRef.current();
      }
    } catch (error) {
      // 清理临时高亮
      if (temporaryHighlightRef.current) {
        temporaryHighlightRef.current.remove();
        temporaryHighlightRef.current = null;
      }
      setState(prev => ({ ...prev, isSubmitting: false, temporaryHighlight: null }));
    }
  }, [state.inputValue, state.currentSelection, user, paperId]); // 移除 scale 依赖项

  // 取消输入
  const cancelInput = useCallback(() => {
    setState(prev => ({
      ...prev,
      showInput: false,
      inputValue: "",
      currentSelection: null,
      needLogin: false,
    }));

    // 直接清理临时高亮，不依赖函数
    if (temporaryHighlightRef.current) {
      temporaryHighlightRef.current.remove();
      temporaryHighlightRef.current = null;
    }
    setState(prev => ({ ...prev, temporaryHighlight: null }));

    window.getSelection()?.removeAllRanges();
  }, []); // 移除函数依赖项

  // 设置输入值
  const setInputValue = useCallback((value: string) => {
    setState(prev => ({ ...prev, inputValue: value }));
  }, []);

  // 监听文本选择事件
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let isMouseDown = false;

    const handleMouseDown = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.closest('.comment-box') || 
          target.closest('.comment-dot') || 
          target.closest('.selection-comment-input') ||
          target.closest('button') ||
          target.closest('input') ||
          target.closest('textarea')) {
        return;
      }

      isMouseDown = true;
      startTextSelection();
    };

    const handleMouseUp = () => {
      if (!isMouseDown) return;
      isMouseDown = false;
      
      setTimeout(() => {
        processTextSelection();
        setTimeout(() => {
          endTextSelection();
        }, 100);
      }, 30);
    };

    const handleMouseLeave = () => {
      if (isMouseDown) {
        isMouseDown = false;
        setTimeout(() => {
          endTextSelection();
        }, 100);
      }
    };

    container.addEventListener("mousedown", handleMouseDown, true);
    container.addEventListener("mouseup", handleMouseUp);
    container.addEventListener("mouseleave", handleMouseLeave);

    return () => {
      container.removeEventListener("mousedown", handleMouseDown, true);
      container.removeEventListener("mouseup", handleMouseUp);
      container.removeEventListener("mouseleave", handleMouseLeave);
      
      if (textSelectionTimeoutRef.current) {
        clearTimeout(textSelectionTimeoutRef.current);
      }
    };
  }, []); // 移除所有函数依赖项，避免无限循环

  // 清理函数
  useEffect(() => {
    return () => {
      if (temporaryHighlightRef.current) {
        temporaryHighlightRef.current.remove();
        temporaryHighlightRef.current = null;
      }
      if (textSelectionTimeoutRef.current) {
        clearTimeout(textSelectionTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...state,
    setInputValue,
    handleSubmitComment,
    cancelInput,
    clearTemporaryHighlight,
  };
}
