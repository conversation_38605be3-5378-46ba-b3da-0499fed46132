import { useState, useEffect, useCallback, useRef } from 'react';
import { ExtendedCommentData, CommentSystemState } from '../types';
import { processCommentGroups, getTopLevelCommentId } from '../utils';

interface UseCommentSystemProps {
  paperId: string;
  containerRef: React.RefObject<HTMLDivElement | null>;
  showComments: boolean;
  showHighlights: boolean;
  targetCommentId?: string | null;
  onCommentFocused?: () => void;
}

export function useCommentSystem({
  paperId,
  containerRef,
  showComments,
  showHighlights,
  targetCommentId,
  onCommentFocused,
}: UseCommentSystemProps) {
  const [state, setState] = useState<CommentSystemState>({
    comments: [],
    isLoading: true,
    error: null,
    showComments,
    showHighlights,
    expandedComments: {},
    visibleComments: new Set(),
    visibleHighlights: new Set(),
    activeHighlightId: null,
    currentlyReplyingId: null,
    replyingComments: new Set(),
  });

  // 延迟关闭定时器
  const closeTimerRef = useRef<Record<string, NodeJS.Timeout>>({});

  // 悬停状态管理
  const hoverStateRef = useRef<Record<string, {
    isHoveringComment: boolean;
    isHoveringHighlight: boolean;
    isInteracting: boolean; // 正在回复、输入等
  }>>({});

  // 获取评论数据
  const fetchComments = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await fetch(`/api/comments?paperId=${paperId}`);
      if (!response.ok) {
        throw new Error(`获取评论失败: ${response.status}`);
      }

      const data = await response.json();
      const processedComments = processCommentGroups(data);

      setState(prev => ({
        ...prev,
        comments: processedComments,
        isLoading: false
      }));

      // 立即显示所有评论和高亮，避免延迟导致的问题
      if (processedComments.length > 0) {
        const topComments = processedComments.filter(c => !c.parentId);
        const newVisibleComments = new Set<string>();
        const newVisibleHighlights = new Set<string>();

        topComments.forEach((comment) => {
          newVisibleComments.add(comment.id);
          if (comment.highlightId) {
            newVisibleHighlights.add(comment.highlightId);
          }
        });

        setState(prev => ({
          ...prev,
          visibleComments: newVisibleComments,
          visibleHighlights: newVisibleHighlights
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: "无法加载评论",
        isLoading: false
      }));
    }
  }, [paperId]); // 移除 showComments 和 showHighlights 依赖项

  // 使用 useRef 避免闭包陷阱
  const stateRef = useRef(state);
  stateRef.current = state;

  // 检查评论树中是否有任何回复正在进行中
  const isCommentTreeReplying = useCallback((topLevelCommentId: string) => {
    const currentState = stateRef.current;
    if (currentState.replyingComments.has(topLevelCommentId)) {
      return true;
    }

    const comment = currentState.comments.find(c => c.id === topLevelCommentId);
    if (comment?.replies) {
      return comment.replies.some(reply => currentState.replyingComments.has(reply.id));
    }

    return false;
  }, []); // 移除依赖项，使用 stateRef

  // 检查是否应该关闭评论
  const shouldCloseComment = useCallback((commentId: string) => {
    const hoverState = hoverStateRef.current[commentId];
    if (!hoverState) return true;

    // 如果正在悬停或交互，则不关闭
    return !hoverState.isHoveringComment &&
           !hoverState.isHoveringHighlight &&
           !hoverState.isInteracting &&
           !isCommentTreeReplying(commentId);
  }, [isCommentTreeReplying]);

  // 延迟关闭评论
  const delayCloseComment = useCallback((commentId: string) => {
    if (closeTimerRef.current[commentId]) {
      clearTimeout(closeTimerRef.current[commentId]);
    }

    closeTimerRef.current[commentId] = setTimeout(() => {
      if (shouldCloseComment(commentId)) {
        setState(prev => {
          const newExpandedComments = { ...prev.expandedComments };
          delete newExpandedComments[commentId];
          return {
            ...prev,
            expandedComments: newExpandedComments
          };
        });
      }
      delete closeTimerRef.current[commentId];
    }, 800);
  }, [shouldCloseComment]);

  // 取消延迟关闭
  const cancelDelayClose = useCallback((commentId: string) => {
    if (closeTimerRef.current[commentId]) {
      clearTimeout(closeTimerRef.current[commentId]);
      delete closeTimerRef.current[commentId];
    }
  }, []);

  // 开始回复
  const startReply = useCallback((commentId: string) => {
    const currentState = stateRef.current;
    if (currentState.currentlyReplyingId && currentState.currentlyReplyingId !== commentId) {
      finishReply(currentState.currentlyReplyingId);
    }

    setState(prev => {
      const newReplyingComments = new Set(prev.replyingComments);
      newReplyingComments.add(commentId);
      return {
        ...prev,
        replyingComments: newReplyingComments,
        currentlyReplyingId: commentId
      };
    });

    // 设置交互状态
    const topLevelCommentId = getTopLevelCommentId(commentId, currentState.comments);
    if (!hoverStateRef.current[topLevelCommentId]) {
      hoverStateRef.current[topLevelCommentId] = {
        isHoveringComment: false,
        isHoveringHighlight: false,
        isInteracting: false
      };
    }
    hoverStateRef.current[topLevelCommentId].isInteracting = true;

    cancelDelayClose(commentId);
    if (topLevelCommentId !== commentId) {
      cancelDelayClose(topLevelCommentId);
    }
  }, [cancelDelayClose]); // 移除状态依赖项

  // 完成回复
  const finishReply = useCallback((commentId: string) => {
    setState(prev => {
      const newReplyingComments = new Set(prev.replyingComments);
      newReplyingComments.delete(commentId);

      return {
        ...prev,
        replyingComments: newReplyingComments,
        currentlyReplyingId: prev.currentlyReplyingId === commentId ? null : prev.currentlyReplyingId
      };
    });

    const currentState = stateRef.current;
    const topLevelCommentId = getTopLevelCommentId(commentId, currentState.comments);

    // 清除交互状态
    if (hoverStateRef.current[topLevelCommentId]) {
      hoverStateRef.current[topLevelCommentId].isInteracting = false;
    }

    setTimeout(() => {
      delayCloseComment(topLevelCommentId);
    }, 100);
  }, [delayCloseComment]); // 移除状态依赖项

  // 处理回复
  const handleReply = useCallback(async (
    parentId: string, 
    text: string, 
    replyToUserId?: string, 
    replyToUserName?: string, 
    notifyAuthor?: boolean
  ) => {
    if (!text.trim()) return;

    try {
      const formattedText = replyToUserId && replyToUserName
        ? `回复${replyToUserName}: ${text.trim()}`
        : text.trim();

      const response = await fetch("/api/comments", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          text: formattedText,
          paperId,
          parentId,
          replyToUserId,
          notifyAuthor,
        }),
      });

      if (!response.ok) {
        throw new Error(`创建回复失败: ${response.status}`);
      }

      const newReply = await response.json();

      setState(prev => ({
        ...prev,
        comments: prev.comments.map(comment => {
          if (comment.id === parentId) {
            return {
              ...comment,
              replies: [...(comment.replies || []), newReply]
            };
          }
          return comment;
        })
      }));
    } catch (error) {
      throw error;
    }
  }, [paperId]);

  // 删除评论
  const handleDeleteComment = useCallback(async (commentId: string) => {
    try {
      const currentState = stateRef.current;
      const comment = currentState.comments.find(c => c.id === commentId);
      const isParentComment = comment && !comment.parentId;
      const hasReplies = comment?.replies && comment.replies.length > 0;

      const response = await fetch(`/api/comments/${commentId}`, {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          cascade: isParentComment && hasReplies,
        }),
      });

      if (!response.ok) {
        throw new Error(`删除评论失败: ${response.status}`);
      }

      setState(prev => ({
        ...prev,
        comments: isParentComment
          ? prev.comments.filter(c => c.id !== commentId)
          : prev.comments.map(comment => ({
              ...comment,
              replies: comment.replies?.filter(reply => reply.id !== commentId) || []
            }))
      }));

      // 移除对应的高亮
      const highlightEl = document.querySelector(`[data-highlight-id="${comment?.highlightId}"]`);
      if (highlightEl) {
        highlightEl.remove();
      }
    } catch (error) {
      throw error;
    }
  }, []); // 移除状态依赖项

  const highlightComment = useCallback((highlightId: string) => {
    setState(prev => ({ ...prev, activeHighlightId: highlightId }));
    // 高亮逻辑在 HighlightManager 中处理
  }, []);

  const removeHighlight = useCallback(() => {
    setState(prev => ({ ...prev, activeHighlightId: null }));
  }, []);

  // 处理评论悬停进入
  const handleCommentHover = useCallback((commentId: string) => {
    if (!hoverStateRef.current[commentId]) {
      hoverStateRef.current[commentId] = {
        isHoveringComment: false,
        isHoveringHighlight: false,
        isInteracting: false
      };
    }
    hoverStateRef.current[commentId].isHoveringComment = true;

    cancelDelayClose(commentId);
    setState(prev => ({
      ...prev,
      expandedComments: {
        ...prev.expandedComments,
        [commentId]: true
      }
    }));
  }, [cancelDelayClose]);

  // 处理评论悬停离开
  const handleCommentLeave = useCallback((commentId: string) => {
    if (hoverStateRef.current[commentId]) {
      hoverStateRef.current[commentId].isHoveringComment = false;
    }
    delayCloseComment(commentId);
  }, [delayCloseComment]);

  // 处理高亮悬停进入
  const handleHighlightHover = useCallback((commentId: string) => {
    if (!hoverStateRef.current[commentId]) {
      hoverStateRef.current[commentId] = {
        isHoveringComment: false,
        isHoveringHighlight: false,
        isInteracting: false
      };
    }
    hoverStateRef.current[commentId].isHoveringHighlight = true;

    cancelDelayClose(commentId);
    setState(prev => ({
      ...prev,
      expandedComments: {
        ...prev.expandedComments,
        [commentId]: true
      }
    }));
  }, [cancelDelayClose]);

  // 处理高亮悬停离开
  const handleHighlightLeave = useCallback((commentId: string) => {
    if (hoverStateRef.current[commentId]) {
      hoverStateRef.current[commentId].isHoveringHighlight = false;
    }
    delayCloseComment(commentId);
  }, [delayCloseComment]);

  const closeAllComments = useCallback((exceptId?: string) => {
    const newState: Record<string, boolean> = {};
    if (exceptId) {
      newState[exceptId] = true;
    }
    setState(prev => ({ ...prev, expandedComments: newState }));
  }, []);

  const isCommentReplying = useCallback((commentId: string) => {
    return state.currentlyReplyingId === commentId;
  }, [state.currentlyReplyingId]);

  // 初始化
  useEffect(() => {
    if (paperId) {
      fetchComments();
    }
  }, [paperId]); // 移除 fetchComments 依赖项

  // 使用 useRef 来存储回调函数
  const onCommentFocusedRef = useRef(onCommentFocused);
  onCommentFocusedRef.current = onCommentFocused;

  // 处理目标评论
  useEffect(() => {
    if (!targetCommentId || !state.comments.length || state.isLoading) return;

    // 自动展开目标评论
    const topLevelCommentId = getTopLevelCommentId(targetCommentId, state.comments);
    setState(prev => ({
      ...prev,
      expandedComments: {
        ...prev.expandedComments,
        [topLevelCommentId]: true
      }
    }));

    if (onCommentFocusedRef.current) {
      setTimeout(onCommentFocusedRef.current, 500);
    }
  }, [targetCommentId, state.comments, state.isLoading]); // 移除 onCommentFocused 依赖项

  // 刷新评论列表
  const refreshComments = useCallback(async () => {
    await fetchComments();
  }, [paperId]); // 只依赖 paperId

  return {
    ...state,
    handleReply,
    handleDeleteComment,
    startReply,
    finishReply,
    highlightComment,
    removeHighlight,
    handleCommentHover,
    handleCommentLeave,
    handleHighlightHover,
    handleHighlightLeave,
    closeAllComments,
    isCommentReplying,
    delayCloseComment,
    cancelDelayClose,
    refreshComments,
  };
}
