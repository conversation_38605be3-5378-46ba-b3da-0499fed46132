"use client";

import { useEffect, useRef, useState } from "react";

interface Comment {
  id: number;
  text: string;
  pageIndex: number; // 所在页面索引
  highlightId: string; // 关联的高亮元素ID
  selection: {
    startElement: string; // 选区起始元素选择器
    startOffset: number;
    endElement: string; // 选区结束元素选择器
    endOffset: number;
  } | null;
  y: number; // 相对页面顶部的位置
  rect: DOMRect | null; // 选区的位置和尺寸
  pageTop: number; // 所在页面相对于容器顶部的距离
}

export interface CommentPluginProps {
  containerRef: React.RefObject<HTMLDivElement>;
  scale: number;
  showComments: boolean;
  showHighlights: boolean;
  setShowComments: (show: boolean) => void;
  setShowHighlights: (show: boolean) => void;
}

export default function CommentPlugin({
  containerRef,
  scale,
  showComments,
  showHighlights,
  setShowComments,
  setShowHighlights,
}: CommentPluginProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [showInput, setShowInput] = useState(false);
  const [inputPos, setInputPos] = useState<{
    pageIndex: number;
    y: number;
    pageTop: number;
  } | null>(null);
  const [inputValue, setInputValue] = useState("");
  const idRef = useRef(0);
  const highlightsLayerRef = useRef<HTMLDivElement>(null);
  const commentRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const currentSelectionRef = useRef<{
    range: Range;
    pageIndex: number;
    highlightId: string;
    selection: Comment["selection"];
    rect: DOMRect;
    pageTop: number; // 添加页面顶部位置
  } | null>(null);

  // 获取当前选中文本所在的页面索引和页面位置
  const getPageInfo = (node: Node | null): { pageIndex: number; pageTop: number } => {
    if (!node || !containerRef.current) return { pageIndex: -1, pageTop: 0 };

    let element = node.nodeType === Node.TEXT_NODE ? node.parentElement : (node as Element);

    // 向上查找.page元素
    while (element && !element.classList.contains("page")) {
      element = element.parentElement;
    }

    if (!element) return { pageIndex: -1, pageTop: 0 };

    // 获取所有页面并找出当前页面的索引
    const pages = containerRef.current.querySelectorAll(".page");
    const pageIndex = Array.from(pages).indexOf(element as Element);

    // 计算页面顶部相对于容器的位置
    const containerRect = containerRef.current.getBoundingClientRect();
    const pageRect = element.getBoundingClientRect();
    const pageTop = (pageRect.top - containerRect.top) / scale;

    return { pageIndex, pageTop };
  };

  // 创建高亮层
  useEffect(() => {
    // 添加自定义样式到文档头
    const style = document.createElement("style");
    style.textContent = `
      .highlight-overlay {
        position: absolute;
        background-color: rgba(255, 255, 0, 0.3);
        pointer-events: auto; /* 改为auto以捕获鼠标事件 */
        z-index: 10;
        transition: all 0.2s ease;
        cursor: pointer;
      }
      
      .highlight-overlay.active {
        background-color: rgba(255, 255, 0, 0.6);
        box-shadow: 0 0 0 2px rgba(255, 200, 0, 0.7);
      }
      
      .highlight-overlay.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .comment-box {
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
      
      .comment-box.active {
        box-shadow: 0 0 0 2px #3b82f6, 0 4px 6px rgba(0, 0, 0, 0.1);
        transform: translateX(-5px);
        background-color: #fef9c3 !important;
        z-index: 100 !important;
      }
      
      @media print {
        .comment-sidebar, .highlight-overlay {
          display: none !important;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleMouseUp = () => {
      const sel = window.getSelection();
      if (!sel || sel.isCollapsed) {
        return;
      }

      if (!container.contains(sel.anchorNode) || !container.contains(sel.focusNode)) {
        return;
      }

      const range = sel.getRangeAt(0);
      const { pageIndex, pageTop } = getPageInfo(range.commonAncestorContainer);

      if (pageIndex === -1) return;

      // 获取页面元素
      const pages = container.querySelectorAll(".page");
      const page = pages[pageIndex];
      const pageRect = page.getBoundingClientRect();
      const rangeRect = range.getBoundingClientRect();

      // 生成唯一的高亮ID
      const highlightId = `highlight-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // 保存选区信息（简化版，实际实现需要更复杂的选区保存逻辑）
      const startElement = sel.anchorNode?.parentElement;
      const endElement = sel.focusNode?.parentElement;

      let selection = null;
      if (startElement && endElement) {
        selection = {
          startElement: getElementPath(startElement),
          startOffset: sel.anchorOffset,
          endElement: getElementPath(endElement),
          endOffset: sel.focusOffset,
        };
      }

      // 计算选区相对于页面的位置
      const rect = {
        top: (rangeRect.top - pageRect.top) / scale,
        left: (rangeRect.left - pageRect.left) / scale,
        width: rangeRect.width / scale,
        height: rangeRect.height / scale,
      };

      // 保存当前选区信息
      currentSelectionRef.current = {
        range,
        pageIndex,
        highlightId,
        selection,
        rect: rangeRect,
        pageTop,
      };

      // 显示输入框在页面右侧
      setInputPos({
        pageIndex,
        y: rect.top,
        pageTop,
      });

      setShowInput(true);
      setInputValue("");
    };

    // 获取元素的唯一路径（简化版）
    const getElementPath = (element: Element): string => {
      let path = element.tagName.toLowerCase();
      if (element.id) {
        path += `#${element.id}`;
      } else if (element.className) {
        path += `.${Array.from(element.classList).join(".")}`;
      }

      // 添加子索引
      const parent = element.parentElement;
      if (parent) {
        const siblings = Array.from(parent.children);
        const index = siblings.indexOf(element);
        path += `:nth-child(${index + 1})`;
      }

      return path;
    };

    container.addEventListener("mouseup", handleMouseUp);
    return () => {
      container.removeEventListener("mouseup", handleMouseUp);
    };
  }, [containerRef, scale]);

  // 处理留言提交
  const handleSubmit = () => {
    if (!inputPos || !inputValue.trim() || !currentSelectionRef.current) {
      setShowInput(false);
      return;
    }

    const id = idRef.current++;
    const { pageIndex, highlightId, selection, rect, pageTop } = currentSelectionRef.current;

    // 添加新评论
    setComments([
      ...comments,
      {
        id,
        text: inputValue.trim(),
        pageIndex,
        highlightId,
        selection,
        y: inputPos.y,
        rect: rect,
        pageTop,
      },
    ]);

    setShowInput(false);
    setInputValue("");

    // 创建浮动高亮层
    createHighlightOverlay(rect, highlightId, pageIndex);

    // 清除选区
    window.getSelection()?.removeAllRanges();

    // 重置当前选区引用
    currentSelectionRef.current = null;
  };

  // 添加高亮和留言之间的交互
  useEffect(() => {
    if (!containerRef.current) return;

    // 为已添加的高亮区域添加鼠标事件监听
    const handleHighlightInteractions = () => {
      const highlights = containerRef.current?.querySelectorAll(".highlight-overlay");
      if (!highlights) return;

      highlights.forEach(highlight => {
        // 防止重复添加事件监听器
        if ((highlight as HTMLElement).dataset.hasListeners === "true") return;

        const highlightId = (highlight as HTMLElement).dataset.highlightId;
        if (!highlightId) return;

        // 鼠标进入高亮区域
        highlight.addEventListener("mouseenter", () => {
          // 找到对应的留言框
          const commentBox = commentRefs.current.get(highlightId);
          if (commentBox) {
            commentBox.classList.add("active");
            // 滚动到留言框位置确保可见
            commentBox.scrollIntoView({ behavior: "smooth", block: "nearest" });
          }
          highlight.classList.add("active");
        });

        // 鼠标离开高亮区域
        highlight.addEventListener("mouseleave", () => {
          const commentBox = commentRefs.current.get(highlightId);
          if (commentBox) {
            commentBox.classList.remove("active");
          }
          highlight.classList.remove("active");
        });

        // 标记已添加监听器
        (highlight as HTMLElement).dataset.hasListeners = "true";
      });
    };

    // 每当评论发生变化时更新交互
    handleHighlightInteractions();

    // 使用MutationObserver监听DOM变化以添加新的高亮区域监听器
    const observer = new MutationObserver(handleHighlightInteractions);
    observer.observe(containerRef.current, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect();
  }, [comments, containerRef]);

  // 创建高亮覆盖层
  const createHighlightOverlay = (rangeRect: DOMRect, highlightId: string, pageIndex: number) => {
    if (!containerRef.current || !showHighlights) return;

    const pages = containerRef.current.querySelectorAll(".page");
    if (!pages || !pages[pageIndex]) return;

    const page = pages[pageIndex];
    const pageRect = page.getBoundingClientRect();

    // 创建高亮层
    const highlight = document.createElement("div");
    highlight.className = "highlight-overlay";
    highlight.dataset.highlightId = highlightId;

    // 设置高亮层的位置和尺寸
    highlight.style.position = "absolute";
    highlight.style.top = `${(rangeRect.top - pageRect.top) / scale}px`;
    highlight.style.left = `${(rangeRect.left - pageRect.left) / scale}px`;
    highlight.style.width = `${rangeRect.width / scale}px`;
    highlight.style.height = `${rangeRect.height / scale}px`;

    // 将高亮层添加到页面中
    page.appendChild(highlight);
  };

  // 渲染评论全局右侧浮动框
  const renderGlobalSidebar = () => {
    if (!showComments) return null;
    return (
      <>
        {comments.map(comment => {
          // 计算留言框的顶部位置（基于页面位置 + 选区在页面内的相对位置）
          // 这样即使页面滚动，留言也能与高亮内容对齐
          const yPosition = comment.pageTop + comment.y;

          return (
            <div
              key={comment.id}
              className="bg-yellow-50 border border-yellow-200 rounded-md p-2 mb-2 shadow-sm absolute pointer-events-auto comment-box"
              style={{
                top: `${yPosition}px`,
                right: 20,
                width: "350px",
                fontSize: `${14 / scale}px`,
                zIndex: 50,
              }}
              ref={el => {
                if (el) {
                  commentRefs.current.set(comment.highlightId, el);
                }
              }}
              onMouseEnter={() => {
                // 鼠标悬停时高亮对应的文本
                const highlight = document.querySelector(
                  `[data-highlight-id="${comment.highlightId}"]`
                );
                if (highlight) {
                  highlight.classList.add("active");
                }
                // 高亮当前留言框
                const currentComment = commentRefs.current.get(comment.highlightId);
                if (currentComment) {
                  currentComment.classList.add("active");
                }
              }}
              onMouseLeave={() => {
                // 鼠标离开时恢复原样
                const highlight = document.querySelector(
                  `[data-highlight-id="${comment.highlightId}"]`
                );
                if (highlight) {
                  highlight.classList.remove("active");
                }
                // 移除留言框高亮
                const currentComment = commentRefs.current.get(comment.highlightId);
                if (currentComment) {
                  currentComment.classList.remove("active");
                }
              }}
            >
              <p className="text-gray-700">{comment.text}</p>
              <button
                className="absolute top-1 right-1 text-gray-400 hover:text-gray-600 text-xs"
                onClick={() => {
                  // 删除评论
                  setComments(comments.filter(c => c.id !== comment.id));
                  // 删除对应的高亮
                  const highlight = document.querySelector(
                    `[data-highlight-id="${comment.highlightId}"]`
                  );
                  if (highlight) {
                    highlight.remove();
                  }
                  // 从引用Map中移除
                  commentRefs.current.delete(comment.highlightId);
                }}
              >
                ×
              </button>
            </div>
          );
        })}
      </>
    );
  };

  // 更新高亮显示状态
  useEffect(() => {
    const highlightElements = document.querySelectorAll(".highlight-overlay");
    highlightElements.forEach(el => {
      if (showHighlights) {
        el.classList.remove("hidden");
      } else {
        el.classList.add("hidden");
      }
    });
  }, [showHighlights]);

  return (
    <>
      <div
        className="absolute left-0 top-0 pointer-events-none"
        style={{
          width: "100%",
          height: "100%",
          transform: `scale(${scale})`,
          transformOrigin: "top center",
        }}
      >
        {renderGlobalSidebar()}
        <div ref={highlightsLayerRef} className="highlights-layer" />
      </div>

      {showInput && inputPos && (
        <div
          className="absolute z-50 bg-white p-2 border rounded shadow pointer-events-auto"
          style={{
            right: "20px",
            // 计算输入框的顶部位置（基于页面位置 + 选区在页面内的相对位置）
            top: `${inputPos.pageTop + inputPos.y}px`,
            width: "350px",
          }}
        >
          <textarea
            className="border p-1 text-sm mb-1 w-full"
            rows={3}
            value={inputValue}
            onChange={e => setInputValue(e.target.value)}
            placeholder="添加留言..."
            autoFocus
          />
          <div className="flex justify-between">
            <button
              className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded"
              onClick={() => {
                setShowInput(false);
                currentSelectionRef.current = null;
                window.getSelection()?.removeAllRanges();
              }}
            >
              取消
            </button>
            <button
              className="bg-blue-500 text-white text-xs px-2 py-1 rounded"
              onClick={handleSubmit}
            >
              保存留言
            </button>
          </div>
        </div>
      )}
    </>
  );
}
