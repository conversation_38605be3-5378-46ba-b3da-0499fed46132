"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ArrowLeft, ZoomIn, ZoomOut, Maximize, Printer, MessageSquareText } from "lucide-react";

import "./page.css";
import CommentSystem from "./CommentSystem";

export default function PagedPreview({ htmlUrl }: { htmlUrl: string }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const containerRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null); // 添加外层容器引用
  const previewContainerRef = useRef<HTMLElement | null>(null); // 用于存储预览容器引用
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [scale, setScale] = useState(1);
  const [showComments, setShowComments] = useState(true);
  const [showHighlights, setShowHighlights] = useState(true);
  const [paperId, setPaperId] = useState<string>("");
  const [targetCommentId, setTargetCommentId] = useState<string | null>(null); // 新增：目标评论ID

  // 添加浮现效果状态
  const [isContentVisible, setIsContentVisible] = useState(false);
  const [isToolbarVisible, setIsToolbarVisible] = useState(false);
  const [visiblePages, setVisiblePages] = useState<Set<number>>(new Set());

  // 新增：处理URL锚点和查询参数
  useEffect(() => {
    // 优先检查查询参数中的commentId
    const commentIdFromQuery = searchParams.get('commentId');
    if (commentIdFromQuery) {
      setTargetCommentId(commentIdFromQuery);
      setShowComments(true);
      setShowHighlights(true);
      return;
    }

    // 回退到URL hash处理
    const hash = window.location.hash;
    if (hash.startsWith("#comment-")) {
      const commentId = hash.replace("#comment-", "");
      setTargetCommentId(commentId);
      setShowComments(true);
      setShowHighlights(true);
    }
  }, [searchParams]);

  // 在组件初始化时获取预览容器引用
  useEffect(() => {
    if (containerRef.current) {
      previewContainerRef.current = containerRef.current.closest(".paged-preview") as HTMLElement;
    }
  }, []);

  // 检查内容是否超出容器宽度
  const checkContentOverflow = useCallback((currentScale: number, containerElement?: HTMLElement) => {
    // 优先使用ref，避免DOM查询
    const previewContainer = containerElement || previewContainerRef.current;
    if (!previewContainer || !containerRef.current || containerRef.current.children.length === 0) {
      return false;
    }

    // 获取第一个页面元素的原始宽度
    const pageElement = containerRef.current.querySelector(".page") as HTMLElement;
    if (!pageElement) return false;

    // 计算缩放后的实际宽度
    const pageOriginalWidth = pageElement.offsetWidth;
    const scaledPageWidth = pageOriginalWidth * currentScale;

    // 获取容器可用宽度（减去padding）
    const containerWidth = previewContainer.clientWidth - 40; // 40px为左右padding

    return scaledPageWidth > containerWidth;
  }, []);

  // 应用布局模式
  const applyLayoutMode = useCallback((isOverflow: boolean, newScale: number, containerElement?: HTMLElement) => {
    // 优先使用ref，避免DOM查询
    const previewContainer = containerElement || previewContainerRef.current;
    if (!previewContainer || !containerRef.current || !wrapperRef.current) return;

    if (isOverflow) {
      // 内容超出容器：左对齐模式
      wrapperRef.current.style.justifyContent = 'flex-start';
      containerRef.current.style.transformOrigin = 'top left';
    } else {
      // 内容未超出容器：居中模式
      wrapperRef.current.style.justifyContent = 'center';
      containerRef.current.style.transformOrigin = 'top center';
    }

    containerRef.current.style.transform = `scale(${newScale})`;
  }, []);

  // 计算最佳缩放比例的函数
  const calculateOptimalScale = useCallback(() => {
    // 如果容器或页面元素不存在，返回默认值
    if (!containerRef.current || containerRef.current.childNodes.length === 0) return 1;

    // 获取预览容器
    const previewContainer =
      previewContainerRef.current ||
      (containerRef.current.closest(".paged-preview") as HTMLElement);
    if (!previewContainer) return 1;

    // 获取页面元素（第一个页面用于计算）
    const pageElement = containerRef.current.querySelector(".page") as HTMLElement;
    if (!pageElement) return 1;

    // 获取容器和页面的尺寸
    const containerWidth = previewContainer.clientWidth - 40; // 减去边距
    const containerHeight = previewContainer.clientHeight - 40;
    const pageWidth = pageElement.offsetWidth;
    const pageHeight = pageElement.offsetHeight;

    // 确保尺寸值有效
    if (containerWidth <= 0 || containerHeight <= 0 || pageWidth <= 0 || pageHeight <= 0) {
      return 1;
    }

    // 计算宽度和高度的缩放比例
    const widthScale = containerWidth / pageWidth;
    const heightScale = containerHeight / pageHeight;

    // 取较小值确保页面能完整显示，并限制最小缩放比例为0.5，最大为1.0
    const optimalScale = Math.max(0.5, Math.min(Math.min(widthScale, heightScale), 1.0));

    return optimalScale;
  }, []);

  // 调整容器高度以适应缩放，接受可选的缩放比例参数
  const adjustContainerHeight = useCallback(
    (newScale?: number) => {
      const currentScale = newScale || scale;

      if (!containerRef.current || !wrapperRef.current) return;

      // 获取所有页面元素
      const pages = containerRef.current.querySelectorAll(".page");
      if (pages.length === 0) return;

      // 获取第一页元素，用于确定A4尺寸（像素）
      const firstPage = pages[0] as HTMLElement;
      const pageHeight = firstPage.offsetHeight;

      // 计算页面间距总和(仅计算一次)
      let totalMargin = 0;
      if (pages.length > 1) {
        const style = window.getComputedStyle(firstPage);
        const marginBottom = parseInt(style.marginBottom || "0");
        totalMargin = (pages.length - 1) * marginBottom;
      }

      // 计算总高度
      const totalHeight = pageHeight * pages.length + totalMargin;

      // 直接设置高度
      wrapperRef.current.style.height = `${totalHeight * currentScale + 40}px`;
    },
    [scale]
  );

  // 智能缩放函数：根据内容实际宽度动态选择布局模式，特别优化了留言系统的缩放适配
  const zoomWithFocus = useCallback(
    (newScale: number) => {
      // 如果缩放比例没有变化，不需要处理
      if (newScale === scale) return;

      // 获取预览容器
      const previewContainer = previewContainerRef.current;
      if (!previewContainer || !containerRef.current) return;

      // 保存当前滚动位置和视口信息
      const viewportWidth = previewContainer.clientWidth;
      const viewportHeight = previewContainer.clientHeight;
      const scrollTop = previewContainer.scrollTop;
      const scrollLeft = previewContainer.scrollLeft;

      // 检查新缩放级别下内容是否会超出容器
      const willOverflow = checkContentOverflow(newScale, previewContainer);

      // 应用新的布局模式
      applyLayoutMode(willOverflow, newScale, previewContainer);

      // 根据布局模式调整滚动位置
      if (!willOverflow) {
        // 居中模式：保持垂直滚动，重置水平滚动
        const centerY = (scrollTop + viewportHeight / 2) / scale;
        const newScrollTop = centerY * newScale - viewportHeight / 2;
        previewContainer.scrollTop = Math.max(0, newScrollTop);
        previewContainer.scrollLeft = 0;
      } else {
        // 左对齐模式：保持视觉中心点
        const centerX = (scrollLeft + viewportWidth / 2) / scale;
        const centerY = (scrollTop + viewportHeight / 2) / scale;

        const newScrollLeft = centerX * newScale - viewportWidth / 2;
        const newScrollTop = centerY * newScale - viewportHeight / 2;

        previewContainer.scrollLeft = Math.max(0, newScrollLeft);
        previewContainer.scrollTop = Math.max(0, newScrollTop);
      }

      // 异步更新React状态
      setTimeout(() => {
        setScale(newScale);
      }, 0);

      // 立即调整容器高度，确保留言系统正确定位
      adjustContainerHeight(newScale);
    },
    [scale, checkContentOverflow, applyLayoutMode, adjustContainerHeight]
  );

  // 处理缩放功能
  const handleZoomIn = () => {
    const newScale = Math.min(scale + 0.1, 2.0);
    zoomWithFocus(newScale);
  };

  const handleZoomOut = () => {
    const newScale = Math.max(scale - 0.1, 0.5);
    zoomWithFocus(newScale);
  };

  const handleResetZoom = () => {
    const newScale = calculateOptimalScale();
    zoomWithFocus(newScale);
  };

  // 处理打印功能
  const handlePrint = () => {
    window.print();
  };

  // 处理返回功能
  const handleGoBack = () => {
    router.push("/papers");
  };

  // 切换留言显示状态
  const toggleComments = () => {
    setShowComments(prev => !prev);
  };

  // 切换高亮显示状态
  const toggleHighlights = () => {
    setShowHighlights(prev => !prev);
  };

  // 添加新的同时切换留言和高亮的函数
  const toggleAnnotations = () => {
    setShowComments(prev => !prev);
    setShowHighlights(prev => !prev);
  };



  // 监听窗口大小变化，调整缩放和布局
  useEffect(() => {
    let resizeTimeout: NodeJS.Timeout | null = null;

    const handleResize = () => {
      if (resizeTimeout) clearTimeout(resizeTimeout);

      resizeTimeout = setTimeout(() => {
        const newScale = calculateOptimalScale();
        if (newScale > 0) {
          zoomWithFocus(newScale);
        } else {
          // 即使没有缩放变化，也要检查布局模式
          const container = previewContainerRef.current;
          if (container) {
            const isOverflow = checkContentOverflow(scale, container);
            applyLayoutMode(isOverflow, scale, container);
          }
        }
      }, 100); // 节流间隔
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
      if (resizeTimeout) clearTimeout(resizeTimeout);
    };
  }, [scale]);

  // 监听缩放比例变化，调整容器高度
  useEffect(() => {
    if (isLoading) return;

    // 短暂延迟确保DOM已完全更新
    const timer = setTimeout(() => {
      adjustContainerHeight();
    }, 150);

    return () => clearTimeout(timer);
  }, [scale, isLoading]);

  useEffect(() => {
    async function loadPagedHtmlContent() {
      try {
        setIsLoading(true);
        setError(null);

        // 从API获取已分页的HTML内容
        const response = await fetch(htmlUrl);

        // 检查返回状态
        if (!response.ok) {
          // 尝试解析错误JSON
          try {
            const errorData = await response.json();
            throw new Error(errorData.error || `Get failed, status code: ${response.status}`);
          } catch (parseError) {
            throw new Error(`Get failed, status code: ${response.status}`);
          }
        }

        // 检查内容类型，确定是HTML还是错误JSON
        const contentType = response.headers.get("content-type") || "";

        if (contentType.includes("application/json")) {
          // 如果返回的是JSON，可能是错误信息
          const errorData = await response.json();
          throw new Error(errorData.error || "Unknown error");
        }

        // 获取已分页的HTML内容
        const pagedHtmlContent = await response.text();

        // 安全地注入已分页的HTML内容
        if (containerRef.current) {
          // 首先移除所有现有内容
          containerRef.current.innerHTML = "";

          try {
            // 处理已分页的HTML内容
            const tempDiv = document.createElement("div");
            tempDiv.innerHTML = pagedHtmlContent;

            // 查找页面容器或页面元素
            let pagesToAdd: Element[] = [];

            // 首先尝试查找pages-container（完整的分页结构）
            const pagesContainer = tempDiv.querySelector('.pages-container');
            if (pagesContainer) {
              pagesToAdd = Array.from(pagesContainer.children);
            } else {
              // 直接查找page元素
              const pages = tempDiv.querySelectorAll('.page');
              if (pages.length > 0) {
                pagesToAdd = Array.from(pages);
              } else {
                // 如果没找到分页结构，直接使用原始内容
                containerRef.current.innerHTML = pagedHtmlContent;
                return;
              }
            }

            // 直接添加所有页面元素到容器，并为每个页面添加初始样式
            pagesToAdd.forEach((page, index) => {
              const clonedPage = page.cloneNode(true) as HTMLElement;
              // 为每个页面添加初始的隐藏状态
              clonedPage.style.opacity = '0';
              clonedPage.style.transform = 'translateY(20px) scale(0.98)';
              clonedPage.style.transition = 'all 0.6s ease-out';
              clonedPage.setAttribute('data-page-index', index.toString());
              containerRef.current?.appendChild(clonedPage);
            });
          } catch (parseError) {
            // 直接使用原始内容作为备用
            containerRef.current.innerHTML = pagedHtmlContent;
          }
        }

        // 从URL获取文章ID
        const urlParts = htmlUrl.split("/");
        const extractedPaperId = urlParts[urlParts.indexOf("papers") + 1];
        if (extractedPaperId) {
          setPaperId(extractedPaperId);
        }

        setIsLoading(false);

        // 内容加载完成后，计算并应用最佳缩放比例
        setTimeout(() => {
          const newScale = calculateOptimalScale();
          if (newScale > 0) {
            zoomWithFocus(newScale);
          }
          // 获取预览容器引用
          if (containerRef.current && !previewContainerRef.current) {
            previewContainerRef.current = containerRef.current.closest(
              ".paged-preview"
            ) as HTMLElement;
          }

          // 为留言系统准备页面结构信息
          const pages = containerRef.current?.querySelectorAll('.page');
          if (pages && pages.length > 0) {
            // 这里可以添加更多针对留言系统的初始化逻辑
          }

          // 触发内容浮现效果
          setTimeout(() => {
            setIsContentVisible(true);

            // 触发每个页面的逐个浮现效果
            const pages = containerRef.current?.querySelectorAll('.page');
            if (pages) {
              pages.forEach((page, index) => {
                setTimeout(() => {
                  const pageElement = page as HTMLElement;
                  pageElement.style.opacity = '1';
                  pageElement.style.transform = 'translateY(0) scale(1)';
                  setVisiblePages(prev => new Set([...prev, index]));
                }, index * 150); // 每个页面延迟150ms显示
              });
            }

            // 延迟显示工具栏，等所有页面都开始动画后
            const totalPageDelay = pages ? pages.length * 150 : 0;
            setTimeout(() => {
              setIsToolbarVisible(true);
            }, Math.max(300, totalPageDelay + 200));
          }, 200);
        }, 100); // 短暂延迟确保DOM已完全渲染
      } catch (err) {
        setError(err instanceof Error ? err.message : "Unknown error when loading document");
        setIsLoading(false);
      }
    }

    loadPagedHtmlContent();
  }, [htmlUrl]);

  // 打印媒体查询监听已通过纯CSS处理，无需JS调整

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="error-message p-8 text-red-500 bg-red-50 rounded-lg border border-red-200 max-w-md text-center">
          <h3 className="text-lg font-semibold mb-2">Load paper failed</h3>
          <p className="text-sm text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      {isLoading && (
        <div className="loading-overlay">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-500 mb-3"></div>
            <div className="text-lg">Loading paper preview...</div>
            <div className="text-sm text-gray-500 mt-1">Loading paged HTML content</div>
          </div>
        </div>
      )}

      <div
        ref={wrapperRef}
        className={`relative w-full flex justify-center paper-container transition-all duration-700 ease-out transform ${isContentVisible
          ? 'opacity-100 translate-y-0 scale-100'
          : 'opacity-0 translate-y-4 scale-98'
          }`}
      >
        <div
          ref={containerRef}
          className="pages-container"
          style={{
            transform: `scale(${scale})`,
            transformOrigin: "top center", // 默认居中，会被JavaScript动态调整
          }}
          data-scale={scale} // 添加数据属性便于调试
        ></div>
        {containerRef.current && paperId && (
          <CommentSystem
            paperId={paperId}
            containerRef={containerRef as React.RefObject<HTMLDivElement>}
            scale={scale}
            showComments={showComments}
            showHighlights={showHighlights}
            targetCommentId={targetCommentId}
            onCommentFocused={() => setTargetCommentId(null)}
          />
        )}
      </div>

      {/* 工具栏 */}
      <div className={`preview-toolbar transition-all duration-500 ease-out transform ${isToolbarVisible
        ? 'opacity-100 translate-y-0 scale-100'
        : 'opacity-0 translate-y-2 scale-95'
        }`}>
        {/* 返回按钮 */}
        <button className="toolbar-button" onClick={handleGoBack} title="Back to paper list">
          <ArrowLeft size={16} />
        </button>

        {/* 分隔线 */}
        <div className="toolbar-divider"></div>

        {/* 缩放控制 */}
        <button className="toolbar-button" onClick={handleZoomIn} title="Zoom in">
          <ZoomIn size={16} />
        </button>
        <button className="toolbar-button" onClick={handleZoomOut} title="Zoom out">
          <ZoomOut size={16} />
        </button>
        <button className="toolbar-button" onClick={handleResetZoom} title="Reset zoom">
          <Maximize size={16} />
        </button>

        {/* 打印功能 */}
        <button className="toolbar-button" onClick={handlePrint} title="Print">
          <Printer size={16} />
        </button>

        {/* 分隔线 */}
        <div className="toolbar-divider"></div>

        {/* 注释控制按钮（合并高亮和留言功能） */}
        <button
          className={`toolbar-button ${showComments && showHighlights ? "active" : ""}`}
          onClick={toggleAnnotations}
          title={showComments && showHighlights ? "Hide annotations" : "Show annotations"}
        >
          <MessageSquareText size={16} />
        </button>
      </div>
    </>
  );
}
