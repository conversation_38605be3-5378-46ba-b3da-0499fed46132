"use client";

import { useEffect, useRef, useState } from "react";
import PaperCard from "./paper-card";
import ResponsivePaperSkeleton from "./ResponsivePaperSkeleton";

interface Paper {
  id: string;
  title: string;
  abstract: string;
  publishedAt: string;
  paperTags: string[];
  readNumber: number;
}

interface LazyPaperCardProps {
  paper: Paper;
  delay?: number; // 可选的延迟时间，让加载看起来更自然
}

export default function LazyPaperCard({ paper, delay = 0 }: LazyPaperCardProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const [isFadingIn, setIsFadingIn] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.disconnect(); // 一旦可见就停止观察
          }
        });
      },
      {
        rootMargin: "50px", // 提前50px开始加载
        threshold: 0.1, // 当10%可见时触发
      }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (isVisible) {
      // 添加小延迟让加载看起来更自然
      const timer = setTimeout(() => {
        setShouldRender(true);
        // 在下一帧开始淡入动画
        requestAnimationFrame(() => {
          setIsFadingIn(true);
        });
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [isVisible, delay]);

  return (
    <div ref={cardRef} className="aspect-[7/10] w-full">
      {shouldRender ? (
        <div
          className={`h-full w-full transition-all duration-500 ease-out transform ${isFadingIn
            ? 'opacity-100 translate-y-0 scale-100'
            : 'opacity-0 translate-y-2 scale-95'
            }`}
        >
          <PaperCard paper={paper} />
        </div>
      ) : (
        <ResponsivePaperSkeleton />
      )}
    </div>
  );
}


