"use client";

import { useState } from "react";
import { useFloatingWindowNavigation } from "@/lib/floating-window-navigation";

import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Badge } from "@/components/ui/badge_papers";
import { Card, CardContent, CardTitle } from "@/components/ui/card_paper";

interface Paper {
  id: string;
  title: string;
  abstract: string;
  publishedAt: string;
  paperTags: string[];
  readNumber: number;
}

interface PaperCardProps {
  paper: Paper;
}

/* PaperCard.tsx */
// Image component with fallback for loading and error states
function ImageWithFallback({ src, alt, paperId }: { src?: string; alt: string; paperId: string }) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // 使用新的assets API路径获取promo.png图片
  const imageSrc = paperId ? `/api/papers/${paperId}/assets?path=promo.png` : src || "";

  return (
    <div className="relative h-full w-full">
      {/* Brand-colored placeholder shown during loading or on error */}
      <div className={`absolute inset-0 bg-brand-800 ${!isLoading && !hasError ? "hidden" : ""}`} />

      {/* Actual image, hidden if error occurred */}
      <img
        src={imageSrc}
        alt={alt}
        className={`h-full w-full object-cover ${hasError ? "hidden" : ""}`}
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setIsLoading(false);
          setHasError(true);
        }}
      />
    </div>
  );
}

export default function PaperCard({ paper }: PaperCardProps) {
  const { id, title, abstract, publishedAt, paperTags, readNumber } = paper;
  const { navigateToPaper, isInFloatingWindow } = useFloatingWindowNavigation();

  // 处理卡片点击
  const handleCardClick = async (e: React.MouseEvent) => {
    e.preventDefault();

    try {
      await navigateToPaper(id, title);
    } catch (error) {
      console.error('导航失败:', error);
      // 降级到传统导航
      window.open(`/paper/${id}`, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div
      onClick={handleCardClick}
      role="button"
      tabIndex={0}
      aria-label={`查看论文: ${title}`}
      className="group/paper block aspect-[7/10] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand-50 cursor-pointer"
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleCardClick(e as any);
        }
      }}
    >
      {/* Card 填满盒子，高度一致 */}
      <Card className="relative h-full flex flex-col overflow-hidden group-focus-within/paper:shadow-lg duration-200">
        {/* 图片区域 */}
        <AspectRatio ratio={1}>
          <ImageWithFallback paperId={id} src="" alt="" />
        </AspectRatio>

        {/* 内容区域 - 使用流体响应式设计 */}
        <CardContent
          className="flex flex-col flex-1 px-0"
          style={{
            /* 响应式内边距：减小padding确保内容不被挡住 */
            padding: 'clamp(6px, 1.2vw, 12px)',
            /* 响应式间距：元素间距随容器缩放 */
            gap: 'clamp(3px, 0.6vw, 8px)',
            /* 确保flex布局正确分配空间 */
            display: 'flex',
            flexDirection: 'column',
            minHeight: 0, /* 防止flex子项溢出 */
          }}
        >
          {/* 标题 - 使用clamp实现平滑字体缩放 */}
          <CardTitle
            className="font-semibold leading-tight line-clamp-2"
            style={{
              fontSize: 'clamp(10px, 2vw, 16px)', /* 稍微减小字体 */
              lineHeight: 'clamp(1.2, 1.25, 1.3)', /* 减小行高 */
              marginBottom: 'clamp(2px, 0.5vw, 6px)', /* 添加底部间距 */
            }}
          >
            {title}
          </CardTitle>

          {/* Tags - 响应式标签布局 */}
          <div
            className="flex flex-wrap"
            style={{
              gap: 'clamp(2px, 0.4vw, 4px)', /* 减小间距 */
              marginBottom: 'clamp(2px, 0.5vw, 6px)', /* 添加底部间距 */
            }}
          >
            {paperTags.slice(0, 3).map(tag => (
              <Badge
                key={tag}
                variant="default"
                className="px-1 py-0.5 shrink-0"
                style={{
                  fontSize: 'clamp(7px, 1.3vw, 10px)', /* 减小字体 */
                  padding: 'clamp(1px, 0.2vw, 3px) clamp(2px, 0.6vw, 6px)', /* 减小padding */
                }}
              >
                {tag}
              </Badge>
            ))}
            {paperTags.length > 3 && (
              <Badge
                variant="outline"
                className="px-1 py-0.5 shrink-0"
                style={{
                  fontSize: 'clamp(7px, 1.3vw, 10px)', /* 减小字体 */
                  padding: 'clamp(1px, 0.2vw, 3px) clamp(2px, 0.6vw, 6px)', /* 减小padding */
                }}
              >
                +{paperTags.length - 3}
              </Badge>
            )}
          </div>

          {/* 发布日期和阅读数 - 响应式底部信息 */}
          <div
            className="mt-auto flex items-center justify-between text-gray-600"
            style={{
              fontSize: 'clamp(7px, 1.5vw, 12px)', /* 减小字体确保显示完整 */
              marginTop: 'auto', /* 确保推到底部 */
              paddingTop: 'clamp(2px, 0.5vw, 4px)', /* 添加顶部间距 */
              flexShrink: 0, /* 防止被压缩 */
            }}
          >
            <span>{publishedAt ? new Date(publishedAt).toISOString().slice(0, 10) : "—"}</span>
            <span>Read {readNumber}</span>
          </div>
        </CardContent>

        {/* Hover Overlay - 响应式悬停效果 */}
        <div
          className="pointer-events-none absolute inset-0 z-10 flex items-center justify-center
                     bg-brand-50 text-brand-600 opacity-0
                     transition-opacity duration-300
                     group-hover/paper:opacity-100 group-focus-within/paper:opacity-100 group-active/paper:opacity-100"
          style={{
            padding: 'clamp(4px, 2vw, 16px)',
          }}
        >
          <p
            className="line-clamp-20 text-left leading-relaxed"
            style={{
              fontSize: 'clamp(9px, 2vw, 16px)',
              lineHeight: 'clamp(1.3, 1.5, 1.6)',
            }}
          >
            {abstract}
          </p>
        </div>
      </Card>
    </div>
  );
}
