/* ===== 现代化响应式Paper卡片网格布局 ===== */

/* CSS自定义属性：稳定的响应式设计变量 */
:root {
  /* 基础网格配置 - 使用固定列数策略 */
  --paper-grid-columns: 3; /* 默认3列 */
  --paper-grid-gap: clamp(16px, 2vw, 24px);
  --paper-grid-padding: 0; /* 取消外框padding */

  /* 动画变量 */
  --paper-transition-duration: 0.2s;
  --paper-transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 基础网格容器 - 使用稳定的固定列数策略 */
.papers-grid-container {
  display: grid;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;

  /* 固定列数网格：避免列数频繁变化 */
  grid-template-columns: repeat(var(--paper-grid-columns), 1fr);

  /* 使用CSS变量的响应式间距 */
  gap: var(--paper-grid-gap);
  padding: var(--paper-grid-padding);

  /* 确保网格居中对齐 */
  justify-content: center;
  align-content: start;

  /* 容器查询准备 */
  container-type: inline-size;
  container-name: papers-grid;
}

/* 向后兼容的papers-grid类 */
.papers-grid {
  display: grid;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;

  /* 与papers-grid-container保持一致 */
  grid-template-columns: repeat(var(--paper-grid-columns), 1fr);
  gap: var(--paper-grid-gap);
  padding: var(--paper-grid-padding);
  justify-content: center;
  align-content: start;

  /* 容器查询准备 */
  container-type: inline-size;
  container-name: papers-grid;
}

/* ===== 稳定的响应式断点策略 ===== */

/* 超大屏幕：4列布局 */
@media (min-width: 1400px) {
  :root {
    --paper-grid-columns: 4;
    --paper-grid-gap: clamp(20px, 2vw, 28px);
  }
}

/* 大屏幕：3列布局 */
@media (min-width: 900px) and (max-width: 1399px) {
  :root {
    --paper-grid-columns: 3;
    --paper-grid-gap: clamp(16px, 2vw, 24px);
  }
}

/* 中等屏幕：2列布局 */
@media (min-width: 600px) and (max-width: 899px) {
  :root {
    --paper-grid-columns: 2;
    --paper-grid-gap: clamp(12px, 2vw, 20px);
  }
}

/* 小屏幕：2列布局（较小间距） */
@media (min-width: 400px) and (max-width: 599px) {
  :root {
    --paper-grid-columns: 2;
    --paper-grid-gap: clamp(8px, 2vw, 16px);
  }
}

/* 极小屏幕：1列布局 */
@media (max-width: 399px) {
  :root {
    --paper-grid-columns: 1;
    --paper-grid-gap: clamp(8px, 2vw, 12px);
  }
}

/* ===== 容器查询支持（现代浏览器） ===== */
/* 基于容器宽度的稳定列数策略 */
@container papers-grid (min-width: 1400px) {
  .papers-grid-container,
  .papers-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: clamp(20px, 2cqw, 28px);
  }
}

@container papers-grid (min-width: 900px) and (max-width: 1399px) {
  .papers-grid-container,
  .papers-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: clamp(16px, 2cqw, 24px);
  }
}

@container papers-grid (min-width: 600px) and (max-width: 899px) {
  .papers-grid-container,
  .papers-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: clamp(12px, 2cqw, 20px);
  }
}

@container papers-grid (min-width: 400px) and (max-width: 599px) {
  .papers-grid-container,
  .papers-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: clamp(8px, 2cqw, 16px);
  }
}

@container papers-grid (max-width: 399px) {
  .papers-grid-container,
  .papers-grid {
    grid-template-columns: repeat(1, 1fr);
    gap: clamp(8px, 2cqw, 12px);
  }
}

/* ===== 性能优化 ===== */
/* 为网格项目添加will-change以优化动画性能 */
.papers-grid-container > *,
.papers-grid > * {
  will-change: transform, opacity;
  contain: layout style paint;
}

/* 减少重绘和回流 */
.papers-grid-container,
.papers-grid {
  contain: layout style;
}
