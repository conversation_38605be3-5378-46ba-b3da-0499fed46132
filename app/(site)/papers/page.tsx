"use client";

import { PaperType } from "@prisma/client";
import { SlidersHorizontal } from "lucide-react";
import { useSearch<PERSON><PERSON><PERSON>, useRouter, usePathname } from "next/navigation";
import qs from "qs";
import * as React from "react";
import { useEffect, useRef, useState, useCallback } from "react";

/* ───────── shadcn/ui ───────── */
import LazyPaperCard from "./LazyPaperCard";
import ResponsivePaperSkeleton, { ResponsivePaperSkeletonGrid } from "./ResponsivePaperSkeleton";

import { Input } from "@/components/ui/input_papers";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select_papers";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ontent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet_papers";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";

/* ───────── 样式 ───────── */
import "./papers.css";

/* ────────── 类型 ─────────── */
type Paper = {
  id: string;
  title: string;
  abstract: string;
  publishedAt: string;
  paperTags: string[];
  readNumber: number;
  type: PaperType;
};

type Issue = { id: string; name: string };

/* ────────── 常量 ─────────── */
const PAGE_SIZE = 3;

/* ────────── 组件 ─────────── */
export default function PublicPapersPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  /* ----- UI state ----- */
  const [sort, setSort] = useState<"new" | "views" | "comments">("new");
  const [issue, setIssue] = useState<string>("ALL");
  const [search, setSearch] = useState<string>("");
  const [paperType, setPaperType] = useState<"ALL" | "FULL" | "GALLERY" | "PREPRINT">("FULL"); // 默认改为FULL

  /* ----- data state ----- */
  const [issues, setIssues] = useState<Issue[]>([]);
  const [papers, setPapers] = useState<Paper[]>([]);
  const [skip, setSkip] = useState(0); // 已加载条目
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  /* ----- URL参数初始化 ----- */
  useEffect(() => {
    const urlSort = searchParams.get("sort") as "new" | "views" | "comments" | null;
    const urlIssue = searchParams.get("issue");
    const urlSearch = searchParams.get("search");
    const urlPaperType = searchParams.get("paperType") as "ALL" | "FULL" | "GALLERY" | "PREPRINT" | null;

    // 从URL参数初始化状态，如果没有参数则使用默认值
    if (urlSort && ["new", "views", "comments"].includes(urlSort)) {
      setSort(urlSort);
    }
    if (urlIssue) {
      setIssue(urlIssue);
    }
    if (urlSearch) {
      setSearch(urlSearch);
    }
    if (urlPaperType && ["ALL", "FULL", "GALLERY", "PREPRINT"].includes(urlPaperType)) {
      setPaperType(urlPaperType);
    }
  }, [searchParams]);

  /* ----- 更新URL参数 ----- */
  const updateURL = useCallback((newParams: {
    sort?: string;
    issue?: string;
    search?: string;
    paperType?: string;
  }) => {
    const current = new URLSearchParams(searchParams.toString());

    Object.entries(newParams).forEach(([key, value]) => {
      if (value && value !== "ALL" && value !== "") {
        current.set(key, value);
      } else {
        current.delete(key);
      }
    });

    const queryString = current.toString();
    const newURL = queryString ? `${pathname}?${queryString}` : pathname;

    // 使用replace避免在浏览器历史中创建过多条目
    router.replace(newURL, { scroll: false });
  }, [searchParams, pathname, router]);

  /* ----- fetch Issues once ----- */
  useEffect(() => {
    (async () => {
      try {
        const res = await fetch("/api/issues?fields=id,name");
        const data = (await res.json()) as Issue[];
        setIssues(data);
      } catch {
        /* 忽略错误，issues 为空时仅显示 ALL */
      }
    })();
  }, []);

  /* ----- 重置 & 加载第一页 ----- */
  const resetAndLoad = useCallback(async () => {
    // 重置所有相关状态
    isLoadingRef.current = false; // 重置加载锁
    setLoading(true);
    setSkip(0);
    setPapers([]); // 先清空现有数据
    setHasMore(true); // 重置为有更多数据

    try {
      const query = qs.stringify(
        { skip: 0, take: PAGE_SIZE, sort, issue, search, paperType },
        { addQueryPrefix: true, encodeValuesOnly: true }
      );
      const res = await fetch(`/api/papers${query}`);
      const data = (await res.json()) as { papers: Paper[] };

      // 数据去重处理
      const uniquePapers = data.papers.filter(
        (paper, index, self) => index === self.findIndex(p => p.id === paper.id)
      );

      setPapers(uniquePapers);
      setSkip(uniquePapers.length);
      setHasMore(uniquePapers.length === PAGE_SIZE);
    } catch (error) {
      console.error('[Papers] 重置加载失败:', error);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [sort, issue, search, paperType]);

  /* ----- 加载更多 (无限滚动) ----- */
  const loadMoreRef = useRef<(() => Promise<void>) | null>(null);
  const isLoadingRef = useRef(false);

  const loadMore = useCallback(async () => {
    // 防止重复调用
    if (isLoadingRef.current || !hasMore) {
      return;
    }

    isLoadingRef.current = true;
    setLoading(true);

    try {
      const query = qs.stringify(
        { skip, take: PAGE_SIZE, sort, issue, search, paperType },
        { addQueryPrefix: true, encodeValuesOnly: true }
      );
      const res = await fetch(`/api/papers${query}`);
      const data = (await res.json()) as { papers: Paper[] };

      // 数据去重处理
      const uniqueNewPapers = data.papers.filter(
        (newPaper, index, self) => index === self.findIndex(p => p.id === newPaper.id)
      );

      setPapers(prev => {
        // 与现有数据合并并去重
        const combined = [...prev, ...uniqueNewPapers];
        const deduped = combined.filter(
          (paper, index, self) => index === self.findIndex(p => p.id === paper.id)
        );
        return deduped;
      });

      setSkip(prev => prev + uniqueNewPapers.length);
      setHasMore(uniqueNewPapers.length === PAGE_SIZE);

    } catch (error) {
      console.error('[Papers] 加载更多失败:', error);
    } finally {
      isLoadingRef.current = false;
      setLoading(false);
    }
  }, [skip, sort, issue, search, hasMore, paperType]);

  // 更新loadMoreRef
  useEffect(() => {
    loadMoreRef.current = loadMore;
  }, [loadMore]);

  /* 参数变化时加载数据 */
  useEffect(() => {
    resetAndLoad();
  }, [resetAndLoad]);

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      // 清理所有ref
      loadMoreRef.current = null;
      isLoadingRef.current = false;

      // 确保observer被清理
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };
  }, []);

  /* ----- IntersectionObserver 触底加载 ----- */
  const sentinelRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // 初始化 IntersectionObserver，使用稳定的回调函数
  useEffect(() => {
    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      const entry = entries[0];
      if (entry.isIntersecting) {
        // 使用ref中的最新函数，避免依赖问题
        if (loadMoreRef.current) {
          loadMoreRef.current();
        }
      }
    };

    // 创建观察器，使用更敏感的设置
    observerRef.current = new IntersectionObserver(handleIntersection, {
      root: null, // 使用viewport作为root
      rootMargin: '100px', // 提前100px触发
      threshold: 0.1 // 10%可见时触发
    });

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };
  }, []); // 空依赖数组，只初始化一次

  // 观察sentinel元素 - 在papers数据变化时重新设置观察
  useEffect(() => {
    const sentinel = sentinelRef.current;
    const observer = observerRef.current;

    if (sentinel && observer) {
      observer.observe(sentinel);

      return () => {
        if (observer && sentinel) {
          observer.unobserve(sentinel);
        }
      };
    }
  }, [papers.length]); // 依赖papers长度的变化，确保在数据更新后重新观察

  // 添加滚动结束后的安全检查机制
  useEffect(() => {
    let scrollTimeout: NodeJS.Timeout;

    const handleScrollEnd = () => {
      // 滚动结束后检查sentinel是否在视窗内
      const sentinel = sentinelRef.current;
      if (sentinel && hasMore && !isLoadingRef.current) {
        const rect = sentinel.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

        if (isVisible) {
          if (loadMoreRef.current) {
            loadMoreRef.current();
          }
        }
      }
    };

    const handleScroll = () => {
      // 清除之前的定时器
      clearTimeout(scrollTimeout);
      // 设置新的定时器，滚动结束200ms后执行检查
      scrollTimeout = setTimeout(handleScrollEnd, 200);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, [hasMore]); // 依赖hasMore状态

  /* ----- 状态变化时同步URL ----- */
  useEffect(() => {
    updateURL({ sort, issue, search, paperType });
  }, [sort, issue, search, paperType, updateURL]);

  /* === 可复用控件组 === */
  const Controls = (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-end w-full">
      {/* Sort */}
      <div className="flex items-center gap-2 w-full md:w-auto">
        <span className="text-sm shrink-0">Sort by</span>
        <Select value={sort} onValueChange={v => setSort(v as any)}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Sort" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="new">Newest</SelectItem>
            <SelectItem value="views">Most Views</SelectItem>
            <SelectItem value="comments">Most Comments</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Issue - 仅在非Gallery和非Preprint模式下显示 */}
      {paperType !== "GALLERY" && paperType !== "PREPRINT" && (
        <div className="flex items-center gap-2 w-full md:w-auto">
          <span className="text-sm shrink-0">Issue</span>
          <Select value={issue} onValueChange={setIssue}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Issue" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">ALL</SelectItem>
              {issues.map(is => (
                <SelectItem key={is.id} value={is.id}>
                  {is.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Search */}
      <Input
        placeholder="Search title, author, tag, affiliation…"
        className="w-[280px] md:max-w-sm"
        value={search}
        onChange={e => setSearch(e.currentTarget.value)}
        onKeyDown={e => {
          if (e.key === "Enter") resetAndLoad();
        }}
      />
    </div>
  );

  /* ----- 获取当前paper类型的显示名称 ----- */
  const getPaperTypeDisplayName = () => {
    switch (paperType) {
      case "FULL": return "Full Papers";
      case "GALLERY": return "Gallery Short Papers";
      case "PREPRINT": return "Preprint Papers";
      default: return "All Papers";
    }
  };

  /* ───────────────────────────── JSX ───────────────────────────── */
  return (
    <section className="mx-auto py-4 sm:py-6 lg:py-8 space-y-4 sm:space-y-6">
      {/* 统一的内容容器 */}
      <div className="mx-auto max-w-[1400px] px-5 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            {getPaperTypeDisplayName()}
          </h1>
          {paperType !== "ALL" && (
            <p className="text-sm text-gray-600">
              Showing {paperType.toLowerCase()} papers
              {issue !== "ALL" && issues.find(i => i.id === issue) &&
                ` from ${issues.find(i => i.id === issue)?.name}`}
              {search && ` matching "${search}"`}
            </p>
          )}
        </div>

        {/* ① 控件组：桌面右对齐 + 手机抽屉 */}
        <div className="hidden md:flex w-full mb-4 sm:mb-6">{Controls}</div>

        {/* 手机：浮动按钮 + 底部抽屉 */}
        <Sheet>
          <SheetTrigger asChild>
            <button
              aria-label="Filter & Sort"
              className="md:hidden fixed bottom-4 right-4 z-50 rounded-full bg-brand-600
                         p-3 shadow-lg text-white"
            >
              <SlidersHorizontal className="size-5" />
            </button>
          </SheetTrigger>

          <SheetContent
            side="bottom"
            className="md:hidden max-h-[70vh] overflow-y-auto px-6 py-6 space-y-4"
          >
            <SheetHeader>
              <SheetTitle>Filter & Sort</SheetTitle>
            </SheetHeader>
            {Controls}
          </SheetContent>
        </Sheet>

        {/* ② 列表：动态自适应网格布局 */}
        <div className="papers-grid-container">
          {papers.length > 0 ? (
            papers.map((p, index) => (
              <LazyPaperCard
                key={`${p.id}-${index}`} // 使用组合key确保唯一性
                paper={p}
                delay={index * 50} // 每个card延迟50ms，创造波浪式加载效果
              />
            ))
          ) : !loading ? (
            <div className="col-span-full text-center py-12">
              <p className="text-muted-foreground">暂无内容</p>
            </div>
          ) : (
            // 初始加载skeleton - 使用响应式组件
            Array.from({ length: 3 }).map((_, i) => (
              <ResponsivePaperSkeleton key={`initial-skeleton-${i}`} />
            ))
          )}
        </div>

        {/* ③ Skeleton & sentinel */}
        <div className="mt-6">
          {loading && <ResponsivePaperSkeletonGrid count={3} />}

          {/* Sentinel元素：用于触发无限滚动，确保有足够的高度被IntersectionObserver检测到 */}
          <div
            ref={sentinelRef}
            className="h-10 w-full"
            style={{
              minHeight: '40px',
              // 调试模式下可以显示sentinel元素
              // backgroundColor: 'rgba(255, 0, 0, 0.1)',
              // border: '1px dashed red'
            }}
          />

          {!hasMore && !loading && papers.length > 0 && (
            <p className="text-center text-sm text-muted-foreground py-4">— No more papers —</p>
          )}

          {!hasMore && !loading && papers.length === 0 && (
            <p className="text-center text-sm text-muted-foreground py-8">暂无内容</p>
          )}
        </div>
      </div>
    </section>
  );
}
