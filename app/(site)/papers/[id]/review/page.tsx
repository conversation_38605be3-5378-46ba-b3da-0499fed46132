// app/papers/[id]/review/page.tsx
import { Role } from "@prisma/client";
import { CommentType, PaperStatus } from "@prisma/client";
import { FilePlus, Hourglass, PenLine, CheckCircle, XCircle } from "lucide-react";
import { notFound, redirect } from "next/navigation";
import ReactMarkdown from "react-markdown";
import remarkGemoji from "remark-gemoji";
import remarkGfm from "remark-gfm";

import CommentForm from "./CommentForm";
import EditorPanel from "./EditorPanel";
import NotifyButtons from "./NotifyButtons";
import ResponsiveLayout from "./ResponsiveLayout.client";
import ReviewPreviewPanel from "./ReviewPreviewPanel.client";
import ScrollToBottom from "./ScrollToBottom";
import VisibilityToggle from "./VisibilityToggle";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge_papers";
import { StatusBadge } from "@/components/ui/StatusBadge";
import { TypeBadge } from "@/components/ui/TypeBadge";
import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";
import { createRoleAvatarStyle, getRoleAbbreviation } from "@/lib/reviewAvatars";
import { formatDateTime } from "@/lib/dateUtils";
import { checkPaperAccess, checkCommentPermission } from "@/lib/auth/permissions";
import { getClientIP, getUserAgent } from "@/lib/security/utils";
import { inputValidation } from "@/lib/security/utils";

// 将ENUM格式（如FIRST_CONTENT_CHECK）转换为更友好的格式（如First Content Check）
function formatEnumName(enumName: string): string {
  return enumName
    .split("_")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}



/* ---------- 页面 ---------- */
export default async function ReviewPage({ params }: { params: Promise<{ id: string }> }) {
  /* 0. 路径参数验证 --------------------------------------------------- */
  const { id } = await params;

  // 验证论文ID格式
  let paperId: string;
  try {
    paperId = inputValidation.cuid.parse(id);
  } catch {
    notFound();
  }

  /* 1. 当前会话验证 --------------------------------------------------- */
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session?.user) redirect("/login");

  const uid = session.user.id;
  const clientIP = await getClientIP();
  const userAgent = await getUserAgent();

  /* 2. 企业级权限验证 --------------------------------------------- */
  const permissionResult = await checkPaperAccess(
    uid,
    paperId,
    [Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR, Role.PAPER_EDITOR, Role.REVIEWER, Role.AUTHOR],
    {
      requireAudit: true,
      ipAddress: clientIP,
      userAgent: userAgent,
    }
  );

  if (!permissionResult.allowed) {
    console.warn(`Access denied for user ${uid} to paper ${paperId}: ${permissionResult.reason}`);
    redirect("/403");
  }

  /* 3. 拉取论文数据 --------------------------------------------- */
  const paper = await prisma.paper.findUnique({
    where: { id: paperId },
    select: {
      id: true,
      title: true,
      status: true,
      type: true,
      authorId: true,
      issueId: true,
      authorCanReply: true,
      reviewersCanReply: true,
      needsAttention: true,
      issue: { select: { name: true } },
    },
  });
  if (!paper) notFound();

  /* 4. 获取用户角色和评论权限 ------------------------------- */
  const commentPermission = await checkCommentPermission(uid, paperId, {
    requireAudit: false, // 评论权限检查不需要审计
    ipAddress: clientIP,
    userAgent: userAgent,
  });

  const userRole = permissionResult.userRole!;
  const canReply = commentPermission.canReply;

  /* 5. 拉取评论数据 ------------------------------- */
  const comments = await prisma.reviewComment.findMany({
    where: { paperId },
    orderBy: { createdAt: "asc" },
    select: {
      id: true,
      body: true,
      type: true,
      createdAt: true,
      authorId: true,
      roleLabel: true,
      visibleToAuthor: true,
      visibleToReviewers: true,
    },
  });

  /* 6. 角色判断 --------------------------------------------------- */
  const editorRoles: Role[] = [Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR, Role.PAPER_EDITOR];
  const isEditor = editorRoles.includes(userRole);

  /* 7. Reviewer 池（仅 editor 需要） ----------------------------- */
  let reviewerPool: { id: string; name: string | null; email: string }[] = [];
  if (isEditor) {
    const reviewers = await prisma.user.findMany({
      where: { roleBindings: { some: { role: Role.REVIEWER } } },
      select: { id: true, name: true, email: true },
      take: 100,
    });
    reviewerPool = reviewers.map(u => ({
      id: u.id,
      name: u.name,
      email: u.email ?? "",
    }));
  }

  /* 8. 评论可见性过滤（服务端强制过滤，确保安全性） ----------- */
  const visibleComments = comments.filter(c => {
    // 编辑可以看到所有评论
    if (isEditor) return true;

    // 审稿人的可见性规则
    if (userRole === Role.REVIEWER) {
      // 审稿人评论：只能看自己的，或者设置为审稿人可见的其他审稿人评论
      if (c.roleLabel?.startsWith("Reviewer")) {
        return c.authorId === uid || c.visibleToReviewers;
      }
      // 编辑评论：严格根据审稿人可见性设置
      if (c.roleLabel?.includes("Editor") || c.roleLabel?.includes("Admin")) {
        return c.visibleToReviewers;
      }
      // 作者评论：严格根据审稿人可见性设置
      if (c.roleLabel?.startsWith("Author")) {
        return c.visibleToReviewers;
      }
      // 状态变更：根据审稿人可见性设置（如果有该字段）
      if (c.type === CommentType.STATUS_CHANGE) {
        return c.visibleToReviewers;
      }
      // 其他类型评论：默认不可见
      return false;
    }

    // 作者的可见性规则
    if (userRole === Role.AUTHOR) {
      // 审稿人评论：严格根据作者可见性设置
      if (c.roleLabel?.startsWith("Reviewer")) {
        return c.visibleToAuthor;
      }
      // 编辑评论：严格根据作者可见性设置
      if (c.roleLabel?.includes("Editor") || c.roleLabel?.includes("Admin")) {
        return c.visibleToAuthor;
      }
      // 自己的评论：总是可见
      if (c.authorId === uid) {
        return true;
      }
      // 状态变更：根据作者可见性设置（如果有该字段）
      if (c.type === CommentType.STATUS_CHANGE) {
        return c.visibleToAuthor;
      }
      // 其他类型评论：默认不可见
      return false;
    }

    // 其他角色默认不可见
    return false;
  });

  /* 9. 获取版本列表（通过安全API） ---- */
  let versions: string[] = ["draft"]; // 默认版本

  try {
    // 使用内部API调用，避免网络请求
    const { createClient } = await import("@supabase/supabase-js");
    const storage = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      { auth: { persistSession: false } }
    );

    const { data: versionObjs } = await storage.storage.from("submitted").list(paperId);

    if (versionObjs && versionObjs.length > 0) {
      const folderVersions = versionObjs
        .filter(v => v.id === null) // 只筛选文件夹
        .map(folder => {
          let priority = 0;
          if (/^v\d+$/.test(folder.name)) {
            const vNum = parseInt(folder.name.slice(1));
            priority = 1000 + vNum;
          } else if (folder.name.startsWith("revision_V")) {
            const vNum = parseInt(folder.name.match(/revision_V(\d+)/)?.[1] || "0");
            priority = 3000 + vNum; // 最高优先级
          } else if (folder.name === "revision") {
            priority = 2000;
          } else if (folder.name === "draft") {
            priority = 100; // 最低优先级
          } else {
            priority = 500;
          }
          return { name: folder.name, priority };
        })
        .sort((a, b) => b.priority - a.priority) // 按优先级降序排序
        .map(v => v.name);

      if (folderVersions.length > 0) {
        versions = folderVersions;
      }
    }
  } catch (error) {
    console.warn('Error fetching versions:', error);
    // 使用默认版本，不阻塞页面渲染
  }

  /* 10. JSX -------------------------------------------------------- */
  // ——— 定义左右两栏内容，避免重复 ———
  const previewNode = <ReviewPreviewPanel paperId={paper.id} versions={versions} />;

  const discussionNode = (
    <div className="flex-1 p-4 md:p-8 min-h-0 overscroll-contain" data-discussion-area>
      {/* 头部 */}
      <div className="mb-4">
        <h1 className="text-2xl font-bold">{paper.title}</h1>
        <div className="mt-2 flex items-center gap-2">
          <TypeBadge type={paper.type} />
          <StatusBadge status={paper.status} />
        </div>
      </div>

      {/* Editor 面板（仅 editor 可见）*/}
      {isEditor && (
        <EditorPanel
          paperId={paper.id}
          status={paper.status}
          authorCanReply={paper.authorCanReply}
          reviewersCanReply={paper.reviewersCanReply}
          needsAttention={paper.needsAttention}
          userRole={userRole}
          issueId={paper.issueId}
          issueName={paper.issue?.name}
        />
      )}

      {/* 时间线 */}
      <ol className="mt-4 md:mt-8 space-y-0 relative">
        {visibleComments.map((c, idx) => {
          /* ----- 1. 垂直线 (精确控制位置) ----- */
          const isFirst = idx === 0;
          const isLast = idx === visibleComments.length - 1;

          const line = (
            <span
              className="absolute -z-10 w-0.5 bg-gray-200"
              style={{
                left: "calc(5rem - 5px)", // 精确位置调整以对齐图标中心
                top: isFirst ? "1.5rem" : "0",
                bottom: isLast ? "1.5rem" : "0",
              }}
              aria-hidden
            />
          );

          /* ----- 2. 状态图标映射 ----- */
          const iconMap: Record<string, any> = {
            SUBMITTED: FilePlus,
            UNDER_REVIEW: Hourglass,
            REVISION: PenLine,
            ACCEPTED: CheckCircle,
            REJECTED: XCircle,
          };

          /* ----- 3. 获取状态及图标 ----- */
          let newStatus = "";
          if (c.type === CommentType.STATUS_CHANGE) {
            // 检查是否包含状态文本
            const m = c.body.match(/\*\*(.+?)\*\*/);
            if (m) {
              newStatus = m[1];
            } else if (c.body.includes("submitted")) {
              // 处理作者提交的情况，例如"Revision has been **submitted**"
              newStatus = c.roleLabel === "Author" ? "REVISION_SUBMITTED" : "UPDATE";
            } else {
              newStatus = "UPDATE";
            }
          }
          const IconCmp = iconMap[newStatus as keyof typeof iconMap] || PenLine;

          /* ----- 4. 头像节点 ----- */
          // 正常大头像（用于评论）
          const avatarNode = (
            <Avatar className="h-8 w-8 mt-1 shrink-0">
              <AvatarFallback
                style={createRoleAvatarStyle(c.roleLabel, 'medium')}
              >
                {getRoleAbbreviation(c.roleLabel)}
              </AvatarFallback>
            </Avatar>
          );

          // 小头像（用于状态变更行内显示）
          const smallAvatarNode = (
            <Avatar className="h-5 w-5 shrink-0">
              <AvatarFallback
                style={createRoleAvatarStyle(c.roleLabel, 'small')}
              >
                {getRoleAbbreviation(c.roleLabel)}
              </AvatarFallback>
            </Avatar>
          );

          /* ----- 5. 左侧节点（图标或头像） ----- */
          let bulletNode: React.ReactNode;
          if (c.type === CommentType.STATUS_CHANGE) {
            bulletNode = (
              <span className="flex items-center justify-center h-6 w-6 shrink-0 rounded-full bg-gray-100 border-2 border-white">
                <IconCmp className="w-3.5 h-3.5 text-gray-500" />
              </span>
            );
          } else {
            bulletNode = avatarNode;
          }

          /* ----- 6. 渲染节点 ----- */
          if (c.type === CommentType.STATUS_CHANGE) {
            return (
              <li key={c.id} id={`comment-${c.id}`} className="relative flex items-center gap-4 pl-16 py-4">
                {line}
                <div className="flex items-center self-start">{bulletNode}</div>
                <div className="text-xs flex items-center gap-2 flex-1">
                  {smallAvatarNode}
                  <span className="font-medium text-sm">{c.roleLabel}</span>
                  {newStatus &&
                    (Object.values(PaperStatus).includes(newStatus as PaperStatus) ? (
                      <StatusBadge status={newStatus as PaperStatus} />
                    ) : (
                      <Badge variant="secondary">{formatEnumName(newStatus)}</Badge>
                    ))}
                  <time className="ml-auto text-gray-500" suppressHydrationWarning>
                    {formatDateTime(new Date(c.createdAt))}
                  </time>
                  {/* 状态变更的通知按钮 */}
                  {isEditor && (
                    <NotifyButtons
                      commentId={c.id}
                      paperId={paper.id}
                      userRole={userRole}
                      commentPreview={c.body}
                      isStatusChange={true}
                    />
                  )}
                </div>
              </li>
            );
          }

          // 普通 MESSAGE
          return (
            <li key={c.id} id={`comment-${c.id}`} className="relative flex gap-4 pl-0 py-4">
              {line}
              {bulletNode}
              {/* Comment box */}
              <div className="flex-1 border rounded-md bg-white">
                {/* Header */}
                <div className="flex items-center justify-between px-3 h-9 bg-gray-50 rounded-t-md text-xs text-gray-600">
                  <span className="font-medium">{c.roleLabel}</span>
                  <div className="flex items-center gap-2">
                    <time suppressHydrationWarning>{formatDateTime(new Date(c.createdAt))}</time>
                    {isEditor && (
                      <>
                        <NotifyButtons
                          commentId={c.id}
                          paperId={paper.id}
                          userRole={userRole}
                          commentPreview={c.body}
                        />
                        <VisibilityToggle
                          commentId={c.id}
                          initAuthor={c.visibleToAuthor}
                          initReviewer={c.visibleToReviewers}
                        />
                      </>
                    )}
                  </div>
                </div>
                {/* Body */}
                <div className="px-3 py-2 prose prose-sm max-w-none">
                  <ReactMarkdown remarkPlugins={[remarkGfm, remarkGemoji]}>{c.body}</ReactMarkdown>
                </div>
              </div>
            </li>
          );
        })}
      </ol>

      {/* 回复表单 */}
      {canReply ? (
        <CommentForm
          paperId={paper.id}
          disabled={false}
          userRoleLabel={commentPermission.roleLabel}
        />
      ) : (
        <p className="text-gray-500 mt-6">Discussion is closed or you don't have permission to reply.</p>
      )}
    </div>
  );

  return (
    <main className="h-screen overflow-hidden">
      <ScrollToBottom />
      <ResponsiveLayout preview={previewNode} discussion={discussionNode} />
    </main>
  );
}
