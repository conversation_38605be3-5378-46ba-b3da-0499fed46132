// app/papers/[id]/review/VisibilityToggle.tsx
"use client";

import { Eye, EyeOff } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useTransition } from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Toggle } from "@/components/ui/toggle";

export default function VisibilityToggle({
  commentId,
  initAuthor,
  initReviewer,
}: {
  commentId: string;
  initAuthor: boolean;
  initReviewer: boolean;
}) {
  const router = useRouter();
  const [authorVis, setAuthorVis] = useState(initAuthor);
  const [reviewVis, setReviewVis] = useState(initReviewer);
  const [isPending, startTransition] = useTransition();
  const [showErrorDialog, setShowErrorDialog] = useState(false);

  /** PATCH helper */
  const patch = async (field: "visibleToAuthor" | "visibleToReviewers", val: boolean) => {
    const id = commentId.trim(); // ← 去掉尾部空白
    const url = `/api/review-comments/${encodeURIComponent(id)}/visibility`;
    const payload = { [field]: val };

    try {
      const res = await fetch(url, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      if (res.ok) {
        const json = await res.json();
        if (json.success) {
          setAuthorVis(json.visibleToAuthor);
          setReviewVis(json.visibleToReviewers);
          router.refresh();
        } else {
          throw new Error(json.error || 'Update failed');
        }
      } else {
        const errorData = await res.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `HTTP ${res.status}`);
      }
    } catch (error) {
      console.error('Visibility update failed:', error);
      // 回滚乐观更新
      if (field === "visibleToAuthor") setAuthorVis(p => !p);
      else setReviewVis(p => !p);
      setShowErrorDialog(true);
    }
  };

  return (
    <div className="flex gap-3 text-xs text-gray-600">
      <Toggle
        pressed={authorVis}
        disabled={isPending}
        onPressedChange={val =>
          startTransition(async () => {
            setAuthorVis(val); // 乐观更新
            await patch("visibleToAuthor", val);
          })
        }
      >
        <span className="flex items-center gap-1">
          <span>A</span>
          {authorVis ? (
            <Eye className="w-4 h-4" aria-label="Visible to Author" />
          ) : (
            <EyeOff className="w-4 h-4" aria-label="Hidden from Author" />
          )}
        </span>
      </Toggle>

      <Toggle
        pressed={reviewVis}
        disabled={isPending}
        onPressedChange={val =>
          startTransition(async () => {
            setReviewVis(val); // 乐观更新
            await patch("visibleToReviewers", val);
          })
        }
      >
        <span className="flex items-center gap-1">
          <span>R</span>
          {reviewVis ? (
            <Eye className="w-4 h-4" aria-label="Visible to Reviewers" />
          ) : (
            <EyeOff className="w-4 h-4" aria-label="Hidden from Reviewers" />
          )}
        </span>
      </Toggle>

      {/* 错误提示Dialog */}
      <AlertDialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>更新失败</AlertDialogTitle>
            <AlertDialogDescription>
              更新可见性失败，请稍后重试。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => setShowErrorDialog(false)}>
              确定
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
