"use client";

import { commands as mdCommands } from "@uiw/react-md-editor";
import EmojiPicker from "emoji-picker-react";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import { useState, FormEvent, useRef } from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { getRoleAvatarConfig } from "@/lib/reviewAvatars";

// 动态加载 MDEditor（避免 SSR 警告）
const MDEditor = dynamic(() => import("@uiw/react-md-editor"), { ssr: false });

export default function CommentForm({
  paperId,
  disabled,
  userRoleLabel
}: {
  paperId: string;
  disabled: boolean;
  userRoleLabel?: string;
}) {
  const [value, setValue] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const emojiButtonRef = useRef<HTMLSpanElement>(null);
  const router = useRouter();

  // 获取用户身份配置（用于显示身份badge）
  const userAvatarConfig = userRoleLabel ? getRoleAvatarConfig(userRoleLabel) : null;

  // 处理表单提交 - 显示确认对话框
  function handleSubmit(e: FormEvent) {
    e.preventDefault();
    if (!value.trim()) return;
    setShowConfirmDialog(true);
  }

  // 实际提交评论
  async function handleConfirmSubmit() {
    if (!value.trim()) return;

    setLoading(true);
    setShowConfirmDialog(false);

    try {
      // 发评论
      const res = await fetch(`/api/papers/${paperId}/review-comments`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ body: value }),
      });

      if (!res.ok) {
        const msg = await res.text();
        setErrorMessage(`Failed to send: ${msg || res.status}`);
        setShowErrorDialog(true);
        return;
      }

      // 获取新创建的评论ID
      const newComment = await res.json();
      const commentId = newComment.id;

      // 自动通知编辑者（如果有评论ID）
      if (commentId) {
        try {
          await fetch('/api/notifications/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              commentId: commentId,
              paperId: paperId,
              notifyType: 'EDITORS',
              message: value,
              notificationType: 'REVIEW_UPDATE'
            }),
          });
          // 静默发送通知，不显示toast（因为是自动的）
        } catch (notifyError) {
          // 不阻止正常流程，静默处理通知失败
        }
      }

      // 成功 → 复位 UI
      setValue("");
      router.refresh();
    } catch (err: any) {
      setErrorMessage(`Error: ${err.message ?? err}`);
      setShowErrorDialog(true);
    } finally {
      setLoading(false);
    }
  }

  // 处理添加表情
  const handleEmojiClick = (emojiData: any) => {
    setValue(prev => prev + emojiData.emoji);
    setShowEmojiPicker(false);
  };

  // 图标组件（非 button，避免嵌套）
  const EmojiIcon = (
    <span ref={emojiButtonRef} className="select-none cursor-pointer">
      😊
    </span>
  );

  return (
    <form onSubmit={handleSubmit} className="relative mt-8 space-y-3" data-color-mode="light">
      {/* Markdown 编辑/预览 */}
      <MDEditor
        value={value}
        onChange={v => setValue(v || "")}
        height={250}
        preview="edit"
        textareaProps={{ placeholder: "Use Markdown syntax to write comments..." }}
        className="border rounded"
        commands={(function () {
          const myCmd = {
            name: "emoji",
            keyCommand: "emoji",
            buttonProps: { "aria-label": "Insert emoji" },
            icon: EmojiIcon,
            execute: () => setShowEmojiPicker(s => !s),
          } as any;
          const baseCommands: any[] = (mdCommands as any).getCommands
            ? (mdCommands as any).getCommands()
            : Object.values(mdCommands as any);
          const idx = baseCommands.findIndex((c: any) => c.keyCommand === "table");
          if (idx === -1) return [...baseCommands, myCmd];
          const arr = [...baseCommands];
          arr.splice(idx + 1, 0, myCmd);
          return arr;
        })()}
      />

      {showEmojiPicker && (
        <div className="absolute bottom-full left-0 mb-2 z-20">
          <EmojiPicker
            className="emoji-picker-mini"
            onEmojiClick={handleEmojiClick}
            width={300}
            height={200}
            lazyLoadEmojis
            skinTonesDisabled
            searchDisabled
            previewConfig={{ showPreview: false }}
            style={
              {
                fontSize: "14px",
                "--epr-emoji-size": "20px",
                "--epr-picker-border-radius": "8px",
                "--epr-category-navigation-height": "16px",
                "--epr-category-icon-size": "16px",
              } as any
            }
          />
          <style jsx global>{`
            /* ---------------- 现有：隐藏冗余 ---------------- */
            .emoji-picker-mini .epr-emoji-category-label,
            .emoji-picker-mini .epr-preview {
              display: none !important;
            }
            .emoji-picker-mini .epr-emoji-category-content {
              padding-top: 4px;
            }

            /* ---------------- 新增：缩小 & 去圆圈 ---------------- */

            /* 整个导航行高度压缩 */
            .emoji-picker-mini .epr-category-nav {
              height: 32px !important; /* 行高 */
              padding: 0 4px !important; /* 左右留 4px */
            }

            /* 每个 tab 按钮去掉默认 padding，图标保持居中 */
            .emoji-picker-mini [role="tab"] {
              padding: 2px !important;
            }
          `}</style>
        </div>
      )}

      <div className="flex justify-between items-center">
        {/* 左侧：身份提示badge */}
        {userAvatarConfig && userRoleLabel && (
          <div
            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white"
            style={{ backgroundColor: userAvatarConfig.color }}
          >
            You are " {userAvatarConfig.abbreviation} "
          </div>
        )}

        {/* 右侧：提交按钮 */}
        <button
          type="submit"
          disabled={disabled || loading}
          className="bg-black text-white rounded-lg px-6 py-2 disabled:opacity-50"
        >
          {loading ? "Sending..." : "Submit comment"}
        </button>
      </div>

      {/* 确认提交Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm comment submission</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to submit this comment? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmSubmit}
              disabled={loading}
              className="bg-black text-white hover:bg-gray-800"
            >
              {loading ? "Sending..." : "Submit comment"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 错误提示Dialog */}
      <AlertDialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Submit failed</AlertDialogTitle>
            <AlertDialogDescription>
              {errorMessage}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => setShowErrorDialog(false)}>
              OK
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </form>
  );
}
