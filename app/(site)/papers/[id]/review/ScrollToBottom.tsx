"use client";

import { useEffect } from "react";

export default function ScrollToBottom() {
    useEffect(() => {
        const hash = window.location.hash;

        if (!hash) return;

        // 延迟滚动以确保内容已渲染
        setTimeout(() => {
            if (hash === '#bottom') {
                // 滚动到页面底部
                const discussionElement = document.querySelector('[data-discussion-area]');
                if (discussionElement) {
                    discussionElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'end'
                    });
                } else {
                    window.scrollTo({
                        top: document.body.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            } else if (hash.startsWith('#comment-')) {
                // 滚动到特定评论的开头，将开头放在屏幕中央
                const commentId = hash.substring(1); // 移除 # 号
                const commentElement = document.getElementById(commentId);

                if (commentElement) {
                    // 找到包含滚动的容器
                    const discussionContainer = document.querySelector('[data-discussion-area]')?.parentElement;

                    if (discussionContainer) {
                        // 计算评论元素相对于容器的位置
                        const containerRect = discussionContainer.getBoundingClientRect();
                        const commentRect = commentElement.getBoundingClientRect();

                        // 计算需要滚动的距离，使评论的开头位于屏幕中央
                        const scrollTop = discussionContainer.scrollTop +
                                        commentRect.top - containerRect.top -
                                        containerRect.height / 2;

                        // 平滑滚动到目标位置
                        discussionContainer.scrollTo({
                            top: Math.max(0, scrollTop),
                            behavior: 'smooth'
                        });
                    } else {
                        // 回退到默认的滚动行为，将评论开头放在中央
                        commentElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });
                    }
                }
            }
        }, 300);
    }, []);

    return null; // 这个组件不渲染任何内容
}