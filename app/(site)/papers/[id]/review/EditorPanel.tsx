"use client";

import { <PERSON><PERSON>tatus, Role } from "@prisma/client";
import { ChevronsUpDown, Check, Plus, Trash2, ChevronRight, ChevronDown, Loader2 } from "lucide-react";
import * as React from "react";
import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";

import StatusSelect from "@/app/(site)/papers/[id]/review/StatusSelect";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Command,
  CommandInput,
  CommandList,
  CommandItem,
  CommandEmpty,
  CommandGroup,
} from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { StatusBadge } from "@/components/ui/StatusBadge";
import { Switch } from "@/components/ui/switch";
import { getStatusBadgeConfig } from "@/lib/paperStatusConfig";

interface User {
  id: string;
  name: string | null;
  email: string;
}
interface Issue {
  id: string;
  name: string;
}

interface Row {
  alias: 1 | 2 | 3;
  user: User | null; // 已确认
  draftUser: User | null; // 刚刚选择、未确认
  originalUserId: string | null;
  assignmentId?: string;
  open: boolean;
}

interface EditorRow {
  user: User | null; // 已确认
  draftUser: User | null; // 刚刚选择、未确认
  originalUserId: string | null;
  roleBindingId?: string;
  open: boolean;
}

interface Props {
  paperId: string;
  status: PaperStatus;
  authorCanReply: boolean;
  reviewersCanReply: boolean;
  needsAttention: boolean;
  userRole: Role; // 当前用户角色
  issueId?: string | null; // 当前论文的期次ID
  issueName?: string | null; // 当前论文的期次名称
}

export default function EditorPanel(props: Props) {
  const { paperId, status, userRole } = props;
  const router = useRouter();

  /* ---------------- 权限控制 ---------------- */
  const isGlobalAdmin = userRole === Role.ADMIN || userRole === Role.CHIEF_EDITOR;
  const isIssueEditor = userRole === Role.ISSUE_EDITOR;
  const isPaperEditor = userRole === Role.PAPER_EDITOR;

  const canAssignIssue = isGlobalAdmin; // 只有全局管理员可以分配期次
  const canAssignEditor = isGlobalAdmin || isIssueEditor; // 全局管理员和期次编辑可以分配编辑
  const canAssignReviewers = isGlobalAdmin || isIssueEditor || isPaperEditor; // 所有编辑角色可以分配审稿人

  /* ---------------- 折叠状态 ---------------- */
  const [isExpanded, setIsExpanded] = useState(false);

  /* ---------------- state ---------------- */
  // 用户和期次数据
  const [users, setUsers] = useState<User[]>([]);
  const [issues, setIssues] = useState<Issue[]>([]);

  // 审稿人分配状态
  const [rows, setRows] = useState<Row[]>([
    { alias: 1, user: null, draftUser: null, originalUserId: null, open: false },
    { alias: 2, user: null, draftUser: null, originalUserId: null, open: false },
    { alias: 3, user: null, draftUser: null, originalUserId: null, open: false },
  ]);

  // 论文编辑分配状态
  const [editorRow, setEditorRow] = useState<EditorRow>({
    user: null,
    draftUser: null,
    originalUserId: null,
    open: false,
  });

  // 期次分配状态
  const [currentIssue, setCurrentIssue] = useState<Issue | null>(
    props.issueId && props.issueName ? { id: props.issueId, name: props.issueName } : null
  );
  const [draftIssue, setDraftIssue] = useState<Issue | null>(null);

  // 加载状态
  const [loadingIdx, setLoadingIdx] = useState<number | null>(null);
  const [loadingEditor, setLoadingEditor] = useState<boolean>(false);
  const [loadingIssue, setLoadingIssue] = useState<boolean>(false);
  const [loadingStatus, setLoadingStatus] = useState<boolean>(false);

  // 本地开关状态
  const [authorReply, setAuthorReply] = useState(props.authorCanReply);
  const [reviewerReply, setReviewerReply] = useState(props.reviewersCanReply);
  const [needsAtt, setNeedsAtt] = useState(props.needsAttention);

  // 状态变更
  const [statusDraft, setStatusDraft] = useState<PaperStatus | null>(null);
  const statusList = Object.values(PaperStatus);

  // 错误提示Dialog状态
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [showErrorDialog, setShowErrorDialog] = useState(false);

  /* ---------------- 初始化数据 ----------------- */
  useEffect(() => {
    (async () => {
      try {
        // 1) 加载用户列表
        const usersResponse = await fetch("/api/get_users");
        if (!usersResponse.ok) {
          return;
        }
        const us = (await usersResponse.json()) as User[];
        // 确保ID为字符串类型
        const normalizedUsers = us.map(u => ({
          ...u,
          id: typeof u.id === "number" ? String(u.id) : u.id,
          name: u.name || "(unnamed)",
        }));
        setUsers(normalizedUsers);

        // 2) 加载期次列表(如果有权限)
        if (canAssignIssue) {
          const issuesResponse = await fetch("/api/issues?fields=id,name&includeUnpublished=true");
          if (issuesResponse.ok) {
            const issuesList = (await issuesResponse.json()) as Issue[];
            setIssues(issuesList);
          }
        }

        // 3) 获取论文编辑信息
        if (canAssignEditor) {
          try {
            const editorsResponse = await fetch(`/api/papers/${paperId}/editors`);
            if (editorsResponse.ok) {
              const editors = await editorsResponse.json();
              if (editors && editors.length > 0) {
                const editor = editors[0]; // 默认只处理第一个编辑
                const editorUser: User = {
                  id: editor.userId,
                  name: editor.userName,
                  email: editor.userEmail,
                };
                setEditorRow({
                  user: editorUser,
                  draftUser: null,
                  originalUserId: editorUser.id,
                  roleBindingId: editor.roleBindingId,
                  open: false,
                });
              }
            }
          } catch (error) {
            // 静默处理获取编辑失败
          }
        }

        // 4) 获取审稿人分配
        if (canAssignReviewers) {
          const assignResponse = await fetch(`/api/papers/${paperId}/assign-reviewers`, {
            cache: "no-store",
            headers: { "Content-Type": "application/json" },
          });

          if (!assignResponse.ok) {
            return;
          }

          const asn = (await assignResponse.json()) as {
            alias: string; // "Reviewer 1" | "Reviewer 2" ...
            assignmentId?: string;
            userId: string;
            userName: string | null;
            userEmail: string;
          }[];

          const base = [...rows]; // 3 固定槽
          for (const a of asn) {
            // "Reviewer 1" → 1
            const num = parseInt(a.alias.replace(/\D/g, ""), 10);
            if (num >= 1 && num <= 3) {
              const u: User = {
                id: a.userId,
                name: a.userName,
                email: a.userEmail,
              };
              base[num - 1] = {
                alias: num as 1 | 2 | 3,
                user: u,
                draftUser: null,
                originalUserId: u.id,
                assignmentId: a.assignmentId,
                open: false,
              };
            }
          }
          setRows(base);
        }
      } catch (error) {
        setErrorMessage("Failed to load data, please refresh the page and try again.");
        setShowErrorDialog(true);
      }
    })();
  }, [paperId, canAssignIssue, canAssignEditor, canAssignReviewers]);

  // 审稿人行更新辅助函数
  const setRow = (i: number, patch: Partial<Row>) =>
    setRows(prev => {
      const n = [...prev];
      n[i] = { ...n[i], ...patch };
      return n;
    });

  /* ---------------- 判断是否有更改 ---------------- */
  const reviewerChanged = (r: Row) => !r.assignmentId && r.draftUser !== null;
  const editorChanged = !editorRow.roleBindingId && editorRow.draftUser !== null;
  const issueChanged = draftIssue !== null && draftIssue.id !== currentIssue?.id;

  /* ---------------- 处理审稿人分配 -------------- */
  const confirmReviewer = async (i: number) => {
    const r = rows[i];
    if (!reviewerChanged(r)) return;
    setLoadingIdx(i);

    try {
      /* 删除旧指派？(有 assignmentId 且 draftUser 为空或不同) */
      if (r.assignmentId) {
        const deleteResponse = await fetch(
          `/api/papers/${paperId}/assign-reviewers/${encodeURIComponent(`Reviewer ${r.alias}`)}`,
          { method: "DELETE" }
        );

        if (!deleteResponse.ok) {
          throw new Error("Failed to delete");
        }
      }

      /* 新建？(draftUser 存在) */
      if (r.draftUser) {
        if (!r.draftUser.id) {
          throw new Error("Reviewer ID cannot be empty");
        }

        const res = await fetch(`/api/papers/${paperId}/assign-reviewers`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            alias: `Reviewer ${r.alias}`,
            userId: r.draftUser.id,
          }),
        });

        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(`Failed to save: ${errorText}`);
        }

        const data = await res.json();

        setRow(i, {
          user: r.draftUser,
          draftUser: null,
          originalUserId: r.draftUser.id,
          assignmentId: String(data.id ?? data.assignmentId ?? r.alias),
        });
      } else {
        return;
      }
    } catch (error) {
      setErrorMessage(`Failed to update reviewer: ${error instanceof Error ? error.message : "Unknown error"}`);
      setShowErrorDialog(true);
    } finally {
      setLoadingIdx(null);
    }
  };

  // 删除审稿人
  const removeReviewer = async (idx: number) => {
    const r = rows[idx];
    if (!r.user) return; // 没有 reviewer，本应不出现按钮，保险起见再判空
    setLoadingIdx(idx);
    try {
      const aliasStr = encodeURIComponent(`Reviewer ${r.alias}`);
      const del = await fetch(`/api/papers/${paperId}/assign-reviewers/${aliasStr}`, {
        method: "DELETE",
      });
      if (!del.ok) throw new Error("delete failed");
      // clear slot
      setRow(idx, {
        user: null,
        draftUser: null,
        originalUserId: null,
        assignmentId: undefined,
      });
    } catch (e) {
      setErrorMessage("Failed to remove reviewer");
      setShowErrorDialog(true);
    } finally {
      setLoadingIdx(null);
    }
  };

  /* ---------------- 处理编辑分配 ---------------- */
  const confirmEditor = async () => {
    if (!editorChanged) return;
    setLoadingEditor(true);

    try {
      // 如果有已分配的编辑，先删除
      if (editorRow.roleBindingId) {
        const deleteResponse = await fetch(
          `/api/papers/${paperId}/editors/${editorRow.roleBindingId}`,
          { method: "DELETE" }
        );

        if (!deleteResponse.ok) {
          throw new Error("Failed to delete");
        }
      }

      // 添加新的编辑
      if (editorRow.draftUser) {
        if (!editorRow.draftUser.id) {
          throw new Error("Editor ID cannot be empty");
        }

        const res = await fetch(`/api/papers/${paperId}/editors`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            userId: editorRow.draftUser.id,
          }),
        });

        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(`Failed to save: ${errorText}`);
        }

        const data = await res.json();

        setEditorRow({
          ...editorRow,
          user: editorRow.draftUser,
          draftUser: null,
          originalUserId: editorRow.draftUser.id,
          roleBindingId: data.roleBindingId,
        });
      }
    } catch (error) {
      setErrorMessage(`Failed to update editor: ${error instanceof Error ? error.message : "Unknown error"}`);
      setShowErrorDialog(true);
    } finally {
      setLoadingEditor(false);
    }
  };

  // 删除编辑
  const removeEditor = async () => {
    if (!editorRow.roleBindingId || !editorRow.user) return;
    setLoadingEditor(true);
    try {
      const del = await fetch(`/api/papers/${paperId}/editors/${editorRow.roleBindingId}`, {
        method: "DELETE",
      });
      if (!del.ok) throw new Error("delete failed");
      setEditorRow({
        user: null,
        draftUser: null,
        originalUserId: null,
        roleBindingId: undefined,
        open: false,
      });
    } catch (e) {
      setErrorMessage("Failed to remove editor");
      setShowErrorDialog(true);
    } finally {
      setLoadingEditor(false);
    }
  };

  /* ---------------- 处理期次分配 ---------------- */
  const confirmIssue = async () => {
    if (!issueChanged) return;
    setLoadingIssue(true);

    try {
      const res = await fetch(`/api/papers/${paperId}/issue`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          issueId: draftIssue?.id || null,
        }),
      });

      if (!res.ok) {
        const errorText = await res.text();
        throw new Error(`Failed to save: ${errorText}`);
      }

      setCurrentIssue(draftIssue);
      setDraftIssue(null);
    } catch (error) {
      setErrorMessage(`Failed to update issue: ${error instanceof Error ? error.message : "Unknown error"}`);
      setShowErrorDialog(true);
    } finally {
      setLoadingIssue(false);
    }
  };

  /* ---------------- JSX ---------------- */
  return (
    <Accordion type="single" collapsible className="mb-6">
      <AccordionItem
        value="editor-panel"
        className="border border-solid border-gray-200 rounded-md overflow-hidden"
      >
        <AccordionTrigger className="px-4 py-2 text-base font-medium hover:no-underline">
          Editor Panel
        </AccordionTrigger>
        <AccordionContent>
          <div className="px-4 pb-3 space-y-3">
            {/* ────────── Issue Assignment (仅ADMIN和CHIEF_EDITOR可见) ────────── */}
            {canAssignIssue && (
              <div className="flex items-center justify-between pt-1">
                <div className="text-sm font-medium">Issue Assignment</div>

                <div className="flex items-center gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[220px] justify-between h-8">
                        {draftIssue?.name || currentIssue?.name || "Select issue..."}
                        <ChevronsUpDown className="ml-2 h-3 w-3 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[220px] p-0">
                      <Command>
                        <CommandInput placeholder="Search issue..." />
                        <CommandList>
                          <CommandEmpty>No matching result</CommandEmpty>
                          <CommandGroup>
                            <CommandItem
                              value="__unassigned__"
                              onSelect={() => setDraftIssue(null)}
                            >
                              <Check
                                className={`flex-shrink-0 mr-1 h-4 w-4 ${draftIssue === null && !currentIssue ? "" : "opacity-0"}`}
                              />
                              <span>Unassigned</span>
                            </CommandItem>
                            {issues.map(issue => (
                              <CommandItem
                                key={issue.id}
                                value={issue.id}
                                onSelect={() => setDraftIssue(issue)}
                              >
                                <div className="flex items-center w-full">
                                  <Check
                                    className={`flex-shrink-0 mr-1 h-4 w-4 ${draftIssue?.id === issue.id || (!draftIssue && currentIssue?.id === issue.id) ? "" : "opacity-0"}`}
                                  />
                                  <span className="truncate">{issue.name}</span>
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  <Button
                    variant="secondary"
                    className="w-[70px] h-8"
                    disabled={!issueChanged || loadingIssue}
                    onClick={confirmIssue}
                  >
                    {loadingIssue ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      "Confirm"
                    )}
                  </Button>
                </div>
              </div>
            )}

            {/* 添加分隔线 */}
            {canAssignIssue && canAssignEditor && <Separator className="my-3" />}

            {/* ────────── Editor Assignment (ADMIN/CHIEF_EDITOR/ISSUE_EDITOR可见) ────────── */}
            {canAssignEditor && (
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium">Editor Assignment</div>

                <div className="flex items-center gap-2">
                  {editorRow.user && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="destructive"
                          size="icon"
                          className="h-8 w-8"
                          disabled={loadingEditor}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Remove editor {editorRow.user.name}?</AlertDialogTitle>
                          <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={removeEditor} disabled={loadingEditor}>
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}

                  <Popover
                    open={editorRow.open}
                    onOpenChange={o => setEditorRow({ ...editorRow, open: o })}
                  >
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[220px] justify-between h-8">
                        {editorRow.draftUser?.name || editorRow.user?.name || "Select editor..."}
                        <ChevronsUpDown className="ml-2 h-3 w-3 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[220px] p-0">
                      <Command>
                        <CommandInput placeholder="Search user..." />
                        <CommandList>
                          <CommandEmpty>No matching result</CommandEmpty>
                          <CommandGroup>
                            <CommandItem
                              value="__unassigned__"
                              onSelect={() =>
                                setEditorRow({ ...editorRow, draftUser: null, open: false })
                              }
                            >
                              <Check
                                className={`mr-2 h-4 w-4 ${editorRow.draftUser === null ? "" : "opacity-0"}`}
                              />
                              Unassigned
                            </CommandItem>
                            {users.map(u => (
                              <CommandItem
                                key={u.id}
                                value={u.id}
                                onSelect={() =>
                                  setEditorRow({ ...editorRow, draftUser: u, open: false })
                                }
                              >
                                <Check
                                  className={`mr-2 h-4 w-4 ${editorRow.draftUser?.id === u.id ? "" : "opacity-0"}`}
                                />
                                {u.name}{" "}
                                <span className="text-xs text-muted-foreground">({u.email})</span>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  <Button
                    variant="secondary"
                    className="w-[70px] h-8"
                    disabled={!editorChanged || loadingEditor}
                    onClick={confirmEditor}
                  >
                    {loadingEditor ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      "Confirm"
                    )}
                  </Button>
                </div>
              </div>
            )}

            {/* 添加分隔线 */}
            {(canAssignEditor || canAssignIssue) && canAssignReviewers && (
              <Separator className="my-3" />
            )}

            {/* ────────── Reviewer rows (所有编辑角色可见) ────────── */}
            {canAssignReviewers && (
              <div className="space-y-3">
                {rows.map((row, i) => (
                  <div key={row.alias} className="flex items-center justify-between">
                    <div className="text-sm">
                      Reviewer {row.alias}: {row.user?.name || "Unassigned"}
                    </div>

                    <div className="flex items-center gap-2">
                      {row.user && (
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="destructive" size="icon" className="h-8 w-8">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Remove reviewer {row.alias}?</AlertDialogTitle>
                              <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => removeReviewer(i)}
                                disabled={loadingIdx === i}
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      )}

                      <Popover open={row.open} onOpenChange={o => setRow(i, { open: o })}>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className="w-[220px] justify-between h-8">
                            {row.draftUser
                              ? row.draftUser.name
                              : row.user
                                ? row.user.name
                                : "Select reviewer..."}
                            <ChevronsUpDown className="ml-2 h-3 w-3 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[220px] p-0">
                          <Command>
                            <CommandInput placeholder="Search user..." />
                            <CommandList>
                              <CommandEmpty>No matching result</CommandEmpty>
                              <CommandGroup>
                                <CommandItem
                                  value="__unassigned__"
                                  onSelect={() => {
                                    if (row.assignmentId) return; // already assigned – disallow
                                    setRow(i, { draftUser: null, open: false });
                                  }}
                                >
                                  <Check
                                    className={`mr-2 h-4 w-4 ${row.draftUser === null ? "" : "opacity-0"}`}
                                  />
                                  Unassigned
                                </CommandItem>
                                {users.map(u => (
                                  <CommandItem
                                    key={u.id}
                                    value={u.id}
                                    keywords={[u.name ?? "", u.email]}
                                    onSelect={() => {
                                      if (row.assignmentId) return;
                                      setRow(i, { draftUser: u, open: false });
                                    }}
                                  >
                                    <Check
                                      className={`mr-2 h-4 w-4 ${row.draftUser?.id === u.id ? "" : "opacity-0"}`}
                                    />
                                    {u.name ?? "(未命名)"}{" "}
                                    <span className="text-xs text-muted-foreground">
                                      ({u.email})
                                    </span>
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>

                      <Button
                        variant="secondary"
                        className="w-[70px] h-8"
                        disabled={!reviewerChanged(row) || loadingIdx === i}
                        onClick={() => confirmReviewer(i)}
                      >
                        {loadingIdx === i ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          "Confirm"
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 添加分隔线 */}
            <Separator className="my-3" />

            {/* ────────── Status change (所有编辑角色可见) ────────── */}
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium flex items-center gap-2">
                Paper Status
                {statusDraft ? (
                  <StatusBadge status={statusDraft} />
                ) : (
                  <StatusBadge status={status} />
                )}
              </div>

              <div className="flex items-center gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-[220px] justify-between h-8">
                      {statusDraft
                        ? getStatusBadgeConfig(statusDraft).label || statusDraft
                        : "Select status..."}
                      <ChevronsUpDown className="ml-2 h-3 w-3 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[220px] p-0">
                    <Command>
                      <CommandInput placeholder="Search status..." />
                      <CommandList>
                        <CommandEmpty>No matching result</CommandEmpty>
                        <CommandGroup>
                          {statusList.map(st => {
                            const config = getStatusBadgeConfig(st);
                            return (
                              <CommandItem
                                key={st}
                                onSelect={() => {
                                  setStatusDraft(st);
                                }}
                              >
                                <div className="flex items-center w-full">
                                  <Check
                                    className={`flex-shrink-0 mr-1 h-4 w-4 ${statusDraft === st ? "" : "opacity-0"}`}
                                  />
                                  <div
                                    className="flex-shrink-0 w-2 h-2 rounded-full mr-1"
                                    style={{ backgroundColor: config.color || "transparent" }}
                                  />
                                  <span className="truncate">{config.label || st}</span>
                                </div>
                              </CommandItem>
                            );
                          })}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>

                <Button
                  variant="secondary"
                  className="w-[70px] h-8"
                  disabled={statusDraft === null || statusDraft === status || loadingStatus}
                  onClick={async () => {
                    if (statusDraft === null || statusDraft === status) return;
                    setLoadingStatus(true);
                    try {
                      const ok = await fetch(`/api/papers/${paperId}/status`, {
                        method: "PATCH",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({ status: statusDraft }),
                      })
                        .then(r => r.ok)
                        .catch(() => false);
                      if (ok) {
                        // update parent status
                        setStatusDraft(null);
                        // 使用router.refresh()替代硬刷新
                        router.refresh();
                      } else {
                        setErrorMessage("Failed to update status");
                        setShowErrorDialog(true);
                      }
                    } finally {
                      setLoadingStatus(false);
                    }
                  }}
                >
                  {loadingStatus ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    "Confirm"
                  )}
                </Button>
              </div>
            </div>

            {/* 添加分隔线 */}
            <Separator className="my-3" />

            {/* ────────── Switches (所有编辑角色可见) ────────── */}
            <div className="flex flex-wrap gap-4 justify-between">
              {/* Author can reply */}
              <div className="flex items-center gap-2">
                <Label htmlFor="author-reply" className="text-sm">
                  Author can reply
                </Label>
                <Switch
                  id="author-reply"
                  checked={authorReply}
                  onCheckedChange={async val => {
                    // optimistic update
                    setAuthorReply(val);
                    const ok = await fetch(`/api/papers/${paperId}/reply-perms`, {
                      method: "PATCH",
                      headers: { "Content-Type": "application/json" },
                      body: JSON.stringify({ authorCanReply: val }),
                    })
                      .then(r => r.ok)
                      .catch(() => false);
                    if (!ok) setAuthorReply(!val); // revert on error
                  }}
                />
              </div>

              {/* Reviewers can reply */}
              <div className="flex items-center gap-2">
                <Label htmlFor="reviewer-reply" className="text-sm">
                  Reviewer can reply
                </Label>
                <Switch
                  id="reviewer-reply"
                  checked={reviewerReply}
                  onCheckedChange={async val => {
                    setReviewerReply(val);
                    const ok = await fetch(`/api/papers/${paperId}/reply-perms`, {
                      method: "PATCH",
                      headers: { "Content-Type": "application/json" },
                      body: JSON.stringify({ reviewersCanReply: val }),
                    })
                      .then(r => r.ok)
                      .catch(() => false);
                    if (!ok) setReviewerReply(!val);
                  }}
                />
              </div>

              {/* Needs attention */}
              <div className="flex items-center gap-2">
                <Label htmlFor="needs-att" className="text-sm">
                  Needs attention
                </Label>
                <Switch
                  id="needs-att"
                  checked={needsAtt}
                  onCheckedChange={async val => {
                    setNeedsAtt(val);
                    const ok = await fetch(`/api/papers/${paperId}/attention`, {
                      method: "PATCH",
                      headers: { "Content-Type": "application/json" },
                      body: JSON.stringify({ value: val }),
                    })
                      .then(r => r.ok)
                      .catch(() => false);
                    if (!ok) setNeedsAtt(!val);
                  }}
                />
              </div>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>

      {/* 错误提示Dialog */}
      <AlertDialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Operation failed</AlertDialogTitle>
            <AlertDialogDescription>
              {errorMessage}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => setShowErrorDialog(false)}>
              OK
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Accordion>
  );
}
