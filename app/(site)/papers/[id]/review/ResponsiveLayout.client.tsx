"use client";

import * as React from "react";
import { PanelGroup, Panel, PanelResizeHandle } from "react-resizable-panels";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, <PERSON>bsContent } from "@/components/ui/tabs";

export default function ResponsiveLayout({
  preview,
  discussion,
}: {
  preview: React.ReactNode;
  discussion: React.ReactNode;
}) {
  return (
    <>
      {/* Desktop ≥ md */}
      <div className="hidden md:flex h-full overflow-hidden">
        <PanelGroup direction="horizontal" autoSaveId="review-layout">
          <Panel
            defaultSize={50}
            minSize={35}
            maxSize={100}
            className="min-w-[300px] h-full overflow-hidden"
          >
            <div className="h-full overflow-auto">{preview}</div>
          </Panel>
          <PanelResizeHandle className="w-1 cursor-col-resize bg-muted/40" />
          <Panel
            defaultSize={50}
            minSize={35}
            maxSize={100}
            className="min-w-[300px] h-full overflow-hidden"
          >
            <div className="h-full overflow-auto">{discussion}</div>
          </Panel>
        </PanelGroup>
      </div>
      {/* Mobile < md */}
      <div className="md:hidden h-full flex flex-col">
        <Tabs defaultValue="discussion" className="flex-1 flex flex-col overflow-hidden">
          <TabsList className="shrink-0">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="discussion">Timeline</TabsTrigger>
          </TabsList>
          <TabsContent value="preview" className="flex-1 overflow-y-auto">
            {preview}
          </TabsContent>
          <TabsContent value="discussion" className="flex-1 overflow-y-auto">
            {discussion}
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
