"use client";
import { PaperStatus } from "@prisma/client";
import { useRouter } from "next/navigation";
import { useState } from "react";

import { getStatusBadgeConfig } from "@/lib/paperStatusConfig";

export default function StatusSelect({
  paperId,
  current,
}: {
  paperId: string;
  current: PaperStatus;
}) {
  const options = [
    PaperStatus.SUBMITTED,
    PaperStatus.FIRST_CONTENT_CHECK,
    PaperStatus.REVIEWER_NEEDED,
    PaperStatus.REVIEWER_INVITED,
    PaperStatus.UNDER_REVIEW,
    PaperStatus.FIRST_REVIEW_DONE,
    PaperStatus.WATING_AUTHOR_REPLY,
    PaperStatus.WATING_REVIEWER_REPLY,
    PaperStatus.SECOND_REVIEW_DONE,
    PaperStatus.WAITING_MATERIAL_UPDATE,
    PaperStatus.REVISION_SUBMITTED,
    PaperStatus.ACCEPTED,
    PaperStatus.REJECTED,
    PaperStatus.PUBLISHED,
    PaperStatus.ARCHIVED,
    PaperStatus.IN_PRESS,
    PaperStatus.RETRACTED,
    PaperStatus.TASK_WATING_APPROVAL,
    PaperStatus.WAITING_PAYMENT,
    PaperStatus.PAYMENT_RECEIVED,
  ];

  const [value, setValue] = useState<PaperStatus>(current);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  async function change(e: React.ChangeEvent<HTMLSelectElement>) {
    const nxt = e.target.value as PaperStatus;
    setValue(nxt);
    setLoading(true);
    await fetch(`/api/papers/${paperId}/status`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ status: nxt }),
    });
    setLoading(false);
    router.refresh(); // 刷新时间线
  }

  return (
    <div className="my-4">
      <label className="text-sm mr-2">Status:</label>
      <select
        value={value}
        onChange={change}
        disabled={loading}
        className="border rounded px-2 py-1"
      >
        {options.map(s => {
          const config = getStatusBadgeConfig(s);
          return (
            <option
              key={s}
              value={s}
              style={{
                backgroundColor: config.color,
                color: config.color ? "white" : undefined,
              }}
            >
              {config.label || s}
            </option>
          );
        })}
      </select>
    </div>
  );
}
