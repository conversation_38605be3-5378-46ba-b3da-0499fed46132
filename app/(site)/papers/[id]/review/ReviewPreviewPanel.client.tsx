"use client";

import { Loader2, ZoomIn, ZoomOut } from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";

import { useIframeMessaging } from "@/app/editor/[name]/hooks/useIframeMessaging";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Props {
  paperId: string;
  versions: string[]; // 文件夹名称列表
}

const ReviewPreviewPanel: React.FC<Props> = ({ paperId, versions }) => {
  // 服务端已经按创建时间倒序排序（最新的在前面），直接使用
  const sortedVersions = versions;
  const latest = sortedVersions[0] ?? "draft";
  const [version, setVersion] = useState<string>(latest);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement | null>(null);
  const { sendHtml, clearHtml } = useIframeMessaging({ debug: false, projectName: paperId });
  const previousPaperIdRef = useRef<string>(paperId);
  const previousVersionRef = useRef<string>(version);

  /** 加载并发送 HTML 到 iframe */
  const loadHtml = useCallback(
    async (ver: string) => {
      // 首先检查论文ID或版本是否变化
      const isPaperChanged = previousPaperIdRef.current !== paperId;
      const isVersionChanged = previousVersionRef.current !== ver;

      // 如果论文或版本有变化，清除iframe中的内容
      if (isPaperChanged || isVersionChanged) {
        if (iframeRef.current) {
          // 发送空内容清除iframe
          await clearHtml(iframeRef.current);
        }
        // 更新引用
        previousPaperIdRef.current = paperId;
        previousVersionRef.current = ver;
      }

      setLoading(true);
      setError(null);

      try {
        // 使用统一的预览API（优先使用预编译HTML，失败则自动编译）
        const res = await fetch(`/api/papers/${encodeURIComponent(paperId)}/preview`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ version: ver }),
        });

        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(errorText || "加载预览失败");
        }

        // 获取HTML内容并发送到iframe
        const html = await res.text();

        // 将HTML发送到iframe
        if (iframeRef.current) {
          await sendHtml(iframeRef.current, html);
        }
      } catch (e) {
        setError(e instanceof Error ? e.message : String(e));
      } finally {
        setLoading(false);
      }
    },
    [paperId, sendHtml, clearHtml]
  );

  /* 组件挂载与版本变动时加载 */
  useEffect(() => {
    loadHtml(version);
  }, [version, loadHtml]);

  /* iframe ref handler */
  const onIframeRef = useCallback((el: HTMLIFrameElement | null) => {
    iframeRef.current = el;
  }, []);

  return (
    <div className="flex flex-col h-full w-full overflow-hidden border-r relative">
      {/* iframe 预览区域 */}
      <div className="flex-1 overflow-hidden">
        <iframe
          ref={iframeRef}
          id="preview-frame"
          title="preview"
          className="w-full h-full"
          src="/paged-preview"
          sandbox="allow-same-origin allow-scripts allow-modals allow-presentation"
        />
      </div>

      {/* 在底部工具栏添加版本选择下拉框 */}
      <div className="fixed bottom-5 left-5 z-50 flex items-center gap-1 bg-background p-2 rounded-md shadow-md">
        <Select value={version} onValueChange={setVersion}>
          <SelectTrigger className="w-32">
            <SelectValue>{version}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            {sortedVersions.map(v => (
              <SelectItem key={v} value={v}>
                {v}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {loading && <Loader2 className="w-4 h-4 animate-spin text-muted-foreground ml-2" />}
        {error && <span className="text-red-500 text-xs ml-2">{error}</span>}
      </div>
    </div>
  );
};

export default ReviewPreviewPanel;
