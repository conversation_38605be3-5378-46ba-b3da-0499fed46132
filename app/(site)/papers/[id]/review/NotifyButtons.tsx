"use client";

import { useState } from "react";
import { Bell, Users } from "lucide-react";
import { toast } from "sonner";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";

interface NotifyButtonsProps {
    commentId: string;
    paperId: string;
    userRole?: string;
    commentPreview: string;
    isStatusChange?: boolean;
}

export default function NotifyButtons({
    commentId,
    paperId,
    userRole,
    commentPreview,
    isStatusChange = false
}: NotifyButtonsProps) {
    const [isNotifying, setIsNotifying] = useState<string | null>(null);

    const handleNotify = async (notifyType: 'AUTHOR' | 'REVIEWERS') => {
        setIsNotifying(notifyType);

        try {
            const response = await fetch('/api/notifications/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    commentId,
                    paperId,
                    notifyType,
                    message: commentPreview,
                    notificationType: isStatusChange ? 'PAPER_STATUS_CHANGE' : 'REVIEW_UPDATE'
                }),
            });

            if (response.ok) {
                const data = await response.json();
                toast.success(data.message || `Notification sent`);
            } else {
                const error = await response.json();
                toast.error(error.error || 'Failed to send notification');
            }
        } catch (error) {
            toast.error('Failed to send notification');
        } finally {
            setIsNotifying(null);
        }
    };

    // 预览文本，限制长度
    const previewText = commentPreview.length > 50
        ? commentPreview.substring(0, 50) + "..."
        : commentPreview;

    return (
        <div className="flex items-center gap-1">
            {/* 通知作者按钮 */}
            <AlertDialog>
                <AlertDialogTrigger asChild>
                    <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                        disabled={isNotifying === 'AUTHOR'}
                    >
                        <Bell className="h-3 w-3 mr-1" />
                        Author
                    </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm notification to author</AlertDialogTitle>
                        <AlertDialogDescription>
                            You are about to send a notification to the paper author about this comment:
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <div className="px-6 py-2">
                        <div className="p-3 bg-gray-50 rounded text-sm italic border-l-2 border-l-blue-400">
                            "{previewText}"
                        </div>
                        <p className="text-sm text-gray-600 mt-3">Are you sure you want to send the notification?</p>
                    </div>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => handleNotify('AUTHOR')}
                            disabled={isNotifying === 'AUTHOR'}
                        >
                            {isNotifying === 'AUTHOR' ? 'Sending...' : 'Confirm send'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* 通知审稿人按钮 */}
            <AlertDialog>
                <AlertDialogTrigger asChild>
                    <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                        disabled={isNotifying === 'REVIEWERS'}
                    >
                        <Users className="h-3 w-3 mr-1" />
                        Reviewers
                    </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm notification to reviewers</AlertDialogTitle>
                        <AlertDialogDescription>
                            You are about to send a notification to all reviewers about this comment:
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <div className="px-6 py-2">
                        <div className="p-3 bg-gray-50 rounded text-sm italic border-l-2 border-l-green-400">
                            "{previewText}"
                        </div>
                        <p className="text-sm text-gray-600 mt-3">Are you sure you want to send the notification?</p>
                    </div>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => handleNotify('REVIEWERS')}
                            disabled={isNotifying === 'REVIEWERS'}
                        >
                            {isNotifying === 'REVIEWERS' ? 'Sending...' : 'Confirm send'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
} 