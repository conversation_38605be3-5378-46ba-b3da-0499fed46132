"use client";
import { useEffect, useRef } from "react";
import "./preview.css";

export default function PaperPreview({ html }: { html: string }) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      // 确保页面可以滚动
      document.body.style.overflow = "auto";
      document.documentElement.style.overflow = "auto";

      // 如果有工具栏相关的初始化代码，在这里添加
      const container = containerRef.current;

      // 允许事件冒泡，确保工具栏可以使用
      container.addEventListener("click", e => e.stopPropagation(), false);

      // 添加必要的类名，以便CSS样式生效
      container.classList.add("paged-preview-container");
    }

    return () => {
      // 清理代码
      document.body.style.overflow = "";
      document.documentElement.style.overflow = "";
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="min-h-screen w-full paged-preview"
      dangerouslySetInnerHTML={{ __html: html }}
    />
  );
}
