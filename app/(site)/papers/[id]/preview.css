/* 导入共享的页面排版样式 */
@import "../../../shared/page-layout.css";

/* 分页预览样式 */
.paged-preview {
  background: #f5f5f5;
  min-height: 100vh;
  width: 100%;
  overflow: auto !important; /* 强制允许滚动 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
}

/* 确保HTML内容中的页面有合适的样式 */
.paged-preview .page {
  width: 210mm;
  background: white;
  margin: 10mm auto;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
  position: relative;
}

/* 打印工具栏样式 */
.paged-preview .toolbar {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 5px;
}

/* 修复全局滚动问题 */
html,
body {
  overflow: auto !important;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 确保缩放控件可用 */
.paged-preview [data-zoom-controls] {
  pointer-events: auto !important;
}
