"use client";

import { Skeleton } from "@/components/ui/skeleton";

/**
 * 响应式Paper卡片骨架屏组件
 * 使用clamp()函数实现平滑的响应式缩放
 */
export default function ResponsivePaperSkeleton() {
  return (
    <div className="aspect-[7/10] w-full">
      <div
        className="h-full w-full bg-brand-50 animate-pulse"
        style={{
          padding: 'clamp(6px, 1.2vw, 12px)', /* 匹配实际卡片的padding */
          gap: 'clamp(3px, 0.6vw, 8px)', /* 匹配实际卡片的gap */
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0, /* 防止flex子项溢出 */
        }}
      >
        {/* 图片区域 skeleton */}
        <Skeleton className="w-full aspect-square" />

        {/* 标题 skeleton */}
        <div
          style={{
            gap: 'clamp(2px, 0.5vw, 6px)',
            display: 'flex',
            flexDirection: 'column',
            marginBottom: 'clamp(2px, 0.5vw, 6px)', /* 匹配实际标题的marginBottom */
          }}
        >
          <Skeleton
            className="w-full"
            style={{ height: 'clamp(10px, 2vw, 16px)' }} /* 匹配实际字体大小 */
          />
          <Skeleton
            className="w-3/4"
            style={{ height: 'clamp(10px, 2vw, 16px)' }} /* 匹配实际字体大小 */
          />
        </div>

        {/* 标签 skeleton */}
        <div
          className="flex"
          style={{
            gap: 'clamp(2px, 0.4vw, 4px)', /* 匹配实际标签间距 */
            marginBottom: 'clamp(2px, 0.5vw, 6px)', /* 匹配实际标签的marginBottom */
          }}
        >
          <Skeleton
            className="rounded-full"
            style={{
              height: 'clamp(14px, 2.5vw, 20px)', /* 减小高度匹配实际标签 */
              width: 'clamp(28px, 7vw, 40px)', /* 减小宽度 */
            }}
          />
          <Skeleton
            className="rounded-full"
            style={{
              height: 'clamp(14px, 2.5vw, 20px)', /* 减小高度匹配实际标签 */
              width: 'clamp(36px, 9vw, 56px)', /* 减小宽度 */
            }}
          />
          <Skeleton
            className="rounded-full"
            style={{
              height: 'clamp(14px, 2.5vw, 20px)', /* 减小高度匹配实际标签 */
              width: 'clamp(20px, 5vw, 32px)', /* 减小宽度 */
            }}
          />
        </div>

        {/* 底部信息 skeleton */}
        <div
          className="flex justify-between items-center"
          style={{
            marginTop: 'auto', /* 确保推到底部 */
            paddingTop: 'clamp(2px, 0.5vw, 4px)', /* 匹配实际的paddingTop */
            flexShrink: 0, /* 防止被压缩 */
          }}
        >
          <Skeleton
            style={{
              height: 'clamp(7px, 1.5vw, 12px)', /* 匹配实际字体大小 */
              width: 'clamp(60px, 15vw, 80px)',
            }}
          />
          <Skeleton
            style={{
              height: 'clamp(7px, 1.5vw, 12px)', /* 匹配实际字体大小 */
              width: 'clamp(40px, 10vw, 48px)',
            }}
          />
        </div>
      </div>
    </div>
  );
}

/**
 * 响应式Paper卡片骨架屏网格组件
 * 用于显示多个骨架屏卡片
 */
export function ResponsivePaperSkeletonGrid({ count = 3 }: { count?: number }) {
  return (
    <div className="papers-grid-container">
      {Array.from({ length: count }).map((_, i) => (
        <ResponsivePaperSkeleton key={`skeleton-${i}`} />
      ))}
    </div>
  );
}
