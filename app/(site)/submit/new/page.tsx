"use client";

import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import React, { useState, useEffect, FormEvent } from "react";
import yaml from "yaml";
import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON>T<PERSON>le, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

/* ──────────────── ① 常量 ──────────────── */
const MAX_WORDS = 150;
const MAX_PDF_MB = 25;
const MAX_PNG_MB = 1;
const MAX_PDF = MAX_PDF_MB * 1024 * 1024;
const MAX_PNG = MAX_PNG_MB * 1024 * 1024;

/* ──────────────── ② YAML 校验 ──────────────── */
const schema = z.object({
  title: z.string().min(2, "title 必填"),
  abstract: z
    .string()
    .min(10, "abstract 过短")
    .refine(v => v.trim().split(/\s+/).length <= MAX_WORDS, {
      message: `abstract 不能超过 ${MAX_WORDS} 词`,
    }),
  tags: z.array(z.string()).min(1, "tags 至少 1 项"),
  authors: z.array(z.object({ name: z.string() })).min(1),
  affiliations: z.array(z.object({ name: z.string() })).optional(),
  videoUrl: z.string().url().optional(),
});
type Meta = z.infer<typeof schema>;
type ErrMap = Record<string, string>;
const PREVIEW_KEYS: (keyof Meta)[] = ["title", "tags", "authors", "affiliations", "abstract"];

/* ──────────────── ③ 组件 ──────────────── */
export default function NewSubmit() {
  const router = useRouter();

  /* 3-1 鉴权：必须含 AUTHOR 角色 */
  useEffect(() => {
    (async () => {
      const r = await fetch("/api/me", { cache: "no-store" });
      if (!r.ok) {
        router.push("/account");
        return;
      }
      const { roles } = await r.json(); // 可能是 string[] | object[]
      const ok =
        Array.isArray(roles) &&
        roles.some((x: any) => (typeof x === "string" ? x === "AUTHOR" : x.role === "AUTHOR"));
      if (!ok) router.push("/submit");
    })();
  }, [router]);

  /* 3-2 本地状态 */
  const [pdf, setPdf] = useState<File | null>(null);
  const [png, setPng] = useState<File | null>(null);
  const [errPdf, setErrPdf] = useState<string | null>(null);
  const [errPng, setErrPng] = useState<string | null>(null);

  const [yamlText, setYaml] = useState("");
  const [meta, setMeta] = useState<Meta | null>(null);
  const [errs, setErrs] = useState<ErrMap>({});
  const [loading, setLoad] = useState(false);

  /* ───── 解析 YAML ───── */
  function parse(text: string) {
    setYaml(text);
    try {
      const obj = yaml.parse(text);
      const res = schema.safeParse(obj);
      if (!res.success) {
        const map: ErrMap = {};
        res.error.issues.forEach(i => (map[i.path[0] as string] = i.message));
        setErrs(map);
        setMeta(null);
      } else {
        setErrs({});
        setMeta(res.data);
      }
    } catch {
      setErrs({ yaml: "YAML 解析错误" });
      setMeta(null);
    }
  }

  /* ───── 处理文件选择 ───── */
  function onPdf(e: React.ChangeEvent<HTMLInputElement>) {
    const f = e.target.files?.[0];
    if (!f) return;
    if (f.type !== "application/pdf") {
      setErrPdf("必须是 PDF");
      setPdf(null);
      return;
    }
    if (f.size > MAX_PDF) {
      setErrPdf(`≤${MAX_PDF_MB} MB`);
      setPdf(null);
      return;
    }
    setErrPdf(null);
    setPdf(f);
  }
  function onPng(e: React.ChangeEvent<HTMLInputElement>) {
    const f = e.target.files?.[0];
    if (!f) return;
    if (f.type !== "image/png") {
      setErrPng("必须是 PNG");
      setPng(null);
      return;
    }
    if (f.size > MAX_PNG) {
      setErrPng(`≤${MAX_PNG_MB} MB`);
      setPng(null);
      return;
    }
    /* 检查尺寸 */
    const url = URL.createObjectURL(f);
    const img = new Image();
    img.onload = () => {
      if (img.width !== 300 || img.height !== 300) {
        setErrPng("须 300×300");
        setPng(null);
      } else {
        setErrPng(null);
        setPng(f);
      }
      URL.revokeObjectURL(url);
    };
    img.onerror = () => {
      setErrPng("读取失败");
      setPng(null);
      URL.revokeObjectURL(url);
    };
    img.src = url;
  }

  /* ───── 提交 ───── */
  async function submit(e: FormEvent) {
    e.preventDefault();
    if (!pdf || !png || !meta) {
      alert("信息不完整");
      return;
    }
    setLoad(true);
    const fd = new FormData();
    fd.append("file", pdf);
    fd.append("promo", png);
    fd.append("meta", JSON.stringify(meta));
    const res = await fetch("/api/papers/submit", { method: "POST", body: fd });
    setLoad(false);
    res.ok ? router.push("/submit") : alert(await res.text());
  }
  const ready = !!pdf && !!png && !errPdf && !errPng && meta && Object.keys(errs).length === 0;

  /* ──────────────── ④ UI ──────────────── */
  return (
    <div className="max-w-3xl mx-auto py-12 space-y-8">
      <h1 className="text-3xl font-bold">New Submission</h1>

      <form onSubmit={submit} className="space-y-6">
        {/* 文件卡 */}
        <Card>
          <CardHeader className="font-semibold text-lg">Files</CardHeader>
          <CardContent className="space-y-4">
            {/* PDF */}
            <div className="grid gap-2">
              <Label>Paper PDF ≤ {MAX_PDF_MB} MB</Label>
              <Input type="file" accept=".pdf" onChange={onPdf} />
              {pdf && <p className="text-sm text-gray-500">{pdf.name}</p>}
              {errPdf && <p className="text-sm text-red-600">{errPdf}</p>}
            </div>
            {/* PNG */}
            <div className="grid gap-2">
              <Label>Promo PNG 300×300 ≤ {MAX_PNG_MB} MB</Label>
              <Input type="file" accept=".png" onChange={onPng} />
              {png && <p className="text-sm text-gray-500">{png.name}</p>}
              {errPng && <p className="text-sm text-red-600">{errPng}</p>}
            </div>
          </CardContent>
        </Card>

        {/* YAML */}
        <Card>
          <CardHeader className="font-semibold text-lg">Metadata (YAML)</CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              value={yamlText}
              rows={10}
              onChange={e => parse(e.target.value)}
              placeholder="Paste YAML metadata here…"
              className="font-mono text-sm resize-y"
            />
            <p className="text-xs text-gray-500 text-right">
              Abstract: {meta ? meta.abstract.split(/\s+/).length : 0}/{MAX_WORDS} words
            </p>
            {errs.yaml && (
              <Alert variant="destructive">
                <AlertTitle>YAML 错误</AlertTitle>
                <AlertDescription>{errs.yaml}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* 预览 */}
        <Card>
          <CardHeader className="font-semibold text-lg">Preview</CardHeader>
          <CardContent>
            <div className="grid grid-cols-[6rem_auto] gap-x-2 gap-y-1 text-sm">
              {PREVIEW_KEYS.map(k => (
                <React.Fragment key={k}>
                  <div className="capitalize text-gray-600">{k}:</div>
                  <div>
                    {errs[k as string] ? (
                      <span className="text-red-600">{errs[k as string]}</span>
                    ) : meta ? (
                      k === "tags" ? (
                        meta.tags.join(", ")
                      ) : k === "authors" ? (
                        meta.authors.map(a => a.name).join("; ")
                      ) : k === "affiliations" ? (
                        meta.affiliations ? (
                          meta.affiliations.map(a => a.name).join("; ")
                        ) : (
                          "—"
                        )
                      ) : (
                        meta[k]
                      )
                    ) : (
                      "—"
                    )}
                  </div>
                </React.Fragment>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 提交 */}
        <Button type="submit" disabled={!ready || loading}>
          {loading ? "Uploading…" : "Submit"}
        </Button>
      </form>
    </div>
  );
}
