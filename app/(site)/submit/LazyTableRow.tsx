"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { TableCell, TableRow } from "@/components/ui/table";
import { useEffect, useRef, useState } from "react";

interface LazyTableRowProps {
  children?: React.ReactNode;
  delay?: number;
  colSpan: number;
  skeletonType?: "draft" | "paper" | "custom";
  isEditor?: boolean;
}

export default function LazyTableRow({
  children,
  delay = 0,
  colSpan,
  skeletonType = "custom",
  isEditor = false,
}: LazyTableRowProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const rowRef = useRef<HTMLTableRowElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (rowRef.current) {
      observer.observe(rowRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => setShouldRender(true), delay);
      return () => clearTimeout(timer);
    }
  }, [isVisible, delay]);

  if (!shouldRender) {
    return (
      <TableRow ref={rowRef} className="animate-pulse table-row-uniform">
        {skeletonType === "draft" && (
          <>
            {/* 项目名称列 */}
            <TableCell className="font-medium">
              <Skeleton className="h-5 w-3/4" />
            </TableCell>
            {/* 创建时间列 */}
            <TableCell className="text-sm">
              <Skeleton className="h-4 w-24" />
            </TableCell>
            {/* 修改时间列 */}
            <TableCell className="text-sm">
              <Skeleton className="h-4 w-24" />
            </TableCell>
            {/* 操作列 */}
            <TableCell className="text-right">
              <div className="flex justify-end items-center gap-2">
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
              </div>
            </TableCell>
          </>
        )}

        {skeletonType === "paper" && (
          <>
            {/* Title列 */}
            <TableCell className="font-medium">
              <Skeleton className="h-5 w-3/4" />
            </TableCell>
            {/* Submitted时间列 */}
            <TableCell className="text-sm">
              <Skeleton className="h-4 w-20" />
            </TableCell>
            {/* Last Update时间列 */}
            <TableCell className="text-sm">
              <Skeleton className="h-4 w-20" />
            </TableCell>
            {/* Status列 */}
            <TableCell>
              <Skeleton className="h-6 w-20 rounded-full" />
            </TableCell>
            {/* Need-Attention列（仅编辑者可见） */}
            {isEditor && (
              <TableCell className="text-right">
                <Skeleton className="h-6 w-16 rounded-full ml-auto" />
              </TableCell>
            )}
          </>
        )}

        {skeletonType === "custom" && (
          <TableCell colSpan={colSpan} className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <div className="flex space-x-2">
                <Skeleton className="h-6 w-16 rounded-full" />
                <Skeleton className="h-6 w-12 rounded-full" />
              </div>
            </div>
          </TableCell>
        )}
      </TableRow>
    );
  }

  // 如果没有children，只渲染空行（用于placeholder skeleton）
  if (!children) {
    return <TableRow ref={rowRef} className="animate-in fade-in-0 duration-300 table-row-uniform" />;
  }

  return (
    <TableRow ref={rowRef} className="animate-in fade-in-0 duration-300 table-row-uniform">
      {children}
    </TableRow>
  );
}
