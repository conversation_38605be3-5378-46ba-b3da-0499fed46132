"use client";

import { PaperStatus, PaperType } from "@prisma/client";
import { saveAs } from "file-saver";
import J<PERSON><PERSON><PERSON> from "jszip";
import { Edit2, Trash2, Download, Loader2, ChevronsUpDown } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState, useMemo } from "react";

import LazyTableRow from "./LazyTableRow";

import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, Di<PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { StatusBadge } from "@/components/ui/StatusBadge";
import { TypeBadge } from "@/components/ui/TypeBadge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useFloatingWindowNavigation } from "@/lib/floating-window-navigation";
import { formatDateTime } from "@/lib/dateUtils";

// 已提交论文的类型定义
type Paper = {
  id: string;
  title: string;
  promoUrl: string | null;
  status: PaperStatus;
  type: PaperType;
  needsAttention: boolean;
  category: string | null;
  createdAt: Date | string; // ← 接受 Date 或 ISO 字符串
  submittedAt: Date | string;
  lastUpdatedAt: Date | string | null;
  issueId: string | null;
};

// 草稿项目的类型定义
type ProjectRow = {
  name: string;
  createdAt: string | null;
  updatedAt: string | null;
  lastAccessedAt: string | null;
};

export default function MergedSubmissionBoard({
  papers,
  isEditor,
  isAuthor,
}: {
  papers: Paper[];
  isEditor: boolean;
  isAuthor: boolean;
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { navigate, isInFloatingWindow } = useFloatingWindowNavigation();

  // 从URL参数获取初始标签
  const tabParam = searchParams?.get("tab");

  // 标签状态
  const [activeTab, setActiveTab] = useState<string>(
    tabParam === "submitted" ? "submitted" : "drafts"
  );

  // 当URL参数改变时切换标签
  useEffect(() => {
    if (tabParam === "submitted") {
      setActiveTab("submitted");
    }
  }, [tabParam]);

  // 已提交论文相关状态
  const [sortBy, setSortBy] = useState<"time" | "attention" | "issue">("time");
  const [submittedSortBy, setSubmittedSortBy] = useState<"title" | "submittedAt" | "lastUpdatedAt">("submittedAt");

  // 草稿项目相关状态
  const [rows, setRows] = useState<ProjectRow[]>([]);
  const [newName, setNewName] = useState("");
  const [loading, setLoading] = useState(false);
  const [downloading, setDl] = useState<string | null>(null);
  const [err, setErr] = useState<string | null>(null);
  const [createProjectErr, setCreateProjectErr] = useState<string | null>(null);
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [draftSortBy, setDraftSortBy] = useState<"name" | "createdAt" | "updatedAt">("name");
  const [nameInputWarning, setNameInputWarning] = useState<string | null>(null);

  // Dialog状态
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [renameConfirmDialogOpen, setRenameConfirmDialogOpen] = useState(false);
  const [currentProjectName, setCurrentProjectName] = useState("");
  const [renameInputValue, setRenameInputValue] = useState("");
  const [pendingRename, setPendingRename] = useState<{ oldName: string, newName: string, sanitizedName: string } | null>(null);

  // 滚动加载相关状态
  const PAGE_SIZE = 6; // 减少每页显示数量，让懒加载效果更明显
  const [displayedDraftRows, setDisplayedDraftRows] = useState<ProjectRow[]>([]);
  const [displayedPapers, setDisplayedPapers] = useState<Paper[]>([]);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMoreDrafts, setHasMoreDrafts] = useState(true);
  const [hasMorePapers, setHasMorePapers] = useState(true);
  // 添加状态跟踪内容是否超出屏幕高度
  const [draftsExceedsViewport, setDraftsExceedsViewport] = useState(false);
  const [papersExceedsViewport, setPapersExceedsViewport] = useState(false);

  // 引用相关元素 - 使用forwardRef兼容TableCell
  const draftsLoadingRef = React.useRef<HTMLDivElement>(null);
  const papersLoadingRef = React.useRef<HTMLDivElement>(null);
  // 添加表格容器引用，用于检测高度
  const draftsTableRef = React.useRef<HTMLDivElement>(null);
  const papersTableRef = React.useRef<HTMLDivElement>(null);

  /* ---------- 拉取草稿项目列表 ---------- */
  async function fetchRows() {
    try {
      setLoading(true);
      const res = await fetch("/api/editor/projects", {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
        },
      });
      if (!res.ok) {
        const errText = await res.text();
        setErr(errText || "Failed to get project list");
        return;
      }
      const json = await res.json();
      setRows(json as ProjectRow[]);
      setErr(null);
    } catch (error) {
      setErr("Failed to get project list: " + (error instanceof Error ? error.message : String(error)));
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    if (activeTab === "drafts") {
      fetchRows();
    }
  }, [activeTab]);

  /* ---------- 已提交论文的排序 ---------- */
  const orderedPapers = useMemo(() => {
    const list = [...papers];
    if (submittedSortBy === "title") {
      list.sort((a, b) => a.title.localeCompare(b.title));
    } else if (submittedSortBy === "submittedAt") {
      list.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime());
    } else if (submittedSortBy === "lastUpdatedAt") {
      list.sort((a, b) => {
        // 处理lastUpdatedAt可能为null的情况
        const aTime = a.lastUpdatedAt ? new Date(a.lastUpdatedAt).getTime() : 0;
        const bTime = b.lastUpdatedAt ? new Date(b.lastUpdatedAt).getTime() : 0;
        return bTime - aTime;
      });
    } else if (sortBy === "attention") {
      list.sort((a, b) => Number(b.needsAttention) - Number(a.needsAttention));
    } else if (sortBy === "issue") {
      list.sort((a, b) => (a.issueId || "").localeCompare(b.issueId || ""));
    }
    return list;
  }, [papers, submittedSortBy, sortBy]);

  /* ---------- 草稿项目的排序 ---------- */
  const orderedDrafts = useMemo(() => {
    const list = [...rows];
    if (draftSortBy === "createdAt") {
      list.sort((a, b) => {
        if (!a.createdAt && !b.createdAt) return 0;
        if (!a.createdAt) return 1;
        if (!b.createdAt) return -1;
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
    } else if (draftSortBy === "updatedAt") {
      list.sort((a, b) => {
        if (!a.updatedAt && !b.updatedAt) return 0;
        if (!a.updatedAt) return 1;
        if (!b.updatedAt) return -1;
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
      });
    } else {
      // 按名称排序
      list.sort((a, b) => a.name.localeCompare(b.name));
    }
    return list;
  }, [rows, draftSortBy]);

  /* ---------- 基于屏幕高度的智能加载逻辑 ---------- */
  const checkAndLoadMore = React.useCallback(
    (
      containerRef: React.RefObject<HTMLDivElement | null>,
      currentDisplayed: any[],
      totalData: any[],
      setDisplayed: React.Dispatch<React.SetStateAction<any[]>>,
      setHasMore: React.Dispatch<React.SetStateAction<boolean>>,
      setExceedsViewport: React.Dispatch<React.SetStateAction<boolean>>
    ) => {
      if (!containerRef.current || loadingMore) return false;

      const container = containerRef.current;
      const containerHeight = container.scrollHeight;
      const viewportHeight = window.innerHeight;
      const threshold = viewportHeight * 0.8;

      // 更新是否超出屏幕高度的状态
      const exceedsViewport = containerHeight > threshold;
      setExceedsViewport(exceedsViewport);

      // 如果容器高度小于视窗高度的80%，且还有更多数据，则继续加载
      if (!exceedsViewport && currentDisplayed.length < totalData.length) {
        const nextBatch = totalData.slice(
          currentDisplayed.length,
          currentDisplayed.length + PAGE_SIZE
        );
        if (nextBatch.length > 0) {
          setDisplayed(prev => [...prev, ...nextBatch]);

          // 检查是否还有更多数据
          const newLength = currentDisplayed.length + nextBatch.length;
          setHasMore(newLength < totalData.length);
          return true; // 返回true表示进行了加载
        } else {
          setHasMore(false);
        }
      } else {
        // 容器已经足够高，启用滚动加载
        setHasMore(currentDisplayed.length < totalData.length);
      }
      return false; // 返回false表示没有进行加载
    },
    [loadingMore]
  );

  // 监听草稿项目状态变化，自动检查是否需要继续加载
  useEffect(() => {
    if (orderedDrafts.length > 0 && displayedDraftRows.length > 0 && activeTab === "drafts") {
      const timer = setTimeout(() => {
        const shouldContinue = checkAndLoadMore(
          draftsTableRef,
          displayedDraftRows,
          orderedDrafts,
          setDisplayedDraftRows,
          setHasMoreDrafts,
          setDraftsExceedsViewport
        );
      }, 300); // 给DOM更新留出时间

      return () => clearTimeout(timer);
    }
  }, [displayedDraftRows, orderedDrafts, activeTab, checkAndLoadMore]);

  // 监听已提交项目状态变化，自动检查是否需要继续加载
  useEffect(() => {
    if (orderedPapers.length > 0 && displayedPapers.length > 0 && activeTab === "submitted") {
      const timer = setTimeout(() => {
        const shouldContinue = checkAndLoadMore(
          papersTableRef,
          displayedPapers,
          orderedPapers,
          setDisplayedPapers,
          setHasMorePapers,
          setPapersExceedsViewport
        );
      }, 300); // 给DOM更新留出时间

      return () => clearTimeout(timer);
    }
  }, [displayedPapers, orderedPapers, activeTab, checkAndLoadMore]);

  /* ---------- 滚动加载相关函数 ---------- */
  // 加载更多草稿项目
  const loadMoreDrafts = React.useCallback(() => {
    if (loadingMore || !hasMoreDrafts) return;

    setLoadingMore(true);
    setTimeout(() => {
      const currentLength = displayedDraftRows.length;
      const nextItems = orderedDrafts.slice(currentLength, currentLength + PAGE_SIZE);

      if (nextItems.length > 0) {
        setDisplayedDraftRows(prev => [...prev, ...nextItems]);
        // 修复：使用更新后的长度计算
        const newLength = currentLength + nextItems.length;
        setHasMoreDrafts(newLength < orderedDrafts.length);
      } else {
        setHasMoreDrafts(false);
      }

      setLoadingMore(false);
    }, 300); // 添加短暂延迟让加载状态更明显
  }, [loadingMore, hasMoreDrafts, orderedDrafts, displayedDraftRows.length]);

  // 加载更多已提交项目
  const loadMorePapers = React.useCallback(() => {
    if (loadingMore || !hasMorePapers) return;

    setLoadingMore(true);
    setTimeout(() => {
      const currentLength = displayedPapers.length;
      const nextItems = orderedPapers.slice(currentLength, currentLength + PAGE_SIZE);

      if (nextItems.length > 0) {
        setDisplayedPapers(prev => [...prev, ...nextItems]);
        // 修复：使用更新后的长度计算
        const newLength = currentLength + nextItems.length;
        setHasMorePapers(newLength < orderedPapers.length);
      } else {
        setHasMorePapers(false);
      }

      setLoadingMore(false);
    }, 300); // 添加短暂延迟让加载状态更明显
  }, [loadingMore, hasMorePapers, orderedPapers, displayedPapers.length]);

  // 初始化交叉观察器（用于超出屏幕后的滚动加载）
  useEffect(() => {
    // 草稿项目观察器
    const draftsObserver = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMoreDrafts && !loadingMore && draftsExceedsViewport) {
          loadMoreDrafts();
        }
      },
      { threshold: 0.5 }
    );

    // 已提交项目观察器
    const papersObserver = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMorePapers && !loadingMore && papersExceedsViewport) {
          loadMorePapers();
        }
      },
      { threshold: 0.5 }
    );

    // 注册观察器
    if (draftsLoadingRef.current && hasMoreDrafts && draftsExceedsViewport) {
      draftsObserver.observe(draftsLoadingRef.current);
    }

    if (papersLoadingRef.current && hasMorePapers && papersExceedsViewport) {
      papersObserver.observe(papersLoadingRef.current);
    }

    return () => {
      draftsObserver.disconnect();
      papersObserver.disconnect();
    };
  }, [
    hasMoreDrafts,
    hasMorePapers,
    loadMoreDrafts,
    loadMorePapers,
    loadingMore,
    draftsExceedsViewport,
    papersExceedsViewport,
  ]);

  // 当orderedDrafts变化时重置草稿项目的分页
  useEffect(() => {
    const initialItems = orderedDrafts.slice(0, PAGE_SIZE);
    setDisplayedDraftRows(initialItems);
    setHasMoreDrafts(orderedDrafts.length > PAGE_SIZE);
  }, [orderedDrafts]);

  // 当orderedPapers变化时重置已提交项目的分页
  useEffect(() => {
    const initialItems = orderedPapers.slice(0, PAGE_SIZE);
    setDisplayedPapers(initialItems);
    setHasMorePapers(orderedPapers.length > PAGE_SIZE);
  }, [orderedPapers]);

  // 监听窗口大小变化，重新检查是否需要加载更多
  useEffect(() => {
    const handleResize = () => {
      // 草稿项目检查
      if (activeTab === "drafts" && orderedDrafts.length > 0 && displayedDraftRows.length < orderedDrafts.length) {
        setTimeout(() => {
          checkAndLoadMore(
            draftsTableRef,
            displayedDraftRows,
            orderedDrafts,
            setDisplayedDraftRows,
            setHasMoreDrafts,
            setDraftsExceedsViewport
          );
        }, 50);
      }
      // 已提交项目检查
      if (
        activeTab === "submitted" &&
        orderedPapers.length > 0 &&
        displayedPapers.length < orderedPapers.length
      ) {
        setTimeout(() => {
          checkAndLoadMore(
            papersTableRef,
            displayedPapers,
            orderedPapers,
            setDisplayedPapers,
            setHasMorePapers,
            setPapersExceedsViewport
          );
        }, 50);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [activeTab, orderedDrafts, displayedDraftRows, orderedPapers, displayedPapers, checkAndLoadMore]);

  /* ---------- 浮窗导航处理 ---------- */
  // 处理草稿项目点击
  const handleDraftClick = async (e: React.MouseEvent, projectName: string) => {
    e.preventDefault();

    try {
      await navigate({
        url: `/editor/${projectName}`,
        title: `Editor: ${projectName}`,
        x: 0.1,
        y: 0.1,
        width: 0.8,
        height: 0.8
      });
    } catch (error) {
      // 降级到传统导航
      window.open(`/editor/${projectName}`, '_blank', 'noopener,noreferrer');
    }
  };

  // 处理已提交项目点击
  const handleSubmittedClick = async (e: React.MouseEvent, paperId: string, title: string) => {
    e.preventDefault();

    try {
      await navigate({
        url: `/papers/${paperId}/review`,
        title: `Paper Review: ${title}`,
        x: 0.1,
        y: 0.1,
        width: 0.8,
        height: 0.8
      });
    } catch (error) {
      // 降级到传统导航
      window.open(`/papers/${paperId}/review`, '_blank', 'noopener,noreferrer');
    }
  };

  /* ---------- 草稿项目操作 ---------- */

  // 项目名称最终转换函数（用于提交时）
  const sanitizeProjectName = (name: string): string => {
    return name
      .trim() // 去除首尾空格
      .replace(/\s+/g, "_") // 将空格（包括多个连续空格）替换为下划线
      .replace(/[<>:"/\\|?*]/g, "") // 移除文件系统不允许的特殊字符
      .replace(/[^\w\-_.]/g, "") // 只保留字母、数字、下划线、连字符和点
      .replace(/_{2,}/g, "_") // 将多个连续下划线替换为单个下划线
      .replace(/^[._]|[._]$/g, ""); // 移除开头和结尾的点和下划线
  };

  // 检查输入字符是否允许
  const isCharacterAllowed = (char: string): boolean => {
    // 允许：字母、数字、空格、下划线、连字符、点
    return /[\w\s\-.]/.test(char);
  };

  // 处理键盘输入
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const char = e.key;

    // 处理Enter键
    if (char === "Enter") {
      if (newName.trim() && !loading) {
        createProject();
      }
      return;
    }

    // 允许控制键（Backspace, Delete, Arrow keys, etc.）
    if (char.length > 1) return;

    if (!isCharacterAllowed(char)) {
      e.preventDefault();
      setNameInputWarning("Only letters, numbers, spaces, underscores, hyphens and dots are allowed");

      // 3秒后清除警告
      setTimeout(() => {
        setNameInputWarning(null);
      }, 3000);
    } else {
      // 清除之前的警告
      setNameInputWarning(null);
    }
  };

  // 处理输入变化
  const handleProjectNameChange = (value: string) => {
    setNewName(value);
  };

  // 新建项目
  async function createProject() {
    if (!newName.trim()) return;

    // 转换项目名称
    const sanitizedName = sanitizeProjectName(newName);
    if (!sanitizedName) {
      setCreateProjectErr("Project name cannot be empty or only contain special characters");
      return;
    }

    if (rows.some(r => r.name === sanitizedName)) {
      setCreateProjectErr("Project name already exists");
      return;
    }

    setLoading(true);
    setCreateProjectErr(null); // 清除之前的错误
    try {
      const res = await fetch("/api/editor/projects", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: sanitizedName }),
      });

      if (!res.ok) {
        setCreateProjectErr(await res.text());
        return;
      }

      setNewName("");
      await fetchRows();
      setPopoverOpen(false);
      setCreateProjectErr(null); // 成功后清除错误
    } catch (error) {
      setCreateProjectErr("Failed to create project");
    } finally {
      setLoading(false);
    }
  }

  // 打开重命名对话框
  function openRenameDialog(projectName: string) {
    setCurrentProjectName(projectName);
    setRenameInputValue(projectName);
    setRenameDialogOpen(true);
  }

  // 执行重命名项目
  async function executeRename() {
    const oldName = currentProjectName;
    const rawNewName = renameInputValue.trim();

    if (!rawNewName || rawNewName === oldName) {
      setRenameDialogOpen(false);
      return;
    }

    // 验证和转换新名称
    const newN = sanitizeProjectName(rawNewName);
    if (!newN) {
      alert("Project name cannot be empty or only contain special characters");
      return;
    }

    if (newN === oldName) {
      setRenameDialogOpen(false);
      return; // 转换后名称相同，无需更新
    }

    // 检查转换后的名称是否已存在
    if (rows.some(r => r.name === newN)) {
      alert(`Project name "${newN}" already exists`);
      return;
    }

    // 如果名称被转换了，提示用户
    if (newN !== rawNewName) {
      setPendingRename({ oldName, newName: rawNewName, sanitizedName: newN });
      setRenameDialogOpen(false);
      setRenameConfirmDialogOpen(true);
      return;
    }

    // 直接执行重命名
    await performRename(oldName, newN);
  }

  // 确认重命名（当名称被转换时）
  async function confirmRename() {
    if (!pendingRename) return;
    setRenameConfirmDialogOpen(false);
    await performRename(pendingRename.oldName, pendingRename.sanitizedName);
    setPendingRename(null);
  }

  // 执行实际的重命名操作
  async function performRename(oldName: string, newName: string) {
    try {
      setLoading(true); // 设置加载状态，禁用所有按钮

      // 确保URL正确编码
      const res = await fetch(`/api/editor/projects/${encodeURIComponent(oldName)}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
        },
        body: JSON.stringify({ newName: newName }),
        // 确保不使用缓存
        cache: "no-store",
      });

      if (!res.ok) {
        const errorText = await res.text();
        alert(errorText || "Failed to rename project");
        return;
      }

      // 立即更新本地数据，然后再请求最新数据
      setRows(prev => prev.map(r => (r.name === oldName ? { ...r, name: newName } : r)));
      // 刷新项目列表
      await fetchRows();
    } catch (error) {
      alert("Failed to rename project: " + (error instanceof Error ? error.message : String(error)));
    } finally {
      setLoading(false);
    }
  }

  // 打开删除确认对话框
  function openDeleteDialog(projectName: string) {
    setCurrentProjectName(projectName);
    setDeleteDialogOpen(true);
  }

  // 执行删除项目
  async function executeDelete() {
    const name = currentProjectName;
    setDeleteDialogOpen(false);

    try {
      setLoading(true); // 设置加载状态，禁用所有按钮
      // 确保URL正确编码
      const res = await fetch(`/api/editor/projects/${encodeURIComponent(name)}`, {
        method: "DELETE",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
        },
        // 确保不使用缓存
        cache: "no-store",
      });

      if (!res.ok) {
        const errorText = await res.text();
        alert(errorText || "Failed to delete project");
        return;
      }

      // 立即从当前列表中移除该项目
      setRows(prev => prev.filter(r => r.name !== name));
      // 刷新项目列表确保同步
      await fetchRows();
    } catch (error) {
      alert("Failed to delete project: " + (error instanceof Error ? error.message : String(error)));
    } finally {
      setLoading(false);
    }
  }

  // 检测是否在浮窗环境中（用于下载功能）
  function isInFloatingWindowForDownload(): boolean {
    try {
      // 检查是否在iframe中
      return window.self !== window.top;
    } catch (e) {
      // 如果访问window.top抛出异常，说明在跨域iframe中
      return true;
    }
  }

  // 浮窗环境下的下载处理
  async function downloadInFloatingWindow(name: string, blob: Blob) {
    try {
      // 方法1: 尝试使用URL.createObjectURL + a标签下载（带用户交互）
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${name}.zip`;
      a.style.display = 'none';

      // 添加到DOM并触发点击
      document.body.appendChild(a);

      // 使用setTimeout确保DOM更新完成
      setTimeout(() => {
        try {
          a.click();
        } catch (clickError) {
          // 静默处理点击失败
        }

        // 清理
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }, 100);
      }, 10);

      return true;
    } catch (error) {

      try {
        // 方法2: 尝试通过postMessage发送到父窗口
        if (window.parent && window.parent !== window) {
          const reader = new FileReader();
          reader.onload = function () {
            try {
              window.parent.postMessage({
                type: 'DOWNLOAD_FILE',
                filename: `${name}.zip`,
                data: reader.result,
                mimeType: 'application/zip',
                size: blob.size
              }, '*');
            } catch (postError) {
              // 静默处理postMessage失败
            }
          };
          reader.onerror = function () {
            // 静默处理FileReader失败
          };
          reader.readAsDataURL(blob);
          return true;
        }
      } catch (postMessageError) {
        // 静默处理postMessage方法失败
      }

      // 方法3: 降级到saveAs
      try {
        saveAs(blob, `${name}.zip`);
        return true;
      } catch (saveAsError) {
        // 方法4: 最后的备用方案 - 打开新窗口
        try {
          const url = URL.createObjectURL(blob);
          const newWindow = window.open(url, '_blank');
          if (newWindow) {
            setTimeout(() => URL.revokeObjectURL(url), 1000);
            return true;
          }
        } catch (windowError) {
          // 静默处理新窗口失败
        }

        throw new Error('All download methods failed');
      }
    }
  }

  // 下载项目
  async function downloadZip(name: string) {
    // 先检查项目是否存在于当前列表中
    if (!rows.some(r => r.name === name)) {
      alert(`Project "${name}" does not exist or has been deleted, please refresh the page to view the latest project list`);
      return;
    }

    try {
      setDl(name); // 设置下载状态

      // 确保URL正确编码
      const res = await fetch(`/api/editor/projects/${encodeURIComponent(name)}/download`, {
        cache: "no-store", // 确保不使用缓存
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
        },
      });

      if (!res.ok) {
        const errorText = await res.text();

        // 如果是找不到对象，可能项目已被删除，刷新列表
        if (errorText.includes("not found") || errorText.includes("Object not found")) {
          alert(`Project "${name}" may have been deleted, refreshing project list...`);
          await fetchRows();
          return;
        }

        alert(errorText || "Failed to download project");
        return;
      }

      const { dirs, files }: { dirs: string[]; files: { path: string; url: string }[] } =
        await res.json();

      // 检查是否有文件可下载
      if ((!dirs || dirs.length === 0) && (!files || files.length === 0)) {
        alert(`Project "${name}" has no files to download`);
        return;
      }

      const zip = new JSZip();

      /* 1. 创建空目录 */
      dirs.forEach(dir => {
        zip.folder(dir);
      });

      /* 2. 并行抓文件写入 zip */
      await Promise.all(
        files.map(async ({ path, url }) => {
          try {
            const f = await fetch(url, {
              cache: "no-store",
              headers: {
                "Cache-Control": "no-cache, no-store, must-revalidate",
                Pragma: "no-cache",
              },
            });
            if (!f.ok) throw new Error(`Download failed: ${path} (status code: ${f.status})`);
            const buf = await f.arrayBuffer();
            zip.file(path, buf);
          } catch (fileError) {
            throw new Error(`Failed to download file: ${path}`);
          }
        })
      );

      /* 3. 生成 & 保存 */
      const blob = await zip.generateAsync({ type: "blob" });

      // 检测环境并选择合适的下载方法
      if (isInFloatingWindowForDownload()) {
        await downloadInFloatingWindow(name, blob);
      } else {
        saveAs(blob, `${name}.zip`);
      }
    } catch (e: any) {
      alert("Failed to pack: " + (e?.message ?? String(e)));

      // 如果出现错误，尝试刷新项目列表
      if (String(e).includes("not found") || String(e).includes("Object not found")) {
        await fetchRows();
      }
    } finally {
      setDl(null);
    }
  }

  /* ---------- 渲染 UI ---------- */
  return (
    <>
      <Tabs defaultValue="drafts" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="drafts">Drafts</TabsTrigger>
            <TabsTrigger value="submitted">Submitted</TabsTrigger>
          </TabsList>

          {/* 在各自标签页显示不同的操作按钮 */}
          {isAuthor && activeTab === "drafts" && (
            <div className="flex gap-4">
              <Popover
                open={popoverOpen}
                onOpenChange={(open) => {
                  setPopoverOpen(open);
                  if (open) {
                    // 打开popover时清除之前的错误状态
                    setCreateProjectErr(null);
                    setNameInputWarning(null);
                  }
                }}
              >
                <PopoverTrigger asChild>
                  <Button>
                    New Draft
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="space-y-4">
                    <div>
                      <Input
                        placeholder="Enter project name..."
                        value={newName}
                        onChange={e => handleProjectNameChange(e.target.value)}
                        onKeyDown={handleKeyPress}
                      />
                      {nameInputWarning ? (
                        <p className="text-xs text-red-500 mt-1">
                          {nameInputWarning}
                        </p>
                      ) : (
                        <p className="text-xs text-muted-foreground mt-1">
                          Spaces will be automatically converted to underscores when submitting
                        </p>
                      )}
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setPopoverOpen(false);
                          setNewName("");
                          setCreateProjectErr(null);
                          setNameInputWarning(null);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        disabled={loading || !newName.trim()}
                        onClick={createProject}
                      >
                        {loading ? "Creating..." : "Create"}
                      </Button>
                    </div>
                    {createProjectErr && (
                      <div className="text-red-500 text-sm mt-2">
                        {createProjectErr}
                      </div>
                    )}
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          )}
        </div>

        {err && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{err}</AlertDescription>
          </Alert>
        )}

        <TabsContent value="drafts" className="mt-0">
          {/* 草稿项目列表 */}
          <div className="border" ref={draftsTableRef}>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className={`w-[40%] cursor-pointer hover:bg-muted/50 ${draftSortBy === "name" ? "bg-muted/30" : ""}`}
                    onClick={() => setDraftSortBy("name")}
                  >
                    <div className="flex items-center gap-1">
                      Project Name
                      {draftSortBy === "name" && <ChevronsUpDown className="h-3 w-3" />}
                    </div>
                  </TableHead>
                  <TableHead
                    className={`w-[25%] cursor-pointer hover:bg-muted/50 ${draftSortBy === "createdAt" ? "bg-muted/30" : ""}`}
                    onClick={() => setDraftSortBy("createdAt")}
                  >
                    <div className="flex items-center gap-1">
                      Created At
                      {draftSortBy === "createdAt" && <ChevronsUpDown className="h-3 w-3" />}
                    </div>
                  </TableHead>
                  <TableHead
                    className={`w-[25%] cursor-pointer hover:bg-muted/50 ${draftSortBy === "updatedAt" ? "bg-muted/30" : ""}`}
                    onClick={() => setDraftSortBy("updatedAt")}
                  >
                    <div className="flex items-center gap-1">
                      Updated At
                      {draftSortBy === "updatedAt" && <ChevronsUpDown className="h-3 w-3" />}
                    </div>
                  </TableHead>
                  <TableHead className="text-right w-[10%]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {displayedDraftRows.map((row, index) => (
                  <LazyTableRow key={row.name} delay={index * 50} colSpan={4} skeletonType="draft">
                    <TableCell className="font-medium">
                      <a
                        href={`/editor/${row.name}`}
                        onClick={(e) => handleDraftClick(e, row.name)}
                        className="font-medium hover:underline truncate block cursor-pointer"
                        title={row.name}
                      >
                        {row.name}
                      </a>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDateTime(row.createdAt)}
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDateTime(row.updatedAt)}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end items-center gap-2">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="icon"
                                variant="ghost"
                                disabled={loading || !!downloading}
                                onClick={() => openRenameDialog(row.name)}
                              >
                                {loading ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Edit2 className="h-4 w-4" />
                                )}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Rename project</p>
                            </TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="icon"
                                variant="ghost"
                                disabled={loading || !!downloading}
                                onClick={() => openDeleteDialog(row.name)}
                              >
                                {loading ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Trash2 className="h-4 w-4 text-red-500" />
                                )}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Delete project</p>
                            </TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="icon"
                                variant="ghost"
                                disabled={loading || !!downloading}
                                onClick={() => downloadZip(row.name)}
                              >
                                {downloading === row.name ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : loading ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Download className="h-4 w-4" />
                                )}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Download project</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </TableCell>
                  </LazyTableRow>
                ))}

                {/* 初始加载skeleton */}
                {loading && rows.length === 0 && (
                  <>
                    {Array.from({ length: 3 }).map((_, i) => (
                      <LazyTableRow
                        key={`loading-skeleton-${i}`}
                        delay={i * 50}
                        colSpan={4}
                        skeletonType="draft"
                      >
                        {/* 内容会被LazyTableRow的skeleton替换 */}
                      </LazyTableRow>
                    ))}
                  </>
                )}

                {/* 加载状态行 */}
                {hasMoreDrafts && draftsExceedsViewport && (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center p-4">
                      <div
                        className="flex justify-center items-center py-2 text-muted-foreground"
                        ref={draftsLoadingRef}
                      >
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Loading more...
                      </div>
                    </TableCell>
                  </TableRow>
                )}

                {rows.length === 0 && !loading && (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                      No projects yet, create one now!
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>

            {/* 显示当前状态 */}
            {orderedDrafts.length > 0 && (
              <div className="px-4 py-2 text-xs text-muted-foreground text-center border-t">
                Showing {displayedDraftRows.length} / {orderedDrafts.length} projects
                {!hasMoreDrafts && displayedDraftRows.length === orderedDrafts.length && " · All loaded"}
                {hasMoreDrafts && !draftsExceedsViewport && " · Loading..."}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="submitted" className="mt-0">
          {/* 已提交项目列表 */}
          <div className="border" ref={papersTableRef}>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className={`w-[35%] cursor-pointer hover:bg-muted/50 ${submittedSortBy === "title" ? "bg-muted/30" : ""}`}
                    onClick={() => setSubmittedSortBy("title")}
                  >
                    <div className="flex items-center gap-1">
                      Title
                      {submittedSortBy === "title" && <ChevronsUpDown className="h-3 w-3" />}
                    </div>
                  </TableHead>
                  <TableHead
                    className={`w-[10%] cursor-pointer hover:bg-muted/50 ${submittedSortBy === "submittedAt" ? "bg-muted/30" : ""}`}
                    onClick={() => setSubmittedSortBy("submittedAt")}
                  >
                    <div className="flex items-center gap-1">
                      Submitted
                      {submittedSortBy === "submittedAt" && <ChevronsUpDown className="h-3 w-3" />}
                    </div>
                  </TableHead>
                  <TableHead
                    className={`w-[10%] cursor-pointer hover:bg-muted/50 ${submittedSortBy === "lastUpdatedAt" ? "bg-muted/30" : ""}`}
                    onClick={() => setSubmittedSortBy("lastUpdatedAt")}
                  >
                    <div className="flex items-center gap-1">
                      Last Update
                      {submittedSortBy === "lastUpdatedAt" && <ChevronsUpDown className="h-3 w-3" />}
                    </div>
                  </TableHead>
                  <TableHead className="w-[10%]">Type</TableHead>
                  <TableHead className="w-[15%]">Status</TableHead>
                  {isEditor && <TableHead className="text-right w-[20%]">Need-Attention</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {displayedPapers.map((p, index) => (
                  <LazyTableRow
                    key={p.id}
                    delay={index * 50}
                    colSpan={isEditor ? 6 : 5}
                    skeletonType="paper"
                    isEditor={isEditor}
                  >
                    <TableCell>
                      <a
                        href={`/papers/${p.id}/review`}
                        onClick={(e) => handleSubmittedClick(e, p.id, p.title)}
                        className="font-medium hover:underline truncate block cursor-pointer"
                        title={p.title}
                      >
                        {p.title}
                      </a>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDateTime(p.submittedAt)}
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDateTime(p.lastUpdatedAt)}
                    </TableCell>
                    <TableCell>
                      <TypeBadge type={p.type} />
                    </TableCell>
                    <TableCell>
                      <StatusBadge status={p.status} />
                    </TableCell>
                    {isEditor && (
                      <TableCell className="text-right">
                        {p.needsAttention ? <Badge variant="destructive">Need-Attention</Badge> : "-"}
                      </TableCell>
                    )}
                  </LazyTableRow>
                ))}

                {/* 初始加载skeleton */}
                {papers.length === 0 && (
                  <>
                    {Array.from({ length: 3 }).map((_, i) => (
                      <LazyTableRow
                        key={`papers-skeleton-${i}`}
                        delay={i * 50}
                        colSpan={isEditor ? 6 : 5}
                        skeletonType="paper"
                        isEditor={isEditor}
                      >
                        {/* 内容会被LazyTableRow的skeleton替换 */}
                      </LazyTableRow>
                    ))}
                  </>
                )}

                {/* 加载状态行 */}
                {hasMorePapers && papersExceedsViewport && (
                  <TableRow>
                    <TableCell colSpan={isEditor ? 6 : 5} className="text-center p-4">
                      <div
                        className="flex justify-center items-center py-2 text-muted-foreground"
                        ref={papersLoadingRef}
                      >
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Loading more...
                      </div>
                    </TableCell>
                  </TableRow>
                )}

                {orderedPapers.length === 0 && !loading && papers.length > 0 && (
                  <TableRow>
                    <TableCell
                      colSpan={isEditor ? 6 : 5}
                      className="h-24 text-center text-muted-foreground"
                    >
                      No submitted papers yet
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>

            {/* 显示当前状态 */}
            {orderedPapers.length > 0 && (
              <div className="px-4 py-2 text-xs text-muted-foreground text-center border-t">
                Showing {displayedPapers.length} / {orderedPapers.length} papers
                {!hasMorePapers && displayedPapers.length === orderedPapers.length && " · All loaded"}
                {hasMorePapers && !papersExceedsViewport && " · Loading..."}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* 重命名对话框 */}
      <Dialog open={renameDialogOpen} onOpenChange={setRenameDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Rename project</DialogTitle>
            <DialogDescription>
              Please enter a new project name. Spaces will be automatically converted to underscores.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="rename-input" className="text-right">
                Project name
              </label>
              <Input
                id="rename-input"
                value={renameInputValue}
                onChange={(e) => setRenameInputValue(e.target.value)}
                className="col-span-3"
                placeholder="Enter new project name"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    executeRename();
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRenameDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={executeRename} disabled={!renameInputValue.trim() || loading}>
              {loading ? "Renaming..." : "Confirm"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 重命名确认对话框（当名称被转换时） */}
      <AlertDialog open={renameConfirmDialogOpen} onOpenChange={setRenameConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm rename</AlertDialogTitle>
            <AlertDialogDescription>
              Project name has been automatically adjusted:「{pendingRename?.sanitizedName}」
              <br />
              Spaces have been converted to underscores, special characters have been removed.
              <br />
              Continue?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setPendingRename(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmRename} disabled={loading}>
              {loading ? "Renaming..." : "Continue"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm delete project</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete project「{currentProjectName}」? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={executeDelete} disabled={loading}>
              {loading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}