/* ---------- submit/page.tsx ---------- */
import { Role, ScopeType } from "@prisma/client";
import { redirect } from "next/navigation";

import MergedSubmissionBoard from "./MergedSubmissionBoard.client";

import { prisma } from "@/lib/prisma";
import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";

export default async function SubmitPage() {
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);
  if (!session?.user) redirect("/account");

  const uid = session.user.id;

  /* 1️⃣ 获取当前用户所有 role-binding */
  const bindings = await prisma.roleBinding.findMany({
    where: { principalId: uid, principalType: "USER" },
    select: { role: true, scopeType: true, scopeId: true },
  });

  /* 2️⃣ 辅助判断函数 */
  const hasRole = (role: Role) => bindings.some(b => b.role === role);
  const hasGlobalAdmin = bindings.some(
    b =>
      (b.role === Role.ADMIN || b.role === Role.CHIEF_EDITOR) &&
      (b.scopeType === ScopeType.GLOBAL || b.scopeType === null)
  );

  const issueIds = bindings
    .filter(b => b.role === Role.ISSUE_EDITOR && b.scopeId)
    .map(b => b.scopeId as string);

  const paperIds = bindings
    .filter(b => (b.role === Role.REVIEWER || b.role === Role.PAPER_EDITOR) && b.scopeId)
    .map(b => b.scopeId as string);

  const isAuthor = hasRole(Role.AUTHOR);

  /* 3️⃣ 组合可见稿件条件 */
  let papers = [] as any[];

  if (hasGlobalAdmin) {
    // ① 全局管理员 / 总编：查看所有
    papers = await prisma.paper.findMany({
      orderBy: { submittedAt: "desc" },
      select: {
        id: true,
        title: true,
        status: true,
        type: true,
        needsAttention: true,
        category: true,
        submittedAt: true,
        lastUpdatedAt: true,
        issueId: true,
      }
    });
  } else {
    const orConds: any[] = [];
    if (issueIds.length) {
      orConds.push({ issueId: { in: issueIds } });
    }
    if (paperIds.length) {
      orConds.push({ id: { in: paperIds } });
    }
    if (isAuthor) {
      orConds.push({ authorId: uid });
    }

    if (orConds.length === 0) {
      // 无任何可见稿件
      redirect("/");
    }

    papers = await prisma.paper.findMany({
      where: { OR: orConds },
      orderBy: { submittedAt: "desc" },
      select: {
        id: true,
        title: true,
        status: true,
        type: true,
        needsAttention: true,
        category: true,
        submittedAt: true,
        lastUpdatedAt: true,
        issueId: true,
      }
    });
  }

  /* 4️⃣ 判断是否拥有编辑权限 —— 影响 Need-Attention Badge */
  const editorRoles: Role[] = [Role.ADMIN, Role.CHIEF_EDITOR, Role.ISSUE_EDITOR, Role.PAPER_EDITOR];
  const isEditor = bindings.some(b => editorRoles.includes(b.role));

  /* 5️⃣ 渲染 */
  return (
    <section className="max-w-4xl mx-auto py-8 space-y-6">
      <MergedSubmissionBoard papers={papers as any} isEditor={isEditor} isAuthor={isAuthor} />
    </section>
  );
}
