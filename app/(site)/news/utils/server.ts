import fs from "fs";
import path from "path";
import { BlogPost } from "@/app/(site)/news/utils";

export function parseFrontmatter(fileContent: string) {
  const frontmatterRegex = /---\s*([\s\S]*?)\s*---/;
  const match = frontmatterRegex.exec(fileContent);
  if (!match) {
    return {
      metadata: {
        title: "Untitled",
        publishedAt: new Date().toISOString(),
        summary: "No summary available",
      },
      content: fileContent,
    };
  }

  const frontMatterBlock = match[1];
  const content = fileContent.replace(frontmatterRegex, "").trim();
  const frontMatterLines = frontMatterBlock.trim().split("\n");
  const metadata: any = {};

  frontMatterLines.forEach(line => {
    const [key, ...valueArr] = line.split(": ");
    let value = valueArr.join(": ").trim();
    value = value.replace(/^['"](.*)['"]$/, "$1"); // Remove quotes
    if (key) metadata[key.trim()] = value;
  });

  if (!metadata.title) metadata.title = "Untitled";
  if (!metadata.publishedAt) metadata.publishedAt = new Date().toISOString();
  if (!metadata.summary) metadata.summary = "No summary available";

  return { metadata, content };
}

export function getMDXFiles(dir: string): string[] {
  return fs.readdirSync(dir).filter(file => path.extname(file) === ".mdx");
}

export async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    const postsDirectory = path.join(process.cwd(), "app", "(site)", "news", "posts");
    const mdxFiles = getMDXFiles(postsDirectory);

    return mdxFiles
      .map(file => {
        const fileContent = fs.readFileSync(path.join(postsDirectory, file), "utf8");
        const { metadata, content } = parseFrontmatter(fileContent);
        const slug = path.basename(file, path.extname(file));

        return {
          slug,
          metadata,
          content,
        };
      })
      .sort((a, b) => {
        if (new Date(a.metadata.publishedAt) > new Date(b.metadata.publishedAt)) {
          return -1;
        }
        return 1;
      });
  } catch (error) {
    console.error("Error getting all posts:", error);
    return [];
  }
}

export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    const filePath = path.join(process.cwd(), "app", "(site)", "news", "posts", `${slug}.mdx`);

    if (!fs.existsSync(filePath)) {
      return null;
    }

    const fileContent = fs.readFileSync(filePath, "utf8");
    const { metadata, content } = parseFrontmatter(fileContent);

    return {
      slug,
      metadata,
      content,
    };
  } catch (error) {
    console.error(`Error reading post ${slug}:`, error);
    return null;
  }
}
