/* 博客文章页面样式 */

/* 文章容器 */
.blog-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
}

/* 返回链接 */
.blog-back-link {
  display: inline-flex;
  align-items: center;
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1.5rem;
  text-decoration: none;
  transition: color 0.2s ease;
}

.blog-back-link:hover {
  color: #2563eb;
}

.blog-back-link svg {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}

/* 文章标题 */
.blog-title {
  font-size: 2.5rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: #111827;
}

/* 文章元数据 */
.blog-meta {
  display: flex;
  align-items: center;
  margin-bottom: 2.5rem;
  /*border-bottom: 1px solid #e5e7eb;*/
  padding-bottom: 1.5rem;
}

.blog-category {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 1.5rem;
}

.blog-date {
  font-size: 0.875rem;
  color: #6b7280;
}

/* 文章内容区 */
.blog-content {
  font-size: 1.125rem;
  line-height: 1.8;
  color: #1f2937;
}

/* 标题锚点 */
.heading-anchor {
  display: inline-block;
  position: relative;
  margin-left: -1.5em;
  padding-right: 0.5em;
  width: 1.5em;
  opacity: 0;
  text-decoration: none;
  cursor: pointer;
}

.blog-content h1:hover .heading-anchor,
.blog-content h2:hover .heading-anchor,
.blog-content h3:hover .heading-anchor,
.blog-content h4:hover .heading-anchor,
.blog-content h5:hover .heading-anchor,
.blog-content h6:hover .heading-anchor {
  opacity: 1;
}

/* 标题样式 */
.blog-content h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-top: 2.5rem;
  margin-bottom: 1.25rem;
  color: #111827;
}

.blog-content h2 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-top: 2.25rem;
  margin-bottom: 1rem;
  color: #111827;
}

.blog-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
  color: #111827;
}

.blog-content h4,
.blog-content h5,
.blog-content h6 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.75rem;
  margin-bottom: 0.75rem;
  color: #111827;
}

/* 段落样式 */
.blog-content p {
  margin-bottom: 1.5rem;
}

/* 列表样式 */
.blog-content ul,
.blog-content ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.blog-content li {
  margin-bottom: 0.5rem;
}

.blog-content ul {
  list-style-type: disc;
}

.blog-content ol {
  list-style-type: decimal;
}

/* 链接样式 */
.blog-content a {
  color: #2563eb;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.blog-content a:hover {
  border-bottom-color: #2563eb;
}

/* 引用样式 */
.blog-content blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: #4b5563;
  margin-bottom: 1.5rem;
}

/* 代码样式 */
.blog-content code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
  background-color: #f3f4f6;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #ef4444;
}

.blog-content pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin-bottom: 1.5rem;
}

.blog-content pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
  font-size: 0.875rem;
  line-height: 1.6;
}

/* 图片样式 */
.blog-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
}

/* 表格样式 */
.blog-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.5rem;
}

.blog-content th,
.blog-content td {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  text-align: left;
}

.blog-content th {
  font-weight: 600;
  background-color: #f9fafb;
}

/* 水平线样式 */
.blog-content hr {
  border: 0;
  border-top: 1px solid #e5e7eb;
  margin: 2rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .blog-container {
    padding: 1.5rem 1rem;
  }

  .blog-title {
    font-size: 2rem;
  }

  .blog-content {
    font-size: 1rem;
  }
}

/* 暗色模式支持已禁用 - 强制使用明亮主题 */
