// app/(site)/news/[slug]/page.tsx
import type { Metadata } from "next";
import { notFound } from "next/navigation";
import Link from "next/link";
import { formatDate, BlogPost } from "@/app/(site)/news/utils";
import { baseUrl } from "@/app/sitemap";
import { CustomMDX } from "@/components/mdx";
import "../news.css";
import { getBlogPost, getBlogPosts } from "@/app/(site)/news/utils/server";

/* ---------- 路由参数类型 ---------- */
type BlogParams = {
  slug: string;
};

/* ---------- 生成静态路径 ---------- */
export async function generateStaticParams(): Promise<Array<BlogParams>> {
  const posts = await getBlogPosts();
  return posts.map(post => ({ slug: post.slug }));
}

/* ---------- 生成 Metadata ---------- */
export async function generateMetadata({
  params,
}: {
  params: Promise<BlogParams>;
}): Promise<Metadata> {
  const { slug } = await params;
  const post = await getBlogPost(slug);
  if (!post) return {}; // 不存在则返回空对象

  const { title, publishedAt: publishedTime, summary: description, image } = post.metadata;

  const ogImage = image ?? `${baseUrl}/og?title=${encodeURIComponent(title)}`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: "article",
      publishedTime,
      url: `${baseUrl}/news/${post.slug}`,
      images: [{ url: ogImage }],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [ogImage],
    },
  };
}

/* ---------- 主页面组件 ---------- */
export default async function Blog({ params }: { params: Promise<BlogParams> }) {
  const { slug } = await params;
  const post = await getBlogPost(slug);
  if (!post) notFound();

  return (
    <div className="blog-container">
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        suppressHydrationWarning
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BlogPosting",
            headline: post.metadata.title,
            datePublished: post.metadata.publishedAt,
            dateModified: post.metadata.publishedAt,
            description: post.metadata.summary,
            image: post.metadata.image
              ? `${baseUrl}${post.metadata.image}`
              : `/og?title=${encodeURIComponent(post.metadata.title)}`,
            url: `${baseUrl}/news/${post.slug}`,
            author: { "@type": "Person", name: "My Portfolio" },
          }),
        }}
      />

      <Link href="/news" className="blog-back-link">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path
            fillRule="evenodd"
            d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
            clipRule="evenodd"
          />
        </svg>
        Back to article list
      </Link>

      <h1 className="blog-title">{post.metadata.title}</h1>

      <div className="blog-meta">
        {post.metadata.category && <span className="blog-category">{post.metadata.category}</span>}
        <span className="blog-date">{formatDate(post.metadata.publishedAt)}</span>
      </div>

      <article className="blog-content">
        <CustomMDX source={post.content} />
      </article>
    </div>
  );
}
