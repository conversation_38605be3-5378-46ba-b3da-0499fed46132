"use client";

import { useState } from "react";
import { BlogPost } from "@/app/(site)/news/utils";
import LazyNewsItem from "./LazyNewsItem";

export function ClientBlogPosts({ initialPosts }: { initialPosts: BlogPost[] }) {
  const [filter, setFilter] = useState("All");

  // 从文章元数据中获取所有唯一分类
  const categories = [
    "All",
    ...new Set(initialPosts.map(post => post.metadata.category || "Uncategorized")),
  ];

  // 根据当前筛选器过滤博客文章
  const filteredBlogs =
    filter === "All"
      ? initialPosts
      : initialPosts.filter(post => post.metadata.category === filter);

  // 按发布时间排序
  const sortedBlogs = filteredBlogs.sort((a, b) => {
    if (new Date(a.metadata.publishedAt) > new Date(b.metadata.publishedAt)) {
      return -1;
    }
    return 1;
  });

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* 筛选器 */}
      <div className="flex justify-between items-center border-b pb-3">
        <div className="flex flex-wrap gap-3 sm:gap-6">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setFilter(category)}
              className={`text-sm sm:text-base hover:text-blue-600 transition-colors ${filter === category
                ? "text-black font-medium"
                : "text-gray-500"
                }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* 新闻列表 */}
      <div className="space-y-4 sm:space-y-6">
        {sortedBlogs.map((post, index) => (
          <div
            key={post.slug}
          >
            <LazyNewsItem
              post={post}
              delay={index * 50} // 每个项目延迟50ms
            />
          </div>
        ))}
      </div>

      {sortedBlogs.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-sm sm:text-base">
            没有找到符合条件的文章
          </p>
        </div>
      )}
    </div>
  );
}
