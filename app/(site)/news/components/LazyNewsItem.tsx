"use client";

import Link from "next/link";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { formatDate, BlogPost } from "@/app/(site)/news/utils";
import { Skeleton } from "@/components/ui/skeleton";

interface LazyNewsItemProps {
  post: BlogPost;
  delay?: number;
}

export default function LazyNewsItem({ post, delay = 0 }: LazyNewsItemProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const [imageError, setImageError] = useState(false);
  const itemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (itemRef.current) {
      observer.observe(itemRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => setShouldRender(true), delay);
      return () => clearTimeout(timer);
    }
  }, [isVisible, delay]);

  if (!shouldRender) {
    return (
      <div ref={itemRef} className="animate-pulse h-32 sm:h-36 md:h-40">
        <div className="flex h-full">
          {/* 图片skeleton - 宽度等于容器高度 */}
          <div className="h-full aspect-square flex-shrink-0">
            <Skeleton className="w-full h-full" />
          </div>

          {/* 内容skeleton */}
          <div className="flex-1 p-2 sm:p-3 md:p-4 space-y-1 sm:space-y-2 flex flex-col justify-center">
            <div className="space-y-1">
              <Skeleton className="h-4 sm:h-5 md:h-6 w-full" />
              <Skeleton className="h-4 sm:h-5 md:h-6 w-3/4" />
            </div>
            <div className="space-y-1">
              <Skeleton className="h-3 sm:h-3 md:h-4 w-full" />
              <Skeleton className="h-3 sm:h-3 md:h-4 w-5/6" />
            </div>
            <div className="flex gap-1 sm:gap-2">
              <Skeleton className="h-4 sm:h-5 w-12 sm:w-16 rounded-full" />
              <Skeleton className="h-4 sm:h-5 w-16 sm:w-20 rounded-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  const imagePath = post.metadata.image ? `/news/images/${post.metadata.image}` : null;

  return (
    <div
      ref={itemRef}
      className="animate-in fade-in-0 duration-300 overflow-hidden h-32 sm:h-36 md:h-40"
    >
      <Link href={`/news/${post.slug}`} className="block group h-full">
        <div className="flex h-full hover:bg-gray-50 transition-colors">
          {/* 图片区域 - 宽度等于容器高度，形成正方形 */}
          <div className="h-full aspect-square flex-shrink-0 relative overflow-hidden">
            {imagePath && !imageError ? (
              <Image
                src={imagePath}
                alt={post.metadata.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                onError={() => setImageError(true)}
                sizes="(max-width: 640px) 64px, (max-width: 768px) 80px, (max-width: 1024px) 96px, 112px"
              />
            ) : (
              <div className="w-full h-full bg-brand-600 flex items-center justify-center"></div>
            )}
          </div>

          {/* 内容区域 - 只有内容部分有padding，并撑满父容器高度 */}
          <div className="flex-1 min-w-0 p-2 sm:p-3 md:p-4 flex flex-col justify-center">
            <div className="space-y-1 sm:space-y-2">
              {/* 标题 */}
              <h3 className="text-sm sm:text-lg md:text-xl font-medium text-black group-hover:text-blue-600 transition-colors leading-tight">
                {post.metadata.title}
              </h3>

              {/* 分类和日期 */}
              <div className="flex flex-wrap gap-1 sm:gap-2 text-xs">
                {post.metadata.category && (
                  <span className="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-100 text-gray-600 rounded-full font-medium text-xs">
                    {post.metadata.category}
                  </span>
                )}
                <span className="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-50 text-gray-500 rounded-full text-xs">
                  {formatDate(post.metadata.publishedAt, false)}
                </span>
              </div>

              {/* 摘要 */}
              <p className="text-xs sm:text-sm md:text-base text-gray-700 leading-relaxed line-clamp-2">
                {post.metadata.summary}
              </p>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
}
