import { getBlogPosts } from "@/app/(site)/news/utils/server";
import LazyNewsItem from "./LazyNewsItem";

export async function StaticBlogPosts({ viewType = "list" }: { viewType?: "list" | "grid" }) {
  const allBlogs = await getBlogPosts();

  if (allBlogs.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-sm sm:text-base">没有找到文章</p>
      </div>
    );
  }

  // 按发布时间排序
  const sortedBlogs = allBlogs.sort((a, b) => {
    if (new Date(a.metadata.publishedAt) > new Date(b.metadata.publishedAt)) {
      return -1;
    }
    return 1;
  });

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* 文章计数 */}
      <div className="flex justify-end items-center border-b pb-3">
        <div className="text-xs sm:text-sm text-gray-500">
          {sortedBlogs.length} article{sortedBlogs.length !== 1 ? "s" : ""}
        </div>
      </div>

      {/* 新闻列表 */}
      <div className="space-y-4 sm:space-y-6">
        {sortedBlogs.map((post, index) => (
          <div
            key={post.slug}
            className="border-b border-gray-100 last:border-b-0"
          >
            <LazyNewsItem
              post={post}
              delay={index * 50} // 每个项目延迟50ms
            />
          </div>
        ))}
      </div>
    </div>
  );
}
