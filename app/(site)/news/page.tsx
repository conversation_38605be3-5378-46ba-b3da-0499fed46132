import { getBlogPosts } from "@/app/(site)/news/utils/server";
import { ClientBlogPosts } from "./components/ClientBlogPosts";

export const metadata = {
  title: "Research",
  description: "Research publications, releases, and findings.",
};

export default async function Page() {
  // 在服务器端获取博客文章
  const posts = await getBlogPosts();

  return (
    <section className="max-w-5xl mx-auto px-4 py-8">
      {/* 将文章数据传递给客户端组件处理交互功能 */}
      <ClientBlogPosts initialPosts={posts} />
    </section>
  );
}
