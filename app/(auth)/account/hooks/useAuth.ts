/**
 * 认证 Hook - 专业级实现
 * 提供类型安全、健壮的认证功能
 */

import { useCallback, useRef, useState } from 'react';
import { toast } from 'sonner';
import { useSupabase } from '@/components/SupabaseProvider';

// 类型定义
export interface AuthConfig {
  maxRetries: number;
  timeoutMs: number;
  retryDelays: number[];
  emailCheckTimeoutMs: number;
}

export interface AuthState {
  isLoading: boolean;
  isRetrying: boolean;
  retryCount: number;
  lastError: string | null;
}

export interface AuthResult {
  success: boolean;
  requiresEmailVerification?: boolean;
  error?: string;
}

// 默认配置
const DEFAULT_CONFIG: AuthConfig = {
  maxRetries: 3,
  timeoutMs: 15000,
  retryDelays: [1000, 2000, 4000],
  emailCheckTimeoutMs: 8000,
};

// 错误类型
enum AuthErrorType {
  NETWORK = 'NETWORK',
  AUTH = 'AUTH',
  VALIDATION = 'VALIDATION',
  RATE_LIMIT = 'RATE_LIMIT',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN',
}

interface ClassifiedError {
  type: AuthErrorType;
  message: string;
  isRetryable: boolean;
}

// 输入验证
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

const validatePassword = (password: string): boolean => {
  // 至少8位，包含字母和数字
  const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{8,}$/;
  return passwordRegex.test(password);
};

const validateName = (name: string): boolean => {
  return name.trim().length >= 2 && name.trim().length <= 50;
};

// 错误分类
const classifyError = (error: any): ClassifiedError => {
  const message = error?.message || String(error);
  const lowerMessage = message.toLowerCase();

  // 网络错误
  if (lowerMessage.includes('timeout') || lowerMessage.includes('network')) {
    return {
      type: AuthErrorType.TIMEOUT,
      message: 'Network connection timeout, please check the network and try again',
      isRetryable: true,
    };
  }

  // 认证错误
  if (lowerMessage.includes('invalid login credentials')) {
    return {
      type: AuthErrorType.AUTH,
      message: 'Email or password error',
      isRetryable: false,
    };
  }

  if (lowerMessage.includes('user not found')) {
    return {
      type: AuthErrorType.AUTH,
      message: 'Email does not exist, please check the email address or register first',
      isRetryable: false,
    };
  }

  if (lowerMessage.includes('email not confirmed')) {
    return {
      type: AuthErrorType.AUTH,
      message: 'Email not verified, please check the email and click the verification link',
      isRetryable: false,
    };
  }

  if (lowerMessage.includes('user already registered')) {
    return {
      type: AuthErrorType.AUTH,
      message: 'This email has been registered, please login directly',
      isRetryable: false,
    };
  }

  // 速率限制
  if (lowerMessage.includes('rate limit') || lowerMessage.includes('too many')) {
    return {
      type: AuthErrorType.RATE_LIMIT,
      message: 'Request too frequent, please try again later',
      isRetryable: true,
    };
  }

  // 密码验证错误
  if (lowerMessage.includes('password should be at least')) {
    return {
      type: AuthErrorType.VALIDATION,
      message: 'Password does not meet the requirements: at least 8 digits and contains letters and numbers',
      isRetryable: false,
    };
  }

  return {
    type: AuthErrorType.UNKNOWN,
    message: 'Operation failed, please try again later',
    isRetryable: true,
  };
};

// 超时包装器
const withTimeout = <T>(promise: Promise<T>, timeoutMs: number): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('TIMEOUT')), timeoutMs);
    }),
  ]);
};

// 延迟函数
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export function useAuth(config: Partial<AuthConfig> = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const { supabase } = useSupabase();

  const [state, setState] = useState<AuthState>({
    isLoading: false,
    isRetrying: false,
    retryCount: 0,
    lastError: null,
  });

  // 防止重复请求
  const activeRequestRef = useRef<Promise<AuthResult> | null>(null);
  const emailCheckCacheRef = useRef<Map<string, { result: boolean; timestamp: number }>>(new Map());

  // 清理缓存
  const cleanupEmailCache = useCallback(() => {
    const now = Date.now();
    const cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

    for (const [email, cache] of emailCheckCacheRef.current.entries()) {
      if (now - cache.timestamp > cacheTimeout) {
        emailCheckCacheRef.current.delete(email);
      }
    }
  }, []);

  // 检查邮箱是否存在（带缓存）
  const checkEmailExists = useCallback(async (email: string): Promise<boolean> => {
    const normalizedEmail = email.trim().toLowerCase();

    // 检查缓存
    cleanupEmailCache();
    const cached = emailCheckCacheRef.current.get(normalizedEmail);
    if (cached) {
      return cached.result;
    }

    try {
      const response = await withTimeout(
        fetch('/api/auth/check-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email: normalizedEmail }),
        }),
        finalConfig.emailCheckTimeoutMs
      );

      if (!response.ok) {
        // 安全策略：检查失败时假设邮箱存在
        return true;
      }

      const { exists } = await response.json();

      // 缓存结果
      emailCheckCacheRef.current.set(normalizedEmail, {
        result: exists,
        timestamp: Date.now(),
      });

      return exists;
    } catch (error) {
      // 生产环境：静默处理错误，安全策略假设邮箱存在
      if (process.env.NODE_ENV === 'development') {
        console.warn('[Auth] Email check failed:', error);
      }
      return true;
    }
  }, [finalConfig.emailCheckTimeoutMs, cleanupEmailCache]);

  // 执行认证操作
  const performAuth = useCallback(async (
    mode: 'login' | 'register',
    email: string,
    password: string,
    name?: string
  ): Promise<AuthResult> => {
    // 输入验证
    if (!validateEmail(email)) {
      throw new Error('Email format is incorrect');
    }

    // 仅在注册时验证密码强度，登录时允许任何密码
    if (mode === 'register' && !validatePassword(password)) {
      throw new Error('Password does not meet the requirements: at least 8 digits and contains letters and numbers');
    }

    if (mode === 'register' && name && !validateName(name)) {
      throw new Error('Name length should be between 2 and 50 characters');
    }

    const normalizedEmail = email.trim().toLowerCase();

    if (mode === 'login') {
      // 登录逻辑
      const { error } = await withTimeout(
        supabase.auth.signInWithPassword({
          email: normalizedEmail,
          password,
        }),
        finalConfig.timeoutMs
      );

      if (error) throw error;
      return { success: true };
    } else {
      // 注册逻辑 - 先检查邮箱
      const emailExists = await checkEmailExists(normalizedEmail);
      if (emailExists) {
        throw new Error('User already registered');
      }

      const { data, error } = await withTimeout(
        supabase.auth.signUp({
          email: normalizedEmail,
          password,
          options: { data: { name: name?.trim() } },
        }),
        finalConfig.timeoutMs
      );

      if (error) throw error;

      return {
        success: true,
        requiresEmailVerification: !!(data.user && !data.session),
      };
    }
  }, [supabase, finalConfig.timeoutMs, checkEmailExists]);

  // 带重试的认证
  const authenticateWithRetry = useCallback(async (
    mode: 'login' | 'register',
    email: string,
    password: string,
    name?: string
  ): Promise<AuthResult> => {
    // 防止重复请求
    if (activeRequestRef.current) {
      return activeRequestRef.current;
    }

    const authPromise = (async (): Promise<AuthResult> => {
      let lastError: Error | null = null;

      for (let attempt = 0; attempt <= finalConfig.maxRetries; attempt++) {
        try {
          setState(prev => ({
            ...prev,
            isLoading: true,
            isRetrying: attempt > 0,
            retryCount: attempt,
            lastError: null,
          }));

          const result = await performAuth(mode, email, password, name);

          setState(prev => ({
            ...prev,
            isLoading: false,
            isRetrying: false,
            retryCount: 0,
            lastError: null,
          }));

          return result;
        } catch (error) {
          lastError = error as Error;
          const classified = classifyError(error);

          // 不可重试的错误直接抛出
          if (!classified.isRetryable || attempt === finalConfig.maxRetries) {
            setState(prev => ({
              ...prev,
              isLoading: false,
              isRetrying: false,
              lastError: classified.message,
            }));

            const toastTitle = mode === 'login' ? 'Login failed' : 'Register failed';
            toast.error(toastTitle, {
              description: classified.message,
              duration: 4000,
            });

            throw error;
          }

          // 等待后重试
          if (attempt < finalConfig.maxRetries) {
            const delayMs = finalConfig.retryDelays[attempt] || 4000;
            await delay(delayMs);
          }
        }
      }

      throw lastError;
    })();

    activeRequestRef.current = authPromise;

    try {
      return await authPromise;
    } finally {
      activeRequestRef.current = null;
    }
  }, [finalConfig, performAuth]);

  // 重置状态
  const resetState = useCallback(() => {
    setState({
      isLoading: false,
      isRetrying: false,
      retryCount: 0,
      lastError: null,
    });
  }, []);

  return {
    state,
    authenticate: authenticateWithRetry,
    resetState,
    validateEmail,
    validatePassword,
    validateName,
  };
}
