/**
 * 浮窗通信 Hook - 简化高效实现
 * 提供类型安全的浮窗通信功能，解决 windowId 获取时序问题
 */

import { useCallback, useEffect, useRef, useState } from 'react';

interface WindowMessage {
  id: string;
  type: string;
  timestamp: number;
  windowId?: string;
  source: string;
  payload: Record<string, any>;
}

interface FloatingWindowState {
  isInFloatingWindow: boolean;
  windowId: string | null;
  isReady: boolean;
}

// 全局存储 windowId，避免时序问题
let globalWindowId: string | null = null;
let isGlobalListenerSetup = false;

// 设置全局消息监听器，在窗口初始化时立即捕获 windowId
const setupGlobalListener = () => {
  if (isGlobalListenerSetup || typeof window === 'undefined') {
    return;
  }

  const messageHandler = (event: MessageEvent) => {
    if (event.source === window.parent && event.data?.type === 'WINDOW_INIT') {
      const windowId = event.data.windowId || event.data.payload?.windowInfo?.id;
      if (windowId && typeof windowId === 'string') {
        globalWindowId = windowId;
        if (process.env.NODE_ENV === 'development') {
          // console.log('[FloatingWindow] Global windowId captured:', windowId);
        }
      }
    }
  };

  window.addEventListener('message', messageHandler);
  isGlobalListenerSetup = true;

  // 立即请求窗口信息
  try {
    if (window.parent && window.parent !== window) {
      window.parent.postMessage({
        type: 'REQUEST_WINDOW_INFO',
        timestamp: Date.now(),
        id: `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      }, '*');
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      // console.warn('[FloatingWindow] Failed to request window info:', error);
    }
  }
};

// 在模块加载时立即设置监听器
if (typeof window !== 'undefined') {
  setupGlobalListener();
}

export function useFloatingWindow() {
  const [state, setState] = useState<FloatingWindowState>({
    isInFloatingWindow: false,
    windowId: null,
    isReady: false,
  });

  // 检测浮窗环境 - 简化逻辑
  const checkFloatingEnvironment = useCallback((): boolean => {
    try {
      return typeof window !== 'undefined' &&
        window.parent &&
        window.parent !== window;
    } catch {
      return false;
    }
  }, []);

  // 获取窗口ID - 简化为同步获取
  const getWindowId = useCallback((): string | null => {
    // 优先返回全局存储的 windowId
    if (globalWindowId) {
      return globalWindowId;
    }

    // 回退到状态中的 windowId
    if (state.windowId) {
      return state.windowId;
    }

    return null;
  }, [state.windowId]);

  // 发送命令到 Terminal - 简化逻辑
  const sendCommand = useCallback((command: string, params?: Record<string, any>): boolean => {
    if (!state.isInFloatingWindow) {
      if (process.env.NODE_ENV === 'development') {
        // console.warn('[FloatingWindow] Not in floating window environment');
      }
      return false;
    }

    const windowId = getWindowId();
    if (!windowId) {
      if (process.env.NODE_ENV === 'development') {
        // console.warn('[FloatingWindow] WindowId not available');  
      }
      return false;
    }

    try {
      const messageId = `cmd-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      const message: WindowMessage = {
        id: messageId,
        type: 'TERMINAL_COMMAND',
        timestamp: Date.now(),
        windowId,
        source: 'window',
        payload: {
          command,
          params,
          expectResponse: true,
        },
      };

      window.parent.postMessage(message, '*');
      if (process.env.NODE_ENV === 'development') {
        // console.log('[FloatingWindow] Command sent:', command, 'windowId:', windowId);
      }
      return true;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // console.warn('[FloatingWindow] Failed to send command:', error);
      }
      return false;
    }
  }, [state.isInFloatingWindow, getWindowId]);

  // 关闭当前窗口 - 简化为同步操作
  const closeWindow = useCallback((): boolean => {
    const windowId = getWindowId();
    if (!windowId) {
      if (process.env.NODE_ENV === 'development') {
        // console.warn('[FloatingWindow] Cannot close window: windowId not available');
      }
      return false;
    }

    return sendCommand(`close_window windowId=${windowId}`);
  }, [getWindowId, sendCommand]);

  // 初始化 - 简化逻辑
  useEffect(() => {
    const isFloating = checkFloatingEnvironment();

    setState(prev => ({
      ...prev,
      isInFloatingWindow: isFloating,
      windowId: globalWindowId,
      isReady: !!globalWindowId,
    }));

    // 如果在浮窗环境中但还没有 windowId，设置监听器
    if (isFloating && !globalWindowId) {
      setupGlobalListener();
    }
  }, [checkFloatingEnvironment]);

  return {
    ...state,
    sendCommand,
    closeWindow,
    getWindowId,
  };
}
