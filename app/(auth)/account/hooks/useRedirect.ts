/**
 * 重定向 Hook - 专业级实现
 * 处理登录成功后的重定向逻辑
 */

import { useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';

interface RedirectConfig {
  successMessage?: string;
  successDescription?: string;
  toastDuration?: number;
  windowCloseDelay?: number;
  roleCheckTimeout?: number;
}

interface RedirectOptions {
  isFloatingWindow?: boolean;
  closeWindow?: () => boolean;
}

const DEFAULT_CONFIG: Required<RedirectConfig> = {
  successMessage: 'Login Success!',
  successDescription: 'Welcome back',
  toastDuration: 2000,
  windowCloseDelay: 1500,
  roleCheckTimeout: 8000,
};

// 角色到路由的映射
const ROLE_ROUTES = {
  ADMIN: '/admin',
  CHIEF_EDITOR: '/admin',
  SECTION_EDITOR: '/editor',
  MANAGING_EDITOR: '/editor',
} as const;

const DEFAULT_ROUTE = '/submit';

// 超时包装器
const withTimeout = <T>(promise: Promise<T>, timeoutMs: number): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('TIMEOUT')), timeoutMs);
    }),
  ]);
};

// 验证回调URL的安全性
const isValidCallbackUrl = (url: string): boolean => {
  try {
    // 只允许相对路径
    if (!url.startsWith('/')) {
      return false;
    }

    // 防止开放重定向攻击
    if (url.startsWith('//') || url.includes('..')) {
      return false;
    }

    // 检查是否为有效的路径
    new URL(url, 'http://localhost');
    return true;
  } catch {
    return false;
  }
};

// 获取用户角色
const getUserRoles = async (timeoutMs: number): Promise<string[]> => {
  try {
    const response = await withTimeout(fetch('/api/me'), timeoutMs);

    if (!response.ok) {
      console.warn('[Redirect] Failed to fetch user roles:', response.status);
      return [];
    }

    const data = await response.json();
    return Array.isArray(data.roles) ? data.roles : [];
  } catch (error) {
    console.warn('[Redirect] Error fetching user roles:', error);
    return [];
  }
};

// 根据角色确定重定向路由
const getRouteByRoles = (roles: string[]): string => {
  for (const role of roles) {
    if (role in ROLE_ROUTES) {
      return ROLE_ROUTES[role as keyof typeof ROLE_ROUTES];
    }
  }
  return DEFAULT_ROUTE;
};

export function useRedirect(config: RedirectConfig = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const router = useRouter();
  const searchParams = useSearchParams();

  // 处理普通环境的重定向
  const handleNormalRedirect = useCallback(async (): Promise<void> => {
    try {
      // 检查回调URL
      const callbackUrl = searchParams.get('callbackUrl');
      if (callbackUrl && isValidCallbackUrl(callbackUrl)) {
        const decodedUrl = decodeURIComponent(callbackUrl);
        router.push(decodedUrl);
        return;
      }

      // 根据用户角色重定向
      const roles = await getUserRoles(finalConfig.roleCheckTimeout);
      const targetRoute = getRouteByRoles(roles);

      router.push(targetRoute);
    } catch (error) {
      console.error('[Redirect] Error during redirect:', error);
      // 发生错误时重定向到默认路由
      router.push(DEFAULT_ROUTE);
    }
  }, [searchParams, router, finalConfig.roleCheckTimeout]);

  // 处理浮窗环境的重定向 - 简化为同步操作
  const handleFloatingWindowRedirect = useCallback((
    closeWindow: () => boolean
  ): void => {
    // 显示成功提示
    toast.success(finalConfig.successMessage, {
      description: finalConfig.successDescription,
      duration: finalConfig.toastDuration,
    });

    // 延迟关闭窗口，让用户看到成功提示
    setTimeout(() => {
      try {
        const closed = closeWindow();
        if (!closed) {
          console.warn('[Redirect] Failed to close floating window, falling back to normal redirect');
          handleNormalRedirect();
        }
      } catch (error) {
        console.error('[Redirect] Error closing floating window:', error);
        handleNormalRedirect();
      }
    }, finalConfig.windowCloseDelay);
  }, [finalConfig, handleNormalRedirect]);

  // 主重定向函数 - 简化为同步操作
  const redirect = useCallback((options: RedirectOptions = {}): void => {
    const { isFloatingWindow = false, closeWindow } = options;

    try {
      if (isFloatingWindow && closeWindow) {
        handleFloatingWindowRedirect(closeWindow);
      } else {
        // 显示成功提示
        toast.success(finalConfig.successMessage, {
          description: finalConfig.successDescription,
          duration: finalConfig.toastDuration,
        });

        handleNormalRedirect();
      }
    } catch (error) {
      console.error('[Redirect] Unexpected error during redirect:', error);

      // 显示错误提示
      toast.error('Redirect failed', {
        description: 'Page jump failed, please refresh the page manually',
        duration: 4000,
      });

      // 尝试重定向到默认路由
      try {
        router.push(DEFAULT_ROUTE);
      } catch (routerError) {
        console.error('[Redirect] Failed to redirect to default route:', routerError);
      }
    }
  }, [handleFloatingWindowRedirect, handleNormalRedirect, finalConfig]);

  return {
    redirect,
    handleNormalRedirect,
    isValidCallbackUrl,
  };
}
