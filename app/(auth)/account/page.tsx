"use client";

import { useState, useMemo, FormEvent, useC<PERSON>back, memo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// 专业级 Hooks
import { useAuth } from "./hooks/useAuth";
import { useFloatingWindow } from "./hooks/useFloatingWindow";
import { useRedirect } from "./hooks/useRedirect";
import { AuthErrorBoundary } from "./components/ErrorBoundary";

// 导入配置常量
import {
  MODES,
  UI_CONFIG,
  REGEX_PATTERNS,
  type AuthMode as Mode
} from "./config/constants";

/**
 * Account 页面内部组件 - 专业级实现
 * 提供安全、健壮的登录和注册功能
 */
const AccountPageContent = memo(function AccountPageContent() {
  // Hooks
  const auth = useAuth();
  const floatingWindow = useFloatingWindow();
  const redirect = useRedirect();

  // 状态管理
  const [mode, setMode] = useState<Mode>("login");
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [password, setPassword] = useState("");
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [showRegisterDialog, setShowRegisterDialog] = useState(false);

  // 计算属性 - 优化性能
  const isPasswordValid = useMemo(() => REGEX_PATTERNS.PASSWORD.test(password), [password]);

  const isFormValid = useMemo(() => {
    const emailValid = auth.validateEmail(email);
    // 仅在注册时验证密码强度，登录时只要有密码即可
    const passwordValid = mode === 'register' ? auth.validatePassword(password) : password.length > 0;
    const nameValid = mode === 'register' ? auth.validateName(name) : true;

    return emailValid && passwordValid && nameValid;
  }, [auth.validateEmail, auth.validatePassword, auth.validateName, email, password, name, mode]);

  // 表单重置
  const resetForm = useCallback(() => {
    setEmail("");
    setName("");
    setPassword("");
    setPasswordFocused(false);
    auth.resetState();
  }, [auth]);

  // 切换模式时重置状态
  const handleModeChange = useCallback((newMode: Mode) => {
    setMode(newMode);
    auth.resetState();
  }, [auth]);

  // 处理表单提交
  const handleSubmit = useCallback(async (e: FormEvent) => {
    e.preventDefault();

    if (!isFormValid) {
      return;
    }

    try {
      const result = await auth.authenticate(mode, email, password, name);

      if (result.requiresEmailVerification) {
        // 显示注册成功对话框
        setShowRegisterDialog(true);
      } else {
        // 登录成功，处理重定向
        redirect.redirect({
          isFloatingWindow: floatingWindow.isInFloatingWindow,
          closeWindow: floatingWindow.closeWindow,
        });
      }
    } catch (error) {
      // 错误已在 useAuth 中处理，生产环境静默处理
      if (process.env.NODE_ENV === 'development') {
        console.warn('[Account] Authentication failed:', error);
      }
    }
  }, [isFormValid, auth, mode, email, password, name, redirect, floatingWindow]);

  // 处理注册确认对话框
  const handleRegisterConfirm = useCallback(() => {
    setShowRegisterDialog(false);
    setMode("login");
    resetForm();
  }, [resetForm]);




  /* ---------- UI ---------- */
  return (
    <>
      <div className="min-h-screen flex items-center justify-center px-4">
        <Card className="w-full max-w-sm">
          <CardHeader className="text-xl font-bold text-center">Account</CardHeader>
          <CardContent>
            <Tabs
              defaultValue="login"
              className="w-full"
              onValueChange={v => handleModeChange(v as Mode)}
            >
              <TabsList className="grid grid-cols-2 mb-6">
                <TabsTrigger value="login">Login</TabsTrigger>
                <TabsTrigger value="register">Register</TabsTrigger>
              </TabsList>

              {MODES.map(tab => (
                <TabsContent key={tab} value={tab}>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    {/* 邮箱输入 */}
                    <Input
                      type="email"
                      placeholder="Email"
                      value={email}
                      onChange={e => setEmail(e.target.value.trim())}
                      disabled={auth.state.isLoading}
                      required
                      autoComplete="email"
                    />

                    {/* 用户名输入（仅注册） */}
                    {tab === "register" && (
                      <Input
                        placeholder="User Name"
                        value={name}
                        onChange={e => setName(e.target.value)}
                        disabled={auth.state.isLoading}
                        required
                        autoComplete="name"
                        minLength={2}
                        maxLength={50}
                      />
                    )}

                    {/* 密码输入 + 验证提示 */}
                    <Tooltip
                      open={tab === "register" && passwordFocused && !!password && !isPasswordValid}
                    >
                      <TooltipTrigger asChild>
                        <Input
                          type="password"
                          placeholder="Password"
                          value={password}
                          onChange={e => setPassword(e.target.value)}
                          onFocus={() => setPasswordFocused(true)}
                          onBlur={() => setPasswordFocused(false)}
                          disabled={auth.state.isLoading}
                          required
                          autoComplete={tab === "login" ? "current-password" : "new-password"}
                          {...(tab === "register" && { minLength: 8 })}
                        />
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        <p className="text-xs leading-tight">
                          {UI_CONFIG.TOOLTIP_CONTENT}
                        </p>
                      </TooltipContent>
                    </Tooltip>

                    {/* 重试状态显示 */}
                    {auth.state.isRetrying && (
                      <div className="text-center text-sm text-muted-foreground">
                        {UI_CONFIG.RETRY_DISPLAY_FORMAT(auth.state.retryCount)}
                      </div>
                    )}

                    {/* 提交按钮 */}
                    <Button
                      type="submit"
                      disabled={auth.state.isLoading || !isFormValid}
                      className="w-full"
                    >
                      {auth.state.isLoading
                        ? auth.state.isRetrying
                          ? UI_CONFIG.LOADING_STATES.RETRY(auth.state.retryCount)
                          : tab === "login"
                            ? UI_CONFIG.LOADING_STATES.LOGIN
                            : UI_CONFIG.LOADING_STATES.REGISTER
                        : tab === "login"
                          ? "Login"
                          : "Register"}
                    </Button>
                  </form>
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* 注册成功确认对话框 */}
      <AlertDialog open={showRegisterDialog} onOpenChange={setShowRegisterDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Register Success</AlertDialogTitle>
            <AlertDialogDescription>
              The confirmation email has been sent to your email <strong>{email}</strong>.
              <br />
              <br />
              Please check your email (including spam folder), click the confirmation link to complete the email verification and then you can login.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={handleRegisterConfirm}>
              Confirm, go to login
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
});

/**
 * Account 页面主组件 - 带错误边界
 */
export default function AccountPage() {
  return (
    <AuthErrorBoundary>
      <AccountPageContent />
    </AuthErrorBoundary>
  );
}
