/**
 * Account 页面配置常量
 * 集中管理所有配置项，便于维护和环境适配
 */

// 环境检测
export const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';
export const IS_PRODUCTION = process.env.NODE_ENV === 'production';

// 认证配置
export const AUTH_CONFIG = {
  // 重试配置
  MAX_RETRIES: IS_PRODUCTION ? 3 : 1,
  TIMEOUT_MS: IS_PRODUCTION ? 15000 : 5000,
  RETRY_DELAYS: IS_PRODUCTION ? [1000, 2000, 4000] : [500, 1000, 2000],

  // 邮箱检查配置
  EMAIL_CHECK_TIMEOUT_MS: IS_PRODUCTION ? 8000 : 3000,
  EMAIL_CACHE_DURATION_MS: 5 * 60 * 1000, // 5分钟

  // 验证规则
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
} as const;

// 浮窗配置
export const FLOATING_WINDOW_CONFIG = {
  // 消息超时
  MESSAGE_TIMEOUT_MS: 10000,
  WINDOW_ID_TIMEOUT_MS: 5000,

  // 重定向延迟
  REDIRECT_DELAY_MS: 1500,
  TOAST_DURATION_MS: 2000,
} as const;

// UI 配置
export const UI_CONFIG = {
  // 提示文本
  TOOLTIP_CONTENT: "At least 8 characters, containing letters and numbers",

  // 加载状态文本
  LOADING_STATES: {
    LOGIN: "Logging in...",
    REGISTER: "Registering...",
    RETRY: (count: number) => `Retrying... (${count}/${AUTH_CONFIG.MAX_RETRIES})`,
  },

  // 重试显示格式
  RETRY_DISPLAY_FORMAT: (count: number) => `Retrying... (${count}/${AUTH_CONFIG.MAX_RETRIES})`,

  // Toast 配置
  TOAST_DURATION: {
    SUCCESS: 2000,
    ERROR: 4000,
    WARNING: 3000,
  },
} as const;

// 正则表达式
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  // 至少8位，包含字母和数字
  PASSWORD: /^(?=.*[a-zA-Z])(?=.*\d).{8,}$/,
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  EMAIL_INVALID: "Email format is incorrect",
  PASSWORD_INVALID: "Password does not meet the requirements: at least 8 digits and contains letters and numbers",
  NAME_INVALID: "Name length should be between 2 and 50 characters",

  // 网络错误
  NETWORK_TIMEOUT: "Network connection timeout, please check the network and try again",
  RATE_LIMIT: "Request too frequent, please try again later",

  // 认证错误
  INVALID_CREDENTIALS: "Email or password error",
  USER_NOT_FOUND: "Email does not exist, please check the email address or register first",
  EMAIL_NOT_CONFIRMED: "Email not verified, please check the email and click the verification link",
  USER_ALREADY_REGISTERED: "This email has been registered, please login directly",
  PASSWORD_REQUIREMENTS: "Password does not meet the requirements: at least 8 digits and contains letters and numbers",

  // 通用错误
  UNKNOWN_ERROR: "Operation failed, please try again later",
  SYSTEM_ERROR: "System error, please try again later",
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: "Login Success!",
  LOGIN_SUCCESS_DESCRIPTION: "Welcome back",
  REGISTER_SUCCESS: "Register Success!",
  REGISTER_SUCCESS_DESCRIPTION: "Confirmation email has been sent to your email",
} as const;

// 模式常量
export const MODES = ["login", "register"] as const;
export type Mode = typeof MODES[number];

// 导出类型
export type AuthMode = Mode;
