/* 共享的A4页面布局和排版样式 */
@import "highlight.js/styles/github.css";

/* A4页面尺寸设置 */
@page {
  size: A4 portrait;
  margin: 10mm;
}

/* 屏幕显示的基础样式 */
@media screen {
  .page {
    margin: 5mm auto;
  }

  .embed-media {
    display: block;
  }
  .qr-frame {
    display: none;
  }
}

/* 打印时的基础样式 */
@media print {
  body {
    margin: 0;
  }

  html,
  body {
    height: auto !important;
    overflow: visible !important;
  }

  .page {
    margin: 0;
    border: none;
    box-shadow: none;
    page-break-after: always;
  }

  .page:last-child {
    page-break-after: auto;
  }

  /* 禁用缩放确保打印正常 */
  .scaled-container {
    transform: none !important;
  }

  /* 隐藏预览浮动工具栏/按钮等 */
  .paged-preview .fixed,
  .paged-preview .absolute {
    display: none !important;
  }

  /* 隐藏所有右下角的工具栏和缩放控制 */
  .fixed.bottom-5.right-5,
  .fixed.bottom-4.right-4,
  .fixed.bottom-6.right-6 {
    display: none !important;
  }

  /* 隐藏具有特定类名的工具栏 */
  .preview-toolbar,
  .toolbar,
  .zoom-controls,
  .paged-preview .toolbar {
    display: none !important;
  }

  .embed-media {
    display: none;
  }

  .qr-frame {
    height: 360px;
    width: 100%;
    display: block;
  }

  .qr-print-link {
    display: flex;
    height: inherit;
    width: 100%;
    justify-content: center;
    align-items: center;
  }

  .qr-print {
    max-width: 100%;
    height: auto;
  }
}

/* 标题样式层 */
@layer components {
  /* 一至六级标题 */
  .page h1 {
    @apply text-3xl font-extrabold leading-tight mb-6 tracking-tight;
  }
  .page h2 {
    @apply text-2xl font-bold leading-tight mb-5;
  }
  .page h3 {
    @apply text-xl font-semibold leading-snug mb-4;
  }
  .page h4 {
    @apply text-lg font-semibold leading-snug mb-3;
  }
  .page h5 {
    @apply text-base font-medium leading-normal mb-3;
  }
  .page h6 {
    @apply text-sm font-medium leading-normal mb-2 uppercase;
  }

  /* Paper Header 区域样式 */
  .page .paper-header {
    @apply mb-8 text-center pb-3;
  }

  /* Paper 标题样式 */
  .page .paper-header h1 {
    @apply text-3xl font-bold leading-tight mb-4 text-gray-900;
  }

  /* 作者列表样式 */
  .page .paper-header .authors {
    @apply text-lg mb-3 text-gray-700;
    font-weight: 500;
  }

  .page .paper-header .authors sup {
    @apply text-sm text-blue-600 font-normal;
  }

  /* 机构列表样式 */
  .page .paper-header .affiliations {
    @apply text-sm mb-4 text-gray-600 italic;
    line-height: 1.4;
  }

  .page .paper-header .affiliations sup {
    @apply text-blue-600 font-medium mr-1;
  }

  /* 摘要样式 */
  .page .paper-header .abstract {
    @apply text-sm mb-3 text-gray-800 text-left;
    line-height: 1.6;
  }

  .page .paper-header .abstract strong {
    @apply font-semibold text-gray-900;
  }

  /* 标签样式 */
  .page .paper-header .tags {
    @apply text-sm text-gray-600 text-left;
  }

  .page .paper-header .tags strong {
    @apply font-semibold text-gray-900;
  }

  /* Section 标题样式（带自动编号） */
  .page h1[id^="sec:"] {
    @apply text-2xl font-bold mt-8 mb-4 text-gray-900 pb-2;
  }

  .page h2[id^="sec:"] {
    @apply text-xl font-semibold mt-6 mb-3 text-gray-800;
  }

  .page h3[id^="sec:"] {
    @apply text-lg font-medium mt-5 mb-3 text-gray-800;
  }

  /* Appendix 标题样式 */
  .page h1[id^="appendix:"] {
    @apply text-xl font-bold mt-8 mb-4 text-gray-900 pb-2;
  }

  /* 图片和图表样式 */
  .page figure {
    @apply my-6 text-center;
    break-inside: avoid;
  }

  .page figure img {
    @apply max-w-full h-auto mx-auto block;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .page figure figcaption,
  .page table figcaption {
    @apply text-sm mt-2 text-gray-700 font-medium;
    line-height: 1.4;
  }

  /* 表格样式 */
  .page table {
    @apply w-full my-6 border-collapse;
    break-inside: avoid;
  }

  .page table th {
    @apply bg-gray-50 font-semibold text-left px-4 py-2 border border-gray-300;
  }

  .page table td {
    @apply px-4 py-2 border border-gray-300;
  }

  .page table tr:nth-child(even) {
    @apply bg-gray-50;
  }

  /* 代码块样式 */
  .page pre[id^="code:"] {
    @apply my-6 p-4 bg-gray-50 border border-gray-200 rounded;
    break-inside: avoid;
  }

  .page pre[id^="code:"] code {
    @apply text-sm font-mono;
  }

  /* 数学公式样式 */
  .page .math {
    @apply my-4;
  }

  .page .math-display {
    @apply text-center;
    break-inside: avoid;
  }

  .page .eq-anchor {
    @apply block h-0 invisible;
  }

  /* 引用链接样式 */
  .page a[href^="#sec:"],
  .page a[href^="#fig:"],
  .page a[href^="#table:"],
  .page a[href^="#code:"],
  .page a[href^="#equation:"],
  .page a[href^="#appendix:"] {
    @apply text-blue-600 hover:text-blue-800 no-underline font-medium;
  }

  /* 参考文献样式 */
  .page .references {
    @apply mt-1;
  }

  .page .references h1 {
    @apply text-xl font-bold mt-8 mb-4 text-gray-900 pb-2;
  }

  /* 通用的References/Bibliography标题样式 */
  .page h1:last-child,
  .page h1 + * + h1:last-of-type {
    @apply text-xl font-bold mt-8 mb-4 text-gray-900 pb-2;
  }

  .page .csl-bib-body {
    @apply space-y-3;
  }

  .page .csl-entry {
    @apply text-sm leading-relaxed text-gray-800 pl-4 mb-0;
    text-indent: -1rem;
  }

  /* 数字引用样式 */
  .page sup a {
    @apply text-blue-600 hover:text-blue-800 no-underline text-xs;
  }

  /* Citation 引用样式 */
  .page span[id^="citation--"] {
    @apply text-gray-600 italic;
    font-size: inherit;
  }

  .page span[id^="citation--"] a {
    @apply text-gray-600 hover:text-gray-800 no-underline italic;
  }
}

/* 页面排版工具层 */
@layer utilities {
  /* A4页面核心样式 */
  .page {
    /* A4 固定尺寸 */
    width: 210mm;
    height: 297mm;
    box-sizing: border-box;
    padding: 15mm;
    position: relative;
    margin: 15mm auto;
    display: flex;
    flex-direction: column;

    /* 背景和视觉效果 */
    background-color: white;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);

    /* 移除宽度限制 */
    max-width: unset;
  }

  /* 页眉页脚样式 */
  .page-header,
  .page-footer {
    break-inside: avoid;
  }

  /* MathJax数学公式样式 */
  .page mjx-container.MathJax:not([display="true"]) {
    display: inline-block !important;
    margin: 0 !important;
  }

  .page mjx-container.MathJax:not([display="true"]) svg {
    display: inline !important;
  }

  /* 页面内通用段落样式 */
  .page p {
    /* 字体和行距 */
    @apply text-base leading-[1.6];

    /* 颜色 */
    color: var(--foreground);

    /* 对齐和字距 */
    @apply tracking-normal break-words;

    /* 垂直间距 */
    @apply mb-4;
  }

  /* 正文段落样式增强 */
  .page p:not([class]) {
    @apply text-justify;
    hyphens: auto;
  }

  /* 段落边距优化 */
  .page p:first-of-type {
    /* 页内首段 */
    @apply mt-0;
  }
  .page p:last-of-type {
    /* 页内末段 */
    @apply mb-0;
  }
  .page p.continued {
    /* 续页开头段落 */
    @apply mt-0;
  }
  .page p.break-after {
    /* 强制在段末分页 */
    @apply mb-0;
  }

  /* 页面测量容器 */
  .page-measure {
    display: flex;
    flex-direction: column; /* 防止段落margin折叠 */
    padding: 0;
    box-sizing: content-box;
    height: auto;
  }

  /* 强调文本样式 */
  .page strong {
    @apply font-semibold;
  }

  .page em {
    @apply italic;
  }

  /* 列表样式 */
  .page ul {
    @apply my-4 pl-6;
  }

  .page ol {
    @apply my-4 pl-6;
  }

  .page li {
    @apply mb-2;
  }

  /* 行内代码样式 */
  .page code:not(pre code) {
    @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono;
  }

  /* 打印时段落优化 */
  @media print {
    .page p {
      orphans: 2;
      widows: 2;
    }

    .page figure,
    .page table,
    .page pre[id^="code:"] {
      break-inside: avoid;
    }

    .page h1,
    .page h2,
    .page h3 {
      break-after: avoid;
    }
  }
}
