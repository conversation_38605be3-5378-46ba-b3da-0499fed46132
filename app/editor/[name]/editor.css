/* app/(site)/editor/[name]/editor.css */

/* ============== Color Theme Variables ============== */
/* Light mode (default) */
:root {
  /* Basic colors - CodeMirror Basic Light style */
  --editor-bg-primary: #ffffff; /* Pure white background */
  --editor-bg-secondary: #f5f5f5; /* Light gray background */
  --editor-bg-tertiary: #eeeeee; /* Medium light gray background */
  --editor-text-primary: #333333; /* Dark gray text */
  --editor-text-secondary: #5c5c5c; /* Medium gray text */
  --editor-text-tertiary: #7e7e7e; /* Light gray text */

  /* Borders */
  --editor-border-color: #ddd; /* Light gray border */

  /* State colors */
  --editor-active-bg: #f0f0f0; /* Light blue selected background */
  --editor-hover-bg: #f0f0f0; /* Light gray hover background */
  --editor-drag-target-bg: #e6f2ff; /* Light blue drag background */

  /* Panel resize handle */
  --editor-resizehandle-bg: #ddd; /* Light gray handle */
  --editor-resizehandle-hover: #bbb; /* Medium gray handle hover */

  /* File tree icon colors */
  --editor-folder-icon: #90a4ae; /* 蓝灰色文件夹 */
  --editor-file-icon: #7e7e7e; /* 中灰文件 */
  --editor-image-icon: #10b981; /* 绿色图片文件 */
  --editor-arrow-icon: #7e7e7e; /* 中灰箭头 */

  /* File status colors */
  --editor-file-dirty: #d32f2f; /* 红色未保存 */
  --editor-file-dirty-dot: #ef5350; /* 浅红圆点 */
  --editor-file-cached: #1976d2; /* 蓝色已缓存 */
  --editor-file-cached-dot: #42a5f5; /* 浅蓝圆点 */
  --editor-file-synced: #388e3c; /* 绿色已同步 */
  --editor-file-synced-dot: #66bb6a; /* 浅绿圆点 */

  /* Menu colors */
  --editor-menu-hover: #f0f0f0; /* 浅灰菜单悬停 */
  --editor-menu-danger: #d32f2f; /* 红色菜单危险项 */

  /* Compile error */
  --editor-error-text: #d32f2f; /* 红色错误文本 */
  --editor-error-bg: #ffebee; /* 极浅红错误背景 */
  --editor-error-border: #ffcdd2; /* 浅红错误边框 */

  --outline-indent: 12px;
  --outline-line-height: 2;
  --outline-font-size: 13px;

  --outline-h1-color: #2b2b2b;
  --outline-h2-color: #444444;
  --outline-h3-color: #666666;
  --outline-h4-color: #888888;
  --outline-h5-color: #aaaaaa;
  --outline-h6-color: #bbbbbb;

  /* CodeMirror 语法高亮 Atom One Pro Light主题 */
  --cm-background: #ffffff;
  --cm-foreground: #383a42;
  --cm-caret: #526fff;
  --cm-caret-width: 2px; /* 光标宽度 */
  --cm-selection: #d0d0e0; /* 选中背景改为浅蓝灰色，增强对比 */
  --cm-selectionMatch: #d0d0e0; /* 匹配选择跟随选中背景 */
  --cm-lineHighlight: rgba(247, 247, 247, 0.6); /* 半透明，避免覆盖选区 */
  --cm-gutterBackground: #ffffff;
  --cm-gutterForeground: #9d9d9f;
  --cm-gutterActiveForeground: #383a42;
  --cm-gutterBorder: transparent;

  /* 语法标记 */
  --cm-keyword: #a626a4;
  --cm-operator: #383a42;
  --cm-specialVariable: #e45649;
  --cm-typeName: #c18401;
  --cm-atom: #0184bc;
  --cm-number: #986801;
  --cm-definition: #4078f2;
  --cm-string: #50a14f;
  --cm-specialString: #50a14f;
  --cm-comment: #a0a1a7;
  --cm-variableName: #383a42;
  --cm-tagName: #e45649;
  --cm-bracket: #383a42;
  --cm-meta: #383a42;
  --cm-attributeName: #986801;
  --cm-propertyName: #4078f2;
  --cm-className: #c18401;
  --cm-invalid: #ff0000;

  /* 额外语法元素 */
  --cm-heading: #e45649;
  --cm-link: #4078f2;
  --cm-url: #4078f2;
  --cm-regexp: #50a14f;
  --cm-processingInstruction: #a626a4;
  --cm-content: #383a42;
  --cm-character: #50a14f;
  --cm-inserted: #50a14f;
  --cm-insertedBackground: #e6ffed;
  --cm-deleted: #e45649;
  --cm-deletedBackground: #ffeef0;
  --cm-changed: #c18401;
  --cm-changedBackground: #fff5b1;
  --cm-punctuation: #383a42;
  --cm-literal: #0184bc;
  --cm-unit: #986801;
  --cm-list: #e45649;
  --cm-quote: #a0a1a7;
  --cm-documentMeta: #a0a1a7;
  --cm-functionName: #4078f2;
  --cm-methodName: #4078f2;
  --cm-controlKeyword: #a626a4;
  --cm-separator: #383a42;
  --cm-colorLiteral: #986801;
  --cm-definitionKeyword: #a626a4;
  --cm-labelName: #e45649;
  --cm-typeDefinition: #c18401;
  --cm-angleBracket: #383a42;
  --cm-brace: #383a42;
  --cm-squareBracket: #383a42;
}

/* 为暗色模式添加变量 */
.dark-theme,
[data-theme="dark"],
.dark,
:root.dark {
  /* 基本颜色 - Atom One Dark主题 */
  --editor-bg-primary: #282c34; /* 主背景色 */
  --editor-bg-secondary: #2c313a; /* 次要背景色 */
  --editor-bg-tertiary: #343a45; /* 第三背景色 */
  --editor-text-primary: #abb2bf; /* 主要文本 */
  --editor-text-secondary: #9da5b4; /* 次要文本 */
  --editor-text-tertiary: #7f848e; /* 第三文本 */

  /* 边框 */
  --editor-border-color: #3e4451; /* 边框颜色 */

  /* 状态颜色 */
  --editor-active-bg: #3e4451; /* 选中背景 */
  --editor-hover-bg: #3a3f4b; /* 悬停背景 */
  --editor-drag-target-bg: #41485b; /* 拖拽目标背景 */

  /* 面板拉伸手柄 */
  --editor-resizehandle-bg: #343a45; /* 面板手柄 */
  --editor-resizehandle-hover: #4b5363; /* 手柄悬停 */

  /* 大纲颜色 - Atom One Dark主题对应的颜色 */
  --outline-h1-color: #abb2bf; /* 一级标题 - 主要文本色 */
  --outline-h2-color: #9da5b4; /* 二级标题 - 次要文本色 */
  --outline-h3-color: #8a8f99; /* 三级标题 - 中等灰色 */
  --outline-h4-color: #7f848e; /* 四级标题 - 第三文本色 */
  --outline-h5-color: #6c7278; /* 五级标题 - 深灰色 */
  --outline-h6-color: #5a5e64; /* 六级标题 - 最深灰色 */

  /* CodeMirror 语法高亮 Atom One Dark主题 */
  --cm-background: #282c34; /* 编辑器背景 */
  --cm-foreground: #abb2bf; /* 编辑器文本 */
  --cm-caret: #528bff; /* 光标颜色 - 蓝色 */
  --cm-caret-width: 2px; /* 光标宽度（暗色） */
  --cm-selection: #3d4966; /* 选中背景改为蓝灰色，增强对比 */
  --cm-selectionMatch: #3d4966; /* 匹配选择跟随选中背景 */
  --cm-lineHighlight: rgba(42, 45, 53, 0.4); /* 半透明，避免覆盖选区 */
  --cm-gutterBackground: #282c34; /* 行号背景 */
  --cm-gutterForeground: #636d83; /* 行号颜色 */
  --cm-gutterActiveForeground: #abb2bf; /* 当前行号 */
  --cm-gutterBorder: transparent; /* 行号边框 */

  /* 语法标记 - Atom One Dark配色 */
  --cm-keyword: #c678dd; /* 关键字 - 紫色 */
  --cm-operator: #56b6c2; /* 操作符 - 青色 */
  --cm-specialVariable: #e06c75; /* 特殊变量 - 红色 */
  --cm-typeName: #e5c07b; /* 类型名 - 黄色 */
  --cm-atom: #d19a66; /* 原子 - 橙色 */
  --cm-number: #d19a66; /* 数字 - 橙色 */
  --cm-definition: #61afef; /* 定义 - 蓝色 */
  --cm-string: #98c379; /* 字符串 - 绿色 */
  --cm-specialString: #98c379; /* 特殊字符串 - 绿色 */
  --cm-comment: #5c6370; /* 注释 - 灰色 */
  --cm-variableName: #abb2bf; /* 变量名 - 浅灰色 */
  --cm-tagName: #e06c75; /* 标签名 - 红色 */
  --cm-bracket: #abb2bf; /* 括号 - 浅灰色 */
  --cm-meta: #61afef; /* 元数据 - 蓝色 */
  --cm-attributeName: #d19a66; /* 属性名 - 橙色 */
  --cm-propertyName: #61afef; /* 属性名 - 蓝色 */
  --cm-className: #e5c07b; /* 类名 - 黄色 */
  --cm-invalid: #e06c75; /* 无效 - 红色 */

  /* 额外语法元素 - Atom One Dark对应的颜色 */
  --cm-heading: #e06c75; /* 标题 - 红色 */
  --cm-link: #61afef; /* 链接 - 蓝色 */
  --cm-url: #56b6c2; /* URL - 青色 */
  --cm-regexp: #98c379; /* 正则 - 绿色 */
  --cm-processingInstruction: #c678dd; /* 处理指令 - 紫色 */
  --cm-content: #abb2bf; /* 内容 - 浅灰色 */
  --cm-character: #98c379; /* 字符 - 绿色 */
  --cm-inserted: #98c379; /* 插入 - 绿色 */
  --cm-insertedBackground: #283c34; /* 插入背景 - 深绿 */
  --cm-deleted: #e06c75; /* 删除 - 红色 */
  --cm-deletedBackground: #3e2c31; /* 删除背景 - 深红 */
  --cm-changed: #e5c07b; /* 更改 - 黄色 */
  --cm-changedBackground: #3a3828; /* 更改背景 - 深黄 */
  --cm-punctuation: #abb2bf; /* 标点 - 浅灰色 */
  --cm-literal: #d19a66; /* 文字 - 橙色 */
  --cm-unit: #d19a66; /* 单位 - 橙色 */
  --cm-list: #e06c75; /* 列表 - 红色 */
  --cm-quote: #5c6370; /* 引用 - 灰色 */
  --cm-documentMeta: #5c6370; /* 文档元数据 - 灰色 */
  --cm-functionName: #61afef; /* 函数名 - 蓝色 */
  --cm-methodName: #61afef; /* 方法名 - 蓝色 */
  --cm-controlKeyword: #c678dd; /* 控制关键字 - 紫色 */
  --cm-separator: #abb2bf; /* 分隔符 - 浅灰色 */
  --cm-colorLiteral: #56b6c2; /* 颜色字面量 - 青色 */
  --cm-definitionKeyword: #c678dd; /* 定义关键字 - 紫色 */
  --cm-labelName: #e06c75; /* 标签名 - 红色 */
  --cm-typeDefinition: #e5c07b; /* 类型定义 - 黄色 */
  --cm-angleBracket: #abb2bf; /* 尖括号 - 浅灰色 */
  --cm-brace: #abb2bf; /* 大括号 - 浅灰色 */
  --cm-squareBracket: #abb2bf; /* 方括号 - 浅灰色 */

  /* Atom One Dark特有颜色变量 */
  --editor-arrow-icon: #5c6370; /* 箭头图标颜色 */
  --editor-folder-icon: #61afef; /* 文件夹图标颜色 */
  --editor-file-icon: #abb2bf; /* 文件图标颜色 */
  --editor-image-icon: #56b6c2; /* 青色图片文件 */
}

/* ------- 全局高度锁定，避免页面本身滚动 ------- */
html,
body {
  height: 100%;
  overflow: hidden; /* 让滚动发生在各 Panel 内部 */
}

/* ------- CodeMirror 容器及滚动 ------- */
.code-panel {
  height: 100%;
}

/* 编辑器顶栏专用样式 */
.editor-header {
  background-color: var(--editor-bg-primary);
  border-bottom-color: var(--editor-border-color);
  color: var(--editor-text-primary);
}

.cm-theme {
  height: 100%;
}

.code-panel .cm-editor {
  height: 100%;
  min-height: 0; /* 关键：允许缩回父容器 */
}

.code-panel .cm-editor {
  display: flex;
  flex-direction: column;
  min-height: 0; /* 再保险一次 */
}

.code-panel .cm-scroller {
  flex: 1 1 auto;
  overflow: auto;
  overscroll-behavior: contain;
  min-height: 0; /* 没有就加上 */
}

/* ------- File tree / Editor / Preview 各自滚动 ------- */
/* file tree 默认已 overflow-y:auto；如有需要可加： */
aside {
  overflow-y: auto;
}

/* Preview Panel：阻止滚动链到页面 */
.preview-panel {
  overscroll-behavior: contain;
}

/* PanelResizeHandle 提供更明显可拖拽手柄（可选美化） */
.react-resizable-panels-PanelResizeHandle {
  background: var(--editor-resizehandle-bg);
  transition: background-color 0.2s;
}
.react-resizable-panels-PanelResizeHandle:hover {
  background: var(--editor-resizehandle-hover);
}

/* 暗色模式下的滚动条样式优化 */
.dark ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.dark ::-webkit-scrollbar-track {
  background: #2d2a2e;
}

.dark ::-webkit-scrollbar-thumb {
  background: #5b595c;
  border-radius: 5px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #727072;
}

/* ------- 文件树自定义颜色 ------- */
.filetree-panel {
  background-color: var(--editor-bg-primary);
  color: var(--editor-text-primary);
  height: 100%; /* 确保填满父容器高度 */
  display: flex;
  flex-direction: column;
}

/* 文件树内容区域 */
.filetree-panel .file-tree-content {
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* 关键：让flex子项能够收缩 */
}

/* 文件树项间距 */
.filetree-panel ul {
  padding: 0;
  margin: 0;
}

.filetree-panel li {
  margin-bottom: 2px; /* 列表项之间的垂直间距 */
}

.filetree-panel li:last-child {
  margin-bottom: 0; /* 最后一个项目不需要底部间距 */
}

/* 文件/文件夹项样式 */
.filetree-panel .item-content {
  padding: 3px 0; /* 增加每项内部的上下内边距 */
  border-radius: 4px; /* 圆角效果 */
  transition: background-color 0.1s ease; /* 平滑过渡效果 */
}

/* 文件树项状态样式 */
.filetree-panel .active-item {
  background-color: var(--editor-active-bg);
}

.filetree-panel .hover-item:hover {
  background-color: var(--editor-hover-bg);
}

.filetree-panel .drag-target {
  background-color: var(--editor-drag-target-bg);
}

/* 文件状态颜色类 */
.file-status-dirty {
  color: var(--editor-file-dirty);
  font-weight: 300;
}
.file-status-cached {
  color: var(--editor-file-cached);
  font-weight: 300;
}
.file-status-synced {
  color: var(--editor-file-synced);
  font-weight: 300;
}

/* 状态点样式 */
.status-dot-dirty {
  background-color: var(--editor-file-dirty-dot);
}
.status-dot-cached {
  background-color: var(--editor-file-cached-dot);
}
.status-dot-synced {
  background-color: var(--editor-file-synced-dot);
}

/* ------- 右键菜单样式 ------- */
.context-menu {
  background-color: var(--editor-bg-primary);
  border: 1px solid var(--editor-border-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.context-menu-item {
  color: var(--editor-text-primary);
}

.context-menu-item:hover {
  background-color: var(--editor-menu-hover);
}

.context-menu-item-danger {
  color: var(--editor-menu-danger);
}

/* ------- 编译错误样式 ------- */
.compile-error {
  color: var(--editor-error-text);
}

.compile-error-container {
  background-color: var(--editor-error-bg);
  border: 1px solid var(--editor-error-border);
}

/* ------- Outline Panel 样式 ------- */
.outline-panel {
  background-color: var(--editor-bg-primary);
  color: var(--editor-text-primary);
  overflow: hidden; /* 防止内容溢出 */
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.outline-row {
  line-height: var(--outline-line-height);
  font-size: var(--outline-font-size);
  padding-right: 4px;
  cursor: pointer;
  user-select: none;
  position: relative;
  padding-top: 1px;
  padding-bottom: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.outline-row:hover {
  background-color: var(--editor-hover-bg);
}

.outline-row.active {
  background-color: var(--editor-active-bg);
  color: var(--editor-file-cached);
}

/* 不同层级字体粗细/颜色 */
.outline-level-1 {
  font-weight: 600;
  color: var(--outline-h1-color);
}
.outline-level-2 {
  font-weight: 500;
  color: var(--outline-h2-color);
}
.outline-level-3 {
  font-weight: 400;
  color: var(--outline-h3-color);
}
.outline-level-4 {
  font-weight: 400;
  color: var(--outline-h4-color);
}
.outline-level-5 {
  font-weight: 400;
  color: var(--outline-h5-color);
}
.outline-level-6 {
  font-weight: 400;
  color: var(--outline-h6-color);
}

/* 标题前圆点 */
.outline-dot {
  display: inline-block;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  margin-right: 6px;
  vertical-align: middle;
  position: relative;
}

/* 不同级别圆点颜色 */
.outline-dot.level-1 {
  background-color: var(--outline-h1-color);
}
.outline-dot.level-2 {
  background-color: var(--outline-h2-color);
}
.outline-dot.level-3 {
  background-color: var(--outline-h3-color);
}
.outline-dot.level-4 {
  background-color: var(--outline-h4-color);
}
.outline-dot.level-5 {
  background-color: var(--outline-h5-color);
}
.outline-dot.level-6 {
  background-color: var(--outline-h6-color);
}

/* 竖线 */
.outline-row::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1.5px;
  background-color: var(--editor-border-color);
  opacity: 0.4;
}

/* 各层级左边距及竖线位置 */
.outline-level-1::before {
  left: calc(var(--outline-indent, 12px) * 1);
}
.outline-level-2::before {
  left: calc(var(--outline-indent, 12px) * 2);
}
.outline-level-3::before {
  left: calc(var(--outline-indent, 12px) * 3);
}
.outline-level-4::before {
  left: calc(var(--outline-indent, 12px) * 4);
}
.outline-level-5::before {
  left: calc(var(--outline-indent, 12px) * 5);
}

/* 目前显示标题的层级竖线不显示，只显示父级竖线 */
.outline-level-1::before {
  display: none;
}
.outline-level-2.active::before,
.outline-level-2:hover::before {
  opacity: 0;
}
.outline-level-3.active::before,
.outline-level-3:hover::before {
  opacity: 0;
}
.outline-level-4.active::before,
.outline-level-4:hover::before {
  opacity: 0;
}
.outline-level-5.active::before,
.outline-level-5:hover::before {
  opacity: 0;
}
.outline-level-6.active::before,
.outline-level-6:hover::before {
  opacity: 0;
}

/* 编辑器主面板样式 */
.editor-main-panel {
  background-color: var(--editor-bg-primary);
  color: var(--editor-text-primary);
}

/* 按钮禁用状态样式 */
.dark button:disabled {
  opacity: 0.6;
  color: #727072;
}

.dark button:disabled svg {
  color: #727072;
}

/* ======================  修复 CodeMirror 首行选区高亮消失  ===================== */
/* 选区背景需要位于活动行 (.cm-activeLine) 之上，否则首行会被其覆盖 */
.cm-editor .cm-selectionBackground,
.cm-editor .cm-selectionLayer .cm-selectionBackground {
  background-color: var(--cm-selection, #d0d0e0) !important;
  z-index: 50; /* 高于 .cm-activeLine 的默认层级 (0) */
}

.cm-editor.cm-theme-dark .cm-selectionBackground,
.cm-editor.cm-theme-dark .cm-selectionLayer .cm-selectionBackground {
  background-color: var(--cm-selection, #3d4966) !important;
  z-index: 50;
}

/* 将活动行背景层级调低，避免覆盖选区 */
.cm-editor .cm-activeLine {
  position: relative;
  z-index: 0;
}
.cm-editor .cm-cursor,
.cm-editor .cm-dropCursor {
  border-left: var(--cm-caret-width, 2px) solid var(--cm-caret, #526fff) !important;
  /* 避免浏览器对高对比度主题自动加 invert 滤镜 */
  filter: none !important;
}

/* ======================  定制 CodeMirror 光标宽度与颜色  ===================== */
/* 通过变量 --cm-caret (颜色) 与 --cm-caret-width (宽度) 统一控制 */

/* ======================  折叠面板标签样式  ===================== */
.collapsed-panel-handle {
  opacity: 0;
  animation: fadeIn 0.3s ease-out 0.5s forwards;
}

.collapsed-panel-handle:hover {
  background: var(--editor-hover-bg) !important;
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.collapsed-panel-handle.dragging {
  transform: translateY(-50%) scale(0.95);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.collapsed-panel-handle .handle-content {
  transition: all 0.2s ease;
}

.collapsed-panel-handle:hover .handle-content {
  color: var(--editor-text-primary);
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}

/* 手机端优化 */
@media (max-width: 768px) {
  .collapsed-panel-handle {
    padding: 16px 12px;
    min-height: 60px;
    min-width: 32px;
  }

  .collapsed-panel-handle .handle-content svg {
    width: 20px;
    height: 20px;
  }
}

/* 暗色主题适配 */
.dark .collapsed-panel-handle {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.dark .collapsed-panel-handle:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
}

.dark .collapsed-panel-handle.dragging {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.6);
}
