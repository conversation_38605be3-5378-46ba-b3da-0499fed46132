"use client";
import { get, set } from "idb-keyval";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState, useCallback, useRef } from "react";
import {
  PanelGroup,
  Panel,
  PanelResizeHandle,
  ImperativePanelHandle,
} from "react-resizable-panels";

// 导入自定义类型

// 导入自定义钩子
import { ContextMenu } from "./components/ContextMenu";
import { EditorPanel } from "./components/EditorPanel";
import { FileTreePanel } from "./components/FileTreePanel";
import { OutlinePanel } from "./components/OutlinePanel";
import { PreviewPanel } from "./components/PreviewPanel";
import { CollapsedPanelHandle } from "./components/CollapsedPanelHandle";
import { useFileOperations } from "./hooks/useFileOperations";
import { useIframeMessaging } from "./hooks/useIframeMessaging";
import { useEditorGlobalSetup } from "./hooks/useEditorGlobalSetup";
import { useAutoOpenFile } from "./hooks/useAutoOpenFile";
import { useAutoCompile } from "./hooks/useAutoCompile";
import { useNotifications } from "./hooks/useNotifications";

// 导入子组件
import { useOfflineBuffer } from "./hooks/useOfflineBuffer";
import { NotificationDialog } from "./components/NotificationDialog";
import { TreeNode, MenuConfig, ProcessingState } from "./types";
import { hasPaper } from "./utils/treeUtils";

import { Toaster } from "@/components/ui/sonner";
import { Progress } from "@/components/ui/progress";

// 导入样式
import "./editor.css";

/* ====================================================================== */
export default function ProjectEditor() {
  /* ---------- 加载 CodeMirror 桥接脚本 ---------- */
  useEffect(() => {
    const scriptSrc = '/codemirror-iframe-bridge.js';

    // 检查脚本是否已经加载
    if (document.querySelector(`script[src="${scriptSrc}"]`)) {
      return;
    }

    // 动态加载桥接脚本
    const script = document.createElement('script');
    script.src = scriptSrc;
    script.async = true;
    script.onload = () => {
      // 如果编辑器已经存在，立即触发检查
      const { editorView, codeMirrorBridge } = window as any;
      if (editorView && codeMirrorBridge?.checkForEditor) {
        setTimeout(() => codeMirrorBridge.checkForEditor(), 100);
      }
    };

    document.head.appendChild(script);

    // 清理函数
    return () => {
      const existingScript = document.querySelector(`script[src="${scriptSrc}"]`);
      existingScript?.remove();
    };
  }, []);

  const { name } = useParams<{ name: string }>();
  const projectName = name as string;

  /* ------- iframe ref ------- */
  const iframeRef = useRef<HTMLIFrameElement | null>(null);

  /* ------- 面板引用和折叠状态 ------- */
  const fileTreePanelRef = useRef<ImperativePanelHandle>(null);
  const previewPanelRef = useRef<ImperativePanelHandle>(null);
  const [isFileTreeCollapsed, setIsFileTreeCollapsed] = useState(false);
  const [isPreviewCollapsed, setIsPreviewCollapsed] = useState(false);

  /* ---------- 离线缓冲 ---------- */
  // 初始化时使用空集合，稍后会在树加载后折叠所有文件夹
  const [collapsed, setCollapsed] = useState<Set<string>>(new Set());
  const {
    cache,
    load,
    markSynced,
    markDirty,
    flush,
    getStats,
    getDirtyFiles,
    hasDirtyFiles,
    fileStatuses,
  } = useOfflineBuffer(projectName);

  /* ---------- 文件状态 ---------- */
  const [tree, setTree] = useState<TreeNode[]>([]);
  const [activePath, setPath] = useState<string | null>(null);
  const [content, setContent] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [dirty, setDirty] = useState(false);
  const [fontSize, setFontSize] = useState(14);
  const contentRef = useRef("");
  const editorViewRef = useRef<any>(null);

  /* ---------- 预览状态 ---------- */
  const [html, setHtml] = useState("");
  const [compileErr, setErr] = useState<string | null>(null);
  const [autoCompiled, setAutoCompiled] = useState(false);
  const [compileInProgress, setCompileInProgress] = useState(false); // 编译锁定状态，防止重复编译

  /* ---------- 处理状态 ---------- */
  const [saving, setSaving] = useState<ProcessingState>({ isProcessing: false });
  const [compiling, setCompiling] = useState<ProcessingState>({ isProcessing: false });
  const [loadingImage, setLoadingImage] = useState(false);

  /* ---------- 在线状态 ---------- */
  const [isOnline, setIsOnline] = useState<boolean | null>(null);
  const [mounted, setMounted] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  /* ---------- 上传状态 ---------- */
  const [uploading, setUploading] = useState(false);
  const [uploadFileName, setUFName] = useState("");
  const [uploadPct, setUploadPct] = useState(0);

  /* ---------- 右键菜单 ---------- */
  const [menu, setMenu] = useState<MenuConfig | null>(null);
  const [clipboard, setClipboard] = useState<string | null>(null);

  /* ---------- iframe通信 ---------- */
  const { sendHtml, resetScale, getPagedHtml } = useIframeMessaging({ debug: false });

  /* ---------- 编辑器全局设置 ---------- */
  const { setupGlobalEditor } = useEditorGlobalSetup();

  /* ---------- 通知系统 ---------- */
  const { notification, showAlert, showConfirm, showPrompt, closeNotification, handleConfirm, handleCancel } = useNotifications();

  /* ---------- 文件操作 ---------- */
  const setActivePathIfNeeded = useCallback(
    (oldPath: string, newPath: string) => {
      if (activePath === oldPath) {
        setPath(newPath);
      }
    },
    [activePath]
  );

  /* 在 refreshTree 或第一次 setTree 时，外层包一层根节点 */
  const normalizeTree = useCallback(
    (nodes: TreeNode[]): TreeNode => ({
      name: decodeURIComponent(projectName),
      path: "",
      isDir: true,
      children: nodes,
    }),
    [projectName]
  );

  /* 将后端返回的 nodes 包一层根节点并写入 state */
  const applyTree = useCallback(
    (nodes: TreeNode[]) => {
      setTree([normalizeTree(nodes)]);
    },
    [normalizeTree]
  );

  /* 刷新文件树 */
  const refreshTree = useCallback(async () => {
    try {
      const res = await fetch(`/api/editor/projects/${encodeURIComponent(projectName)}/tree`);
      if (!res.ok) {
        throw new Error(`获取文件树失败: ${res.status}`);
      }
      const json = await res.json();
      if (Array.isArray(json)) applyTree(json);
    } catch (error) {
      throw error;
    }
  }, [projectName, applyTree]);

  /* ---------- 文件操作 ---------- */
  const { renameItem, deleteItem, copyItem, downloadItem, createFile, createDirectory } =
    useFileOperations({
      projectName,
      refreshTree,
      setActivePathIfNeeded,
    });

  /* ---------- 初始化在线状态监听 ---------- */
  useEffect(() => {
    setIsOnline(navigator.onLine);
    setMounted(true);
    const onOnline = () => setIsOnline(true);
    const onOffline = () => setIsOnline(false);
    window.addEventListener("online", onOnline);
    window.addEventListener("offline", onOffline);
    return () => {
      window.removeEventListener("online", onOnline);
      window.removeEventListener("offline", onOffline);
    };
  }, []);

  /* ---------- 加载状态 ---------- */
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [loadingPct, setLoadPct] = useState(0);
  const updatePct = (pct: number) => setLoadPct(v => Math.min(100, Math.max(v, pct)));

  /* ---------- 暗色模式状态 ---------- */
  const [isDarkMode, setIsDarkMode] = useState(false);

  /* ---------- 切换暗色模式 ---------- */
  const toggleDarkMode = useCallback(() => {
    setIsDarkMode(prev => !prev);
    // 根据isDarkMode更新根元素的暗色模式类
    if (typeof document !== "undefined") {
      document.documentElement.classList.toggle("dark", !isDarkMode);
    }
  }, [isDarkMode]);

  /* ---------- 获取文件树 ---------- */
  useEffect(() => {
    (async () => {
      updatePct(10);
      const cacheKey = `TREE|${projectName}`;
      if (!navigator.onLine) {
        const cached = await get(cacheKey);
        if (cached && (cached as any[]).length) {
          setTree(cached as TreeNode[]);
          updatePct(100);
          setTimeout(() => setLoading(false), 200);
          return;
        }
        // 离线且无缓存 → 返回 submit
        router.push("/submit");
        return;
      }
      try {
        const res = await fetch(`/api/editor/projects/${encodeURIComponent(projectName)}/tree`);
        if (!res.ok) {
          router.push("/submit");
          return;
        }
        const json = await res.json();
        if (!Array.isArray(json) || json.length === 0) {
          router.push("/submit");
          return;
        }
        // 设置文件树
        setTree([normalizeTree(json)]);
        await set(cacheKey, json);

        // 收集所有文件夹路径并折叠它们
        const foldersToCollapse = new Set<string>();
        const collectFolders = (nodes: TreeNode[]) => {
          for (const node of nodes) {
            if (node.isDir) {
              foldersToCollapse.add(node.path);
              if (node.children?.length) {
                collectFolders(node.children);
              }
            }
          }
        };

        if (json.length > 0) {
          collectFolders(json);
          setCollapsed(foldersToCollapse);
        }

        updatePct(100);
        setTimeout(() => setLoading(false), 200);
      } catch (error) {
        router.push("/submit");
      }
    })();
  }, [projectName]);

  /* ---------- 处理编辑器内容修改 ---------- */
  const handleContentChange = useCallback(
    (value: string) => {
      // 确保ref和state同步
      contentRef.current = value;
      setContent(value);

      // 如果内容不同，标记为dirty
      if (!dirty) {
        setDirty(true);
        if (activePath) {
          // 标记为脏文件 (红色状态)
          markDirty(activePath);
        }
      }
    },
    [activePath, dirty, markDirty]
  );

  // 同步dirty状态和文件状态
  useEffect(() => {
    if (activePath && fileStatuses.has(activePath)) {
      const fileStatus = fileStatuses.get(activePath)!;

      // 如果文件状态变为非脏，更新编辑器状态
      if (!fileStatus.dirty && dirty) {
        setDirty(false);
      }

      // 如果文件已保存，更新最后保存时间
      if (fileStatus.cached && fileStatus.synced && !lastSaved) {
        setLastSaved(new Date(fileStatus.lastModified));
      }
    }
  }, [activePath, fileStatuses, dirty, lastSaved]);

  /* ---------- 保存当前文件逻辑 ---------- */
  const persist = useCallback(
    async (why: string, localOnly: boolean = false, retryCount = 0) => {
      if (!activePath) return;

      setSaving({ isProcessing: true });
      try {
        const latestText = contentRef.current;

        // 先缓存到本地 (蓝色状态)
        const saveTime = await cache(activePath, latestText);

        // 如果localOnly=true，只保存到本地，不上传服务器
        if (localOnly) {
          setSaving({ isProcessing: false, status: "success" });
          return;
        }

        // 否则，继续上传到服务器
        if (navigator.onLine) {
          try {
            // 设置超时控制
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

            const res = await fetch(
              `/api/editor/projects/${encodeURIComponent(projectName)}/file`,
              {
                method: "PUT",
                headers: {
                  "Content-Type": "application/json",
                  "Cache-Control": "no-cache, no-store, must-revalidate",
                  Pragma: "no-cache",
                  Expires: "0",
                },
                signal: controller.signal,
                body: JSON.stringify({
                  path: activePath,
                  content: latestText,
                  saveTime,
                  forceUpdate: true,
                  timestamp: Date.now(),
                }),
              }
            );

            clearTimeout(timeoutId); // 清除超时

            if (!res.ok) {
              throw new Error((await res.text()) || `保存失败: ${res.status}`);
            }

            // 标记为已同步 (绿色状态)
            await markSynced(activePath);

            // 成功保存 - 更新状态
            setDirty(false);
            setLastSaved(new Date());
            setSaving({ isProcessing: false, status: "success", message: "已保存到服务器" });

            // 保存成功后刷新文件树，确保新增的带时间戳文件名能够显示
            refreshTree();
            return true; // 表示保存成功
          } catch (e) {
            // 尝试重试 (最多3次)
            if (retryCount < 2) {
              setSaving({
                isProcessing: true,
                status: "warning",
                message: `保存重试中 (${retryCount + 1}/3)`,
              });

              // 延迟后重试
              await new Promise(resolve => setTimeout(resolve, 2000));
              return persist(why, localOnly, retryCount + 1);
            } else {
              // 重试失败
              throw new Error(e instanceof Error ? e.message : "保存到服务器失败，请稍后再试");
            }
          }
        } else {
          // 离线状态 - 只保存到本地
          setSaving({
            isProcessing: false,
            status: "warning",
            message: "已缓存，但设备离线无法同步到服务器",
          });
          setDirty(false);
          setLastSaved(new Date());
        }
      } catch (error) {
        setSaving({
          isProcessing: false,
          status: "error",
          message: error instanceof Error ? error.message : String(error),
        });
        return false; // 表示保存失败
      }
    },
    [activePath, projectName, cache, markSynced]
  );

  /* ---------- 打开文件 ---------- */
  const IMG_RE = /\.(png|jpe?g|gif|webp)$/i;

  const openFile = useCallback(
    async (node: TreeNode) => {
      if (node.isDir) return;

      /* --- 如果点击当前打开的文件，则不重新加载 --- */
      if (activePath === node.path) {
        // 如果文件已修改，则保存到本地缓存
        if (dirty) {
          await persist("self-click", true);
        }
        return; // 直接返回，不重新加载文件
      }

      /* --- 切换前保存当前文件到本地缓存 --- */
      if (activePath && dirty) {
        // 跳转文件时只保存到本地缓存，不上传服务器
        await persist("navigation", true);
      }

      /* ================= 图片文件 ================= */
      if (IMG_RE.test(node.path)) {
        setLoadingImage(true);
        setImageUrl("");
        setContent("");
        setDirty(false);
        setPath(node.path);

        try {
          if (!navigator.onLine) {
            setLoadingImage(false);
            showAlert("Cannot preview images while offline", "Network Error", "warning");
            return;
          }

          const res = await fetch(
            `/api/editor/projects/${encodeURIComponent(projectName)}/download-tree` +
            `?path=${encodeURIComponent(node.path)}`
          );
          if (!res.ok) throw new Error(await res.text());

          const blob = await res.blob();
          const url = URL.createObjectURL(blob);

          setImageUrl(old => {
            if (old) URL.revokeObjectURL(old);
            return url;
          });
          setLoadingImage(false);
        } catch (e) {
          setLoadingImage(false);
          showAlert("Image preview failed", "Preview Error", "destructive");
        }
        return;
      }

      /* ================ 文本文件 ================ */
      setImageUrl("");
      setLoadingImage(false);

      try {
        // 1) 先看本地缓存
        const cached = await load(node.path, true);

        if (cached !== null) {
          setContent(cached);
          contentRef.current = cached;
          setPath(node.path);

          // 查询该文件是否已被标记为脏
          const isDirty = fileStatuses.get(node.path)?.dirty || false;
          setDirty(isDirty);

          // 如果在线状态，后台异步检查文件是否有更新
          if (navigator.onLine) {
            const cachedContent = cached;

            fetch(
              `/api/editor/projects/${encodeURIComponent(projectName)}/file?path=${encodeURIComponent(node.path)}`
            )
              .then(res => {
                if (!res.ok) throw new Error(`获取文件失败: ${res.status}`);
                return res.text();
              })
              .then(async serverText => {
                // 只在内容不同时处理
                if (serverText !== cachedContent && activePath === node.path) {
                  // 检查服务器内容与缓存相比哪个更新
                  const currentCache: any = await get(`${projectName}|${node.path}`);
                  const cacheTime = currentCache?.ts || 0;
                  const dirty = contentRef.current !== cachedContent; // 检查用户是否已修改内容

                  // 如果本地缓存的最后修改时间戳在2秒内，或者用户已修改内容，则认为本地修改更新
                  const localIsNewer = Date.now() - cacheTime < 2000 || dirty;

                  // 如果本地更新或者用户正在编辑，保留本地版本
                  if (!localIsNewer) {
                    setContent(serverText);
                    contentRef.current = serverText;
                    await set(`${projectName}|${node.path}`, {
                      content: serverText,
                      synced: true,
                      ts: Date.now(),
                    });
                  }
                }
              })
              .catch(() => {
                // 静默处理后台刷新错误
              });
          }
          return;
        }

        // 2) 离线且无缓存
        if (!navigator.onLine) {
          showAlert("Offline with no cache", "Network Error", "warning");
          return;
        }

        // 3) 在线拉取
        const res = await fetch(
          `/api/editor/projects/${encodeURIComponent(projectName)}/file?path=${encodeURIComponent(node.path)}`
        );

        if (!res.ok) {
          throw new Error((await res.text()) || `Failed to get file: ${res.status}`);
        }

        const txt = await res.text();
        setContent(txt);
        contentRef.current = txt;
        setPath(node.path);
        setDirty(false);

        // 4) 写入缓存标记已同步
        await set(`${projectName}|${node.path}`, {
          content: txt,
          synced: true,
          ts: Date.now(),
        });
      } catch (error) {
        showAlert(`Failed to open file: ${error instanceof Error ? error.message : String(error)}`, "File Error", "destructive");
      }
    },
    [projectName, load, set, dirty, activePath, persist, fileStatuses]
  );

  /* ---------- 全局保存功能 (保存所有有修改的文件) ---------- */
  const persistAll = useCallback(
    async (why: string, localOnly: boolean = false): Promise<boolean> => {
      setSaving({ isProcessing: true });

      try {
        // 1. 收集所有需要保存的文件
        const filesToSync = new Set<string>();

        // a) 当前活动文件一定要保存
        if (activePath) filesToSync.add(activePath);

        // b) 其他所有dirty(红色)或cached-but-unsynced(蓝色)文件
        fileStatuses.forEach(status => {
          if (status.dirty || (status.cached && !status.synced)) {
            filesToSync.add(status.path);
          }
        });

        if (filesToSync.size === 0) {
          setSaving({ isProcessing: false, status: "success", message: "没有需要保存的文件" });
          return true;
        }

        // 2. 先保存当前活动文件
        if (activePath && filesToSync.has(activePath)) {
          const saveSuccess = await persist(`${why}-active`, localOnly);

          if (saveSuccess === false) {
            throw new Error(`保存当前文件 ${activePath} 失败`);
          }

          // 从列表中移除已保存的文件
          filesToSync.delete(activePath);
        }

        // 3. 保存其他文件
        for (const path of filesToSync) {
          try {
            // 获取文件内容
            const content =
              localStorage.getItem(`current-content:${path}`) || (await load(path, true)) || "";

            // 先缓存到本地
            const saveTime = await cache(path, content);

            // 如果需要同步到服务器
            if (!localOnly && navigator.onLine) {
              // 设置超时控制
              const controller = new AbortController();
              const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

              const res = await fetch(
                `/api/editor/projects/${encodeURIComponent(projectName)}/file`,
                {
                  method: "PUT",
                  headers: {
                    "Content-Type": "application/json",
                    "Cache-Control": "no-cache, no-store, must-revalidate",
                    Pragma: "no-cache",
                    Expires: "0",
                  },
                  signal: controller.signal,
                  body: JSON.stringify({
                    path,
                    content,
                    saveTime,
                    forceUpdate: true,
                    timestamp: Date.now(),
                  }),
                }
              );

              clearTimeout(timeoutId);

              if (!res.ok) {
                throw new Error(`上传失败: ${path} ${res.status}`);
              }

              // 标记为已同步
              await markSynced(path);
            }
          } catch (e) {
            // 继续保存其他文件，而不中断整个过程
          }
        }

        // 4. 刷新文件树
        refreshTree();

        // 5. 更新状态
        setDirty(false);
        setLastSaved(new Date());
        setSaving({ isProcessing: false, status: "success", message: "所有文件已保存" });

        return true;
      } catch (error) {
        setSaving({
          isProcessing: false,
          status: "error",
          message: error instanceof Error ? error.message : "保存失败",
        });
        return false;
      }
    },
    [projectName, activePath, persist, fileStatuses, cache, load, markSynced, refreshTree]
  );

  /* ---------- 保存文件 ---------- */
  const saveFile = useCallback(async () => {
    if (!activePath) return;
    await persist("manual");
  }, [persist, activePath]);

  /* ---------- 自动保存 ---------- */
  useEffect(() => {
    if (!activePath) return;

    // 设置自动保存间隔
    const id = setInterval(() => {
      if (dirty) {
        // 自动保存只保存至本地缓存，不上传服务器
        persist("interval", true);
      }
    }, 15000);

    return () => {
      clearInterval(id);
    };
  }, [dirty, persist, activePath]);

  /* ---------- 页面隐藏/关闭保存 ---------- */
  useEffect(() => {
    const handler = () => {
      if (dirty && activePath) {
        persist("visibilitychange", true); // 添加localOnly=true参数，只保存到本地
      }
    };
    document.addEventListener("visibilitychange", handler);
    window.addEventListener("beforeunload", handler);
    return () => {
      document.removeEventListener("visibilitychange", handler);
      window.removeEventListener("beforeunload", handler);
    };
  }, [dirty, persist, activePath]);

  /* ---------- 联网后同步缓存 ---------- */
  useEffect(() => {
    const syncAll = async () => {
      if (!navigator.onLine) return;

      try {
        const unsyncedFiles = Array.from(fileStatuses.values()).filter(
          s => s.cached && !s.synced && !s.dirty
        );

        if (unsyncedFiles.length > 0) {
          setCachedFilesCount(unsyncedFiles.length);
        }
      } catch (error) {
        // 静默处理错误
      }
    };

    syncAll();
    window.addEventListener("online", syncAll);
    return () => window.removeEventListener("online", syncAll);
  }, [fileStatuses]);

  /* ---------- 编译功能 ---------- */
  const compile = useCallback(
    async (retryCount = 0) => {
      // 如果已经有编译进程在运行，直接返回以避免重复编译
      if (compileInProgress) {
        return;
      }

      if (!hasPaper(tree)) {
        showAlert("Missing paper.md file, cannot compile.", "Compile Error", "warning");
        return;
      }

      // 设置编译锁定状态
      setCompileInProgress(true);
      updatePct(80);
      setCompiling({ isProcessing: true });
      setErr(null);

      try {
        /* ---------- 0. 先保存项目中所有修改过的文件 ---------- */
        // 使用全局保存功能，保存当前项目中的所有文件
        const saveSuccess = await persistAll("compile-all");

        if (saveSuccess === false) {
          throw new Error("Error saving files, compilation aborted. Please ensure files can be saved normally.");
        }

        // 如果还有未保存的文件，重试
        if (retryCount < 2 && hasDirtyFiles()) {
          setCompiling({
            isProcessing: true,
            status: "warning",
            message: `Retrying compilation (${retryCount + 1}/3)`,
          });

          await new Promise(resolve => setTimeout(resolve, 2000));
          return compile(retryCount + 1);
        }

        /* ---------- 等待所有文件保存完成后，给服务器一些缓冲时间 ---------- */
        await new Promise(res => setTimeout(res, 1000));

        /* ---------- 4. 调用编译 API ---------- */

        // 设置超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

        try {
          const compileRes = await fetch(
            `/api/editor/projects/${encodeURIComponent(projectName)}/compile`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "Cache-Control": "no-cache, no-store, must-revalidate",
                Pragma: "no-cache",
                Expires: "0",
              },
              signal: controller.signal,
              body: JSON.stringify({
                entry: "paper.md",
                timestamp: Date.now(),
                bypassCache: true, // 告诉后端绕过缓存
              }),
            }
          );

          clearTimeout(timeoutId);

          if (!compileRes.ok) {
            throw new Error((await compileRes.text()) || `编译失败: ${compileRes.status}`);
          }

          const html = await compileRes.text();
          setHtml(html);

          if (iframeRef.current) {
            await sendHtml(iframeRef.current, html);
          }

          // 更新本地状态
          if (activePath) {
            setDirty(false);
            setLastSaved(new Date());
          }

          setCompiling({ isProcessing: false, status: "success", message: "编译成功" });
        } catch (error: any) {
          clearTimeout(timeoutId);

          if (error?.name === "AbortError") {
            throw new Error("Compilation timeout, please try again later");
          }
          throw error;
        }
      } catch (error) {
        setErr(error instanceof Error ? error.message : "未知错误");
        setCompiling({
          isProcessing: false,
          status: "error",
          message: error instanceof Error ? error.message : "编译出错",
        });
      } finally {
        // 无论编译成功还是失败，都释放锁定状态
        setCompileInProgress(false);
        updatePct(0);
      }
    },
    [
      projectName,
      tree,
      activePath,
      contentRef,
      fileStatuses,
      cache,
      load,
      markSynced,
      persist,
      setDirty,
      setLastSaved,
      sendHtml,
      setHtml,
      hasPaper,
      compileInProgress,
      persistAll,
      hasDirtyFiles,
    ]
  );

  /* ---------- 仅编译函数（不包含保存，用于提交流程） ---------- */
  const compileWithoutSave = useCallback(
    async () => {
      // 如果已经有编译进程在运行，直接返回以避免重复编译
      if (compileInProgress) {
        return;
      }

      if (!hasPaper(tree)) {
        showAlert("Missing paper.md file, cannot compile.", "Compile Error", "warning");
        return;
      }

      // 设置编译锁定状态
      setCompileInProgress(true);
      updatePct(80);
      setCompiling({ isProcessing: true });
      setErr(null);

      try {
        /* ---------- 调用编译 API ---------- */
        // 设置超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

        try {
          const compileRes = await fetch(
            `/api/editor/projects/${encodeURIComponent(projectName)}/compile`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "Cache-Control": "no-cache, no-store, must-revalidate",
                Pragma: "no-cache",
                Expires: "0",
              },
              signal: controller.signal,
              body: JSON.stringify({
                entry: "paper.md",
                timestamp: Date.now(),
                bypassCache: true, // 告诉后端绕过缓存
              }),
            }
          );

          clearTimeout(timeoutId);

          if (!compileRes.ok) {
            throw new Error((await compileRes.text()) || `编译失败: ${compileRes.status}`);
          }

          const html = await compileRes.text();
          setHtml(html);

          if (iframeRef.current) {
            await sendHtml(iframeRef.current, html);
          }

          // 更新本地状态
          if (activePath) {
            setDirty(false);
            setLastSaved(new Date());
          }

          setCompiling({ isProcessing: false, status: "success", message: "编译成功" });
        } catch (error: any) {
          clearTimeout(timeoutId);

          if (error?.name === "AbortError") {
            throw new Error("Compilation timeout, please try again later");
          }
          throw error;
        }
      } catch (error) {
        setErr(error instanceof Error ? error.message : "未知错误");
        setCompiling({
          isProcessing: false,
          status: "error",
          message: error instanceof Error ? error.message : "编译出错",
        });
      } finally {
        // 无论编译成功还是失败，都释放锁定状态
        setCompileInProgress(false);
        updatePct(0);
      }
    },
    [
      projectName,
      tree,
      activePath,
      sendHtml,
      setHtml,
      hasPaper,
      compileInProgress,
      showAlert,
    ]
  );

  /* ---------- 自动打开文件和编译 ---------- */
  useAutoOpenFile(tree, activePath, openFile);
  useAutoCompile(tree, autoCompiled, setAutoCompiled, compile);

  /* ---------- 确认导航 ---------- */
  const confirmNavigate = useCallback((url: string, newTab = false) => {
    if (typeof window === "undefined") return;
    showConfirm(
      "确定离开当前页面并跳转吗？",
      () => {
        if (newTab) window.open(url, "_blank");
        else window.location.href = url;
      },
      "确认导航"
    );
  }, [showConfirm]);

  /* ---------- 处理iframe引用 ---------- */
  const handleIframeRef = useCallback(
    (ref: HTMLIFrameElement | null) => {
      iframeRef.current = ref;

      if (ref && html) {
        sendHtml(ref, html).catch(() => {
          // 静默处理错误
        });
      }
    },
    [html, sendHtml]
  );

  /* ---------- 文件拖拽上传 ---------- */
  const ALLOWED_RE = /\.(md|bib|png|jpe?g|gif)$/i;
  const MAX_BYTES = 10 * 1024 * 1024; // 10 MB

  /* 选中的拖拽目标（用于高亮） */
  const [dragTarget, setDragTarget] = useState<string | null>(null);

  const uploadWithProgress = useCallback(
    (file: File, targetPath: string) =>
      new Promise<void>((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open("PUT", `/api/editor/projects/${encodeURIComponent(projectName)}/file`, true);

        const isBinary = file.type.startsWith("image/");

        if (isBinary) {
          xhr.setRequestHeader("Content-Type", file.type || "application/octet-stream");
          xhr.setRequestHeader("x-file-path", encodeURIComponent(targetPath));
        } else {
          xhr.setRequestHeader("Content-Type", "application/json");
        }

        xhr.upload.onprogress = e => {
          if (e.lengthComputable) {
            const percent = Math.round((e.loaded / e.total) * 100);
            setUploadPct(percent);
          }
        };

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve();
          } else {
            reject(xhr.responseText);
          }
        };

        xhr.onerror = () => {
          reject(xhr.responseText || "网络错误");
        };

        if (isBinary) {
          xhr.send(file);
        } else {
          file.text().then(txt => xhr.send(JSON.stringify({ path: targetPath, content: txt })));
        }
      }),
    [projectName]
  );

  const handleDrop = useCallback(
    async (e: React.DragEvent<any>) => {
      e.preventDefault();
      e.stopPropagation();

      const files = Array.from(e.dataTransfer.files);
      if (!files.length) {
        return;
      }

      /* ⇣⇣⇣ 关键：决定落在哪个目录 ⇣⇣⇣ */
      const dirPath = dragTarget ?? "";

      // 先清除拖拽目标
      setDragTarget(null);

      for (const file of files) {
        /* ---------- 前置校验 ---------- */
        if (!ALLOWED_RE.test(file.name)) {
          showAlert(`Unsupported file type: ${file.name}`, "File Type Error", "warning");
          continue;
        }
        if (file.size > MAX_BYTES) {
          showAlert(`File exceeds 10 MB: ${file.name}`, "File Size Error", "warning");
          continue;
        }

        const targetPath = dirPath ? `${dirPath}/${file.name}` : file.name;

        /* ---------- 上传并显示进度 ---------- */
        try {
          setUploading(true);
          setUFName(file.name);
          setUploadPct(0);
          await uploadWithProgress(file, targetPath);
        } catch (err) {
          showAlert(`Upload failed: ${file.name}\n${err}`, "Upload Error", "destructive");
        } finally {
          setUploading(false);
          setUFName("");
          setUploadPct(0);
        }
      }

      /* ---------- 刷新文件树 ---------- */
      try {
        await refreshTree();
      } catch (error) {
        // 静默处理错误
      }
    },
    [dragTarget, uploadWithProgress, refreshTree]
  );

  /* 将 oldPath 移动到 newDir/filename */
  const moveFile = useCallback(
    async (oldPath: string, newDir: string) => {
      const filename = oldPath.split("/").pop()!;
      const newPath = newDir ? `${newDir}/${filename}` : filename;

      /* 若目标与原路径相同则忽略 */
      if (newPath === oldPath) return;

      try {
        const res = await fetch(`/api/editor/projects/${encodeURIComponent(projectName)}/move`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ from: oldPath, to: newPath }),
        });

        if (!res.ok) {
          showAlert((await res.text()) || `Move failed: ${res.status}`, "Move Error", "destructive");
          return;
        }

        /* 刷新文件树 */
        await refreshTree();

        /* 如果当前正在编辑被移动文件，更新 activePath */
        if (activePath === oldPath) setPath(newPath);
      } catch (error) {
        showAlert(`Failed to move file: ${error instanceof Error ? error.message : String(error)}`, "Move Error", "destructive");
      }
    },
    [projectName, activePath, refreshTree]
  );





  /* ---------- 右键菜单处理 ---------- */
  const handleContextMenu = useCallback((x: number, y: number, node: TreeNode) => {
    setMenu({ x, y, node });
  }, []);

  /* ---------- 重命名文件 ---------- */
  const handleRename = useCallback(
    async (node: TreeNode, newName: string) => {
      return await renameItem(node, newName);
    },
    [renameItem]
  );

  /* ---------- 删除文件 ---------- */
  const handleDelete = useCallback(
    async (node: TreeNode) => {
      return await deleteItem(node);
    },
    [deleteItem]
  );

  /* ---------- 复制文件 ---------- */
  const handleCopy = useCallback((path: string) => {
    setClipboard(path);
  }, []);

  /* ---------- 粘贴文件 ---------- */
  const handlePaste = useCallback(
    async (targetDir: string, sourcePath: string) => {
      return await copyItem(sourcePath, targetDir);
    },
    [copyItem]
  );

  /* ---------- 下载文件 ---------- */
  const handleDownload = useCallback(
    async (node: TreeNode) => {
      return await downloadItem(node);
    },
    [downloadItem]
  );

  /* ---------- 创建文件 ---------- */
  const handleCreateFile = useCallback(
    async (dirPath: string, fileName: string) => {
      return await createFile(dirPath, fileName);
    },
    [createFile]
  );

  /* ---------- 创建目录 ---------- */
  const handleCreateDir = useCallback(
    async (dirPath: string, dirName: string) => {
      return await createDirectory(dirPath, dirName);
    },
    [createDirectory]
  );

  // 监听文件状态变化事件
  useEffect(() => {
    const handleFileStatusChange = (event: CustomEvent) => {
      const { path, status, timestamp } = event.detail;

      // 只处理当前活动文件的状态变化
      if (path === activePath) {
        if (status === "dirty") {
          setDirty(true);
        } else if (status === "cached") {
          // 文件已缓存但尚未同步，不改变dirty状态
        } else if (status === "synced") {
          setDirty(false);
          setLastSaved(new Date(timestamp));
        }
      }
    };

    // 添加自定义事件监听
    window.addEventListener("file-status-change", handleFileStatusChange as EventListener);

    return () => {
      window.removeEventListener("file-status-change", handleFileStatusChange as EventListener);
    };
  }, [activePath]);

  // 更新编辑器内容到浏览器本地存储，方便后续获取
  useEffect(() => {
    if (activePath && content) {
      // 使用DOM存储当前编辑内容，以便状态管理钩子可以访问
      localStorage.setItem(`current-content:${projectName}|${activePath}`, content);
    }
  }, [activePath, content, projectName]);

  /* ---------- 缓存文件计数 ---------- */
  const [cachedFilesCount, setCachedFilesCount] = useState(0);

  /* 根据 fileStatuses 动态更新未同步文件计数 */
  useEffect(() => {
    const count = Array.from(fileStatuses.values()).filter(
      s => s.cached && !s.synced && !s.dirty
    ).length;
    setCachedFilesCount(count);
  }, [fileStatuses]);

  /* ---------- 面板展开函数 ---------- */
  const expandFileTreePanel = useCallback(() => {
    if (fileTreePanelRef.current) {
      fileTreePanelRef.current.expand();
      setIsFileTreeCollapsed(false);
    }
  }, []);

  const expandPreviewPanel = useCallback(() => {
    if (previewPanelRef.current) {
      previewPanelRef.current.expand();
      setIsPreviewCollapsed(false);
    }
  }, []);

  /* ---------- 文章大纲 ---------- */
  const [outline, setOutline] = useState<{ level: number; text: string; line: number }[]>([]);

  /* ---------- 跳转到指定行（大纲点击） ---------- */
  const jumpToLine = useCallback((lineNumber: number) => {
    const view: any = editorViewRef.current;
    if (!view) return;

    // 设置光标，但不立即滚动
    const line = view.state.doc.line(lineNumber + 1);
    view.dispatch(
      view.state.update({
        selection: { anchor: line.from },
        scrollIntoView: false,
      })
    );

    // 计算目标位置并执行单次平滑滚动
    setTimeout(() => {
      try {
        const scrollDOM: HTMLElement = (view.scrollDOM ?? view.dom) as HTMLElement;
        const coords = view.coordsAtPos(line.from);
        if (!coords) return;

        // 计算目标滚动位置为行位置减去视口四分之一
        const viewportOffset = scrollDOM.clientHeight / 4;
        const containerTop = scrollDOM.getBoundingClientRect().top;
        const lineTop = coords.top - containerTop + scrollDOM.scrollTop;

        // 执行平滑滚动
        scrollDOM.scrollTo({
          top: lineTop - viewportOffset,
          behavior: "smooth",
        });
      } catch (e) {
        // 静默处理滚动计算错误
      }
    }, 10);

    view.focus();
  }, []);

  /* ---------- 大纲同步 ---------- */
  const [activeLine, setActiveLine] = useState<number>(0);

  // 无需预览同步，故去除相关逻辑

  // 添加createFileContent函数处理
  const createFileContent = useCallback(
    (path: string) => {
      if (path.endsWith("paper.md")) {
        return DEFAULT_MD_TEMPLATE;
      }
      if (path.endsWith(".md")) {
        return "# " + path.split("/").pop()?.replace(".md", "") || "New File";
      }
      return localStorage.getItem(`current-content:${projectName}|${path}`) || "";
    },
    [projectName]
  );

  // 默认Markdown模板
  const DEFAULT_MD_TEMPLATE = `---
title: 我的论文标题
abstract: 请在此处添加摘要，不少于10个字符
tags: [tag1, tag2]
authors:
  - name: 作者姓名
status: draft
paperType: full
paperID: 
---

# 引言

请在此处添加引言内容。

# 正文

请在此处添加正文内容。

# 总结

请在此处添加总结内容。

# 参考文献
`;

  // 渲染加载界面
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen text-gray-500 gap-4">
        <Progress value={loadingPct} className="w-64" />
      </div>
    );
  }

  return (
    <>
      {/* ========= 主布局 ========= */}
      <PanelGroup
        direction="horizontal"
        autoSaveId="editor-layout"
        className="w-full h-full max-h-screen"
        onDragOver={e => e.preventDefault()}
        onDrop={handleDrop}
      >
        {/* ---------- 文件树 ---------- */}
        <Panel
          ref={fileTreePanelRef}
          defaultSize={12}
          minSize={14.5}
          collapsible
          className="h-full"
          onCollapse={() => setIsFileTreeCollapsed(true)}
          onExpand={() => setIsFileTreeCollapsed(false)}
        >
          <PanelGroup direction="vertical" className="h-full">
            <Panel defaultSize={50} minSize={20}>
              <FileTreePanel
                nodes={tree}
                activePath={activePath}
                collapsed={collapsed}
                dragTarget={dragTarget}
                uploading={uploading}
                uploadFileName={uploadFileName}
                uploadPct={uploadPct}
                isOnline={isOnline}
                fileStatuses={fileStatuses}
                name={projectName}
                onToggleDir={p =>
                  setCollapsed(s => {
                    const next = new Set(s);
                    next.has(p) ? next.delete(p) : next.add(p);
                    return next;
                  })
                }
                onOpen={openFile}
                onDragTargetChange={setDragTarget}
                onMoveFile={moveFile}
                onContextMenu={handleContextMenu}
                onNavigate={confirmNavigate}
                saveAllFiles={async () => {
                  // 1. 先保存当前活动文件
                  if (activePath && dirty) {
                    await persist("submit-operation");
                  }

                  // 2. 再保存其他所有脏文件
                  const dirtyFiles = getDirtyFiles();
                  if (dirtyFiles.length > 0) {
                    for (const file of dirtyFiles) {
                      // 跳过当前活动文件(已在上一步保存)
                      if (file === activePath) continue;

                      // 获取文件内容
                      const content = localStorage.getItem(
                        `current-content:${projectName}|${file}`
                      );
                      if (content) {
                        await cache(file, content);

                        if (navigator.onLine) {
                          try {
                            const res = await fetch(
                              `/api/editor/projects/${encodeURIComponent(projectName)}/file`,
                              {
                                method: "PUT",
                                headers: { "Content-Type": "application/json" },
                                body: JSON.stringify({
                                  path: file,
                                  content: content,
                                  timestamp: Date.now(),
                                }),
                              }
                            );

                            if (res.ok) {
                              await markSynced(file);
                            }
                          } catch (e) {
                            // 静默处理错误
                          }
                        }
                      }
                    }
                  }

                  // 3. 等待所有操作完成
                  await new Promise(resolve => setTimeout(resolve, 500));
                }}
                compile={compile}
                compileWithoutSave={compileWithoutSave}
                hasPaper={hasPaper}
                getPagedHtml={getPagedHtml}
              />
            </Panel>
            <PanelResizeHandle className="h-px cursor-row-resize react-resizable-panels-PanelResizeHandle" />
            <Panel defaultSize={50} minSize={20}>
              <OutlinePanel
                content={activePath?.endsWith(".md") ? content : ""}
                activeLine={activeLine}
                onJump={line => {
                  jumpToLine(line);
                  setActiveLine(line);
                }}
              />
            </Panel>
          </PanelGroup>
        </Panel>

        <PanelResizeHandle
          className="w-px cursor-col-resize react-resizable-panels-PanelResizeHandle"
          onPointerUp={() => iframeRef.current?.contentWindow?.focus()}
          onPointerDownCapture={() => iframeRef.current?.contentWindow?.blur()}
        />

        {/* ---------- 代码编辑器 ---------- */}
        <Panel defaultSize={44} minSize={30}>
          <EditorPanel
            activePath={activePath}
            content={content}
            imageUrl={imageUrl}
            loadingImage={loadingImage}
            dirty={dirty}
            fontSize={fontSize}
            isOnline={isOnline}
            lastSaved={lastSaved}
            saving={saving}
            compiling={compiling}
            onContentChange={handleContentChange}
            onSave={async () => {
              await persist("Manual save", false); // Return Promise correctly
            }}
            onCompile={compile}
            onFontSizeChange={setFontSize}
            mounted={mounted}
            hasDirtyFiles={hasDirtyFiles()}
            cachedFilesCount={cachedFilesCount}
            onEditorView={(v: any) => {
              editorViewRef.current = v;
              setupGlobalEditor(v);
            }}
            onCursorLineChange={ln => setActiveLine(ln)}
            isDarkMode={isDarkMode}
            onToggleDarkMode={toggleDarkMode}
          />
        </Panel>

        <PanelResizeHandle
          className="w-px cursor-col-resize react-resizable-panels-PanelResizeHandle"
          onPointerUp={() => iframeRef.current?.contentWindow?.focus()}
          onPointerDownCapture={() => iframeRef.current?.contentWindow?.blur()}
        />

        {/* ---------- 预览 ---------- */}
        <Panel
          ref={previewPanelRef}
          defaultSize={44}
          minSize={30}
          collapsible
          onCollapse={() => setIsPreviewCollapsed(true)}
          onExpand={() => setIsPreviewCollapsed(false)}
        >
          <PreviewPanel html={html} compileError={compileErr} onIframeRef={handleIframeRef} />
        </Panel>
      </PanelGroup>

      {/* ========= 折叠面板标签 ========= */}
      <CollapsedPanelHandle
        position="left"
        panelType="filetree"
        isVisible={isFileTreeCollapsed}
        onExpand={expandFileTreePanel}
      />

      <CollapsedPanelHandle
        position="right"
        panelType="preview"
        isVisible={isPreviewCollapsed}
        onExpand={expandPreviewPanel}
      />

      {/* ========= 右键菜单 ========= */}
      <ContextMenu
        menu={menu}
        clipboard={clipboard}
        projectName={projectName}
        onClose={() => setMenu(null)}
        onRename={handleRename}
        onDelete={handleDelete}
        onCopy={handleCopy}
        onPaste={handlePaste}
        onDownload={handleDownload}
        onCreateFile={handleCreateFile}
        onCreateDir={handleCreateDir}
        showAlert={showAlert}
        showConfirm={showConfirm}
        showPrompt={showPrompt}
      />

      {/* 通知对话框 */}
      <NotificationDialog
        notification={notification}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />

      {/* 添加Toaster组件 */}
      <Toaster />
    </>
  );
}
