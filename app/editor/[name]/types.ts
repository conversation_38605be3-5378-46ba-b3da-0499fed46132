// 文件树节点类型
export interface TreeNode {
  name: string;
  path: string;
  isDir: boolean;
  children?: TreeNode[];
}

// 右键菜单配置
export interface MenuConfig {
  x: number;
  y: number;
  node: TreeNode;
}

// 通用状态接口 - 表示加载或处理状态
export interface ProcessingState {
  isProcessing: boolean;
  status?: "idle" | "loading" | "success" | "error" | "warning";
  message?: string;
}

// 文件操作结果
export interface FileOperationResult {
  success: boolean;
  message?: string;
  data?: any;
}

export interface FileStatus {
  path: string;
  dirty: boolean;
  cached: boolean;
  synced: boolean;
  lastModified: number;
}
