import { useCallback } from 'react';

/**
 * 处理编辑器全局设置的自定义钩子
 * 将 CodeMirror 实例暴露到全局，供桥接脚本使用
 */
export function useEditorGlobalSetup() {
  const setupGlobalEditor = useCallback((editorView: any) => {
    if (!editorView || typeof window === 'undefined') return;

    // 提供多种可能的全局访问点，确保桥接脚本能找到编辑器
    (window as any).editorView = editorView;
    (window as any).editor = editorView;
    (window as any).cm = editorView;

    // 延迟设置，确保桥接脚本有时间加载
    setTimeout(() => {
      // 也设置到 CodeMirror Bridge API（如果存在）
      if ((window as any).codeMirrorBridge) {
        if ((window as any).codeMirrorBridge.setEditor) {
          (window as any).codeMirrorBridge.setEditor(editorView);
        } else if ((window as any).codeMirrorBridge.checkForEditor) {
          (window as any).codeMirrorBridge.checkForEditor();
        }
      }
    }, 200);
  }, []);

  return { setupGlobalEditor };
}
