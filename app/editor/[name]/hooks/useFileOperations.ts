import { useState, useCallback } from "react";

import { TreeNode, FileOperationResult } from "../types";

interface FileOperationsProps {
  projectName: string;
  refreshTree: () => Promise<void>;
  setActivePathIfNeeded: (oldPath: string, newPath: string) => void;
}

export function useFileOperations({
  projectName,
  refreshTree,
  setActivePathIfNeeded,
}: FileOperationsProps) {
  // 文件操作状态
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastOperationResult, setLastOperationResult] = useState<FileOperationResult | null>(null);

  // 通用请求错误处理
  const handleRequestError = useCallback((error: any): FileOperationResult => {
    const errorMessage = error?.message || String(error) || "Operation failed";
    return { success: false, message: errorMessage };
  }, []);

  // 重命名文件或目录
  const renameItem = useCallback(
    async (node: TreeNode, newName: string): Promise<FileOperationResult> => {
      if (isProcessing) return { success: false, message: "Operation in progress" };

      setIsProcessing(true);
      try {
        const dir = node.path.split("/").slice(0, -1).join("/");
        const to = dir ? `${dir}/${newName}` : newName;

        // 如果新旧路径相同，跳过操作
        if (to === node.path) {
          return { success: true, message: "File name not changed" };
        }

        const res = await fetch(`/api/editor/projects/${encodeURIComponent(projectName)}/move`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ from: node.path, to }),
        });

        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(errorText || `Rename failed: ${res.status}`);
        }

        await refreshTree();

        // 更新活动文件路径（如果需要）
        setActivePathIfNeeded(node.path, to);

        const result = { success: true, message: "Rename successful" };
        setLastOperationResult(result);
        return result;
      } catch (error) {
        const result = handleRequestError(error);
        setLastOperationResult(result);
        return result;
      } finally {
        setIsProcessing(false);
      }
    },
    [projectName, refreshTree, isProcessing, handleRequestError, setActivePathIfNeeded]
  );

  // 删除文件或目录
  const deleteItem = useCallback(
    async (node: TreeNode): Promise<FileOperationResult> => {
      if (isProcessing) return { success: false, message: "Operation in progress" };

      setIsProcessing(true);
      try {
        const res = await fetch(`/api/editor/projects/${encodeURIComponent(projectName)}/delete`, {
          method: "DELETE",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ path: node.path, isDir: node.isDir }),
        });

        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(errorText || `Delete failed: ${res.status}`);
        }

        await refreshTree();

        const result = { success: true, message: "Delete successful" };
        setLastOperationResult(result);
        return result;
      } catch (error) {
        const result = handleRequestError(error);
        setLastOperationResult(result);
        return result;
      } finally {
        setIsProcessing(false);
      }
    },
    [projectName, refreshTree, isProcessing, handleRequestError]
  );

  // 复制文件
  const copyItem = useCallback(
    async (sourceDir: string, targetDir: string): Promise<FileOperationResult> => {
      if (isProcessing) return { success: false, message: "Operation in progress" };

      setIsProcessing(true);
      try {
        const filename = sourceDir.split("/").pop()!;
        const targetPath = targetDir ? `${targetDir}/${filename}` : filename;

        const res = await fetch(`/api/editor/projects/${encodeURIComponent(projectName)}/copy`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ from: sourceDir, to: targetPath }),
        });

        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(errorText || `Copy failed: ${res.status}`);
        }

        await refreshTree();

        const result = { success: true, message: "Copy successful" };
        setLastOperationResult(result);
        return result;
      } catch (error) {
        const result = handleRequestError(error);
        setLastOperationResult(result);
        return result;
      } finally {
        setIsProcessing(false);
      }
    },
    [projectName, refreshTree, isProcessing, handleRequestError]
  );

  // 下载文件或目录
  const downloadItem = useCallback(
    async (node: TreeNode): Promise<FileOperationResult> => {
      if (isProcessing) return { success: false, message: "Operation in progress" };

      setIsProcessing(true);
      try {
        const endpoint = "download-tree";
        const res = await fetch(
          `/api/editor/projects/${encodeURIComponent(projectName)}/${endpoint}` +
          `?path=${encodeURIComponent(node.path)}`
        );

        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(errorText || `Download failed: ${res.status}`);
        }

        const blob = await res.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = node.isDir ? `${node.name || projectName}.zip` : node.name;
        a.click();
        URL.revokeObjectURL(url);

        const result = { success: true, message: "Download successful" };
        setLastOperationResult(result);
        return result;
      } catch (error) {
        const result = handleRequestError(error);
        setLastOperationResult(result);
        return result;
      } finally {
        setIsProcessing(false);
      }
    },
    [projectName, isProcessing, handleRequestError]
  );

  // 创建新文件
  const createFile = useCallback(
    async (dirPath: string, fileName: string): Promise<FileOperationResult> => {
      if (isProcessing) return { success: false, message: "Operation in progress" };

      setIsProcessing(true);
      try {
        const target = dirPath ? `${dirPath}/${fileName}` : fileName;

        const res = await fetch(
          `/api/editor/projects/${encodeURIComponent(projectName)}/create-file`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ path: target }),
          }
        );

        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(errorText || `Create file failed: ${res.status}`);
        }

        await refreshTree();

        const result = { success: true, message: "File created successfully", data: { path: target } };
        setLastOperationResult(result);
        return result;
      } catch (error) {
        const result = handleRequestError(error);
        setLastOperationResult(result);
        return result;
      } finally {
        setIsProcessing(false);
      }
    },
    [projectName, refreshTree, isProcessing, handleRequestError]
  );

  // 创建新目录
  const createDirectory = useCallback(
    async (dirPath: string, dirName: string): Promise<FileOperationResult> => {
      if (isProcessing) return { success: false, message: "Operation in progress" };

      setIsProcessing(true);
      try {
        const target = dirPath ? `${dirPath}/${dirName}` : dirName;

        const res = await fetch(
          `/api/editor/projects/${encodeURIComponent(projectName)}/create-dir`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ path: target }),
          }
        );

        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(errorText || `Create directory failed: ${res.status}`);
        }

        await refreshTree();

        const result = { success: true, message: "Directory created successfully", data: { path: target } };
        setLastOperationResult(result);
        return result;
      } catch (error) {
        const result = handleRequestError(error);
        setLastOperationResult(result);
        return result;
      } finally {
        setIsProcessing(false);
      }
    },
    [projectName, refreshTree, isProcessing, handleRequestError]
  );

  return {
    isProcessing,
    lastOperationResult,
    renameItem,
    deleteItem,
    copyItem,
    downloadItem,
    createFile,
    createDirectory,
  };
}
