import { useEffect } from 'react';
import { TreeNode } from '../types';
import { hasPaper } from '../utils/treeUtils';

/**
 * 自动编译的自定义钩子
 * 在文件树加载完成且包含 paper.md 时自动触发编译
 */
export function useAutoCompile(
  tree: TreeNode[],
  autoCompiled: boolean,
  setAutoCompiled: (compiled: boolean) => void,
  compile: () => Promise<void>
) {
  useEffect(() => {
    if (!autoCompiled && tree.length && hasPaper(tree)) {
      // 设置标志以防止重复编译
      setAutoCompiled(true);
      compile();
    }
  }, [tree, autoCompiled, setAutoCompiled, compile]);
}


