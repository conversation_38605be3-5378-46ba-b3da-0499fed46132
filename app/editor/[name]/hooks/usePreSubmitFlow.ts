import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface PreSubmitFlowOptions {
  projectName: string;
  saveAllFiles: () => Promise<void>;
  compile: () => Promise<void>;
  compileWithoutSave?: () => Promise<void>; // New: compile function without save
  hasPaper: (tree: any[]) => boolean;
  tree: any[];
}

interface PreSubmitFlowState {
  isProcessing: boolean;
  currentStep: string;
  progress: number;
}

/**
 * Pre-submit flow hook
 * Handles automatic save and compile operations before submission
 */
export function usePreSubmitFlow({
  projectName,
  saveAllFiles,
  compile,
  compileWithoutSave,
  hasPaper,
  tree
}: PreSubmitFlowOptions) {
  const [flowState, setFlowState] = useState<PreSubmitFlowState>({
    isProcessing: false,
    currentStep: '',
    progress: 0
  });

  const executePreSubmitFlow = useCallback(async (): Promise<boolean> => {
    let toastId: string | number | undefined;

    try {
      // Show progress toast
      toastId = toast.loading('Checking project status...', {
        description: 'Preparing for submission...',
      });

      setFlowState({
        isProcessing: true,
        currentStep: 'Checking project status...',
        progress: 10
      });

      // Check if paper.md exists
      if (!hasPaper(tree)) {
        toast.error('Submission failed', {
          description: 'Missing paper.md file in project, cannot submit.',
          id: toastId,
        });
        return false;
      }

      // Update progress
      toast.loading('Saving all files...', {
        description: 'Ensuring all changes are saved',
        id: toastId,
      });

      setFlowState({
        isProcessing: true,
        currentStep: 'Saving all files...',
        progress: 30
      });

      // Save all files
      await saveAllFiles();

      // Update progress
      toast.loading('Compiling latest content...', {
        description: 'Generating latest preview version',
        id: toastId,
      });

      setFlowState({
        isProcessing: true,
        currentStep: 'Compiling latest content...',
        progress: 60
      });

      // Execute compile to ensure compiled.html is up to date (use version without save to avoid duplicate saves)
      if (compileWithoutSave) {
        await compileWithoutSave();
      } else {
        await compile();
      }

      // Update progress
      toast.loading('Preparing submission...', {
        description: 'About to open submission dialog',
        id: toastId,
      });

      setFlowState({
        isProcessing: true,
        currentStep: 'Preparing submission...',
        progress: 90
      });

      // 短暂延迟以显示完成状态
      await new Promise(resolve => setTimeout(resolve, 500));

      // Show success
      toast.success('Preparation complete', {
        description: 'All files saved and compiled successfully',
        id: toastId,
      });

      setFlowState({
        isProcessing: true,
        currentStep: 'Complete',
        progress: 100
      });

      // 再次短暂延迟
      await new Promise(resolve => setTimeout(resolve, 300));

      return true;

    } catch (error) {
      toast.error('Pre-submission preparation failed', {
        description: error instanceof Error ? error.message : 'Unknown error, please try again.',
        id: toastId,
      });

      return false;
    } finally {
      setFlowState({
        isProcessing: false,
        currentStep: '',
        progress: 0
      });
    }
  }, [projectName, saveAllFiles, compile, hasPaper, tree]);

  return {
    flowState,
    executePreSubmitFlow
  };
}
