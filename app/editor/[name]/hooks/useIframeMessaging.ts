import { useCallback, useEffect, useRef } from "react";

interface MessageOptions {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

interface SendMessageResult {
  success: boolean;
  error?: string;
}

interface UseIframeMessagingOptions {
  debug?: boolean;
  projectName?: string;
}

/**
 * 管理iframe消息通信的自定义钩子
 */
export function useIframeMessaging({ debug = false }: UseIframeMessagingOptions = {}) {
  // 跟踪已发送的消息，用于确认
  const pendingMessages = useRef<Map<string, { resolve: Function; reject: Function; timer: any }>>(
    new Map()
  );
  // 跟踪最后收到的消息内容
  const lastReceivedContent = useRef<{ type: string; content: any; timestamp: number } | null>(
    null
  );

  // 记录日志（仅调试模式）
  const log = useCallback((...args: any[]) => {
    if (debug) {
      console.log(`[IframeMessaging]`, ...args);
    }
  }, [debug]);

  /**
   * 生成唯一消息ID
   */
  const generateMessageId = useCallback(() => {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }, []);

  /**
   * 获取当前项目名称
   */
  const getCurrentProjectName = useCallback(() => {
    try {
      // 从URL中提取项目名
      const pathMatch = window.location.pathname.match(/\/editor\/([^\/]+)/);
      return pathMatch ? pathMatch[1] : "";
    } catch (e) {
      log("Failed to get project name:", e);
      return "";
    }
  }, [log]);

  /**
   * 向目标iframe发送消息，并等待确认
   */
  const sendMessage = useCallback(
    async (
      iframe: HTMLIFrameElement | null,
      type: string,
      content: any,
      options: MessageOptions = {}
    ): Promise<SendMessageResult> => {
      const projectName = getCurrentProjectName();
      log(
        `Start sending message: type=${type}, contentSize=${typeof content === "string" ? content.length : "not-string"}, project=${projectName}`
      );

      if (!iframe) {
        log(`Error: iframe is null`);
        return { success: false, error: "Iframe is not available" };
      }

      if (!iframe.contentWindow) {
        log(`Error: iframe.contentWindow is null`);
        return { success: false, error: "iframe.contentWindow is not available" };
      }

      const messageId = generateMessageId();
      const timeout = options.timeout || 5000; // 默认5秒超时
      const retries = options.retries || 2; // 默认重试2次
      const retryDelay = options.retryDelay || 500; // 默认重试间隔500ms

      let currentRetry = 0;

      const attemptSend = (): Promise<SendMessageResult> => {
        return new Promise<SendMessageResult>((resolve, reject) => {
          try {
            // 准备消息数据
            const message = {
              type,
              content,
              messageId,
              projectName,
              timestamp: Date.now(),
            };

            // 存储等待确认的消息
            const timer = setTimeout(() => {
              log(`Message ${messageId} sending timeout`);
              pendingMessages.current.delete(messageId);

              if (currentRetry < retries) {
                currentRetry++;
                log(`Message ${messageId} sending timeout, retrying (${currentRetry}/${retries})...`);

                setTimeout(() => {
                  attemptSend().then(resolve).catch(reject);
                }, retryDelay);
              } else {
                log(`Message ${messageId} reached maximum retry count, giving up`);
                reject(new Error(`Sending message timeout: ${type}`));
              }
            }, timeout);

            pendingMessages.current.set(messageId, { resolve, reject, timer });

            // 发送消息
            try {
              // 由于已经在函数开头检查过contentWindow是否存在，这里类型断言是安全的
              const contentWindow = iframe.contentWindow as Window;
              contentWindow.postMessage(message, "*");
              log(`Message sent: type=${type}, ID=${messageId}, project=${projectName}`);
            } catch (postError) {
              log(`postMessage sending failed:`, postError);
              clearTimeout(timer);
              pendingMessages.current.delete(messageId);
              reject(postError);
            }
          } catch (err) {
            log(`Error sending message:`, err);
            reject(err);
          }
        });
      };

      try {
        const result = await attemptSend();
        log(`Message ${messageId} sent successfully`);
        return result;
      } catch (error) {
        log(`Message ${messageId} sent failed:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
        };
      }
    },
    [generateMessageId, log, getCurrentProjectName]
  );

  /**
   * 获取iframe当前滚动位置
   */
  const getScrollPosition = useCallback(
    async (iframe: HTMLIFrameElement | null): Promise<{ scrollTop: number; scrollLeft: number } | null> => {
      if (!iframe || !iframe.contentWindow) {
        return null;
      }

      try {
        // 发送获取滚动位置的请求
        const messageId = generateMessageId();
        return new Promise<{ scrollTop: number; scrollLeft: number } | null>((resolve) => {
          const timeout = setTimeout(() => {
            resolve(null);
          }, 1000);

          const handleResponse = (e: MessageEvent) => {
            if (e.data?.type === "scroll-position-response" && e.data?.messageId === messageId) {
              clearTimeout(timeout);
              window.removeEventListener("message", handleResponse);
              resolve({ scrollTop: e.data.scrollTop || 0, scrollLeft: e.data.scrollLeft || 0 });
            }
          };

          window.addEventListener("message", handleResponse);
          iframe.contentWindow!.postMessage(
            {
              type: "get-scroll-position",
              messageId,
              timestamp: Date.now(),
            },
            "*"
          );
        });
      } catch (error) {
        log("Error getting scroll position:", error);
        return null;
      }
    },
    [generateMessageId, log]
  );

  /**
   * 恢复iframe滚动位置
   */
  const restoreScrollPosition = useCallback(
    async (iframe: HTMLIFrameElement | null, scrollTop: number, scrollLeft: number = 0): Promise<void> => {
      if (!iframe || !iframe.contentWindow) {
        return;
      }

      try {
        log(`Restoring scroll position: ${scrollTop}, ${scrollLeft}`);
        iframe.contentWindow.postMessage(
          {
            type: "restore-scroll-position",
            scrollTop,
            scrollLeft,
            timestamp: Date.now(),
          },
          "*"
        );
      } catch (error) {
        log("Error restoring scroll position:", error);
      }
    },
    [log]
  );

  /**
   * 简化的发送HTML内容方法（带滚动位置记忆）
   */
  const sendHtml = useCallback(
    async (iframe: HTMLIFrameElement | null, html: string): Promise<SendMessageResult> => {
      const projectName = getCurrentProjectName();
      const startTime = Date.now();

      // 1. 先获取当前滚动位置
      const scrollPosition = await getScrollPosition(iframe);

      // 2. 发送HTML内容
      const result = await sendMessage(iframe, "html", html, { retries: 3 });

      // 3. 如果发送成功且有滚动位置，恢复滚动位置
      if (result.success && scrollPosition && scrollPosition.scrollTop > 0) {
        // 延迟一下确保内容已经渲染完成
        setTimeout(() => {
          restoreScrollPosition(iframe, scrollPosition.scrollTop, scrollPosition.scrollLeft);
        }, 500); // 500ms延迟，给分页计算留出时间
      }

      log(
        `sendHtml completed: result=${result.success ? "success" : "failed"}, time=${Date.now() - startTime}ms`
      );
      return result;
    },
    [sendMessage, log, getCurrentProjectName, getScrollPosition, restoreScrollPosition]
  );

  /**
   * 清除iframe中的HTML内容
   */
  const clearHtml = useCallback(
    async (iframe: HTMLIFrameElement | null): Promise<SendMessageResult> => {
      if (!iframe) return { success: false, error: "Iframe is not available" };

      try {
        // 发送空HTML内容来清除
        const result = await sendMessage(iframe, "html", "", { timeout: 5000 });
        if (result.success) {
          log(`Cleared iframe content`);
          // 更新最后发送的内容为空
          lastReceivedContent.current = {
            type: "html",
            content: "",
            timestamp: Date.now(),
          };
        }
        return result;
      } catch (error) {
        log(`Error clearing HTML:`, error);
        return { success: false, error: (error as Error).message };
      }
    },
    [sendMessage, log]
  );

  /**
   * 获取已分页的HTML内容
   */
  const getPagedHtml = useCallback(
    async (iframe: HTMLIFrameElement | null): Promise<string | null> => {
      if (!iframe?.contentWindow) {
        return null;
      }

      try {
        const messageId = generateMessageId();

        return new Promise<string | null>((resolve) => {
          const timeout = setTimeout(() => {
            resolve(null);
          }, 5000);

          const handleResponse = (e: MessageEvent) => {
            if (e.data?.type === "paged-html-response" && e.data?.messageId === messageId) {
              clearTimeout(timeout);
              window.removeEventListener("message", handleResponse);
              resolve(e.data.error ? null : e.data.html || null);
            }
          };

          window.addEventListener("message", handleResponse);

          iframe.contentWindow!.postMessage({
            type: "get-paged-html",
            messageId,
            timestamp: Date.now(),
          }, "*");
        });
      } catch (error) {
        return null;
      }
    },
    [generateMessageId]
  );

  /**
   * 向iframe发送重置缩放命令
   */
  const resetScale = useCallback(
    (iframe: HTMLIFrameElement | null): void => {
      const projectName = getCurrentProjectName();
      if (!iframe || !iframe.contentWindow) {
        log(`resetScale: iframe or contentWindow is not available`);
        return;
      }

      try {
        iframe.contentWindow.postMessage(
          {
            type: "reset-scale",
            projectName,
            timestamp: Date.now(),
          },
          "*"
        );
        log(`Reset scale command sent, project=${projectName}`);
      } catch (err) {
        log("Failed to send reset scale command:", err);
      }
    },
    [log, getCurrentProjectName]
  );

  /**
   * 设置iframe消息监听器
   */
  useEffect(() => {
    const handleMessage = (e: MessageEvent) => {
      // 处理确认消息
      if (e.data?.type === "message-received" && e.data?.messageId) {
        const { messageId, projectName } = e.data;
        const currentProject = getCurrentProjectName();

        // 验证消息是否属于当前项目
        if (projectName && projectName !== currentProject) {
          log(`Ignoring message confirmation from other project (${projectName}), current project=${currentProject}`);
          return;
        }

        const pending = pendingMessages.current.get(messageId);
        if (pending) {
          log(`Received message confirmation: ${messageId}`);
          clearTimeout(pending.timer);
          pending.resolve({ success: true });
          pendingMessages.current.delete(messageId);
        } else {
          log(`Received unknown message ID confirmation: ${messageId}`);
        }
      }
      // 处理预览组件就绪消息
      else if (e.data?.type === "preview-ready") {
        const messageProjectName = e.data.projectName;
        const currentProject = getCurrentProjectName();

        // 验证消息是否属于当前项目
        if (messageProjectName && messageProjectName !== currentProject) {
          log(`Ignoring preview ready message from other project (${messageProjectName}), current project=${currentProject}`);
          return;
        }

        log(`Received preview ready message, project=${messageProjectName || "unknown"}`);
        // 如果有最后接收的内容，可以立即重新发送
        if (lastReceivedContent.current) {
          log("Detected preview reload, preparing to resend last content");
          const { type, content } = lastReceivedContent.current;

          // 尝试获取iframe元素
          const frame = document.getElementById("preview-frame") as HTMLIFrameElement;
          if (frame && frame.contentWindow) {
            log("Found iframe element, will resend content in 100ms");
            setTimeout(() => {
              try {
                if (!frame.contentWindow) {
                  log("Failed to resend content: iframe.contentWindow is not available");
                  return;
                }

                const newMessageId = generateMessageId();
                frame.contentWindow.postMessage(
                  {
                    type,
                    content,
                    messageId: newMessageId,
                    projectName: currentProject,
                    timestamp: Date.now(),
                  },
                  "*"
                );
                log(`Resent last content, new message ID: ${newMessageId}`);
              } catch (err) {
                log("Error resending content:", err);
              }
            }, 100);
          } else {
            log("Failed to resend content: iframe element not found");
          }
        } else {
          log("No cached content to resend");
        }
      }
      // 存储收到的内容型消息
      else if (e.data?.type && e.data?.content) {
        log(`Received content message: type=${e.data.type}`);
        lastReceivedContent.current = {
          type: e.data.type,
          content: e.data.content,
          timestamp: Date.now(),
        };
      }
      // 忽略 CodeMirror 相关消息，由桥接脚本处理
      else if (e.data?.type && (
        e.data.type.startsWith('CODEMIRROR_') ||
        e.data.type === 'WINDOW_INIT' ||
        e.data.type === 'DOM_QUERY_REQUEST' ||
        e.data.type === 'DOM_UPDATE_REQUEST' ||
        e.data.type === 'DOM_EXECUTE_REQUEST'
      )) {
        // 静默忽略这些消息，由相应的处理器处理
        return;
      } else {
        log("Received unknown message:", e.data);
      }
    };

    log("Installed message listener");
    window.addEventListener("message", handleMessage);
    return () => {
      log("Removed message listener");
      window.removeEventListener("message", handleMessage);
    };
  }, [generateMessageId, log, getCurrentProjectName]);

  return {
    sendMessage,
    sendHtml,
    clearHtml,
    resetScale,
    getScrollPosition,
    restoreScrollPosition,
    getPagedHtml,
    lastReceivedContent: lastReceivedContent.current,
  };
}
