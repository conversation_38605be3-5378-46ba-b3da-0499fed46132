import { get, set, del, keys } from "idb-keyval";
import { useState, useEffect, useCallback, useRef } from "react";

interface CacheStats {
  totalFiles: number;
  unsyncedFiles: number;
  dirtyFiles: string[];
}

interface CacheItem {
  content: string;
  synced: boolean; // 是否已同步到远程
  cached: boolean; // 是否已缓存到本地
  ts: number; // 时间戳
}

interface FileStatus {
  path: string;
  dirty: boolean; // 是否被修改过（还未缓存到本地）
  cached: boolean; // 是否已缓存到本地
  synced: boolean; // 是否已同步到远程
  lastModified: number;
}

/**
 * 文件状态过渡说明：
 * 1. 初始状态：synced=true, cached=true, dirty=false (绿色)
 * 2. 编辑文件：synced=true, cached=true, dirty=true (红色)
 * 3. 本地缓存：synced=false, cached=true, dirty=false (蓝色)
 * 4. 远程同步：synced=true, cached=true, dirty=false (绿色)
 */
export function useOfflineBuffer(projectName: string) {
  const [fileStatuses, setFileStatuses] = useState<Map<string, FileStatus>>(new Map());

  // 添加防抖计时器引用
  const cacheTimerRef = useRef<Record<string, ReturnType<typeof setTimeout>>>({});
  const dirtyFilesRef = useRef<Set<string>>(new Set());

  // 添加防抖状态更新的计时器引用
  const statusUpdateTimerRef = useRef<Record<string, ReturnType<typeof setTimeout>>>({});

  // 清理localStorage中与当前项目无关的缓存
  useEffect(() => {
    // 获取所有localStorage的键
    const localStorageKeys = Object.keys(localStorage);

    // 清理与current-content相关但不属于当前项目的键
    localStorageKeys.forEach(key => {
      if (
        key.startsWith("current-content:") &&
        !key.startsWith(`current-content:${projectName}|`)
      ) {
        // 检查键是否属于当前项目（不含项目前缀的路径）
        const filePath = key.substring("current-content:".length);

        // 确保这不是当前项目的缓存
        if (!filePath.includes("|") || !filePath.startsWith(projectName)) {
          localStorage.removeItem(key);
        }
      }
    });
  }, [projectName]);

  // 初始化时从IndexedDB加载项目中的所有文件状态
  useEffect(() => {
    async function loadFileStatuses() {
      try {
        const statuses = new Map<string, FileStatus>();

        // 实现一个简单的扫描功能，尝试查找项目相关的文件
        try {
          // 获取IndexedDB中的所有键
          const allKeys = await keys();

          // 过滤出当前项目的缓存键
          const projectKeys = allKeys.filter(
            key => typeof key === "string" && key.startsWith(`${projectName}|`)
          );

          // 获取所有匹配键的文件状态
          for (const key of projectKeys) {
            const value = await get(key);
            if (value) {
              const filePath = (key as string).substring(projectName.length + 1);

              // 更新文件状态
              statuses.set(filePath, {
                path: filePath,
                dirty: false, // 初始状态不是脏的
                cached: true, // 从IndexedDB加载的文件已被缓存
                synced: value.synced || false,
                lastModified: value.ts || Date.now(),
              });
            }
          }

          if (statuses.size > 0) {
            setFileStatuses(statuses);
          }
        } catch (dbErr) {
          // 静默处理数据库错误
        }
      } catch (err) {
        // 静默处理加载错误
      }
    }

    loadFileStatuses();
  }, [projectName]);

  /**
   * 缓存文件内容 - 写入本地存储
   * @param path 文件路径
   * @param content 文件内容
   * @returns 缓存时间戳
   */
  const cache = useCallback(
    async (path: string, content: string): Promise<string> => {
      const ts = Date.now();
      const key = `${projectName}|${path}`;

      // 尝试从localStorage获取内容，如果提供的内容为空
      if (!content) {
        const storedContent = localStorage.getItem(`current-content:${projectName}|${path}`);
        if (storedContent) {
          content = storedContent;
        }
      }

      const item: CacheItem = {
        content,
        synced: false, // 未同步到远程
        cached: true, // 已缓存到本地
        ts,
      };

      await set(key, item);

      // 清除可能仍在排队的"脏状态"更新定时器，避免状态被旧的定时器覆盖
      if (statusUpdateTimerRef.current[path]) {
        clearTimeout(statusUpdateTimerRef.current[path]);
        delete statusUpdateTimerRef.current[path];
      }
      // 若自动缓存定时器仍在，则同样清除
      if (cacheTimerRef.current[path]) {
        clearTimeout(cacheTimerRef.current[path]);
        delete cacheTimerRef.current[path];
      }

      // 更新文件状态为已缓存但未同步(蓝色)
      setFileStatuses(prev => {
        const newMap = new Map(prev);
        newMap.set(path, {
          path,
          dirty: false, // 不再是脏的
          cached: true, // 已缓存
          synced: false, // 未同步到远程
          lastModified: ts,
        });
        return newMap;
      });

      // 广播状态变化事件
      const event = new CustomEvent("file-status-change", {
        detail: { path, status: "cached", timestamp: ts },
      });
      window.dispatchEvent(event);

      return new Date(ts).toISOString();
    },
    [projectName]
  );

  /**
   * 从缓存加载文件内容
   * @param path 文件路径
   * @param ignoreSync 是否忽略同步状态
   * @returns 文件内容或null
   */
  const load = useCallback(
    async (path: string, ignoreSync = false): Promise<string | null> => {
      const key = `${projectName}|${path}`;
      try {
        const item = await get(key);
        if (!item) return null;

        // 确保item是预期的类型
        const cacheItem = item as any;

        // 如果文件已缓存但未同步，且不忽略同步状态，则更新状态
        if (cacheItem.cached && !cacheItem.synced && !ignoreSync) {
          // 更新文件状态
          setFileStatuses(prev => {
            const newMap = new Map(prev);
            newMap.set(path, {
              path,
              dirty: false,
              cached: true,
              synced: false,
              lastModified: cacheItem.ts,
            });
            return newMap;
          });
        }

        return cacheItem.content;
      } catch (err) {
        return null;
      }
    },
    [projectName]
  );

  /**
   * 标记文件为已同步状态 (绿色)
   * @param path 文件路径
   */
  const markSynced = useCallback(
    async (path: string): Promise<void> => {
      const key = `${projectName}|${path}`;
      try {
        const item = await get(key);
        if (!item) return;

        // 确保item是预期的类型
        const cacheItem = item as any;
        const ts = Date.now();

        // 更新缓存项
        cacheItem.synced = true;
        cacheItem.cached = true;
        cacheItem.ts = ts;
        await set(key, cacheItem);

        // 更新文件状态为已同步(绿色)
        setFileStatuses(prev => {
          const newMap = new Map(prev);
          newMap.set(path, {
            path,
            dirty: false, // 不是脏的
            cached: true, // 已缓存
            synced: true, // 已同步
            lastModified: ts,
          });
          return newMap;
        });

        // 广播状态变化事件
        const event = new CustomEvent("file-status-change", {
          detail: { path, status: "synced", timestamp: ts },
        });
        window.dispatchEvent(event);
      } catch (err) {
        // 静默处理错误
      }
    },
    [projectName]
  );

  /**
   * 标记文件为脏状态 (红色) - 用于编辑时
   * 优化版本：使用防抖来减少状态更新频率
   * @param path 文件路径
   */
  const markDirty = useCallback(
    (path: string): void => {
      if (!path) return;

      // 添加到脏文件集合
      dirtyFilesRef.current.add(path);

      // 清除现有防抖计时器
      if (statusUpdateTimerRef.current[path]) {
        clearTimeout(statusUpdateTimerRef.current[path]);
      }

      // 创建新的防抖计时器 - 250ms延迟更新状态，减少闪烁
      statusUpdateTimerRef.current[path] = setTimeout(() => {
        // 设置状态为已修改但未缓存(红色)
        setFileStatuses(prev => {
          const newMap = new Map(prev);
          const current = newMap.get(path);
          const now = Date.now();

          if (current) {
            // 只有非脏状态才更新 (避免频繁更新)
            if (!current.dirty) {
              newMap.set(path, {
                ...current,
                dirty: true, // 标记为脏
                lastModified: now,
              });

              // 只在状态真正改变时发送事件
              // 广播状态变化事件，通知编辑器组件
              const event = new CustomEvent("file-status-change", {
                detail: { path, status: "dirty", timestamp: now },
              });
              window.dispatchEvent(event);
            }
          } else {
            // 新文件
            newMap.set(path, {
              path,
              dirty: true,
              cached: false,
              synced: false,
              lastModified: now,
            });

            // 发送新文件状态变化事件
            const event = new CustomEvent("file-status-change", {
              detail: { path, status: "dirty", timestamp: now },
            });
            window.dispatchEvent(event);
          }

          return newMap;
        });

        // 清除计时器引用
        delete statusUpdateTimerRef.current[path];
      }, 250); // 250ms防抖延迟

      // 设置延迟处理 - 5秒后自动缓存
      if (cacheTimerRef.current[path]) {
        clearTimeout(cacheTimerRef.current[path]);
      }

      cacheTimerRef.current[path] = setTimeout(async () => {
        try {
          // 检查文件仍在编辑状态
          const status = fileStatuses.get(path);
          if (status && status.dirty) {
            // 通过localStorage获取内容
            const storedContent = localStorage.getItem(`current-content:${projectName}|${path}`);
            if (storedContent) {
              await cache(path, storedContent);
            }
          }
        } catch (err) {
          // 静默处理自动缓存错误
        }
        delete cacheTimerRef.current[path];
      }, 5000); // 5秒后自动缓存
    },
    [cache, fileStatuses]
  );

  /**
   * 获取当前项目中文件的缓存统计数据
   */
  const getStats = useCallback(async (): Promise<CacheStats> => {
    const dirtyFiles = Array.from(fileStatuses.values())
      .filter(status => status.dirty || (status.cached && !status.synced))
      .map(status => status.path);

    return {
      totalFiles: fileStatuses.size,
      unsyncedFiles: dirtyFiles.length,
      dirtyFiles,
    };
  }, [fileStatuses]);

  /**
   * 将所有未同步的文件刷新到服务器
   * @param syncFn 同步函数，接收路径和内容
   */
  const flush = useCallback(
    async (syncFn: (path: string, content: string) => Promise<void>): Promise<void> => {
      const filesToSync = Array.from(fileStatuses.values()).filter(
        status => status.cached && !status.synced
      );

      if (!filesToSync.length) {
        return;
      }

      for (const status of filesToSync) {
        const key = `${projectName}|${status.path}`;
        try {
          const item = await get(key);
          if (!item) continue;

          // 确保item是预期的类型
          const cacheItem = item as any;

          await syncFn(status.path, cacheItem.content);

          // 标记为已同步
          await markSynced(status.path);
        } catch (err) {
          throw err; // 传播错误
        }
      }
    },
    [projectName, fileStatuses, markSynced]
  );

  /**
   * 获取项目中所有需要处理的文件
   * @param onlyCached 是否只返回已缓存但未同步的文件（不包括脏文件）
   * @returns 文件路径数组
   */
  const getDirtyFiles = useCallback(
    (onlyCached: boolean = false): string[] => {
      if (onlyCached) {
        // 只返回已缓存但未同步的文件
        return Array.from(fileStatuses.values())
          .filter(status => status.cached && !status.synced && !status.dirty)
          .map(status => status.path);
      } else {
        // 返回所有需要处理的文件（脏文件或已缓存但未同步的文件）
        return Array.from(fileStatuses.values())
          .filter(status => status.dirty || (status.cached && !status.synced))
          .map(status => status.path);
      }
    },
    [fileStatuses]
  );

  /**
   * 是否存在任何需要处理的文件
   * @param onlyCached 是否只检查已缓存但未同步的文件
   */
  const hasDirtyFiles = useCallback(
    (onlyCached: boolean = false): boolean => {
      if (onlyCached) {
        return Array.from(fileStatuses.values()).some(
          status => status.cached && !status.synced && !status.dirty
        );
      } else {
        return Array.from(fileStatuses.values()).some(
          status => status.dirty || (status.cached && !status.synced)
        );
      }
    },
    [fileStatuses]
  );

  return {
    cache,
    load,
    markSynced,
    markDirty,
    flush,
    getStats,
    getDirtyFiles,
    hasDirtyFiles,
    fileStatuses,
  };
}
