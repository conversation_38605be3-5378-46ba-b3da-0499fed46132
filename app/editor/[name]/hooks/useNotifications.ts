import { useState, useCallback } from 'react';

export interface NotificationState {
  isOpen: boolean;
  type: 'alert' | 'confirm' | 'prompt';
  title: string;
  message: string;
  variant?: 'default' | 'destructive' | 'warning';
  defaultValue?: string;
  placeholder?: string;
  onConfirm?: (value?: string) => void;
  onCancel?: () => void;
}

/**
 * 统一的通知管理钩子
 * 用于替换原生的 alert 和 confirm 弹窗
 */
export function useNotifications() {
  const [notification, setNotification] = useState<NotificationState>({
    isOpen: false,
    type: 'alert',
    title: '',
    message: '',
    variant: 'default',
  });

  // 显示警告/错误消息
  const showAlert = useCallback((message: string, title: string = 'Notice', variant: 'default' | 'destructive' | 'warning' = 'default') => {
    setNotification({
      isOpen: true,
      type: 'alert',
      title,
      message,
      variant,
    });
  }, []);

  // 显示确认对话框
  const showConfirm = useCallback((
    message: string,
    onConfirm: () => void,
    title: string = 'Confirm',
    onCancel?: () => void
  ) => {
    setNotification({
      isOpen: true,
      type: 'confirm',
      title,
      message,
      variant: 'default',
      onConfirm,
      onCancel,
    });
  }, []);

  // 显示输入对话框
  const showPrompt = useCallback((
    message: string,
    onConfirm: (value?: string) => void,
    title: string = 'Input',
    defaultValue: string = '',
    placeholder: string = '',
    onCancel?: () => void
  ) => {
    setNotification({
      isOpen: true,
      type: 'prompt',
      title,
      message,
      variant: 'default',
      defaultValue,
      placeholder,
      onConfirm,
      onCancel,
    });
  }, []);

  // 关闭通知
  const closeNotification = useCallback(() => {
    setNotification(prev => ({ ...prev, isOpen: false }));
  }, []);

  // 处理确认
  const handleConfirm = useCallback((value?: string) => {
    if (notification.onConfirm) {
      notification.onConfirm(value);
    }
    closeNotification();
  }, [notification.onConfirm, closeNotification]);

  // 处理取消
  const handleCancel = useCallback(() => {
    if (notification.onCancel) {
      notification.onCancel();
    }
    closeNotification();
  }, [notification.onCancel, closeNotification]);

  return {
    notification,
    showAlert,
    showConfirm,
    showPrompt,
    closeNotification,
    handleConfirm,
    handleCancel,
  };
}
