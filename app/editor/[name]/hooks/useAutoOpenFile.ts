import { useEffect } from 'react';
import { TreeNode } from '../types';
import { findPaperNode } from '../utils/treeUtils';

/**
 * 自动打开文件的自定义钩子
 * 在文件树加载完成后自动打开 paper.md 文件
 */
export function useAutoOpenFile(
  tree: TreeNode[],
  activePath: string | null,
  openFile: (node: TreeNode) => Promise<void>
) {
  useEffect(() => {
    // 仅在尚未打开任何文件且树已加载时触发
    if (!activePath && tree.length) {
      const paperNode = findPaperNode(tree);
      if (paperNode) {
        (async () => {
          await openFile(paperNode);
          // 不在这里编译，避免重复编译
          // 编译交给单独的 useEffect 处理
        })();
      }
    }
  }, [tree, activePath, openFile]);
}


