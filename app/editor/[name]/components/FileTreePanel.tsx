"use client";

import {
  <PERSON><PERSON><PERSON>R<PERSON>,
  ChevronDown,
  Folder as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>older<PERSON><PERSON>,
  FileText,
  BookOpen,
  FileStack,
  Send,
  Loader2,
  Image,
} from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import yaml from "yaml";

import { SubmitDialog } from "./SubmitDialog";
import { usePreSubmitFlow } from "../hooks/usePreSubmitFlow";

import { TreeNode, FileStatus } from "@/app/editor/[name]/types";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import Link from "next/link";

interface FileTreePanelProps {
  nodes: TreeNode[];
  activePath: string | null;
  collapsed: Set<string>;
  dragTarget: string | null;
  uploading: boolean;
  uploadFileName: string;
  uploadPct: number;
  isOnline: boolean | null;
  fileStatuses: Map<string, FileStatus>;
  name: string;
  onToggleDir: (path: string) => void;
  onOpen: (node: TreeNode) => void;
  onDragTargetChange: (path: string | null) => void;
  onMoveFile: (from: string, to: string) => void;
  onContextMenu: (x: number, y: number, node: TreeNode) => void;
  onNavigate: (url: string, newTab?: boolean) => void;
  saveAllFiles: () => Promise<void>;
  compile: () => Promise<void>;
  compileWithoutSave?: () => Promise<void>;
  hasPaper: (tree: TreeNode[]) => boolean;
  getPagedHtml?: (iframe: HTMLIFrameElement | null) => Promise<string | null>;
}

export const FileTreePanel: React.FC<FileTreePanelProps> = ({
  nodes,
  activePath,
  collapsed,
  dragTarget,
  uploading,
  uploadFileName,
  uploadPct,
  isOnline,
  fileStatuses,
  name,
  onToggleDir,
  onOpen,
  onDragTargetChange,
  onMoveFile,
  onContextMenu,
  onNavigate,
  saveAllFiles,
  compile,
  compileWithoutSave,
  hasPaper,
  getPagedHtml,
}) => {
  /* 处理拖放事件 */
  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    // 重要: 不要停止传播，让页面级的drop事件处理器捕获事件
    // e.stopPropagation();

    // 通知父组件处理文件上传逻辑
    if (e.dataTransfer.files.length > 0) {
      // 关键：将拖拽的文件夹位置传递给父组件（如果有目标），否则使用根目录
      onDragTargetChange(dragTarget);
    }
  };

  // 在组件内部添加以下状态和函数
  const [submitDialogOpen, setSubmitDialogOpen] = useState(false);
  const [paperStatus, setPaperStatus] = useState<"draft" | "revision">("draft");
  const [paperId, setPaperId] = useState<string | undefined>(undefined);
  const [paperYamlContent, setPaperYamlContent] = useState<string>("");
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);

  // 预提交流程钩子
  const { flowState, executePreSubmitFlow } = usePreSubmitFlow({
    projectName: name,
    saveAllFiles,
    compile,
    compileWithoutSave,
    hasPaper,
    tree: nodes
  });

  // 从paper.md获取最新状态
  const refreshPaperStatus = async () => {
    try {
      const res = await fetch(
        `/api/editor/projects/${encodeURIComponent(name)}/file?path=paper.md`
      );

      if (!res.ok) {
        return false;
      }

      let content;
      const contentType = res.headers.get("content-type");

      if (contentType && contentType.includes("application/json")) {
        const data = await res.json();
        content = data.content;
      } else {
        content = await res.text();
      }

      const match = content.match(/^---\n([\s\S]*?)\n---/);

      if (match && match[1]) {
        try {
          const yamlContent = match[1];
          setPaperYamlContent(yamlContent); // 保存原始YAML内容

          const yamlData = yaml.parse(yamlContent);

          // 设置状态用于传递给SubmitDialog
          const status = yamlData.status || "draft";
          setPaperStatus(status);
          setPaperId(yamlData.paperID);
          return true;
        } catch (error) {
          // 静默处理YAML解析错误
        }
      }

      return false;
    } catch (error) {
      return false;
    }
  };

  // 处理提交按钮点击
  const handleSubmitClick = async () => {
    try {
      // 执行预提交流程（保存 + 编译）
      const success = await executePreSubmitFlow();

      if (!success) {
        return; // 预提交流程失败，错误已在钩子中处理
      }

      // 获取paper.md状态
      const statusSuccess = await refreshPaperStatus();

      if (statusSuccess) {
        // 确保每次打开提交对话框时是全新状态
        setSubmitDialogOpen(false);
        setTimeout(() => {
          setSubmitDialogOpen(true);
        }, 10);
      } else {
        toast.error("Cannot read paper status", {
          description: "Please ensure paper.md format is correct",
        });
      }
    } catch (error) {
      toast.error(`Pre-submit preparation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // 处理提交确认
  const handleSubmitConfirm = async () => {
    try {
      // 刷新获取最新的paper状态（不需要再次保存，预提交流程已经保存过了）
      await refreshPaperStatus();

      // 确保修订提交有正确的paperID
      if (paperStatus === "revision" && (!paperId || paperId.trim() === "")) {
        throw new Error("修订版本必须提供paperID");
      }

      // 获取分页后的HTML内容
      let compiledHtml = null;
      try {
        // 优先使用 getPagedHtml 方法获取已分页的HTML
        if (getPagedHtml) {
          const iframe = document.getElementById("preview-frame") as HTMLIFrameElement;
          if (iframe) {
            compiledHtml = await getPagedHtml(iframe);
          }
        }

        // 备用方案：获取原始编译HTML（未分页）
        if (!compiledHtml) {
          const compileRes = await fetch(`/api/editor/projects/${encodeURIComponent(name)}/compile`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              entry: "paper.md",
              forceRefresh: true,
              compileId: `submit-${Date.now()}`,
            }),
          });

          if (compileRes.ok) {
            compiledHtml = await compileRes.text();
          }
        }
      } catch (htmlErr) {
        // 静默处理HTML获取错误，继续提交
      }

      // 调用API提交项目
      const res = await fetch(`/api/editor/projects/${encodeURIComponent(name)}/submit`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ compiledHtml }),
      });

      if (!res.ok) {
        // 提取错误消息
        let errorMessage;
        try {
          const errorData = await res.json();
          errorMessage = errorData.message || errorData.error || "Submit failed, please try again later";
        } catch (e) {
          const text = await res.text();
          errorMessage = text || "Submit failed, cannot parse response";
        }

        // 抛出错误以便在SubmitDialog中处理
        throw new Error(errorMessage);
      }

      // 获取成功响应
      const data = await res.json();

      // 根据提交类型显示不同的成功消息
      const submitType = paperStatus === "revision" ? "Revision" : "Paper";
      const statusText = paperStatus === "revision" ? "REVISION_SUBMITTED" : "SUBMITTED";

      // 显示成功消息
      toast.success(`${submitType} submitted successfully!${data.paperId ? ` Paper ID: ${data.paperId}` : ""}`, {
        description:
          data.message ||
          (data.revisionFolder
            ? `Revision ${data.revisionFolder} created, status updated to ${statusText}`
            : `Paper submitted, status set to ${statusText}`),
        duration: 5000,
      });

      // 3秒后自动跳转到submit页面
      setTimeout(() => {
        // 关闭对话框
        setSubmitDialogOpen(false);
        // 跳转到submit页面，并在URL中添加参数以便自动切换到submitted标签
        router.push("/submit?tab=submitted");
      }, 3000);

      // 返回响应数据供SubmitDialog显示
      return data;
    } catch (error) {
      // 错误会在SubmitDialog中处理和显示
      throw error;
    }
  };

  // 打开paper.md文件
  const openPaperMd = useCallback(() => {
    // 先找到paper.md节点
    const findPaperMd = (nodes: TreeNode[]): TreeNode | null => {
      for (const node of nodes) {
        if (!node.isDir && node.name === "paper.md") {
          return node;
        }
        if (node.isDir && node.children) {
          const found = findPaperMd(node.children);
          if (found) return found;
        }
      }
      return null;
    };

    const paperNode = findPaperMd(nodes);
    if (paperNode) {
      onOpen(paperNode);
      setSubmitDialogOpen(false); // 关闭对话框
    } else {
      toast.error("paper.md file not found");
    }
  }, [nodes, onOpen]);

  return (
    <div
      className="flex flex-col overflow-hidden filetree-panel"
      onDragOver={e => {
        e.preventDefault();
      }}
      onDragLeave={() => onDragTargetChange(null)}
    // 只使用onDragOver和onDragLeave，不需要自己处理onDrop
    // onDrop={handleDrop}
    >
      {/* 工具栏 */}
      <div className="p-2 h-12 border-b flex items-center justify-between shrink-0 select-none editor-header">
        {/* Logo */}
        <div className="flex-shrink-0 mx-2">
          <svg
              className="h-4 w-auto fill-brand-600"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 156.7 22"
          >
            <path d="M12.53,19.74h-4.97V6.06h4.97v2.05c.74-1.5,2.36-2.42,4.42-2.42,3.74,0,4.94,2.61,4.94,5.46v8.59h-4.97v-7.27c0-1.87-.52-2.85-2.02-2.85-1.69,0-2.36,1.35-2.36,3.4v6.72Z" />
            <path d="M24.92,9.49h-1.84v-3.07l1.01-.15c1.38-.21,1.81-.95,2.15-2.21l.46-1.78h3.13v3.77h3.56v3.43h-3.56v5c0,1.17.55,1.63,1.56,1.63.61,0,1.29-.12,2.12-.46v3.22c-1.04.77-2.24,1.23-4.11,1.23-2.3,0-4.48-.95-4.48-4.17v-6.44Z" />
            <path d="M49.03,15.23c-.71,3.13-3.22,4.88-6.84,4.88-4.39,0-7.51-2.64-7.51-6.99s3.19-7.42,7.45-7.42c4.57,0,6.99,3.13,6.99,6.84v1.38h-9.6c.12,1.69,1.35,2.48,2.85,2.48,1.35,0,2.21-.46,2.67-1.53l3.99.37ZM44.4,11.52c-.06-1.23-.86-2.27-2.33-2.27-1.59,0-2.3.98-2.48,2.27h4.82Z" />
            <path d="M61.45,10.91c-.67-.4-1.41-.58-2.36-.58-1.84,0-2.97.86-2.97,3.16v6.26h-4.97V6.06h4.97v2.42c.49-1.53,1.93-2.79,3.74-2.79.83,0,1.56.25,1.9.52l-.31,4.69Z" />
            <path d="M75.78,15.72c0,.71.31,1.01.83,1.01.4,0,.71-.06,1.1-.21v2.64c-.71.49-1.69.89-3.1.89-1.69,0-3.01-.86-3.47-2.42-.8,1.59-2.36,2.42-4.54,2.42-2.67,0-4.42-1.38-4.42-3.71,0-2.64,1.9-3.77,4.88-4.29l3.83-.64v-.22c0-1.13-.52-1.81-1.72-1.81-1.1,0-1.66.71-1.87,1.72l-4.6-.31c.43-3.01,2.67-5.09,6.78-5.09,3.62,0,6.29,1.56,6.29,5.37v4.66ZM70.9,13.82l-2.02.37c-1.2.25-1.9.61-1.9,1.5,0,.71.55,1.17,1.38,1.17,1.41,0,2.55-1.04,2.55-2.82v-.21Z" />
            <path d="M85.75,5.69c4.11,0,6.41,2.67,6.75,5.95l-4.08.28c-.34-1.69-1.17-2.48-2.61-2.48s-2.55,1.01-2.55,3.47,1.04,3.47,2.55,3.47,2.27-.83,2.61-2.51l4.08.31c-.34,3.31-2.64,5.95-6.75,5.95-4.32,0-7.48-2.85-7.48-7.21s3.16-7.21,7.48-7.21Z" />
            <path d="M94.89,9.49h-1.84v-3.07l1.01-.15c1.38-.21,1.81-.95,2.15-2.21l.46-1.78h3.13v3.77h3.56v3.43h-3.56v5c0,1.17.55,1.63,1.56,1.63.61,0,1.29-.12,2.12-.46v3.22c-1.04.77-2.24,1.23-4.11,1.23-2.3,0-4.48-.95-4.48-4.17v-6.44Z" />
            <path d="M105.35.42h5.03v4.36h-5.03V.42ZM110.35,6.06v13.68h-4.97V6.06h4.97Z" />
            <path d="M116.73,19.74l-5.09-13.68h5.21l2.97,8.5h.12l2.98-8.5h4.82l-5.09,13.68h-5.92Z" />
            <path d="M142.34,15.23c-.71,3.13-3.22,4.88-6.84,4.88-4.39,0-7.51-2.64-7.51-6.99s3.19-7.42,7.45-7.42c4.57,0,6.99,3.13,6.99,6.84v1.38h-9.6c.12,1.69,1.35,2.48,2.85,2.48,1.35,0,2.21-.46,2.67-1.53l3.99.37ZM137.71,11.52c-.06-1.23-.86-2.27-2.33-2.27-1.59,0-2.3.98-2.48,2.27h4.82Z" />
            <path d="M147.81,15.23c.18,1.13,1.1,1.78,2.61,1.78,1.1,0,1.69-.4,1.69-.95,0-.52-.31-.89-1.26-1.04l-2.02-.31c-3.31-.52-5.06-1.59-5.06-4.39s2.36-4.63,6.2-4.63,6.29,1.75,6.56,4.6l-4.08.12c-.18-.98-1.04-1.59-2.58-1.59-.95,0-1.5.37-1.5.98,0,.***********.77l2.67.46c3.07.49,4.85,1.87,4.85,4.54,0,2.88-2.45,4.54-6.35,4.54s-6.59-1.5-6.87-4.82l4.32-.06Z" />
            <polygon points=".03 0 5 0 5 5.31 3.95 13.72 1.11 13.72 .03 5.31 .03 0" />
            <rect y="15.17" width="5" height="4.94" />
          </svg>
        </div>

        {/* 右侧图标按钮组 */}
        <div className="flex items-center gap-1">
          <TooltipProvider>
            {/* 文档 */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-6 w-6"
                  onClick={() => window.open("/docs", "_blank")}
                >
                  <BookOpen className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Docs</p>
              </TooltipContent>
            </Tooltip>

            {/* 提交 */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-6 w-6"
                  onClick={handleSubmitClick}
                  disabled={isSaving || flowState.isProcessing}
                >
                  {(isSaving || flowState.isProcessing) ? <Loader2 className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{flowState.isProcessing ? flowState.currentStep : "Submit"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* 文件树区域 */}
      <div className="file-tree-content">
        <FileTree
          nodes={nodes}
          onOpen={onOpen}
          activePath={activePath}
          collapsed={collapsed}
          toggleDir={onToggleDir}
          dragTarget={dragTarget}
          setDragTarget={onDragTargetChange}
          moveFile={onMoveFile}
          setMenu={onContextMenu}
          fileStatuses={fileStatuses}
        />
      </div>

      {/* 上传进度指示 */}
      {uploading && (
        <div className="p-2 text-xs border-t" style={{ color: "var(--editor-text-secondary)" }}>
          Uploading {uploadFileName}… {uploadPct}%
        </div>
      )}

      {/* 提交弹窗 */}
      <SubmitDialog
        isOpen={submitDialogOpen}
        onClose={() => setSubmitDialogOpen(false)}
        projectName={name}
        paperStatus={paperStatus}
        paperId={paperId}
        onSubmit={handleSubmitConfirm}
        onOpenPaperMd={openPaperMd}
        actualYamlContent={paperYamlContent}
      />
    </div>
  );
};

/* ---------------- 递归文件树 ---------------- */
function FileTree({
  nodes,
  onOpen,
  activePath,
  collapsed,
  toggleDir,
  dragTarget,
  setDragTarget,
  moveFile,
  setMenu,
  fileStatuses,
}: {
  nodes: TreeNode[];
  onOpen: (n: TreeNode) => void;
  activePath: string | null;
  collapsed: Set<string>;
  toggleDir: (p: string) => void;
  dragTarget: string | null;
  setDragTarget: (p: string | null) => void;
  moveFile: (from: string, toDir: string) => void;
  setMenu: (x: number, y: number, node: TreeNode) => void;
  fileStatuses: Map<string, FileStatus>;
}) {
  // 获取文件状态CSS类和点样式
  const getFileStatusStyles = (path: string) => {
    if (!fileStatuses || !fileStatuses.has(path)) {
      return { className: "", dotClassName: "" };
    }

    const status = fileStatuses.get(path);
    if (!status) return { className: "", dotClassName: "" };

    // 判断文件状态
    if (status.dirty) {
      return {
        className: "file-status-dirty",
        dotClassName: "status-dot-dirty",
      };
    } else if (status.cached && !status.synced) {
      return {
        className: "file-status-cached",
        dotClassName: "status-dot-cached",
      };
    } else if (status.cached && status.synced) {
      return {
        className: "file-status-synced",
        dotClassName: "status-dot-synced",
      };
    }

    return { className: "", dotClassName: "" };
  };

  // 判断文件类型并返回相应图标
  const getFileIcon = (fileName: string) => {
    const extension = fileName.toLowerCase().split('.').pop();

    // 图片文件类型 - 支持常见的图片格式
    const imageExtensions = [
      'png', 'jpg', 'jpeg', 'gif', 'webp', 'svg',
      'bmp', 'ico', 'tiff', 'tif', 'avif', 'heic', 'heif'
    ];

    if (imageExtensions.includes(extension || '')) {
      return (
        <Image
          className="w-4 h-4 mx-1 shrink-0"
          style={{ color: "var(--editor-image-icon, #10b981)" }}
        />
      );
    }

    // 默认文件图标
    return (
      <FileText
        className="w-4 h-4 mx-1 shrink-0"
        style={{ color: "var(--editor-file-icon)" }}
      />
    );
  };

  return (
    <ul className="text-sm select-none leading-6">
      {nodes.map(n => {
        const isDir = n.isDir;
        const depth = n.path.split("/").length; // 用于计算缩进
        const isClosed = collapsed.has(n.path);
        const isDragOver = dragTarget === n.path;
        const { className: statusClassName, dotClassName } = !isDir
          ? getFileStatusStyles(n.path)
          : { className: "", dotClassName: "" };

        /* 拖动源：文件可拖拽 */
        const onDragStart = (e: React.DragEvent) => {
          e.dataTransfer.setData("text/plain", n.path);
          e.dataTransfer.effectAllowed = "move";
        };

        /* 作为目标：文件夹可接收 */
        const onDragOver = (e: React.DragEvent) => {
          if (isDir) {
            e.preventDefault();
            setDragTarget(n.path);
          }
        };

        const onDragLeave = () => {
          if (dragTarget === n.path) setDragTarget(null);
        };

        const onDrop = (e: React.DragEvent) => {
          // 阻止默认行为但不阻止传播
          e.preventDefault();

          const from = e.dataTransfer.getData("text/plain");

          /* ---- 外部文件：标记目标并让事件继续冒泡到父组件 ---- */
          if (!from && e.dataTransfer.files.length > 0) {
            // 设置拖拽目标目录
            setDragTarget(n.path);
            // 不要调用stopPropagation，让事件继续传播到父组件
            return;
          }

          /* ---- 内部移动：这种情况才阻止传播 ---- */
          e.stopPropagation();
          moveFile(from, n.path);
          setDragTarget(null);
        };

        // 设置项目的类名
        let itemClassName = `group flex items-center cursor-pointer rounded pr-2 relative ${statusClassName} item-content`;
        if (activePath === n.path) {
          itemClassName += " active-item";
        } else if (isDragOver) {
          itemClassName += " drag-target";
        } else {
          itemClassName += " hover-item";
        }

        return (
          <li key={n.path}>
            <div
              className={itemClassName}
              style={{ paddingLeft: `${depth * 12}px` }}
              draggable={!isDir}
              onDragStart={isDir ? undefined : onDragStart}
              onDragOver={onDragOver}
              onDragLeave={onDragLeave}
              onDrop={onDrop}
              onClick={() => (isDir ? toggleDir(n.path) : onOpen(n))}
              /* ---------- 右键菜单 ---------- */
              onContextMenu={e => {
                e.preventDefault();
                setMenu(e.clientX, e.clientY, n);
              }}
            >
              {/* 折叠箭头 */}
              {isDir &&
                (isClosed ? (
                  <ChevronRight
                    className="w-4 h-4 shrink-0"
                    style={{ color: "var(--editor-arrow-icon)" }}
                  />
                ) : (
                  <ChevronDown
                    className="w-4 h-4 shrink-0"
                    style={{ color: "var(--editor-arrow-icon)" }}
                  />
                ))}

              {/* 图标 */}
              {isDir ? (
                isClosed ? (
                  <FolderIcon
                    className="w-4 h-4 mx-1 shrink-0"
                    style={{ color: "var(--editor-folder-icon)" }}
                  />
                ) : (
                  <FolderOpen
                    className="w-4 h-4 mx-1 shrink-0"
                    style={{ color: "var(--editor-folder-icon)" }}
                  />
                )
              ) : (
                getFileIcon(n.name)
              )}

              {/* 文件/目录名称 */}
              <span className="flex-1 truncate" style={!isDir ? { fontWeight: 500 } : undefined}>
                {n.name}
              </span>

              {/* 状态圆点 */}
              {!isDir && dotClassName && (
                <span className={`absolute right-2 w-2 h-2 rounded-full ${dotClassName}`} />
              )}
            </div>

            {isDir && !isClosed && n.children?.length ? (
              <div className="ml-4">
                <FileTree
                  nodes={n.children}
                  onOpen={onOpen}
                  activePath={activePath}
                  collapsed={collapsed}
                  toggleDir={toggleDir}
                  dragTarget={dragTarget}
                  setDragTarget={setDragTarget}
                  moveFile={moveFile}
                  setMenu={setMenu}
                  fileStatuses={fileStatuses}
                />
              </div>
            ) : null}
          </li>
        );
      })}
    </ul>
  );
}
