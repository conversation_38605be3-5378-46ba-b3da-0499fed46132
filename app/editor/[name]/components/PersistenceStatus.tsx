import { Link2, Link2Off } from "lucide-react";
import React from "react";

import { ProcessingState } from "../types";

interface PersistenceStatusProps {
  isOnline: boolean | null;
  lastSaved: Date | null;
  dirty: boolean;
  saving: ProcessingState;
}

/**
 * 显示文件持久化状态的组件
 * 使用React.memo优化性能，只在相关状态变化时才重渲染
 */
export const PersistenceStatus = React.memo(
  ({ isOnline, lastSaved, dirty, saving }: PersistenceStatusProps) => {
    // 格式化保存时间的辅助函数
    const formatTime = (d: Date) => {
      return d.toLocaleString(undefined, {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    };

    // 根据保存状态获取显示文本
    const getSaveStatusText = () => {
      if (saving.isProcessing) {
        return "Saving...";
      }
      if (lastSaved) {
        return `Last synced: ${formatTime(lastSaved)}`;
      }
      if (dirty) {
        return "Unsaved";
      }
      return " ";
    };

    // 根据状态获取样式
    const getStatusStyle = () => {
      if (saving.status === "error") {
        return "text-red-600 dark:text-red-400";
      }
      if (saving.isProcessing) {
        return "text-blue-600 dark:text-blue-400";
      }
      if (dirty) {
        return "text-amber-600 dark:text-amber-400";
      }
      return "text-gray-500 dark:text-gray-400";
    };

    return (
      <div className="flex items-center gap-2">
        {/* 在线状态指示器 */}
        {isOnline !== null && (
          <span title={isOnline ? "Connected" : "Offline"}>
            {isOnline ? (
              <Link2 size={20} className="text-green-600 dark:text-green-400" />
            ) : (
              <Link2Off size={20} className="text-red-600 dark:text-red-400" />
            )}
          </span>
        )}

        {/* 保存状态指示器 */}
        <span
          className={`text-xs ${getStatusStyle()} transition-colors`}
          title={saving.message || ""}
        >
          {getSaveStatusText()}
        </span>

        {/* 如果有保存错误，显示错误图标 */}
        {saving.status === "error" && (
          <span
            className="text-red-500 dark:text-red-400 cursor-help"
            title={saving.message || "Saving failed"}
          >
            ⚠️
          </span>
        )}
      </div>
    );
  },
  // 自定义比较函数，优化重渲染逻辑
  (prevProps, nextProps) => {
    // 只在这些属性变化时才重渲染
    return (
      prevProps.isOnline === nextProps.isOnline &&
      prevProps.dirty === nextProps.dirty &&
      prevProps.saving.isProcessing === nextProps.saving.isProcessing &&
      prevProps.saving.status === nextProps.saving.status &&
      // 对于lastSaved，只比较时间值是否相同
      ((prevProps.lastSaved === null && nextProps.lastSaved === null) ||
        prevProps.lastSaved?.getTime() === nextProps.lastSaved?.getTime())
    );
  }
);
