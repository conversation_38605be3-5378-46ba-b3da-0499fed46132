import React from "react";

import { MenuConfig, TreeNode, FileOperationResult } from "../types";

interface ContextMenuProps {
  menu: MenuConfig | null;
  clipboard: string | null;
  projectName: string;
  onClose: () => void;
  onRename: (node: TreeNode, newName: string) => Promise<FileOperationResult>;
  onDelete: (node: TreeNode) => Promise<FileOperationResult>;
  onCopy: (path: string) => void;
  onPaste: (targetDir: string, sourcePath: string) => Promise<FileOperationResult>;
  onDownload: (node: TreeNode) => Promise<FileOperationResult>;
  onCreateFile: (dirPath: string, fileName: string) => Promise<FileOperationResult>;
  onCreateDir: (dirPath: string, dirName: string) => Promise<FileOperationResult>;
  showAlert: (message: string, title?: string, variant?: 'default' | 'destructive' | 'warning') => void;
  showConfirm: (message: string, onConfirm: () => void, title?: string) => void;
  showPrompt: (message: string, onConfirm: (value?: string) => void, title?: string, defaultValue?: string, placeholder?: string) => void;
}

export const ContextMenu: React.FC<ContextMenuProps> = ({
  menu,
  clipboard,
  projectName,
  onClose,
  onRename,
  onDelete,
  onCopy,
  onPaste,
  onDownload,
  onCreateFile,
  onCreateDir,
  showAlert,
  showConfirm,
  showPrompt,
}) => {
  if (!menu) return null;

  const { x, y, node } = menu;
  const isDir = node.isDir;

  // 验证文件名格式
  const isValidFileName = (name: string): boolean => {
    // 不能包含以下字符: \ / : * ? " < > |
    return /^[^\\/:*?"<>|]+$/.test(name) && name.trim().length > 0;
  };

  // 重命名操作
  const handleRename = () => {
    showPrompt(
      `Please input new ${node.isDir ? 'folder' : 'file'} name:`,
      async (newName?: string) => {
        if (!newName || newName === node.name) return;

        if (!isValidFileName(newName)) {
          showAlert(
            'File name cannot contain the following characters: \\ / : * ? " < > |',
            'Invalid file name',
            'warning'
          );
          return;
        }

        await onRename(node, newName);
        onClose();
      },
      'Rename',
      node.name,
      `Input ${node.isDir ? 'folder' : 'file'} name`
    );
  };

  // 删除操作
  const handleDelete = () => {
    const confirmMessage = `Are you sure you want to delete ${node.name}${isDir ? " and all its contents" : ""}?`;
    showConfirm(
      confirmMessage,
      async () => {
        await onDelete(node);
        onClose();
      },
      'Confirm delete'
    );
  };

  // 复制操作
  const handleCopy = () => {
    onCopy(node.path);
    onClose();
  };

  // 粘贴操作
  const handlePaste = async () => {
    if (!clipboard) return;

    await onPaste(node.path, clipboard);
    onClose();
  };

  // 下载操作
  const handleDownload = async () => {
    await onDownload(node);
    onClose();
  };

  // 创建文件操作
  const handleCreateFile = () => {
    showPrompt(
      'Please input new file name (with extension):',
      async (fileName?: string) => {
        if (!fileName) return;

        if (!isValidFileName(fileName)) {
          showAlert(
            'File name cannot contain the following characters: \\ / : * ? " < > |',
            'Invalid file name',
            'warning'
          );
          return;
        }

        await onCreateFile(node.path, fileName);
        onClose();
      },
      'Create file',
      'untitled.md',
      'Input file name (e.g., document.md)'
    );
  };

  // 创建目录操作
  const handleCreateDir = () => {
    showPrompt(
      'Please input new folder name:',
      async (dirName?: string) => {
        if (!dirName) return;

        if (!isValidFileName(dirName)) {
          showAlert(
            'Folder name cannot contain the following characters: \\ / : * ? " < > |',
            'Invalid folder name',
            'warning'
          );
          return;
        }

        await onCreateDir(node.path, dirName);
        onClose();
      },
      'Create folder',
      'folder',
      'Input folder name'
    );
  };

  /* ---------- 透明遮罩，点击空白关闭 ---------- */
  return (
    <>
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 z-40" // 低于菜单 z-index
        onClick={onClose}
      />

      {/* 右键菜单主体 */}
      <div
        style={{ top: menu.y, left: menu.x }}
        className="fixed z-50 rounded shadow text-sm select-none min-w-[140px] context-menu"
      >
        {renderItems()}
      </div>
    </>
  );

  /* 将原先的 JSX 抽到函数，便于插入 */
  function renderItems() {
    const { x, y, node } = menu!;
    const isDir = node.isDir;
    return (
      <>
        {/* 重命名 */}
        <div className="px-3 py-1 cursor-pointer context-menu-item" onClick={handleRename}>
          Rename
        </div>
        {/* 删除 */}
        <div
          className="px-3 py-1 cursor-pointer context-menu-item context-menu-item-danger"
          onClick={handleDelete}
        >
          Delete
        </div>
        {/* 复制 */}
        {!isDir && (
          <div className="px-3 py-1 cursor-pointer context-menu-item" onClick={handleCopy}>
            Copy
          </div>
        )}
        {/* 粘贴 */}
        {isDir && clipboard && (
          <div className="px-3 py-1 cursor-pointer context-menu-item" onClick={handlePaste}>
            Paste
          </div>
        )}
        {/* 下载 */}
        <div className="px-3 py-1 cursor-pointer context-menu-item" onClick={handleDownload}>
          Download
        </div>
        {/* 创建文件/目录 */}
        {isDir && (
          <div className="px-3 py-1 cursor-pointer context-menu-item" onClick={handleCreateFile}>
            Create file
          </div>
        )}
        {isDir && (
          <div className="px-3 py-1 cursor-pointer context-menu-item" onClick={handleCreateDir}>
            Create folder
          </div>
        )}
      </>
    );
  }
};
