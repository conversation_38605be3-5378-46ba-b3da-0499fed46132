import dynamic from "next/dynamic";
import React, { useRef, useEffect } from "react";
// import { Button } from "@/components/ui/button"; // 删除后 Button 不再使用，可移除

const ScrollCenter = dynamic(() => import("@/components/ScrollCenter"), { ssr: false });

interface PreviewPanelProps {
  html: string;
  compileError: string | null;
  onIframeRef: (ref: HTMLIFrameElement | null) => void;
}

export const PreviewPanel: React.FC<PreviewPanelProps> = ({ html, compileError, onIframeRef }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 当iframe引用更新时，通知父组件
  useEffect(() => {
    if (iframeRef.current) {
      onIframeRef(iframeRef.current);
    }

    // 组件卸载时清除引用
    return () => {
      onIframeRef(null);
    };
  }, [onIframeRef]);

  // 监听iframe加载完成事件，确保引用传递
  const handleIframeLoad = () => {
    if (iframeRef.current) {
      onIframeRef(iframeRef.current);
    }
  };

  // 确保在DOM更新后也会检查iframe引用
  useEffect(() => {
    // 短暂延迟后再次检查iframe引用，解决可能的时序问题
    const timeoutId = setTimeout(() => {
      // 双重检查：直接从DOM获取
      if (!iframeRef.current) {
        const frameElement = document.getElementById("preview-frame") as HTMLIFrameElement;
        if (frameElement) {
          onIframeRef(frameElement);
        }
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [onIframeRef]);

  return (
    <div className="h-full overflow-hidden relative preview-panel">
      {/* 移除独立打印按钮，打印功能集成到预览页内部 */}
      <ScrollCenter />
      {compileError ? (
        <div className="p-4 whitespace-pre-wrap compile-error">
          <h3 className="text-lg font-semibold mb-2">编译错误</h3>
          <div className="p-3 rounded overflow-auto compile-error-container">{compileError}</div>
        </div>
      ) : (
        <iframe
          ref={iframeRef}
          id="preview-frame"
          title="preview"
          className="w-full h-full"
          src="/paged-preview"
          sandbox="allow-same-origin allow-scripts allow-modals allow-presentation"
          onLoad={handleIframeLoad}
        />
      )}
    </div>
  );
};
