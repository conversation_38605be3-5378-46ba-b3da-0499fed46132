import { createClient } from "@supabase/supabase-js";
import dynamic from "next/dynamic";
import { redirect } from "next/navigation";
import { Suspense } from "react";

import { getSupabaseRoute } from "@/lib/supabaseServer";
import { getSessionCompat } from "@/utils/supabase/compat";

// 创建supabase客户端，使用服务端角色权限
const serverSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  { auth: { persistSession: false } }
);

const BUCKET = "user-temp";

// 动态导入客户端组件
const ProjectEditor = dynamic(() => import("../page"), { ssr: false });

// 项目不存在提示组件
function ProjectNotFound({ projectName }: { projectName: string }) {
  return (
    <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
      <div className="p-8 bg-white rounded-lg shadow-md max-w-md w-full">
        <h2 className="text-2xl font-bold text-red-600 mb-4">Project Not Found</h2>
        <p className="text-gray-700 mb-6">Project "{projectName}" does not exist or you don't have access permission.</p>
        <p className="text-gray-500 text-sm mb-6">
          System will redirect to project list page in <span className="countdown-timer font-mono">3</span> seconds...
        </p>
        <a
          href="/submit"
          className="inline-block w-full bg-blue-600 text-white py-2 px-4 rounded text-center hover:bg-blue-700 transition-colors"
        >
          Go to Project List
        </a>
      </div>

      {/* 加入自动跳转JavaScript */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
        let count = 3;
        const timer = setInterval(() => {
          count--;
          document.querySelector('.countdown-timer').textContent = count;
          if (count <= 0) {
            clearInterval(timer);
            window.location.href = '/submit';
          }
        }, 1000);
      `,
        }}
      />
    </div>
  );
}

// 检查项目是否存在
async function checkProjectExists(uid: string, projectName: string): Promise<boolean> {
  try {
    const { data, error } = await serverSupabase.storage
      .from(BUCKET)
      .list(`${uid}/paperEditor`, { search: projectName });

    if (error) {
      console.error("[checkProjectExists] 列出目录错误:", error);
      return false;
    }

    return data?.some((i: any) => i.name === projectName && i.id === null) || false;
  } catch (error) {
    console.error("[checkProjectExists] 检查项目失败:", error);
    return false;
  }
}

// 服务器端组件入口
export default async function EditorPage({ params }: { params: { name: string } }) {
  const projectName = params.name;

  // 获取用户会话
  const supabase = await getSupabaseRoute();
  const {
    data: { session },
  } = await getSessionCompat(supabase);

  // 如果未登录，重定向到登录页
  if (!session) {
    redirect("/account?redirectTo=" + encodeURIComponent(`/editor/${projectName}`));
  }

  const uid = session.user.id;

  // 检查项目是否存在
  const exists = await checkProjectExists(uid, projectName);

  // 如果项目不存在，显示错误页
  if (!exists) {
    return <ProjectNotFound projectName={projectName} />;
  }

  // 项目存在，渲染编辑器
  return (
    <Suspense
      fallback={<div className="flex items-center justify-center h-screen">Loading editor...</div>}
    >
      <ProjectEditor />
    </Suspense>
  );
}
