"use client";

import React, { useCallback, useRef, useState } from "react";

interface CollapsedPanelHandleProps {
  position: "left" | "right";
  panelType: "filetree" | "preview";
  onExpand: () => void;
  isVisible: boolean;
}

export function CollapsedPanelHandle({
  position,
  panelType,
  onExpand,
  isVisible,
}: CollapsedPanelHandleProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number } | null>(null);
  const handleRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
  }, []);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const touch = e.touches[0];
    setIsDragging(true);
    setDragStart({ x: touch.clientX, y: touch.clientY });
  }, []);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !dragStart) return;

      const deltaX = Math.abs(e.clientX - dragStart.x);
      const deltaY = Math.abs(e.clientY - dragStart.y);

      // 如果拖动距离超过阈值，展开面板
      if (deltaX > 30 || deltaY > 30) {
        onExpand();
        setIsDragging(false);
        setDragStart(null);
      }
    },
    [isDragging, dragStart, onExpand]
  );

  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!isDragging || !dragStart) return;

      const touch = e.touches[0];
      const deltaX = Math.abs(touch.clientX - dragStart.x);
      const deltaY = Math.abs(touch.clientY - dragStart.y);

      // 如果拖动距离超过阈值，展开面板
      if (deltaX > 30 || deltaY > 30) {
        onExpand();
        setIsDragging(false);
        setDragStart(null);
      }
    },
    [isDragging, dragStart, onExpand]
  );

  const handleMouseUp = useCallback(() => {
    if (isDragging && dragStart) {
      // 如果只是点击（没有明显拖动），也展开面板
      onExpand();
    }
    setIsDragging(false);
    setDragStart(null);
  }, [isDragging, dragStart, onExpand]);

  const handleTouchEnd = useCallback(() => {
    if (isDragging && dragStart) {
      // 如果只是点击（没有明显拖动），也展开面板
      onExpand();
    }
    setIsDragging(false);
    setDragStart(null);
  }, [isDragging, dragStart, onExpand]);

  // 添加全局事件监听
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      document.addEventListener("touchmove", handleTouchMove, { passive: false });
      document.addEventListener("touchend", handleTouchEnd);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        document.removeEventListener("touchmove", handleTouchMove);
        document.removeEventListener("touchend", handleTouchEnd);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  if (!isVisible) return null;

  const getIcon = () => {
    if (panelType === "filetree") {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z" />
        </svg>
      );
    } else {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" />
        </svg>
      );
    }
  };

  const getTooltip = () => {
    return panelType === "filetree" ? "open file tree" : "open preview";
  };

  return (
    <div
      ref={handleRef}
      className={`
        collapsed-panel-handle
        ${position === "left" ? "left-handle" : "right-handle"}
        ${isDragging ? "dragging" : ""}
      `}
      style={{
        position: "fixed",
        top: "85%",
        transform: "translateY(-50%)",
        [position]: "0px",
        zIndex: 1000,
        background: "var(--editor-bg-secondary)",
        border: "1px solid var(--editor-border-color)",
        borderRadius: position === "left" ? "0 12px 12px 0" : "12px 0 0 12px",
        padding: "12px 8px",
        cursor: isDragging ? "grabbing" : "grab",
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
        transition: isDragging ? "none" : "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        color: "var(--editor-text-secondary)",
        userSelect: "none",
        touchAction: "none",
        minWidth: "24px",
        minHeight: "52px",
      }}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
      title={getTooltip()}
    >
      <div
        className="handle-content"
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: "4px",
        }}
      >
        {getIcon()}
      </div>
    </div>
  );
}
