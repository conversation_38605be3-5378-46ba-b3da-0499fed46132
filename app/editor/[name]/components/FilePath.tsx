import React, { useCallback } from "react";

interface FilePathProps {
  path: string | null;
}

/**
 * 显示当前文件路径的组件，支持复制路径功能
 */
export const FilePath = React.memo(
  ({ path }: FilePathProps) => {
    // 复制路径到剪贴板
    const copyPathToClipboard = useCallback(() => {
      if (!path) return;

      navigator.clipboard
        .writeText(path)
        .then(() => {
          // 可以添加一个简单的复制成功提示
          const originalTitle = document.title;
          document.title = "Path copied ✓";
          setTimeout(() => {
            document.title = originalTitle;
          }, 1500);
        })
        .catch(err => {
          console.error("Copy path failed:", err);
        });
    }, [path]);

    /**
     * 格式化路径显示，对长路径进行截断处理
     */
    const getFormattedPath = useCallback((fullPath: string): React.ReactNode => {
      // 如果路径很短，直接返回
      if (fullPath.length < 40) return fullPath;

      // 分割路径
      const parts = fullPath.split("/");

      // 如果只有文件名，直接返回
      if (parts.length <= 1) return fullPath;

      // 保留文件名和上一级目录，其余用...替代
      const fileName = parts.pop();
      const parentDir = parts.pop();

      return (
        <>
          <span className="text-gray-400">…/</span>
          <span className="text-gray-600">{parentDir}/</span>
          <span>{fileName}</span>
        </>
      );
    }, []);

    if (!path) {
      return (
        <span className="font-mono text-sm text-gray-400 truncate">&lt; No file selected &gt;</span>
      );
    }

    return (
      <div
        className="font-mono text-sm truncate hover:cursor-pointer"
        onClick={copyPathToClipboard}
        title={`${path} (Click to copy path)`}
      >
        {getFormattedPath(path)}
      </div>
    );
  },
  // 优化比较函数 - 只在路径变化时重新渲染
  (prevProps, nextProps) => prevProps.path === nextProps.path
);
