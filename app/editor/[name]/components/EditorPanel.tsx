import { closeBrackets, closeBracketsKeymap } from "@codemirror/autocomplete";
import { markdown, markdownLanguage } from "@codemirror/lang-markdown";
import { yamlFrontmatter } from "@codemirror/lang-yaml";
import { bracketMatching, syntaxTree } from "@codemirror/language";
import { languages } from "@codemirror/language-data";
import { search, searchKeymap, highlightSelectionMatches } from "@codemirror/search";
import { ViewUpdate } from "@codemirror/view";
import { tags as t } from "@lezer/highlight";
import { createTheme } from "@uiw/codemirror-themes";
import { EditorView, keymap, scrollPastEnd } from "@uiw/react-codemirror";
// 🆕 添加历史操作和多选区支持
import { history, historyKeymap, undo, redo } from "@codemirror/commands";
import { rectangularSelection, crosshairCursor } from "@codemirror/view";
import dynamic from "next/dynamic";
import React, { useMemo, useRef, useEffect } from "react";

import { ProcessingState } from "../types";

// 导入优化的子组件
import { ActionButtons } from "./ActionButtons";
import { PersistenceStatus } from "./PersistenceStatus";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";

// 自定义 Atom One Pro主题 - 使用CSS变量便于用户自定义
const atomOneProTheme = createTheme({
  theme: "light",
  settings: {
    background: "var(--cm-background, #ffffff)",
    foreground: "var(--cm-foreground, #383a42)",
    caret: "var(--cm-caret, #526FFF)",
    selection: "var(--cm-selection, #E5E5E6)",
    selectionMatch: "var(--cm-selectionMatch, #E5E5E6)",
    lineHighlight: "var(--cm-lineHighlight, #f1f1f1)",
    gutterBackground: "var(--cm-gutterBackground, #ffffff)",
    gutterForeground: "var(--cm-gutterForeground, #9d9d9f)",
    gutterActiveForeground: "var(--cm-gutterActiveForeground, #383a42)",
    gutterBorder: "var(--cm-gutterBorder, transparent)",
  },
  styles: [
    { tag: t.keyword, color: "var(--cm-keyword, #a626a4)" },
    { tag: t.operator, color: "var(--cm-operator, #383a42)" },
    { tag: t.special(t.variableName), color: "var(--cm-specialVariable, #e45649)" },
    { tag: t.typeName, color: "var(--cm-typeName, #c18401)" },
    { tag: t.atom, color: "var(--cm-atom, #0184bc)" },
    { tag: t.number, color: "var(--cm-number, #986801)" },
    { tag: t.definition(t.variableName), color: "var(--cm-definition, #4078f2)" },
    { tag: t.string, color: "var(--cm-string, #50a14f)" },
    { tag: t.special(t.string), color: "var(--cm-specialString, #50a14f)" },
    { tag: t.comment, color: "var(--cm-comment, #a0a1a7)" },
    { tag: t.variableName, color: "var(--cm-variableName, #383a42)" },
    { tag: t.tagName, color: "var(--cm-tagName, #e45649)" },
    { tag: t.bracket, color: "var(--cm-bracket, #383a42)" },
    { tag: t.meta, color: "var(--cm-meta, #383a42)" },
    { tag: t.attributeName, color: "var(--cm-attributeName, #986801)" },
    { tag: t.propertyName, color: "var(--cm-propertyName, #4078f2)" },
    { tag: t.className, color: "var(--cm-className, #c18401)" },
    { tag: t.invalid, color: "var(--cm-invalid, #ff0000)" },

    // 额外语法元素
    { tag: t.heading, color: "var(--cm-heading, #e45649)", fontWeight: "bold" },
    { tag: t.emphasis, fontStyle: "italic" },
    { tag: t.strong, fontWeight: "bold" },
    { tag: t.strikethrough, textDecoration: "line-through" },
    { tag: t.link, color: "var(--cm-link, #4078f2)" },
    { tag: t.url, color: "var(--cm-url, #4078f2)" },
    { tag: t.regexp, color: "var(--cm-regexp, #50a14f)" },
    { tag: t.processingInstruction, color: "var(--cm-processingInstruction, #a626a4)" },
    { tag: t.content, color: "var(--cm-content, #383a42)" },
    { tag: t.character, color: "var(--cm-character, #50a14f)" },
    {
      tag: t.inserted,
      color: "var(--cm-inserted, #50a14f)",
      backgroundColor: "var(--cm-insertedBackground, #e6ffed)",
    },
    {
      tag: t.deleted,
      color: "var(--cm-deleted, #e45649)",
      backgroundColor: "var(--cm-deletedBackground, #ffeef0)",
    },
    {
      tag: t.changed,
      color: "var(--cm-changed, #c18401)",
      backgroundColor: "var(--cm-changedBackground, #fff5b1)",
    },
    { tag: t.punctuation, color: "var(--cm-punctuation, #383a42)" },
    { tag: t.literal, color: "var(--cm-literal, #0184bc)" },
    { tag: t.unit, color: "var(--cm-unit, #986801)" },
    { tag: t.list, color: "var(--cm-list, #e45649)" },
    { tag: t.quote, color: "var(--cm-quote, #a0a1a7)" },
    { tag: t.documentMeta, color: "var(--cm-documentMeta, #a0a1a7)" },
    { tag: t.function(t.variableName), color: "var(--cm-functionName, #4078f2)" },
    { tag: t.function(t.propertyName), color: "var(--cm-methodName, #4078f2)" },
    { tag: t.controlKeyword, color: "var(--cm-controlKeyword, #a626a4)" },
    { tag: t.separator, color: "var(--cm-separator, #383a42)" },
    { tag: t.color, color: "var(--cm-colorLiteral, #986801)" },
    { tag: t.definitionKeyword, color: "var(--cm-definitionKeyword, #a626a4)" },
    { tag: t.labelName, color: "var(--cm-labelName, #e45649)" },
    { tag: t.definition(t.typeName), color: "var(--cm-typeDefinition, #c18401)" },
    { tag: t.angleBracket, color: "var(--cm-angleBracket, #383a42)" },
    { tag: t.brace, color: "var(--cm-brace, #383a42)" },
    { tag: t.squareBracket, color: "var(--cm-squareBracket, #383a42)" },
  ],
});

// 暗色模式主题 - 使用相同的CSS变量，但适用于暗色模式
const atomOneProDarkTheme = createTheme({
  theme: "dark",
  settings: {
    background: "var(--cm-background, #282c34)",
    foreground: "var(--cm-foreground, #abb2bf)",
    caret: "var(--cm-caret, #528bff)",
    selection: "var(--cm-selection, #3e4451)",
    selectionMatch: "var(--cm-selectionMatch, #3e4451)",
    lineHighlight: "var(--cm-lineHighlight, #2c313c)",
    gutterBackground: "var(--cm-gutterBackground, #282c34)",
    gutterForeground: "var(--cm-gutterForeground, #636d83)",
    gutterActiveForeground: "var(--cm-gutterActiveForeground, #abb2bf)",
    gutterBorder: "var(--cm-gutterBorder, transparent)",
  },
  styles: [
    { tag: t.keyword, color: "var(--cm-keyword, #c678dd)" },
    { tag: t.operator, color: "var(--cm-operator, #abb2bf)" },
    { tag: t.special(t.variableName), color: "var(--cm-specialVariable, #e06c75)" },
    { tag: t.typeName, color: "var(--cm-typeName, #e5c07b)" },
    { tag: t.atom, color: "var(--cm-atom, #56b6c2)" },
    { tag: t.number, color: "var(--cm-number, #d19a66)" },
    { tag: t.definition(t.variableName), color: "var(--cm-definition, #61afef)" },
    { tag: t.string, color: "var(--cm-string, #98c379)" },
    { tag: t.special(t.string), color: "var(--cm-specialString, #98c379)" },
    { tag: t.comment, color: "var(--cm-comment, #7f848e)" },
    { tag: t.variableName, color: "var(--cm-variableName, #abb2bf)" },
    { tag: t.tagName, color: "var(--cm-tagName, #e06c75)" },
    { tag: t.bracket, color: "var(--cm-bracket, #abb2bf)" },
    { tag: t.meta, color: "var(--cm-meta, #abb2bf)" },
    { tag: t.attributeName, color: "var(--cm-attributeName, #d19a66)" },
    { tag: t.propertyName, color: "var(--cm-propertyName, #61afef)" },
    { tag: t.className, color: "var(--cm-className, #e5c07b)" },
    { tag: t.invalid, color: "var(--cm-invalid, #f44747)" },

    // 额外语法元素
    { tag: t.heading, color: "var(--cm-heading, #e06c75)", fontWeight: "bold" },
    { tag: t.emphasis, fontStyle: "italic" },
    { tag: t.strong, fontWeight: "bold" },
    { tag: t.strikethrough, textDecoration: "line-through" },
    { tag: t.link, color: "var(--cm-link, #61afef)" },
    { tag: t.url, color: "var(--cm-url, #61afef)" },
    { tag: t.regexp, color: "var(--cm-regexp, #98c379)" },
    { tag: t.processingInstruction, color: "var(--cm-processingInstruction, #c678dd)" },
    { tag: t.content, color: "var(--cm-content, #abb2bf)" },
    { tag: t.character, color: "var(--cm-character, #98c379)" },
    {
      tag: t.inserted,
      color: "var(--cm-inserted, #98c379)",
      backgroundColor: "var(--cm-insertedBackground, #1e3521)",
    },
    {
      tag: t.deleted,
      color: "var(--cm-deleted, #e06c75)",
      backgroundColor: "var(--cm-deletedBackground, #3e2326)",
    },
    {
      tag: t.changed,
      color: "var(--cm-changed, #e5c07b)",
      backgroundColor: "var(--cm-changedBackground, #3a3225)",
    },
    { tag: t.punctuation, color: "var(--cm-punctuation, #abb2bf)" },
    { tag: t.literal, color: "var(--cm-literal, #56b6c2)" },
    { tag: t.unit, color: "var(--cm-unit, #d19a66)" },
    { tag: t.list, color: "var(--cm-list, #e06c75)" },
    { tag: t.quote, color: "var(--cm-quote, #7f848e)" },
    { tag: t.documentMeta, color: "var(--cm-documentMeta, #7f848e)" },
    { tag: t.function(t.variableName), color: "var(--cm-functionName, #61afef)" },
    { tag: t.function(t.propertyName), color: "var(--cm-methodName, #61afef)" },
    { tag: t.controlKeyword, color: "var(--cm-controlKeyword, #c678dd)" },
    { tag: t.separator, color: "var(--cm-separator, #abb2bf)" },
    { tag: t.color, color: "var(--cm-colorLiteral, #d19a66)" },
    { tag: t.definitionKeyword, color: "var(--cm-definitionKeyword, #c678dd)" },
    { tag: t.labelName, color: "var(--cm-labelName, #e06c75)" },
    { tag: t.definition(t.typeName), color: "var(--cm-typeDefinition, #e5c07b)" },
    { tag: t.angleBracket, color: "var(--cm-angleBracket, #abb2bf)" },
    { tag: t.brace, color: "var(--cm-brace, #abb2bf)" },
    { tag: t.squareBracket, color: "var(--cm-squareBracket, #abb2bf)" },
  ],
});

// 动态导入编辑器组件以避免SSR问题
const CodeMirror = dynamic(() => import("@uiw/react-codemirror"), { ssr: false });

interface EditorPanelProps {
  activePath: string | null;
  content: string;
  imageUrl: string;
  loadingImage: boolean;
  dirty: boolean;
  fontSize: number;
  isOnline: boolean | null;
  lastSaved: Date | null;
  saving: ProcessingState;
  compiling: ProcessingState;
  hasDirtyFiles: boolean;
  cachedFilesCount: number;
  onContentChange: (value: string) => void;
  onSave: () => Promise<void>;
  onCompile: () => Promise<void>;
  onFontSizeChange: (size: number) => void;
  mounted: boolean;
  /** 编辑器创建完成时触发，返回 CodeMirror EditorView */
  onEditorView?: (view: any) => void;
  /** 光标行变化 */
  onCursorLineChange?: (line: number) => void;
  /** 是否使用暗色主题 */
  isDarkMode?: boolean;
  /** 切换主题 */
  onToggleDarkMode?: () => void;
}

export const EditorPanel: React.FC<EditorPanelProps> = ({
  activePath,
  content,
  imageUrl,
  loadingImage,
  dirty,
  fontSize,
  isOnline,
  lastSaved,
  saving,
  compiling,
  hasDirtyFiles,
  cachedFilesCount,
  onContentChange,
  onSave,
  onCompile,
  onFontSizeChange,
  mounted,
  onEditorView,
  onCursorLineChange,
  isDarkMode = false,
  onToggleDarkMode,
}) => {
  // 保存键引用，用于恢复编辑状态
  const editorRef = useRef<any>(null);
  // 保存上一次内容，避免不必要的重渲染
  const prevContentRef = useRef(content);
  // 保存光标位置和滚动位置
  const cursorPositionRef = useRef<{ line: number; ch: number } | null>(null);
  const scrollPositionRef = useRef<{ top: number; left: number } | null>(null);

  // 当内容改变但路径没变时保存光标位置
  useEffect(() => {
    if (editorRef.current && activePath) {
      // 只在内容变化且文件路径没变时处理
      if (content !== prevContentRef.current) {
        const view = editorRef.current.view;
        if (view) {
          // 更新内容引用
          prevContentRef.current = content;

          // 如果编辑器已失去焦点，则不需要恢复光标位置
          if (!view.hasFocus) return;

          // 获取当前光标位置
          const pos = view.state.selection.main.head;
          if (pos !== undefined) {
            const line = view.state.doc.lineAt(pos);
            cursorPositionRef.current = {
              line: line.number - 1,
              ch: pos - line.from,
            };
          }

          // 获取当前滚动位置
          const scrollDOM = view.scrollDOM;
          if (scrollDOM) {
            scrollPositionRef.current = {
              top: scrollDOM.scrollTop,
              left: scrollDOM.scrollLeft,
            };
          }
        }
      }
    }
  }, [content, activePath]);

  // 恢复光标和滚动位置
  const handleEditorCreated = (editor: any) => {
    editorRef.current = editor;

    // 将 EditorView 实例回传给父组件，兼容不同版本返回值
    if (onEditorView) {
      const viewInst = editor?.view ? editor.view : editor; // 新版直接是 EditorView
      onEditorView(viewInst);

      // 🆕 暴露历史操作命令到全局，供桥接脚本使用
      const view = viewInst;
      if (view && typeof window !== 'undefined') {
        // 暴露 undo 命令
        (window as any).undo = () => {
          try {
            const result = undo(view);
            return result;
          } catch (error) {
            return false;
          }
        };

        // 暴露 redo 命令
        (window as any).redo = () => {
          try {
            const result = redo(view);
            return result;
          } catch (error) {
            return false;
          }
        };

        // 暴露语法树函数
        (window as any).syntaxTree = (state?: any) => {
          try {
            const currentState = state || view.state;
            const tree = syntaxTree(currentState);
            return tree;
          } catch (error) {
            return null;
          }
        };
      }
    }

    // 延迟执行，确保编辑器已完全加载
    setTimeout(() => {
      if (editor && editor.view && cursorPositionRef.current) {
        const view = editor.view;
        const { line, ch } = cursorPositionRef.current;

        // 获取行位置
        if (line < view.state.doc.lines) {
          const lineObj = view.state.doc.line(line + 1);
          const pos = lineObj.from + Math.min(ch, lineObj.length);

          // 设置光标位置
          const transaction = view.state.update({
            selection: { anchor: pos, head: pos },
            scrollIntoView: true,
          });
          view.dispatch(transaction);

          // 恢复滚动位置
          if (scrollPositionRef.current && view.scrollDOM) {
            view.scrollDOM.scrollTop = scrollPositionRef.current.top;
            view.scrollDOM.scrollLeft = scrollPositionRef.current.left;
          }
        }
      }
    }, 50);
  };

  // 根据文件类型选择合适的编辑器扩展
  const languageExt = useMemo(() => {
    if (!activePath) return [];

    if (activePath.endsWith(".md")) {
      const md = markdown({
        base: markdownLanguage,
        codeLanguages: languages,
        addKeymap: true,
      });

      // 使用 yamlFrontmatter 包装 Markdown，实现前置 YAML 头信息高亮
      return [yamlFrontmatter({ content: md })];
    }

    return [];
  }, [activePath]);

  // 字体大小 + 等宽字体主题（继承全局 fontSize、使用本地等宽字体）
  const fontTheme = useMemo(
    () =>
      EditorView.theme(
        {
          /* 根节点 .cm-editor 以及其后代默认继承该字体 */
          "&": {
            fontFamily: "var(--font-code), monospace !important",
          },
          /* 覆盖 CodeMirror 默认 .cm-content monospace 声明 */
          ".cm-content": {
            fontFamily: "var(--font-code), monospace !important",
          },
          /* 动态字号 */
          ".cm-scroller": {
            fontSize: `${fontSize}px`,
          },
          /* 行号栏也跟随正文 */
          ".cm-gutters": {
            fontFamily: "var(--font-code), monospace !important",
          },
        },
        { dark: isDarkMode }
      ),
    [fontSize, isDarkMode]
  );

  // 快捷键设置
  const zoomKeymap = useMemo(
    () =>
      keymap.of([
        {
          key: "Mod-=", // Ctrl+ = / Cmd+ =
          run() {
            onFontSizeChange(Math.min(fontSize + 1, 32));
            return true;
          },
        },
        {
          key: "Mod--",
          run() {
            onFontSizeChange(Math.max(fontSize - 1, 8));
            return true;
          },
        },
        {
          key: "Mod-s", // 添加保存快捷键
          run() {
            if (activePath && !saving.isProcessing) {
              onSave();
            }
            return true;
          },
        },
        {
          key: "Mod-b", // 添加编译快捷键
          run() {
            if (!compiling.isProcessing) {
              onCompile();
            }
            return true;
          },
        },
      ]),
    [
      fontSize,
      onFontSizeChange,
      activePath,
      saving.isProcessing,
      onSave,
      compiling.isProcessing,
      onCompile,
    ]
  );

  // 根据isDarkMode选择合适的主题
  const editorTheme = useMemo(() => {
    return isDarkMode ? atomOneProDarkTheme : atomOneProTheme;
  }, [isDarkMode]);

  return (
    <div className="flex flex-col overflow-hidden code-panel editor-main-panel">
      {/* 顶栏 */}
      <header className="p-2 h-12 flex gap-2 border-b items-center bg-white editor-header">
        {/* 使用状态组件 */}
        {mounted && (
          <PersistenceStatus
            isOnline={isOnline}
            lastSaved={lastSaved}
            dirty={dirty}
            saving={saving}
          />
        )}

        {/* 空白区域用于推动按钮组件到右侧 */}
        <div className="flex-1"></div>

        <TooltipProvider>
          {/* 主题切换按钮 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={() => onToggleDarkMode?.()}
                variant="ghost"
                size="icon"
                className={`h-9 w-9 ${isDarkMode ? 'text-[#FCFCFA] hover:bg-[#403E41]' : 'text-gray-700 hover:bg-gray-100'}`}
              >
                {isDarkMode ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 text-[#FFD866]"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className={`h-4 w-4 ${isDarkMode ? 'text-[#A9DC76]' : 'text-gray-700'}`}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                  </svg>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* 操作按钮组件 */}
        <ActionButtons
          activePath={activePath}
          saving={saving}
          compiling={compiling}
          hasDirtyFiles={hasDirtyFiles}
          cachedFilesCount={cachedFilesCount}
          onSave={onSave}
          onCompile={onCompile}
          isDarkMode={isDarkMode}
        />
      </header>

      {/* 编辑区域 */}
      <div className="flex-1 overflow-hidden relative">
        {loadingImage ? (
          <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
            <svg
              className="animate-spin h-5 w-5 mr-2 text-gray-400 dark:text-[#727072]"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8v4l3-3-3-3v4a12 12 0 00-9 4.5L4 12z"
              />
            </svg>
            图片加载中…
          </div>
        ) : imageUrl ? (
          /* ----------- 图片预览 ----------- */
          <img
            src={imageUrl}
            alt={activePath ?? ""}
            className="max-h-full max-w-full object-contain m-auto select-none"
          />
        ) : activePath ? (
          <CodeMirror
            basicSetup={{ highlightActiveLine: false, highlightActiveLineGutter: false }}
            key={activePath} // 每个文件使用唯一key保持独立状态
            value={content}
            height="100%"
            theme={editorTheme}
            extensions={[
              fontTheme,
              zoomKeymap,
              EditorView.lineWrapping,
              search({ top: true }),
              highlightSelectionMatches(),
              closeBrackets(),
              bracketMatching(),
              scrollPastEnd(),
              // 🆕 添加历史操作支持
              history(),
              // 🆕 添加多选区支持
              rectangularSelection(),
              crosshairCursor(),
              // 🆕 合并历史操作快捷键
              keymap.of([...searchKeymap, ...closeBracketsKeymap, ...historyKeymap]),
              ...languageExt,
              EditorView.updateListener.of((v: ViewUpdate) => {
                if (onCursorLineChange && (v.selectionSet || v.focusChanged)) {
                  const line = v.state.doc.lineAt(v.state.selection.main.head).number - 1;
                  onCursorLineChange(line);
                }
              }),
            ]}
            onChange={onContentChange}
            // 保存编辑器实例引用
            onCreateEditor={handleEditorCreated}
            // 禁用自动获取焦点，避免光标重置
            autoFocus={false}
          />
        ) : (
          <div className="h-full flex flex-col items-center justify-center text-gray-400 dark:text-[#939293]">
            <p>Click a file to edit</p>
          </div>
        )}
      </div>
    </div>
  );
};
