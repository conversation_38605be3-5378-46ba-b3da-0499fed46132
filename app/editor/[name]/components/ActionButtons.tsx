import { Save, Loader2, FileInput } from "lucide-react";
import React from "react";

import { ProcessingState } from "../types";

import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface ActionButtonsProps {
  activePath: string | null;
  saving: ProcessingState;
  compiling: ProcessingState;
  hasDirtyFiles: boolean;
  cachedFilesCount: number;
  onSave: () => Promise<void>;
  onCompile: () => Promise<void>;
  isDarkMode?: boolean;
}

/**
 * 编辑器操作按钮组件
 * 使用React.memo优化性能，减少不必要的重渲染
 */
export const ActionButtons = React.memo(
  ({
    activePath,
    saving,
    compiling,
    hasDirtyFiles,
    cachedFilesCount,
    onSave,
    onCompile,
    isDarkMode = false,
  }: ActionButtonsProps) => {
    return (
      <TooltipProvider>
        <div className="flex items-center gap-2">
          {activePath && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={onSave}
                  disabled={saving.isProcessing}
                  variant={saving.status === "error" ? "destructive" : "ghost"}
                  className={`${isDarkMode ? 'text-[#FCFCFA] hover:bg-[#403E41]' : 'text-gray-700 hover:bg-gray-100'}`}
                  size="icon"
                >
                  {saving.isProcessing ? (
                    <Loader2 className={`w-4 h-4 animate-spin ${isDarkMode ? 'text-[#C1C0C0]' : 'text-gray-600'}`} />
                  ) : (
                    <Save className={`w-4 h-4 ${isDarkMode ? 'text-[#78DCE8]' : 'text-gray-700'}`} />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Save (Ctrl+S)</p>
              </TooltipContent>
            </Tooltip>
          )}

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={onCompile}
                disabled={compiling.isProcessing || !activePath}
                variant={compiling.status === "error" ? "destructive" : "ghost"}
                className={`${isDarkMode ? 'text-[#FCFCFA] hover:bg-[#403E41]' : 'text-gray-700 hover:bg-gray-100'}`}
                size="icon"
              >
                {compiling.isProcessing ? (
                  <Loader2 className={`w-4 h-4 animate-spin ${isDarkMode ? 'text-[#C1C0C0]' : 'text-gray-600'}`} />
                ) : (
                  <FileInput className={`w-4 h-4 ${isDarkMode ? 'text-[#A9DC76]' : 'text-gray-700'}`} />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Compile (Ctrl+B)</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </TooltipProvider>
    );
  },
  (prevProps, nextProps) => {
    // 优化比较函数，减少不必要的重渲染
    return (
      prevProps.activePath === nextProps.activePath &&
      prevProps.saving.isProcessing === nextProps.saving.isProcessing &&
      prevProps.saving.status === nextProps.saving.status &&
      prevProps.compiling.isProcessing === nextProps.compiling.isProcessing &&
      prevProps.compiling.status === nextProps.compiling.status &&
      prevProps.isDarkMode === nextProps.isDarkMode
    );
  }
);
