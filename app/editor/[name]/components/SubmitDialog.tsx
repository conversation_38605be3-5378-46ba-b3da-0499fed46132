"use client";

import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>2,
  <PERSON>ert<PERSON>ircle,
  CheckCircle2,
  LoaderCircle,
  Clock,
  History,
} from "lucide-react";
import React, { useState, useEffect } from "react";
import yaml from "yaml";
import { z } from "zod";

import { isTimestampFile, findLatestFile } from "../utils/filenameUtils";

import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";

/* ──────────────── ① 常量 ──────────────── */
const MAX_WORDS = 150;

/* ──────────────── ② YAML 校验 ──────────────── */
const schema = z.object({
  title: z.string().min(2, "title is required"),
  abstract: z
    .string()
    .min(10, "abstract is too short")
    .refine(v => v.trim().split(/\s+/).length <= MAX_WORDS, {
      message: `abstract cannot be more than ${MAX_WORDS} words`,
    }),
  tags: z.array(z.string()).min(1, "tags must be at least 1 item"),
  authors: z
    .array(
      z.object({
        name: z.string().min(1, "author name is required"),
        affiliations: z.array(z.number()).optional(), // 可选的附属机构ID数组
      })
    )
    .min(1, "at least one author is required"),
  affiliations: z
    .array(
      z.object({
        id: z.number().optional(), // 组织ID，可选
        name: z.string().min(1, "organization name is required"), // 组织名称
      })
    )
    .optional(),
  videoUrl: z.string().url().optional(),
  status: z.enum(["draft", "revision"], {
    errorMap: () => ({ message: "status must be draft or revision" }),
  }),
  paperID: z.string().nullable().optional(),
  paperType: z.enum(["full", "gallery", "preprint"], {
    errorMap: () => ({ message: "paperType must be full, gallery or preprint" }),
  }),
});

type Meta = z.infer<typeof schema>;
type ErrMap = Record<string, string>;

type ValidationResult = {
  valid: boolean;
  meta: Meta | null;
  errors: ErrMap;
};

// 验证YAML内容
function validateYaml(content: string): ValidationResult {
  try {
    const obj = yaml.parse(content);
    const res = schema.safeParse(obj);

    if (!res.success) {
      const errMap: ErrMap = {};
      res.error.issues.forEach(i => {
        errMap[i.path[0] as string] = i.message;
      });
      return { valid: false, meta: obj, errors: errMap }; // 即使验证失败也返回原始对象
    }

    return { valid: true, meta: res.data, errors: {} };
  } catch (error) {
    return { valid: false, meta: null, errors: { yaml: "YAML parsing error" } };
  }
}

interface SubmitDialogProps {
  isOpen: boolean;
  onClose: () => void;
  projectName: string;
  paperStatus: "draft" | "revision";
  paperId?: string | null;
  onSubmit: () => Promise<void>;
  onOpenPaperMd?: () => void;
  actualYamlContent?: string; // 添加实际的YAML内容作为props
  userId?: string; // 用户ID，用于构建存储路径
}

export function SubmitDialog({
  isOpen,
  onClose,
  projectName,
  paperStatus,
  paperId,
  onSubmit,
  onOpenPaperMd,
  actualYamlContent,
  userId,
}: SubmitDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [response, setResponse] = useState<{
    success: boolean;
    message: string;
    paperId?: string;
    revisionFolder?: string;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [yamlContent, setYamlContent] = useState("");
  const [latestHistoryFile, setLatestHistoryFile] = useState<string | null>(null);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [historyYamlContent, setHistoryYamlContent] = useState<string | null>(null);
  const [historyMetadata, setHistoryMetadata] = useState<any>(null);
  // 添加一个用于追踪内容是否已准备好的状态
  const [contentReady, setContentReady] = useState(false);

  // 获取历史文件夹中最新的paper.md文件
  const fetchLatestHistoryPaper = async () => {
    if (!projectName || !isOpen) return;

    setIsLoadingHistory(true);
    try {
      // 构建请求路径
      const userIdParam = userId || "current"; // 如果没有传userId，则使用'current'表示当前用户
      const encodedProjectName = encodeURIComponent(projectName);
      const url = `/api/editor/projects/${encodedProjectName}/history?type=paper`;

      const response = await fetch(url);
      if (!response.ok) throw new Error("Failed to get history file list");

      const data = await response.json();
      if (!data.files || !Array.isArray(data.files) || data.files.length === 0) {
        setLatestHistoryFile(null);
        return;
      }

      // 使用findLatestFile函数找到最新的paper.md文件
      const latestPaper = findLatestFile(
        data.files.map((f: any) => f.name),
        "paper"
      );
      if (latestPaper) {
        setLatestHistoryFile(latestPaper);

        // 获取最新文件的内容
        const fileResponse = await fetch(
          `/api/editor/projects/${encodedProjectName}/file?path=history/${latestPaper}`
        );

        if (fileResponse.ok) {
          const fileContent = await fileResponse.text();
          // 解析YAML前缀，修正正则表达式
          const yamlMatch = fileContent.match(/^---(([\s\S]*?))---/);
          if (yamlMatch && yamlMatch[1]) {
            const extractedYaml = yamlMatch[1].trim();

            try {
              const parsedYaml = yaml.parse(extractedYaml);
              setHistoryMetadata(parsedYaml);
              // 直接设置YAML内容以确保立即生效
              setYamlContent(extractedYaml);
              setHistoryYamlContent(extractedYaml);
            } catch (err) {
              setHistoryMetadata(null);
            }
          } else {
            setHistoryYamlContent(null);
            setHistoryMetadata(null);
          }
        }
      } else {
        setLatestHistoryFile(null);
      }
    } catch (err) {
      setLatestHistoryFile(null);
    } finally {
      setIsLoadingHistory(false);

      // 如果没有找到历史文件，将contentReady设为true，使用默认内容
      if (!latestHistoryFile) {
        setContentReady(true);
      }
    }
  };

  // 构造当前的YAML内容用于验证
  const getCurrentYaml = () => {
    // 如果有实际的YAML内容，优先使用
    if (actualYamlContent) {
      return actualYamlContent;
    }

    // 如果有历史文件中的YAML内容，优先使用
    if (historyYamlContent) {
      return historyYamlContent;
    }

    // 否则使用示例内容作为后备
    return `
title: "Example Paper Title"
abstract: "This is a short abstract of the paper."
tags: [Research, Markdown, remark]
authors:
  - name: Author Name
status: ${paperStatus}
${paperId ? `paperID: ${paperId}` : ""}
`.trim();
  };

  // 根据paperStatus确定提交类型文本
  const getSubmitTypeText = () => {
    if (error) return "Paper";
    if (response?.revisionFolder) return "Revision";

    // 根据YAML中的status字段
    return paperStatus === "revision" ? "Revision" : "New Paper";
  };

  // 执行验证 - 确保使用最新的YAML内容，使用 useMemo 避免不必要的重新计算
  const validationResult = React.useMemo(() => {
    const currentYaml = yamlContent || getCurrentYaml();
    return validateYaml(currentYaml);
  }, [yamlContent, actualYamlContent]);

  const { valid, meta, errors } = validationResult;



  // 判断是否可以提交
  const canSubmit = valid && meta !== null && !isSubmitting && !error;

  // 检查paperID是否满足条件
  // 当paperStatus为revision时，paperID必须存在且非空
  // 当paperStatus为draft时，不需要验证paperID
  const isPaperIdValid = () => {
    if (paperStatus === "revision") {
      return !!paperId && paperId.trim() !== "";
    }
    // draft状态下paperID可以是任何值，包括null、undefined或空字符串
    return true;
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setError(null);
    setResponse(null);

    try {
      await onSubmit();

      // 假设onSubmit成功后不需要做任何处理，失败时会抛出错误
      // 实际可能需要根据项目需求调整这里的逻辑
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Error occurred during submission";

      if (errorMessage.includes("Paper ID already exists") || errorMessage.includes("Paper ID already exists")) {
        setError("Paper ID already exists, please check paperID or change status to revision");
      } else if (errorMessage.includes("Revision must provide paperID") || errorMessage.includes("Revision must provide paperID")) {
        setError("Revision must provide paperID field");
      } else if (errorMessage.includes("Specified paperID does not exist") || errorMessage.includes("Specified paperID does not exist")) {
        setError("Specified paperID does not exist, cannot submit revision");
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // 自定义关闭处理函数，确保状态重置
  const handleClose = () => {
    setError(null);
    setResponse(null);
    setIsSubmitting(false);
    onClose();
  };

  // 每次打开对话框时获取最新历史文件
  useEffect(() => {
    // 每次对话框打开时，先重置contentReady状态
    if (isOpen) {
      // 重置所有状态
      setContentReady(false);
      setError(null);
      setResponse(null);
      setIsSubmitting(false);
      setYamlContent(""); // 清空YAML内容，避免显示旧的内容

      // 立即开始加载历史文件
      fetchLatestHistoryPaper();
    }
  }, [isOpen, projectName]);

  // 当内容状态更新时，设置内容就绪状态
  useEffect(() => {
    if (isOpen) {
      if (historyYamlContent) {
        setYamlContent(historyYamlContent);
        setContentReady(true);
      } else if (!isLoadingHistory) {
        // 如果没有历史内容但已完成加载，使用默认内容
        setYamlContent(getCurrentYaml());
        setContentReady(true);
      }
    }
  }, [isOpen, historyYamlContent, isLoadingHistory, actualYamlContent]);

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && handleClose()}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
        <DialogTitle className="flex items-center gap-2 text-lg">
          Submit {getSubmitTypeText()}
          {latestHistoryFile && (
            <span className="text-xs font-normal text-gray-500 flex items-center gap-1">
              <History className="h-3 w-3" />
              Using lastest: {latestHistoryFile}
            </span>
          )}
        </DialogTitle>
        <DialogDescription className="text-sm">
          You are about to submit project "{projectName}" as {getSubmitTypeText()}.
          {paperStatus === "revision" && paperId && (
            <span className="block mt-1 text-sm font-medium">
              Submitting revision for Paper ID: {paperId}
            </span>
          )}
          {paperStatus === "draft" && (
            <span className="block mt-1 text-xs text-gray-500">
              When submitting a new paper, ensure status is "draft" in paper.md. If paperID is provided, it should be unused.
            </span>
          )}
        </DialogDescription>

        <div className="space-y-3">
          <Card className="border-gray-200 p-0">
            <CardContent className="p-4">
              {/* 验证结果 */}
              <div className="space-y-2">
                {!contentReady || isLoadingHistory ? (
                  <div className="py-6 flex flex-col items-center justify-center">
                    <LoaderCircle className="h-6 w-6 text-primary animate-spin mb-3" />
                    <p className="text-sm text-center text-gray-500">Loading latest content...</p>
                  </div>
                ) : (
                  <>
                    <div>
                      {/* title 检查 */}
                      <ValidationItem
                        valid={!errors.title}
                        label="Title"
                        error={errors.title}
                        meta={meta}
                        field="title"
                      />

                      {/* abstract 检查 */}
                      <ValidationItem
                        valid={!errors.abstract}
                        label="Abstract"
                        error={errors.abstract}
                        detail={
                          meta?.abstract
                            ? `${meta.abstract.trim().split(/\s+/).length}/${MAX_WORDS} words`
                            : undefined
                        }
                        meta={meta}
                        field="abstract"
                      />

                      {/* tags 检查 */}
                      <ValidationItem
                        valid={!errors.tags}
                        label="Tags"
                        error={errors.tags}
                        detail={meta?.tags?.length ? `${meta.tags.length} tags` : undefined}
                        meta={meta}
                        field="tags"
                      />

                      {/* authors 检查 */}
                      <ValidationItem
                        valid={!errors.authors}
                        label="Authors"
                        error={errors.authors}
                        detail={meta?.authors?.length ? `${meta.authors.length} authors` : undefined}
                        meta={meta}
                        field="authors"
                      />

                      {/* affiliations 检查 */}
                      <ValidationItem
                        valid={!errors.affiliations}
                        label="Affiliations)"
                        error={errors.affiliations}
                        detail={
                          meta?.affiliations?.length
                            ? `${meta.affiliations.length} organizations`
                            : undefined
                        }
                        meta={meta}
                        field="affiliations"
                      />

                      {/* status 检查 */}
                      <ValidationItem
                        valid={!errors.status}
                        label="Status"
                        error={errors.status}
                        detail={meta?.status}
                        meta={meta}
                        field="status"
                      />

                      {/* paperID 检查 */}
                      <ValidationItem
                        valid={paperStatus === "revision" ? isPaperIdValid() : true}
                        label="Paper ID"
                        error={
                          paperStatus === "revision" && !isPaperIdValid()
                            ? "Revision must provide paperID"
                            : undefined
                        }
                        detail={paperId || (paperStatus === "draft" ? "Optional" : "Required")}
                        meta={meta}
                        field="paperID"
                      />

                      {/* paperType 检查 */}
                      <ValidationItem
                        valid={!errors.paperType}
                        label="Paper Type"
                        error={errors.paperType}
                        detail={meta?.paperType}
                        meta={meta}
                        field="paperType"
                      />

                      {/* yaml 解析错误 */}
                      {errors.yaml && (
                        <Alert variant="destructive" className="mt-2 py-2">
                          <AlertCircle className="h-4 w-4" />
                          <AlertTitle className="text-sm">YAML Error</AlertTitle>
                          <AlertDescription className="text-sm">{errors.yaml}</AlertDescription>
                        </Alert>
                      )}
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {isSubmitting && (
            <div className="flex flex-col items-center justify-center py-3">
              <Loader2 className="h-5 w-5 text-primary animate-spin mb-2" />
              <p className="text-sm">Submitting...</p>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex">
                <AlertCircle className="h-4 w-4 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm text-red-800 font-medium">Submission Failed</p>
                  <p className="text-sm text-red-700 mt-1">{error}</p>

                  {(error.includes("Paper ID already exists") || error.includes("Paper ID already exists")) && (
                    <p className="text-xs text-red-600 mt-2">
                      Please check the paperID field in paper.md, or change status to "revision" to submit a revision.
                    </p>
                  )}

                  {(error.includes("Revision must provide paperID") || error.includes("Revision must provide paperID")) && (
                    <div className="mt-2">
                      <p className="text-xs text-red-600">
                        Revision must provide a valid paperID field in paper.md.
                      </p>
                      <p className="text-xs text-red-600 mt-1">
                        Please check the YAML header in paper.md, ensure:
                      </p>
                      <pre className="text-xs bg-red-100 p-1 mt-1 rounded overflow-auto">
                        {`---
title: Your Title
# Other fields
status: revision    # Set to revision
paperID: ${paperId || "xyz123"}    # Must have valid ID, cannot be blank or null
---`}
                      </pre>
                      <p className="text-xs text-red-600 mt-1">
                        Note: paperID cannot be null or blank, must be a valid string. If you don't see the error, ensure you completely remove the value after paperID.
                      </p>
                      <p className="text-xs text-red-600 mt-1">
                        Please save the file after modification, then try submitting again.
                      </p>
                      {onOpenPaperMd && (
                        <button
                          onClick={onOpenPaperMd}
                          className="mt-2 text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded"
                        >
                          Edit paper.md
                        </button>
                      )}
                    </div>
                  )}

                  {(error.includes("Specified paperID does not exist") || error.includes("Specified paperID does not exist")) && (
                    <p className="text-xs text-red-600 mt-2">
                      Please check if paperID is correct, ensure it corresponds to an existing paper ID.
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {response && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <div className="flex">
                <CheckCircle2 className="h-4 w-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm text-green-800 font-medium">
                    {response.revisionFolder ? "Revision Submitted Successfully" : "Paper Submitted Successfully"}
                  </p>
                  <p className="text-sm text-green-700 mt-1">{response.message}</p>
                  {response.paperId && (
                    <p className="text-xs text-green-600 mt-2">
                      Paper ID: {response.paperId}
                      {response.revisionFolder && ` (Revision Folder: ${response.revisionFolder})`}
                    </p>
                  )}
                  <p className="text-xs text-green-600 mt-1">
                    {paperStatus === "revision"
                      ? `Paper status updated to "REVISION_SUBMITTED", revision submission record added to review timeline`
                      : `Paper status set to "SUBMITTED", submission record created in review timeline`}
                  </p>
                  <p className="text-xs text-blue-600 mt-2">
                    Will automatically redirect to submission page in 3 seconds, where you can view all submitted papers
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex flex-row justify-between">
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            {response || error ? "Close" : "Cancel"}
          </Button>

          {!response && !error && (
            <Button onClick={handleSubmit} disabled={!canSubmit} className="bg-primary">
              {isSubmitting ? "Submitting..." : "Confirm Submit"}
            </Button>
          )}

          {(response || error) && onOpenPaperMd && (
            <Button
              variant="ghost"
              onClick={() => {
                setError(null);
                setResponse(null);
                onOpenPaperMd();
              }}
              className="text-blue-500 hover:text-blue-700"
            >
              Edit paper.md
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// 验证项目组件
function ValidationItem({
  valid,
  label,
  error,
  detail,
  meta,
  field,
}: {
  valid: boolean;
  label: string;
  error?: string;
  detail?: string | number;
  meta?: any;
  field?: string;
}) {
  // 根据字段类型生成预览文本
  const getPreview = () => {
    if (!meta || !field) return null;

    try {
      if (field === "title" && meta.title) {
        const words = meta.title.split(/\s+/);
        return words.length > 10 ? words.slice(0, 10).join(" ") + "..." : meta.title;
      }

      if (field === "abstract" && meta.abstract) {
        const words = meta.abstract.split(/\s+/);
        return words.length > 10 ? words.slice(0, 10).join(" ") + "..." : meta.abstract;
      }

      if (field === "tags" && Array.isArray(meta.tags)) {
        return meta.tags.slice(0, 3).join(", ") + (meta.tags.length > 3 ? "..." : "");
      }

      if (field === "authors" && Array.isArray(meta.authors)) {
        // 改进作者显示逻辑，确保显示正确的作者名称和数量
        const authorsCount = meta.authors.length;
        if (authorsCount === 0) return null;

        const names = meta.authors.map((a: any) => a.name).filter(Boolean);

        if (names.length === 0) return null;

        // 显示分3个作者，如果超过3个则添加省略号
        return names.slice(0, 3).join(", ") + (names.length > 3 ? ` and ${names.length - 3} more` : "");
      }

      if (field === "affiliations" && Array.isArray(meta.affiliations)) {
        // 类似authors，提取affiliations中的name字段
        const affiliationsCount = meta.affiliations.length;
        if (affiliationsCount === 0) return null;

        const names = meta.affiliations.map((a: any) => a.name).filter(Boolean);

        if (names.length === 0) return null;

        // 显示分3个机构，如果超过3个则添加省略号
        return names.slice(0, 3).join(", ") + (names.length > 3 ? ` and ${names.length - 3} more` : "");
      }

      if (field === "status" && meta.status) {
        return meta.status;
      }

      if (field === "paperID" && meta.paperID) {
        return meta.paperID;
      }

      if (field === "paperType" && meta.paperType) {
        switch (meta.paperType) {
          case "full":
            return "Full Paper";
          case "gallery":
            return "Gallery";
          case "preprint":
            return "Preprint";
          default:
            return meta.paperType;
        }
      }
    } catch (e) {
      return null;
    }

    return null;
  };

  const preview = getPreview();
  // 无论是否验证通过都显示预览，只要有内容
  const shouldShowPreview = preview !== null;

  return (
    <div className="flex flex-col gap-1 mb-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {valid ? (
            <div className="flex items-center justify-center w-4 h-4 rounded-full bg-green-100">
              <Check className="h-2.5 w-2.5 text-green-600" />
            </div>
          ) : (
            <div className="flex items-center justify-center w-4 h-4 rounded-full bg-red-100">
              <X className="h-2.5 w-2.5 text-red-600" />
            </div>
          )}
          <span className="text-sm font-medium">{label}</span>
          {error && <span className="text-xs text-red-500 ml-2">{error}</span>}
        </div>
        {detail && <span className="text-xs text-gray-500">{detail}</span>}
      </div>

      {/* 内容预览 - 即使验证失败也显示 */}
      {shouldShowPreview && (
        <div className="pl-6 text-xs text-gray-600 font-mono bg-gray-50 rounded px-2 py-1 overflow-hidden text-ellipsis border border-gray-100">
          {preview}
        </div>
      )}
    </div>
  );
}
