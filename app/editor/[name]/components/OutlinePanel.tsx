import React from "react";

// Markdown 标题节点类型
interface HeadingItem {
  level: number; // 1 ~ 6
  text: string; // 标题文本
  line: number; // 行号（从 0 开始）
  slug: string; // 生成的 slug，用于定位/高亮
}

interface OutlinePanelProps {
  content: string; // 当前 Markdown 内容
  activeLine: number; // 编辑器当前行号
  onJump: (line: number) => void; // 点击后跳转行
}

/**
 * 文章大纲面板 – 展示当前 Markdown 文件中的所有标题
 */
export const OutlinePanel: React.FC<OutlinePanelProps> = ({ content, activeLine, onJump }) => {
  // slugify: 生成与预览页一致的锚点 id
  const slugify = (str: string) =>
    decodeURIComponent(
      str
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, "") // 移除非字母数字/空格/连字符
        .replace(/\s+/g, "-") // 空格转-
    );

  // 解析 Markdown 标题
  const outline: HeadingItem[] = React.useMemo(() => {
    if (!content) return [];
    return content.split(/\r?\n/).flatMap((line, idx) => {
      // 捕获 #{1,6} 标题，可选自定义 {#id}
      const m = line.match(/^(#{1,6})\s+(.*?)(?:\s*\{#([^}]+)\}\s*)?$/);
      if (!m) return [];
      const text = m[2].trim();
      const customId = m[3];
      const slug = customId ? customId : slugify(text);
      return [{ level: m[1].length, text, line: idx, slug }];
    });
  }, [content]);

  // 计算当前应高亮的索引：最后一个 <= activeLine
  const activeIndex = React.useMemo(() => {
    let idx = -1;
    for (let i = 0; i < outline.length; i++) {
      if (outline[i].line <= activeLine) idx = i;
      else break;
    }
    return idx;
  }, [outline, activeLine]);

  return (
    <div className="flex flex-col h-full overflow-hidden outline-panel">
      {/* 顶栏 */}
      <div className="p-2 h-8 border-b text-xs font-semibold uppercase select-none tracking-wider editor-header">
        OUTLINE
      </div>

      {/* 列表 */}
      <div className="flex-1 overflow-auto text-sm leading-6">
        {outline.length === 0 ? (
          <div className="p-2 text-gray-400 select-none">暂无标题</div>
        ) : (
          outline.map((h, idx) => (
            <div
              key={idx}
              className={`outline-row outline-level-${h.level} ${idx === activeIndex ? "active" : ""}`}
              style={{ paddingLeft: `calc(var(--outline-indent,12px) * ${h.level} + 8px)` }}
              onClick={() => onJump(h.line)}
            >
              <span className={`outline-dot level-${h.level}`}></span>
              {h.text}
            </div>
          ))
        )}
      </div>
    </div>
  );
};
