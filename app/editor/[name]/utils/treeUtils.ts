import { TreeNode } from '../types';

/**
 * Check if there is a paper.md file in the project root
 */
export function hasPaper(nodes: TreeNode[]): boolean {
  for (const n of nodes) {
    if (!n.isDir && n.name === "paper.md") return true;
    if (n.children?.length && hasPaper(n.children)) return true;
  }
  return false;
}

/**
 * Recursively find paper.md node
 */
export function findPaperNode(nodes: TreeNode[]): TreeNode | null {
  for (const n of nodes) {
    if (!n.isDir && n.name === "paper.md") return n;
    if (n.children?.length) {
      const found = findPaperNode(n.children);
      if (found) return found;
    }
  }
  return null;
}
