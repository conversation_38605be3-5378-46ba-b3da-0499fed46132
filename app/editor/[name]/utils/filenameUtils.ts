/**
 * 文件名处理工具函数
 * 实现带时间戳的文件命名和历史记录管理
 */

// 带时间戳的文件名字段
const PAPER_FILE_PREFIX = "paper_";
const REF_FILE_PREFIX = "ref_";
const HISTORY_FOLDER = "history";
const MAX_HISTORY_FILES = 10;

/**
 * 生成带时间戳的文件名
 * @param type 文件类型 ('paper' 或 'ref')
 * @returns 完整文件名，例如 paper_250414183729.md
 */
export function generateTimestampFilename(type: "paper" | "ref"): string {
  // 生成格式为年月日时分秒的时间戳，例如 250414183729
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2); // 取年份的后两位
  const month = (now.getMonth() + 1).toString().padStart(2, "0");
  const day = now.getDate().toString().padStart(2, "0");
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");

  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`;

  if (type === "paper") {
    return `${PAPER_FILE_PREFIX}${timestamp}.md`;
  } else {
    return `${REF_FILE_PREFIX}${timestamp}.bib`;
  }
}

/**
 * 从文件名中提取时间戳
 * @param filename 文件名
 * @param type 文件类型 ('paper' 或 'ref')
 * @returns 提取出的时间戳字符串，如果无法提取则返回null
 */
export function extractTimestamp(filename: string, type: "paper" | "ref"): string | null {
  const prefix = type === "paper" ? PAPER_FILE_PREFIX : REF_FILE_PREFIX;
  const suffix = type === "paper" ? ".md" : ".bib";

  if (!filename.startsWith(prefix) || !filename.endsWith(suffix)) {
    return null;
  }

  const timestampStr = filename.substring(prefix.length, filename.length - suffix.length);

  // 检查是否是正确的时间戳格式 (12位数字)
  if (/^\d{12}$/.test(timestampStr)) {
    return timestampStr;
  }

  return null;
}

/**
 * 检查文件名是否为带时间戳的指定类型
 * @param filename 文件名
 * @param type 文件类型 ('paper' 或 'ref')
 * @returns 是否匹配
 */
export function isTimestampFile(filename: string, type: "paper" | "ref"): boolean {
  const prefix = type === "paper" ? PAPER_FILE_PREFIX : REF_FILE_PREFIX;
  const suffix = type === "paper" ? ".md" : ".bib";

  return (
    filename.startsWith(prefix) &&
    filename.endsWith(suffix) &&
    extractTimestamp(filename, type) !== null
  );
}

/**
 * 查找最新的带时间戳文件
 * @param filenames 文件名列表
 * @param type 文件类型 ('paper' 或 'ref')
 * @returns 最新文件名，如果没有找到则返回null
 */
export function findLatestFile(filenames: string[], type: "paper" | "ref"): string | null {
  const typeFiles = filenames.filter(filename => isTimestampFile(filename, type));

  if (typeFiles.length === 0) {
    return null;
  }

  return typeFiles.sort((a, b) => {
    const timestampA = extractTimestamp(a, type) || "";
    const timestampB = extractTimestamp(b, type) || "";
    return timestampB.localeCompare(timestampA); // 降序排列，字符串比较
  })[0];
}

/**
 * 获取历史文件夹中需要保留的文件
 * @param filenames 历史文件夹中的所有文件名
 * @param type 文件类型 ('paper' 或 'ref')
 * @returns 按时间戳降序排列的需要保留的文件（仅保留MAX_HISTORY_FILES个）
 */
export function getFilesToKeep(filenames: string[], type: "paper" | "ref"): string[] {
  const typeFiles = filenames.filter(filename => isTimestampFile(filename, type));

  return typeFiles
    .sort((a, b) => {
      const timestampA = extractTimestamp(a, type) || "";
      const timestampB = extractTimestamp(b, type) || "";
      return timestampB.localeCompare(timestampA); // 降序排列，字符串比较
    })
    .slice(0, MAX_HISTORY_FILES);
}

/**
 * 获取历史记录路径
 * @param basePath 基础路径
 * @returns 历史文件夹路径
 */
export function getHistoryPath(basePath: string): string {
  return `${basePath}/${HISTORY_FOLDER}`;
}

/**
 * Compatible with legacy paper.md and references.bib
 * Find paper.md or similar paper_timestamp.md files in a filename list
 * @param filenames List of filenames
 * @param legacyName Legacy filename ('paper.md' or 'references.bib')
 * @param type File type ('paper' or 'ref')
 * @returns Found filename, prioritize timestamp version, then legacy filename, return null if none found
 */
export function findFileOfType(
  filenames: string[],
  legacyName: string,
  type: "paper" | "ref"
): string | null {
  // Prioritize timestamp version
  const latest = findLatestFile(filenames, type);
  if (latest) {
    return latest;
  }

  // Find legacy filename
  if (filenames.includes(legacyName)) {
    return legacyName;
  }

  return null;
}
