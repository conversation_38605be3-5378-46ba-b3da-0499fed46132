import type { Plugin } from "unified";
import type { Root } from "hast";

/**
 * rehype 插件：在 HTML 根元素添加行号显示标记
 * 检查 file.data.showLineNumbers 标记，如果为 true，
 * 则在第一个 HTML 元素添加 data-show-line-numbers 属性
 */
const rehypeLineNumbers: Plugin<[], Root> = () => {
  return (tree, file) => {
    if (file.data?.showLineNumbers !== true) return;

    if (tree.type === 'root' && tree.children.length > 0) {
      for (const child of tree.children) {
        if (child.type === 'element') {
          if (!child.properties) child.properties = {};
          child.properties['data-show-line-numbers'] = 'true';
          break;
        }
      }
    }
  };
};

export default rehypeLineNumbers;
