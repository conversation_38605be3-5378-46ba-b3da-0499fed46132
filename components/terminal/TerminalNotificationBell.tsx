"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { Bell } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { enUS } from "date-fns/locale";
import { useSupabase } from "@/components/SupabaseProvider";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { generateNotificationUrl, type NotificationType } from "@/lib/notifications/url-generator";

interface NotificationActor {
    id: string;
    name: string | null;
    image: string | null;
}

interface NotificationData {
    id: string;
    type: string;
    paperId: string | null;
    commentId: string | null;
    preview: string | null;
    paperTitle: string | null;
    actorName: string | null;
    createdAt: string;
    actor: NotificationActor;
}

interface NotificationItem {
    id: string;
    readAt: string | null;
    notification: NotificationData;
}

interface TerminalNotificationBellProps {
    compact?: boolean;
    className?: string;
    onExecuteCommand?: (command: string, source?: string) => void;
    onCloseMenu?: () => void;
}

export function TerminalNotificationBell({
    compact = false,
    className = '',
    onExecuteCommand,
    onCloseMenu
}: TerminalNotificationBellProps) {
    const { user } = useSupabase();
    const [unreadCount, setUnreadCount] = useState(0);
    const [notifications, setNotifications] = useState<NotificationItem[]>([]);
    const [isOpen, setIsOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const hasLoadedNotifications = useRef(false);
    const scrollAreaRef = useRef<HTMLDivElement>(null);

    // 获取未读通知数量
    const fetchUnreadCount = useCallback(async () => {
        if (!user) return;

        try {
            const response = await fetch('/api/notifications', { method: 'HEAD' });
            if (response.ok) {
                const count = parseInt(response.headers.get('X-Unread-Count') || '0', 10);
                setUnreadCount(count);
            }
        } catch (error) {
            console.error("Failed to get unread notification count:", error);
        }
    }, [user]);

    // 获取通知列表（懒加载）
    const fetchNotifications = useCallback(async (page: number = 1, append: boolean = false) => {
        if (!user || (page === 1 ? loading : loadingMore)) return;

        if (page === 1) {
            setLoading(true);
        } else {
            setLoadingMore(true);
        }

        try {
            const response = await fetch(`/api/notifications?page=${page}&limit=10`);
            if (response.ok) {
                const data = await response.json();
                const newNotifications = data.notifications || [];

                if (append) {
                    // 追加新通知，避免重复
                    setNotifications(prev => {
                        const existingIds = new Set(prev.map(n => n.id));
                        const uniqueNew = newNotifications.filter((n: NotificationItem) => !existingIds.has(n.id));
                        return [...prev, ...uniqueNew];
                    });
                } else {
                    // 首次加载或刷新
                    setNotifications(newNotifications);
                }

                // 更新分页状态
                setCurrentPage(page);
                setHasMore(newNotifications.length === 10); // 如果返回的数量少于10，说明没有更多了
                hasLoadedNotifications.current = true;
            }
        } catch (error) {
            console.error("Failed to get notification list:", error);
        } finally {
            if (page === 1) {
                setLoading(false);
            } else {
                setLoadingMore(false);
            }
        }
    }, [user, loading, loadingMore]);

    // 加载更多通知
    const loadMoreNotifications = useCallback(async () => {
        if (!hasMore || loadingMore) return;

        const nextPage = currentPage + 1;
        await fetchNotifications(nextPage, true);
    }, [hasMore, loadingMore, currentPage, fetchNotifications]);

    // 处理滚动事件
    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
        const target = e.target as HTMLDivElement;
        const { scrollTop, scrollHeight, clientHeight } = target;

        // 当滚动到底部附近时（距离底部50px以内）加载更多
        if (scrollHeight - scrollTop - clientHeight < 50 && hasMore && !loadingMore) {
            loadMoreNotifications();
        }
    }, [hasMore, loadingMore, loadMoreNotifications]);

    // 标记通知为已读
    const markAsRead = useCallback(async (notificationId: string) => {
        try {
            const response = await fetch(`/api/notifications/${notificationId}/read`, {
                method: "PATCH",
            });

            if (response.ok) {
                setNotifications(prev =>
                    prev.map(notif =>
                        notif.id === notificationId
                            ? { ...notif, readAt: new Date().toISOString() }
                            : notif
                    )
                );
                setUnreadCount(prev => Math.max(0, prev - 1));
            }
        } catch (error) {
            console.error("Failed to mark notification as read:", error);
        }
    }, []);

    // 处理通知点击
    const handleNotificationClick = useCallback((notification: NotificationItem) => {
        if (!notification.readAt) {
            markAsRead(notification.id);
        }

        setIsOpen(false);

        // 根据通知类型跳转 - 使用Terminal命令系统
        if (notification.notification.paperId && onExecuteCommand) {
            try {
                // 使用统一的URL生成工具
                const notificationType = notification.notification.type as NotificationType;
                const url = generateNotificationUrl(notificationType, {
                    paperId: notification.notification.paperId,
                    commentId: notification.notification.commentId || undefined,
                });

                // 生成窗口标题
                let title = 'Notification';
                if (notificationType === 'REVIEW_UPDATE' || notificationType === 'PAPER_STATUS_CHANGE') {
                    title = 'Review';
                } else if (notificationType === 'COMMENT_ON_PAPER' || notificationType === 'REPLY_ON_COMMENT') {
                    title = 'Paper';
                }

                // 构建create_window命令
                const createWindowCommand = `create_window type=path target=${url} width=0.8 height=0.8 x=0.1 y=0.1 title=${title}`;

                // 执行命令创建浮窗
                onExecuteCommand(createWindowCommand, 'notification');

                // 🔄 执行命令后自动关闭菜单（与menu item保持一致）
                onCloseMenu?.();
            } catch (error) {
                console.error('Failed to generate notification URL:', error);
                // 回退到默认Paper页面
                if (onExecuteCommand) {
                    const fallbackCommand = `create_window type=path target=/paper/${notification.notification.paperId} width=0.8 height=0.8 x=0.1 y=0.1 title=Paper`;
                    onExecuteCommand(fallbackCommand, 'notification');

                    // 🔄 回退情况下也要关闭菜单
                    onCloseMenu?.();
                }
            }
        }
    }, [markAsRead, onExecuteCommand, onCloseMenu]);

    // 初始化和菜单打开时刷新
    useEffect(() => {
        if (user) {
            fetchUnreadCount();

            // 监听菜单打开事件
            const handleMenuOpen = () => {
                fetchUnreadCount();
                // 如果还没有加载过通知，则加载第一页
                if (!hasLoadedNotifications.current) {
                    fetchNotifications(1, false);
                }
            };

            document.addEventListener('terminal-menu-opened', handleMenuOpen);

            return () => {
                document.removeEventListener('terminal-menu-opened', handleMenuOpen);
            };
        }
    }, [user, fetchUnreadCount, fetchNotifications]);

    // 重置状态当用户变化时
    useEffect(() => {
        if (!user) {
            setNotifications([]);
            setCurrentPage(1);
            setHasMore(true);
            hasLoadedNotifications.current = false;
        }
    }, [user]);



    // 格式化时间
    const formatTime = (dateString: string) => {
        try {
            const date = new Date(dateString);
            return formatDistanceToNow(date, { addSuffix: true, locale: enUS });
        } catch (error) {
            return 'Unknown time';
        }
    };

    if (!user) {
        return null;
    }

    if (compact) {
        return (
            <Popover open={isOpen} onOpenChange={setIsOpen}>
                <PopoverTrigger asChild>
                    <button
                        className={`relative p-1 rounded-full hover:bg-gray-100 transition-colors ${className}`}
                        aria-label={`Notification${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
                        onClick={() => {
                            if (!hasLoadedNotifications.current) {
                                fetchNotifications(1, false);
                            }
                        }}
                    >
                        <Bell className="h-4 w-4 text-gray-600" />
                        {unreadCount > 0 && (
                            <span className="absolute -top-1 -right-1 bg-black text-white text-xs rounded-full h-4 w-4 flex items-center justify-center min-w-[16px] text-[10px]">
                                {unreadCount > 9 ? '9+' : unreadCount}
                            </span>
                        )}
                    </button>
                </PopoverTrigger>

                <PopoverContent
                    className="w-80 max-w-80 p-0 rounded-none z-[1100]"
                    align="end"
                    side="left"
                    sideOffset={8}
                >
                    <style>{`
                        [data-radix-scroll-area-viewport] > div {
                            table-layout: fixed !important;
                            width: 100% !important;
                        }
                    `}</style>
                    <div className="p-3 border-b border-gray-100">
                        <h3 className="font-medium text-gray-900">Notification</h3>
                    </div>

                    <ScrollArea className="h-96 max-h-[70vh]">
                        <div
                            className="p-2"
                            ref={scrollAreaRef}
                            onScroll={handleScroll}
                        >
                            {loading ? (
                                <div className="text-center py-8 text-gray-500 text-sm">
                                    <div className="flex items-center justify-center space-x-2">
                                        <div className="w-4 h-4 bg-gray-300 rounded-full animate-pulse"></div>
                                        <span>Loading...</span>
                                    </div>
                                </div>
                            ) : notifications.length === 0 ? (
                                <div className="text-center py-8 text-gray-500 text-sm">
                                    <div className="space-y-2">
                                        <Bell className="h-8 w-8 text-gray-300 mx-auto" />
                                        <p>No notification</p>
                                    </div>
                                </div>
                            ) : (
                                <>
                                    {notifications.map((item) => (
                                        <div
                                            key={item.id}
                                            className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors border-l-2 ${!item.readAt
                                                ? 'bg-gray-50 border-brand-600'
                                                : 'bg-white border-l-transparent hover:border-l-gray-200'
                                                }`}
                                            onClick={() => handleNotificationClick(item)}
                                        >
                                            <div className="flex items-start space-x-2">
                                                <div className="flex-1 min-w-0">
                                                    <p className="text-sm font-medium text-gray-900 truncate">
                                                        {item.notification.preview || 'New notification'}
                                                    </p>
                                                    <p className="text-xs text-gray-500 mt-1 truncate">
                                                        {formatTime(item.notification.createdAt)}
                                                    </p>
                                                </div>
                                                {!item.readAt && (
                                                    <div className="w-2 h-2 bg-brand-500 rounded-full flex-shrink-0 mt-1"></div>
                                                )}
                                            </div>
                                        </div>
                                    ))}

                                    {/* 加载更多指示器 */}
                                    {loadingMore && (
                                        <div className="text-center py-4 text-gray-500 text-sm">
                                            <div className="flex items-center justify-center space-x-2">
                                                <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse"></div>
                                                <span>Loading more...</span>
                                            </div>
                                        </div>
                                    )}

                                    {/* 没有更多数据提示 */}
                                    {!hasMore && notifications.length > 0 && (
                                        <div className="text-center py-4 text-gray-400 text-xs">
                                            All notifications loaded
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    </ScrollArea>
                </PopoverContent>
            </Popover>
        );
    }

    return (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
                <button
                    className={`relative p-1 rounded-full hover:bg-gray-100 transition-colors ${className}`}
                    aria-label={`Notification${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
                    onClick={() => {
                        if (!hasLoadedNotifications.current) {
                            fetchNotifications(1, false);
                        }
                    }}
                >
                    <Bell className="h-4 w-4 text-gray-600" />
                    {unreadCount > 0 && (
                        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center min-w-[16px] text-[10px]">
                            {unreadCount > 9 ? '9+' : unreadCount}
                        </span>
                    )}
                </button>
            </PopoverTrigger>

            <PopoverContent
                className="w-80 max-w-80 p-0 z-[1100]"
                align="end"
                side="left"
                sideOffset={8}
            >
                <style>{`
                    [data-radix-scroll-area-viewport] > div {
                        table-layout: fixed !important;
                        width: 100% !important;
                    }
                `}</style>
                <div className="p-3 border-b border-gray-100">
                    <h3 className="font-medium text-gray-900">Notification</h3>
                </div>

                <ScrollArea className="h-96 max-h-[70vh]">
                    <div
                        className="p-2"
                        onScroll={handleScroll}
                    >
                        {loading ? (
                            <div className="text-center py-8 text-gray-500 text-sm">
                                <div className="flex items-center justify-center space-x-2">
                                    <div className="w-4 h-4 bg-gray-300 rounded-full animate-pulse"></div>
                                    <span>Loading...</span>
                                </div>
                            </div>
                        ) : notifications.length === 0 ? (
                            <div className="text-center py-8 text-gray-500 text-sm">
                                <div className="space-y-2">
                                    <Bell className="h-8 w-8 text-gray-300 mx-auto" />
                                    <p>No notification</p>
                                </div>
                            </div>
                        ) : (
                            <>
                                {notifications.map((item) => (
                                    <div
                                        key={item.id}
                                        className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors border-l-2 ${!item.readAt
                                            ? 'bg-gray-50 border-brand-600'
                                            : 'bg-white border-l-transparent hover:border-l-gray-200'
                                            }`}
                                        onClick={() => handleNotificationClick(item)}
                                    >
                                        <div className="flex items-start space-x-2">
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium text-gray-900 truncate">
                                                    {item.notification.preview || 'New notification'}
                                                </p>
                                                <p className="text-xs text-gray-500 mt-1 truncate">
                                                    {formatTime(item.notification.createdAt)}
                                                </p>
                                            </div>
                                            {!item.readAt && (
                                                <div className="w-2 h-2 bg-brand-600 rounded-full flex-shrink-0 mt-1"></div>
                                            )}
                                        </div>
                                    </div>
                                ))}

                                {/* 加载更多指示器 */}
                                {loadingMore && (
                                    <div className="text-center py-4 text-gray-500 text-sm">
                                        <div className="flex items-center justify-center space-x-2">
                                            <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse"></div>
                                            <span>Loading more...</span>
                                        </div>
                                    </div>
                                )}

                                {/* 没有更多数据提示 */}
                                {!hasMore && notifications.length > 0 && (
                                    <div className="text-center py-4 text-gray-400 text-xs">
                                        All notifications loaded
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </ScrollArea>
            </PopoverContent>
        </Popover>
    );
}
