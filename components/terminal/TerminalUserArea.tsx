"use client";

import React, { useCallback, useState, useEffect } from 'react';
import { useSupabase } from '@/components/SupabaseProvider';
import { UserAvatar } from './UserAvatar';
import { AIQuotaDisplay } from './AIQuotaDisplay';
import { TerminalNotificationBell } from './TerminalNotificationBell';
import { logoutManager } from '@/lib/auth/logout-manager';

interface TerminalUserAreaProps {
    onExecuteCommand: (command: string, source?: string) => void;
    className?: string;
    onCloseMenu?: () => void;
}

export function TerminalUserArea({
    onExecuteCommand,
    className = '',
    onCloseMenu
}: TerminalUserAreaProps) {
    const { user, supabase } = useSupabase();
    const [isLoggingOut, setIsLoggingOut] = useState(false);

    // 监听用户状态变化，重置登出状态
    useEffect(() => {
        if (!user) {
            setIsLoggingOut(false);
        }
    }, [user]);

    // 监听认证状态变化事件，确保UI及时更新
    useEffect(() => {
        const handleAuthStateChange = (event: CustomEvent) => {
            const { event: authEvent, user: eventUser } = event.detail;

            if (authEvent === 'SIGNED_OUT' || !eventUser) {
                setIsLoggingOut(false);
            }
        };

        window.addEventListener('auth-state-changed', handleAuthStateChange as EventListener);

        return () => {
            window.removeEventListener('auth-state-changed', handleAuthStateChange as EventListener);
        };
    }, []);

    // 处理登录按钮点击
    const handleLoginClick = useCallback(() => {
        // 打开账户窗口
        const createWindowCommand = 'create_window type=path target=/account width=0.5 height=0.5 x=0.25 y=0.25 title=ACCOUNT';
        onExecuteCommand(createWindowCommand, 'user-area');

        // 关闭菜单
        const toggleMenuCommand = 'menu action=toggle';
        onExecuteCommand(toggleMenuCommand, 'user-area');
    }, [onExecuteCommand]);

    // 强制登出函数 - 使用统一的LogoutManager
    const forceLogout = useCallback(async () => {
        await logoutManager.logout(supabase, { force: true });
    }, [supabase]);

    // 处理登出 - 使用统一的LogoutManager
    const handleLogout = useCallback(async (e: React.MouseEvent) => {
        e.preventDefault();

        if (isLoggingOut || logoutManager.isLoggingOutNow()) {
            return;
        }

        setIsLoggingOut(true);

        try {
            const result = await logoutManager.logout(supabase, {
                timeout: 3000,
                clearStorage: true
            });

            if (!result.success) {
                console.warn('Logout failed:', result.error);
            }
        } catch (error) {
            console.error('Logout error:', error);
            // 出错时执行强制登出
            await forceLogout();
        } finally {
            // 延迟重置登出状态，确保UI有足够时间更新
            setTimeout(() => {
                setIsLoggingOut(false);
            }, 500);
        }
    }, [supabase, isLoggingOut, forceLogout]);

    // 如果用户未登录，显示游客界面
    if (!user) {
        // 未登录状态
        return (
            <div className={`bg-gray-50 border-t border-gray-200 p-4 space-y-3 ${className}`}>
                {/* 游客AI配额显示 */}
                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Guest</span>
                    <AIQuotaDisplay />
                </div>

                {/* 登录按钮 */}
                <button
                    onClick={handleLoginClick}
                    disabled={isLoggingOut}
                    className={`w-full flex items-center justify-center space-x-2 py-2 px-4 rounded-lg text-sm font-medium transition-colors ${isLoggingOut
                        ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                        : 'bg-black hover:bg-gray-700 text-white'
                        }`}
                >
                    <span>{isLoggingOut ? 'Logout...' : 'Login'}</span>
                </button>
            </div>
        );
    }

    // 已登录状态
    return (
        <div className={`bg-white border-t border-gray-200 p-4 space-y-4 ${className}`}>
            {/* 用户信息区域 */}
            <div className="flex items-center space-x-3">
                <UserAvatar size="md" />
                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-900 truncate">
                            {user.user_metadata?.name || user.email?.split('@')[0] || 'User'}
                        </span>
                        <TerminalNotificationBell compact onExecuteCommand={onExecuteCommand} onCloseMenu={onCloseMenu} />
                    </div>
                    <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-500 truncate">
                            {user.email}
                        </span>
                        <AIQuotaDisplay />
                    </div>
                </div>
            </div>

            {/* 登出按钮 */}
            <button
                onClick={handleLogout}
                disabled={isLoggingOut}
                className={`w-full flex items-center justify-center space-x-2 py-2 px-4 rounded-lg text-sm font-medium transition-colors ${isLoggingOut
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    }`}
            >
                <span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
            </button>
        </div>
    );
}
