"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useSupabase } from '@/components/SupabaseProvider';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';

interface AIQuotaData {
    remaining: number;
    total: number;
    model: string;
    lastRequestTime?: string;
}

interface AIQuotaDisplayProps {
    className?: string;
}

export function AIQuotaDisplay({
    className = ''
}: AIQuotaDisplayProps) {
    const { user } = useSupabase();
    const [quotaData, setQuotaData] = useState<AIQuotaData | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // 获取AI配额数据
    const fetchQuotaData = useCallback(async () => {
        if (loading) return;

        setLoading(true);
        setError(null);

        try {
            const endpoint = user ? '/api/me/ai-quota' : '/api/guest/ai-quota';
            const response = await fetch(endpoint);

            if (!response.ok) {
                // 如果是401错误且用户状态为已登录，说明token已失效，静默处理
                if (response.status === 401 && user) {
                    // 使用游客API重试
                    const guestResponse = await fetch('/api/guest/ai-quota');
                    if (guestResponse.ok) {
                        const guestData = await guestResponse.json();
                        setQuotaData({
                            remaining: guestData.remaining || 0,
                            total: guestData.total || 0,
                            model: 'deepseek-chat'
                        });
                        return;
                    }
                }
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            if (user) {
                // 用户配额数据
                setQuotaData({
                    remaining: data.remaining || 0,
                    total: data.total || 0,
                    model: data.model || 'deepseek-chat',
                    lastRequestTime: data.lastRequestTime
                });
            } else {
                // 游客配额数据
                setQuotaData({
                    remaining: data.remaining || 0,
                    total: data.total || 0,
                    model: 'deepseek-chat'
                });
            }
        } catch (err) {
            console.error('Failed to get AI quota:', err);
            setError('Failed to get quota');
            // 设置默认值避免显示错误
            setQuotaData({
                remaining: 0,
                total: user ? 100 : 10,
                model: 'deepseek-chat'
            });
        } finally {
            setLoading(false);
        }
    }, [user]);

    // 监听菜单打开事件和用户状态变化进行刷新
    useEffect(() => {
        const handleMenuOpen = () => {
            fetchQuotaData();
        };

        // 监听菜单打开事件
        document.addEventListener('terminal-menu-opened', handleMenuOpen);

        // 初始加载
        fetchQuotaData();

        return () => {
            document.removeEventListener('terminal-menu-opened', handleMenuOpen);
        };
    }, [fetchQuotaData]);

    // 监听用户状态变化，立即刷新配额
    useEffect(() => {
        fetchQuotaData();
    }, [user, fetchQuotaData]);

    // 获取配额状态样式（基于剩余数量）
    const getQuotaVariant = () => {
        if (!quotaData) return 'secondary';
        if (quotaData.remaining >= 10) return 'default'; // 黑色
        return 'destructive'; // 红色
    };

    if (loading && !quotaData) {
        return (
            <div className={`flex items-center ${className}`}>
                <div className="animate-pulse flex items-center space-x-1">
                    <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                    <div className="w-8 h-3 bg-gray-300 rounded"></div>
                </div>
            </div>
        );
    }

    if (!quotaData) {
        return null;
    }

    return (
        <TooltipProvider delayDuration={300}>
            <Tooltip>
                <TooltipTrigger asChild>
                    <Badge
                        variant={getQuotaVariant()}
                        className={`cursor-default ${className}`}
                    >
                        AI : {quotaData.remaining}
                    </Badge>
                </TooltipTrigger>
                <TooltipContent className="z-[1002]">
                    <p>AI Requests Remaining</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
}
