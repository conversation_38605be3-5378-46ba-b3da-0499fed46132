"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useSupabase } from '@/components/SupabaseProvider';
import { getUserColorSync, getUserColorAsync, DEFAULT_AVATAR_COLOR } from '@/lib/userColorManager';

interface UserAvatarProps {
    /** 头像大小 */
    size?: 'sm' | 'md' | 'lg';
    /** 是否显示用户名 */
    showName?: boolean;
    /** 额外的CSS类名 */
    className?: string;
}

export function UserAvatar({
    size = 'md',
    showName = false,
    className = ''
}: UserAvatarProps) {
    const { user } = useSupabase();
    const [avatarColor, setAvatarColor] = useState<string>(DEFAULT_AVATAR_COLOR);

    // 获取用户颜色
    useEffect(() => {
        if (!user?.id) {
            setAvatarColor(DEFAULT_AVATAR_COLOR);
            return;
        }

        // 先使用同步方法获取颜色（从缓存或默认颜色）
        const syncColor = getUserColorSync(user.id);
        setAvatarColor(syncColor);

        // 异步获取最新颜色（从数据库）
        getUserColorAsync(user.id)
            .then(asyncColor => {
                if (asyncColor !== syncColor) {
                    setAvatarColor(asyncColor);
                }
            })
            .catch(error => {
                // 静默处理错误，保持当前颜色
                if (process.env.NODE_ENV === 'development') {
                    console.error('获取用户颜色失败:', error);
                }
            });
    }, [user?.id]);

    // 用户信息的记忆化计算
    const userInfo = useMemo(() => {
        if (!user) return null;

        return {
            name: user.user_metadata?.name || user.email?.split('@')[0] || '用户',
            email: user.email || '',
            avatarUrl: user.user_metadata?.avatar_url
        };
    }, [user]);

    // 获取用户名首字母的记忆化函数
    const getInitials = useCallback((name: string): string => {
        if (!name) return '?';
        const words = name.trim().split(' ');
        if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }
        return words.slice(0, 2).map(word => word.charAt(0).toUpperCase()).join('');
    }, []);

    if (!user || !userInfo) {
        return null;
    }

    // 尺寸配置的记忆化计算
    const sizeConfig = useMemo(() => {
        const configs = {
            sm: { avatar: 'h-6 w-6', text: 'text-xs' },
            md: { avatar: 'h-8 w-8', text: 'text-sm' },
            lg: { avatar: 'h-10 w-10', text: 'text-base' }
        };
        return configs[size] || configs.md;
    }, [size]);

    return (
        <div className={`flex items-center space-x-2 ${className}`}>
            <Avatar className={sizeConfig.avatar}>
                <AvatarImage
                    src={userInfo.avatarUrl || undefined}
                    alt={userInfo.name}
                />
                <AvatarFallback
                    style={{
                        backgroundColor: avatarColor,
                        color: '#ffffff'
                    }}
                    className={sizeConfig.text}
                >
                    {getInitials(userInfo.name)}
                </AvatarFallback>
            </Avatar>

            {showName && (
                <div className="flex flex-col min-w-0">
                    <span className={`font-medium text-gray-900 truncate ${sizeConfig.text}`}>
                        {userInfo.name}
                    </span>
                    {size !== 'sm' && (
                        <span className="text-xs text-gray-500 truncate">
                            {userInfo.email}
                        </span>
                    )}
                </div>
            )}
        </div>
    );
}
