"use client";

import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import { formatDate, BlogPost } from "@/app/(site)/news/utils";
import { Skeleton } from "@/components/ui/skeleton";

// 懒加载新闻项组件（复制LazyNewsItem的逻辑）
function NewsItem({ post, delay = 0 }: { post: BlogPost; delay?: number }) {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
      setShouldRender(true);
    }, delay);
    return () => clearTimeout(timer);
  }, [delay]);

  if (!shouldRender) {
    return (
      <div className="animate-pulse h-32 sm:h-36 md:h-40">
        <div className="flex h-full">
          <div className="h-full aspect-square flex-shrink-0">
            <Skeleton className="w-full h-full" />
          </div>
          <div className="flex-1 p-2 sm:p-3 md:p-4 space-y-1 sm:space-y-2 flex flex-col justify-center">
            <div className="space-y-1">
              <Skeleton className="h-4 sm:h-5 md:h-6 w-full" />
              <Skeleton className="h-4 sm:h-5 md:h-6 w-3/4" />
            </div>
            <div className="space-y-1">
              <Skeleton className="h-3 sm:h-3 md:h-4 w-full" />
              <Skeleton className="h-3 sm:h-3 md:h-4 w-5/6" />
            </div>
            <div className="flex gap-1 sm:gap-2">
              <Skeleton className="h-4 sm:h-5 w-12 sm:w-16 rounded-full" />
              <Skeleton className="h-4 sm:h-5 w-16 sm:w-20 rounded-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  const imagePath = post.metadata.image ? `/news/images/${post.metadata.image}` : null;

  return (
    <div className="animate-in fade-in-0 duration-300 overflow-hidden h-32 sm:h-36 md:h-40">
      <Link href={`/news/${post.slug}`} className="block group h-full">
        <div className="flex h-full hover:bg-gray-50 transition-colors">
          <div className="h-full aspect-square flex-shrink-0 relative overflow-hidden">
            {imagePath && !imageError ? (
              <Image
                src={imagePath}
                alt={post.metadata.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                onError={() => setImageError(true)}
                sizes="(max-width: 640px) 64px, (max-width: 768px) 80px, (max-width: 1024px) 96px, 112px"
              />
            ) : (
              <div className="w-full h-full bg-brand-600 flex items-center justify-center">
                <svg
                  className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 text-white opacity-60"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
                  />
                </svg>
              </div>
            )}
          </div>

          <div className="flex-1 min-w-0 p-2 sm:p-3 md:p-4 flex flex-col justify-center">
            <div className="space-y-1 sm:space-y-2">
              <h3 className="text-sm sm:text-lg md:text-xl font-medium text-black group-hover:text-blue-600 transition-colors leading-tight">
                {post.metadata.title}
              </h3>

              <div className="flex flex-wrap gap-1 sm:gap-2 text-xs">
                {post.metadata.category && (
                  <span className="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-100 text-gray-600 rounded-full font-medium text-xs">
                    {post.metadata.category}
                  </span>
                )}
                <span className="inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 bg-gray-50 text-gray-500 rounded-full text-xs">
                  {formatDate(post.metadata.publishedAt, false)}
                </span>
              </div>

              <p className="text-xs sm:text-sm md:text-base text-gray-700 leading-relaxed line-clamp-2">
                {post.metadata.summary}
              </p>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
}

// 原客户端组件 - 保留以便通过API获取
export function BlogPosts() {
  const [allBlogs, setAllBlogs] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState("All");

  useEffect(() => {
    async function fetchPosts() {
      try {
        const response = await fetch("/api/posts");
        const data = await response.json();
        if (data.posts) {
          setAllBlogs(data.posts);
        }
      } catch (error) {
        console.error("Error fetching blog posts:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchPosts();
  }, []);

  // 从文章元数据中获取所有唯一分类
  const categories = [
    "All",
    ...new Set(allBlogs.map(post => post.metadata.category || "Uncategorized")),
  ];

  // 根据当前筛选器过滤博客文章
  const filteredBlogs =
    filter === "All" ? allBlogs : allBlogs.filter(post => post.metadata.category === filter);

  // 按发布时间排序
  const sortedBlogs = filteredBlogs.sort((a, b) => {
    if (new Date(a.metadata.publishedAt) > new Date(b.metadata.publishedAt)) {
      return -1;
    }
    return 1;
  });

  if (loading) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-sm sm:text-base">Loading...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* 筛选器 */}
      <div className="flex justify-between items-center border-b pb-3">
        <div className="flex flex-wrap gap-3 sm:gap-6">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setFilter(category)}
              className={`text-sm sm:text-base hover:text-blue-600 transition-colors ${filter === category
                ? "text-black font-medium"
                : "text-gray-500"
                }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* 文章计数 */}
        <div className="text-xs sm:text-sm text-gray-500">
          {sortedBlogs.length} article{sortedBlogs.length !== 1 ? "s" : ""}
        </div>
      </div>

      {/* 新闻列表 */}
      <div className="space-y-4 sm:space-y-6">
        {sortedBlogs.map((post, index) => (
          <div
            key={post.slug}
            className="border-b border-gray-100 last:border-b-0"
          >
            <NewsItem
              post={post}
              delay={index * 50} // 每个项目延迟50ms
            />
          </div>
        ))}
      </div>

      {sortedBlogs.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-sm sm:text-base">
            No articles found
          </p>
        </div>
      )}
    </div>
  );
}
