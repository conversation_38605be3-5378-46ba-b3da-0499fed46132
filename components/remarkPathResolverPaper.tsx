import type { Root, Image, Link } from "mdast";
import type { Plugin } from "unified";
import { visit } from "unist-util-visit";

/**
 * remarkPathResolverPaper
 * 将 Markdown 中的相对路径（图片/链接）转换为指向
 *   /api/papers/{paperId%2Fversion}/download-tree?path=xxx
 * 的绝对 URL，供 Review 预览使用。
 */
interface Options {
  paperId: string;
  version: string;
}

function isRelative(url: string): boolean {
  return !/^([a-z]+:)?\/\//i.test(url) && !url.startsWith("#") && !url.startsWith("data:");
}

export const remarkPathResolverPaper: Plugin<[Options], Root> = opts => {
  const { paperId, version } = opts;
  // 先编码，避免 / 被解释为路径分隔
  const projectName = encodeURIComponent(`${paperId}/${version}`);
  const base = `/api/papers/${projectName}/download-tree?path=`;
  return tree => {
    visit(tree, "image", (node: Image) => {
      if (isRelative(node.url)) {
        node.url = base + encodeURIComponent(node.url.replace(/^\.\//, ""));
      }
    });
    visit(tree, "link", (node: Link) => {
      if (isRelative(node.url)) {
        node.url = base + encodeURIComponent(node.url.replace(/^\.\//, ""));
      }
    });
  };
};

export default remarkPathResolverPaper;
