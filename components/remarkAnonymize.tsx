import { load, dump } from "js-yaml";
import { Node } from "unist";
import { visit } from "unist-util-visit";

interface YamlNode extends Node {
  type: "yaml";
  value: string;
}

interface Author {
  name?: string;
  [key: string]: any;
}

interface Affiliation {
  name?: string;
  [key: string]: any;
}

interface YamlContent {
  status?: string;
  authors?: Author[];
  affiliations?: Affiliation[];
  [key: string]: any;
}

/**
 * 用于在草稿状态下匿名化作者和机构信息的remark插件
 */
function remarkAnonymize() {
  return (tree: Node) => {
    // 查找yaml节点
    visit(tree, "yaml", (node: YamlNode) => {
      try {
        const yamlContent = node.value;

        // 尝试解析YAML内容
        const parsedYaml = load(yamlContent) as YamlContent;

        // 如果不是对象或没有Status字段，直接返回
        if (!parsedYaml || typeof parsedYaml !== "object") return;

        // 检查Status字段
        console.log("YAML Status:", parsedYaml.status);
        // 确保status字段存在并且正确地将其转换为小写进行比较
        const status = parsedYaml.status?.toLowerCase()?.trim();
        console.log("Processed Status:", status);

        if (status !== "published") {
          // 匿名化authors
          if (Array.isArray(parsedYaml.authors)) {
            parsedYaml.authors = parsedYaml.authors.map((author: Author) => {
              if (author.name) {
                // 替换name字段中的每个字母为*
                author.name = author.name.replace(/[a-zA-Z]/g, "*");
              }
              return author;
            });
          }

          // 匿名化affiliations
          if (Array.isArray(parsedYaml.affiliations)) {
            parsedYaml.affiliations = parsedYaml.affiliations.map((affiliation: Affiliation) => {
              if (affiliation.name) {
                // 替换name字段中的每个字母为*
                affiliation.name = affiliation.name.replace(/[a-zA-Z]/g, "*");
              }
              return affiliation;
            });
          }

          // 将修改后的YAML写回节点
          node.value = dump(parsedYaml);
        }
      } catch (error) {
        console.error("处理YAML时出错:", error);
      }
    });
  };
}

export default remarkAnonymize;
