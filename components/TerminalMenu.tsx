"use client";

import React, { useState, useEffect, useMemo } from "react";
import {
    TerminalMenuConfig,
    TerminalMenuItem,
    defaultTerminalMenuConfig,
} from "@/lib/terminal-menu-config";
import { TerminalUserArea } from './terminal/TerminalUserArea';
import styles from './TerminalMenu.module.css';

interface TerminalMenuProps {
    isOpen: boolean;
    onClose: () => void;
    onExecuteCommand: (command: string, source?: string) => void;
    config?: TerminalMenuConfig;
    menuWidth: number;
}

// 🔧 常量定义
const BACKGROUND_ANIMATION_DELAY = 10;
const BACKGROUND_ANIMATION_DURATION = 800;

export function TerminalMenu({
    isOpen,
    onClose,
    onExecuteCommand,
    config = defaultTerminalMenuConfig,
    menuWidth
}: TerminalMenuProps) {
    // 🎬 简化的动画状态管理
    const [animationState, setAnimationState] = useState<{
        shouldRender: boolean;
        showBackground: boolean;
        showPanel: boolean;
    }>({
        shouldRender: false,
        showBackground: false,
        showPanel: false
    });

    // 📋 所有菜单项的简单列表
    const allMenuItems = useMemo(() => config.items, [config.items]);

    // 🎬 动画状态管理
    useEffect(() => {
        if (isOpen) {
            setAnimationState(prev => ({ ...prev, shouldRender: true, showPanel: true }));
            // 触发菜单打开事件，通知AI配额组件刷新
            const event = new CustomEvent('terminal-menu-opened');
            document.dispatchEvent(event);

            const timer = setTimeout(() => {
                setAnimationState(prev => ({ ...prev, showBackground: true }));
            }, BACKGROUND_ANIMATION_DELAY);
            return () => clearTimeout(timer);
        } else {
            setAnimationState(prev => ({ ...prev, showPanel: false, showBackground: false }));
            const timer = setTimeout(() => {
                setAnimationState(prev => ({ ...prev, shouldRender: false }));
            }, BACKGROUND_ANIMATION_DURATION);
            return () => clearTimeout(timer);
        }
    }, [isOpen]);

    // 🎛️ 事件处理
    const handleItemClick = (item: TerminalMenuItem) => {
        onExecuteCommand(item.command, `menu:${item.id}`);
        // 🔄 执行命令后自动关闭菜单
        onClose();
    };

    const handleOverlayClick = (e: React.MouseEvent) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    // ⌨️ 键盘处理
    useEffect(() => {
        if (!isOpen) return;

        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                onClose();
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [isOpen, onClose]);

    // 🚫 不渲染条件
    if (!animationState.shouldRender) return null;

    return (
        <div
            className={`${styles.overlay} ${animationState.showBackground ? styles.backgroundVisible : styles.backgroundHidden
                }`}
            onClick={handleOverlayClick}
        >
            {animationState.showPanel && (
                <div
                    className={styles.panel}
                    style={{ width: `${menuWidth}px` }}
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex flex-col flex-1">
                        <MenuList items={allMenuItems} onItemClick={handleItemClick} />
                    </div>
                    <TerminalUserArea onExecuteCommand={onExecuteCommand} onCloseMenu={onClose} />
                </div>
            )}
        </div>
    );
}


// 🧩 子组件：简化的菜单列表
function MenuList({
    items,
    onItemClick
}: {
    items: TerminalMenuItem[];
    onItemClick: (item: TerminalMenuItem) => void;
}) {
    return (
        <div className={styles.content}>
            <div className={styles.menuList}>
                {items.map(item => (
                    <MenuItem key={item.id} item={item} onClick={onItemClick} />
                ))}
            </div>
        </div>
    );
}

// 🧩 子组件：简化的菜单项（仅支持文字颜色hover）
function MenuItem({
    item,
    onClick
}: {
    item: TerminalMenuItem;
    onClick: (item: TerminalMenuItem) => void;
}) {
    const [isHovered, setIsHovered] = React.useState(false);

    // 计算文字颜色
    const textColor = isHovered && item.style?.hoverColor
        ? item.style.hoverColor
        : item.style?.color || 'rgba(51, 65, 85, 0.95)';

    // 文字样式
    const labelStyle: React.CSSProperties = {
        color: textColor,
        fontWeight: item.style?.fontWeight || 500,
        fontStyle: item.style?.fontStyle || 'normal',
        fontSize: '18px',
        transition: 'color 0.5s ease'
    };

    return (
        <div
            className={styles.menuItem}
            onClick={() => onClick(item)}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <div
                className={styles.menuItemLabel}
                style={labelStyle}
            >
                {item.label}
            </div>
        </div>
    );
}