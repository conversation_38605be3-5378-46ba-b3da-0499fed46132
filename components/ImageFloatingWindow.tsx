'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Rnd } from 'react-rnd';
import { X } from 'lucide-react';
import { FloatingWindow, useFloatingWindows } from '@/lib/floating-windows-store';
import { globalMessageManager, WindowOperationMessage } from '@/lib/floating-window-messages';

interface ImageFloatingWindowProps {
    window: FloatingWindow;
    onClose: () => void;
    onUpdate: (updates: Partial<FloatingWindow>) => void;
    onBringToFront: () => void;
}

/**
 * 图片浮窗组件 - 专门用于显示图片的浮窗
 *
 * @param window - 浮窗数据对象
 * @param onClose - 关闭回调函数
 * @param onUpdate - 更新回调函数
 * @param onBringToFront - 置顶回调函数
 */
export default function ImageFloatingWindow({
    window,
    onClose,
    onUpdate,
    onBringToFront
}: ImageFloatingWindowProps) {
    const { removeWindow, toggleMaximize } = useFloatingWindows();
    const [isHovered, setIsHovered] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [isDragging, setIsDragging] = useState(false);


    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);
    const [naturalDimensions, setNaturalDimensions] = useState({ width: 0, height: 0 });

    /**
     * 处理图片双击事件 - 切换全屏状态
     */
    const handleImageDoubleClick = useCallback((e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        toggleMaximize(window.id);
    }, [window.id, toggleMaximize]);

    // 检测是否为移动设备
    useEffect(() => {
        const checkMobile = () => {
            const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                ('ontouchstart' in globalThis) ||
                (globalThis.innerWidth <= 768);
            setIsMobile(isMobileDevice);
        };

        checkMobile();
        globalThis.addEventListener('resize', checkMobile);
        return () => globalThis.removeEventListener('resize', checkMobile);
    }, []);

    // 处理关闭动画 - 与 FloatingWindow.tsx 保持一致
    const handleClose = () => {
        // 先启动关闭动画
        onClose();

        // 等待动画完成后真正删除窗口
        setTimeout(() => {
            removeWindow(window.id);
        }, 500); // 与CSS动画时间匹配
    };

    // 处理图片加载
    const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
        const img = event.currentTarget;
        setNaturalDimensions({ width: img.naturalWidth, height: img.naturalHeight });
        setImageLoaded(true);

        // 如果窗口尺寸未设置或过小，自动调整到合适大小
        if (window.width <= 100 || window.height <= 100) {
            const maxWidth = Math.min(800, globalThis.innerWidth * 0.8);
            const maxHeight = Math.min(600, globalThis.innerHeight * 0.8);

            const scale = Math.min(maxWidth / img.naturalWidth, maxHeight / img.naturalHeight, 1);
            const newWidth = Math.round(img.naturalWidth * scale);
            const newHeight = Math.round(img.naturalHeight * scale);

            onUpdate({
                width: newWidth,
                height: newHeight
            });
        }
    };

    const handleImageError = () => {
        setImageError(true);
        setImageLoaded(true);
    };

    // 🆕 注册窗口操作消息监听器 - 与 FloatingWindow.tsx 保持一致
    useEffect(() => {
        const unsubscribeOperation = globalMessageManager.on('WINDOW_OPERATION', async (message: WindowOperationMessage) => {
            if (message.windowId !== window.id) return;

            try {
                const { operation, data } = message.payload;
                let result: any = null;

                switch (operation) {
                    case 'close':
                        handleClose();
                        result = { success: true };
                        break;
                    case 'focus':
                        onBringToFront();
                        result = { success: true };
                        break;
                    case 'move':
                        if (data?.x !== undefined || data?.y !== undefined) {
                            onUpdate({
                                x: data.x ?? window.x,
                                y: data.y ?? window.y
                            });
                        }
                        result = { success: true };
                        break;
                    case 'resize':
                        if (data?.width !== undefined || data?.height !== undefined) {
                            onUpdate({
                                width: data.width ?? window.width,
                                height: data.height ?? window.height
                            });
                        }
                        result = { success: true };
                        break;
                    case 'update':
                        if (data) {
                            onUpdate(data);
                        }
                        result = { success: true };
                        break;
                    default:
                        result = { success: false, error: `Unsupported operation: ${operation}` };
                }

                // 发送响应消息（如果期望响应）
                if (message.payload.expectResponse) {
                    await globalMessageManager.sendWindowOperationResponse(
                        message.id,
                        true,
                        operation,
                        result
                    );
                }
            } catch (error) {
                console.error(`[ImageFloatingWindow] 操作失败: ${message.payload.operation}`, error);

                // 发送错误响应
                if (message.payload.expectResponse) {
                    await globalMessageManager.sendWindowOperationResponse(
                        message.id,
                        false,
                        message.payload.operation,
                        null,
                        error instanceof Error ? error.message : '操作失败'
                    );
                }
            }
        });

        return () => {
            unsubscribeOperation();
        };
    }, [window.id, window.x, window.y, window.width, window.height, onUpdate, onBringToFront]);

    // 计算动画状态
    const animationClass = window.isClosing
        ? 'floating-window-exit'
        : window.isVisible
            ? 'floating-window-enter-active'
            : 'floating-window-enter';

    if (!window.imageUrl) {
        return null;
    }

    return (
        <Rnd
            size={{ width: window.width, height: window.height }}
            position={{ x: window.x, y: window.y }}
            onDragStart={(e) => {
                // 标记开始拖拽
                setIsDragging(true);
            }}
            onDrag={(e, d) => {
                // 拖拽过程中保持拖拽状态
                if (!isDragging) {
                    setIsDragging(true);
                }
            }}
            onDragStop={(e, d) => {
                try {
                    // 确保位置值有效并在边界内，使用全局window对象获取屏幕尺寸
                    const x = Math.max(0, Math.min(globalThis.innerWidth - window.width, d.x || 0));
                    const y = Math.max(0, Math.min(globalThis.innerHeight - window.height, d.y || 0));
                    onUpdate({ x, y });
                } catch (error) {
                    console.error('[ImageFloatingWindow] Error updating position:', error);
                } finally {
                    // 拖拽结束，清除拖拽状态
                    setIsDragging(false);
                }
            }}
            minWidth={100}
            minHeight={100}
            dragHandleClassName="image-drag-handle"
            style={{
                zIndex: window.zIndex,
                pointerEvents: 'auto'
            }}
            enableResizing={false}
        >
            <div
                className={`relative w-full h-full ${animationClass} ${window.isMaximized ? 'floating-window-maximized' : ''} ${isDragging ? 'floating-window-dragging' : ''}`}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                onMouseDown={onBringToFront}
                style={{
                    background: 'transparent',
                    border: 'none',
                    borderRadius: '8px',
                    overflow: 'hidden'
                }}
            >
                {/* 图片容器 - 作为拖拽手柄 */}
                <div
                    className="image-drag-handle w-full h-full cursor-move"
                    onDoubleClick={handleImageDoubleClick}
                >
                    {imageError ? (
                        <div className="w-full h-full flex items-center justify-center bg-gray-50">
                            <div className="text-center text-gray-400">
                                <div className="text-sm">image not found</div>
                            </div>
                        </div>
                    ) : (
                        <img
                            src={window.imageUrl}
                            alt={window.imageAlt || window.title}
                            className="w-full h-full object-contain"
                            style={{
                                borderRadius: '8px',
                                boxShadow: isHovered ? '0 8px 32px rgba(0, 0, 0, 0.3)' : '0 4px 16px rgba(0, 0, 0, 0.1)',
                                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                            }}
                            onLoad={handleImageLoad}
                            onError={handleImageError}
                            draggable={false}
                        />
                    )}
                </div>

                {/* 关闭按钮 - 移动端始终显示，桌面端hover时显示 */}
                {(isHovered || isMobile) && (
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            handleClose();
                        }}
                        onTouchStart={(e) => {
                            e.stopPropagation();
                        }}
                        onTouchEnd={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            handleClose();
                        }}
                        className={`absolute top-2 right-2 p-1 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full transition-all duration-200 z-10 touch-manipulation flex items-center justify-center ${isMobile ? 'w-[14px] h-[14px]' : 'w-[18px] h-[18px]'
                            }`}
                        title="关闭"
                    >
                        <X size={14} />
                    </button>
                )}
            </div>
        </Rnd>
    );
} 