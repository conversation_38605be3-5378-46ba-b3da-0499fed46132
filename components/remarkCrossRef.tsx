/**
 * remarkCrossRef
 * -----------------------------------------------------------------------------
 * 1)  采集 {#type:id} 标签，为节点写入 data.hProperties.id
 * 2)  自动编号：
 *        • 章节   →  1 / 1.1 / 1.1.1 …
 *        • 图表   →  Figure n / Table n
 *        • 代码块 →  Code n
 *        • 公式   →  Equation n（并在 math 后插入锚点 <span id="…">）
 * 3)  将 [#type:id] 引用替换为超链接；若目标缺失则写 “Figure ?” 等占位
 * 4)  为 <figcaption> 补上 “Figure n: ” / “Table n: ” 前缀
 * -----------------------------------------------------------------------------
 */

import type { Root, RootContent, Heading, Image, Code, Table, Text, Parent, Literal } from "mdast";
import type { Plugin } from "unified";
import { inspect } from "unist-util-inspect";
import { visit } from "unist-util-visit";

/* ---------- 扩展 mdast 类型 ------------------------------------------------ */

interface Math extends Literal {
  type: "math";
}
interface InlineMath extends Literal {
  type: "inlineMath";
}
interface Figure extends Parent {
  type: "figure";
}
interface Figcaption extends Parent {
  type: "figcaption";
}

declare module "mdast" {
  interface BlockContentMap {
    figure: Figure;
    figcaption: Figcaption;
  }
  interface StaticPhrasingContentMap {
    inlineMath: InlineMath;
  }
}

/* ---------- 工具 ----------------------------------------------------------- */

const alpha = (n: number) => {
  let s = "";
  let k = n;
  while (k) {
    s = String.fromCharCode(65 + ((k - 1) % 26)) + s;
    k = ((k - 1) / 26) | 0;
  }
  return s;
};

type Num = number | string;
type Kind = "sec" | "fig" | "table" | "code" | "appendix" | "equation";

const labelOf = (k: Kind, n: Num) =>
  k === "appendix"
    ? `Appendix ${alpha(n as number)}`
    : k === "equation"
      ? `Equation ${n}`
      : k === "sec"
        ? `Section ${n}`
        : k === "fig"
          ? `Figure ${n}`
          : k === "table"
            ? `Table ${n}`
            : `Code ${n}`;

/* ---------- 主插件 --------------------------------------------------------- */

export const remarkCrossRef: Plugin<[], Root> = () => tree => {
  /** 子→父 快速索引 */
  const parents = new WeakMap<object, Parent>();
  visit(tree, (n, _i, p) => {
    if (p && Array.isArray((p as Parent).children)) parents.set(n as object, p as Parent);
  });

  /** 全局计数 & 映射 */
  const counters: Record<Kind, number> = {
    sec: 0,
    fig: 0,
    table: 0,
    code: 0,
    appendix: 0,
    equation: 0,
  };
  const map: Record<string, { kind: Kind; num: Num }> = {};

  /** 章节分级计数器 [level-1 … 6] */
  const secLevels = [0, 0, 0, 0, 0, 0];

  /** 把节点登记为某 id，并处理编号 / 锚点 / 标题前缀 */
  const register = (node: RootContent, kind: Kind, id: string) => {
    /* ---------- 章节：多级编号 + 标题前缀 ---------------------- */
    if (kind === "sec" && node.type === "heading") {
      const hd = node as Heading;
      const level = Math.max(1, Math.min(hd.depth ?? 1, 6));

      /* 1. 计算章节号 */
      secLevels[level - 1]++; // 同级 +1
      for (let i = level; i < 6; i++) secLevels[i] = 0; // 低级清零
      const numStr = secLevels.slice(0, level).join(".");

      /* 2. 记录到映射表 */
      map[id] = { kind: "sec", num: numStr };
      if (level === 1) counters.sec = secLevels[0];

      /* 3. 给标题文本加前缀（仅加一次） */
      const first = hd.children[0] as Text | undefined;
      if (!(first?.type === "text" && /^\d+(\.\d+)*\s/.test(first.value))) {
        hd.children.unshift({ type: "text", value: `${numStr} ` } as Text);
      }

      /* 4. **关键新增** —— 把 id 写进 data.hProperties，供 rehype 使用 */
      (hd.data ||= {}).hProperties ||= {};
      (hd.data.hProperties as Record<string, unknown>).id = id;

      /* 5. 章节已处理完毕，返回 */
      return;
    }

    /* ---------- 已登记则跳过（避免重复） ----------------------- */
    if (map[id]) return;

    /* ---------- 其他种类：简单计数 ---------------------------- */
    counters[kind] += 1;
    map[id] = { kind, num: counters[kind] };

    if (kind === "equation") {
      const math = node as Math;

      if (!/\\tag\{.*?}/.test(math.value)) {
        const tag = `\\tag{${counters.equation}}`;
        // 1️⃣ 改 math.value
        math.value = math.value.trimEnd() + "\n" + tag;

        // 2️⃣ 若已有 hChildren → 同步改内部 <code> 文本
        const dataAny = math.data as any;
        const hChildren = dataAny?.hChildren;
        if (Array.isArray(hChildren) && hChildren[0]?.children?.[0]?.type === "text") {
          hChildren[0].children[0].value = hChildren[0].children[0].value.trimEnd() + "\n" + tag;
        }
      }

      /* 数学公式：在 <math> 后插入锚点 */
      const parent = parents.get(node as object);
      if (parent) {
        const idx = parent.children.indexOf(node as RootContent);
        parent.children.splice(idx + 1, 0, {
          type: "html",
          value: `<span id="${id}" class="eq-anchor"></span>`,
        } as RootContent);
      }
    } else {
      /* 其它：id 写到自身 */
      const any = node as any;
      any.data ||= {};
      any.data.hProperties ||= {};
      any.data.hProperties.id = id;
    }
  };

  /* ---------- Pass-1：扫描所有节点，收集标签 ------------------ */

  visit(tree, (node, _i, parent) => {
    /* 内联 id 属性 */
    const idAttr = (node as any).data?.hProperties?.id as string | undefined;
    if (idAttr) {
      const m = /^(sec|fig|table|code|appendix|equation):/.exec(idAttr);
      if (m) register(node as RootContent, m[1] as Kind, idAttr);
    }

    /* 标题尾部 {#sec:foo} / {#appendix:bar} */
    if (node.type === "heading") {
      const hd = node as Heading;
      const tail = hd.children.at(-1) as Text | undefined;
      if (tail?.type === "text") {
        const m = tail.value.match(/\s*\{#(sec|appendix):([\w\-]+)}$/);
        if (m) {
          tail.value = tail.value.replace(/\s*\{#[^}]+}$/, "");
          register(hd as RootContent, m[1] as Kind, `${m[1]}:${m[2]}`);
        }
      }
    }

    /* figcaption 内标签 */
    if (node.type === "figcaption") {
      (node as Parent).children.forEach(child => {
        if (child.type !== "text") return;
        const m = child.value.match(/\{#(fig|table):([\w\-]+)}/);
        if (m && (parent?.type === "figure" || parent?.type === "table")) {
          child.value = child.value.replace(/\{#[^}]+}/, "").trim();
          register(parent as RootContent, m[1] as Kind, `${m[1]}:${m[2]}`);
        }
      });
    }

    /* 代码块 info 中标签 */
    if (node.type === "code") {
      const cb = node as Code;
      const m = cb.meta?.match(/\{#code:([\w\-]+)}/);
      if (m) {
        cb.meta = cb.meta!.replace(/\{#[^}]+}/, "").trim() || undefined;
        register(cb as RootContent, "code", `code:${m[1]}`);
      }
    }
  });

  /* 单独段落的 {#type:id} */
  visit(tree, "paragraph", (p, i, parent) => {
    if (!parent || typeof i !== "number") return;
    const text = (p as Parent).children.map(n => (n.type === "text" ? n.value : "")).join("");
    const m = text.match(/^\s*\{#(table|equation|fig|code):([\w\-]+)}\s*$/);
    if (!m) return;
    const kind = m[1] as Kind;
    const id = `${kind}:${m[2]}`;

    let target: RootContent | null = null;
    if (kind === "table") target = parent.children[i + 1] as Table;
    else if (kind === "equation") target = parent.children[i - 1] as Math;
    else if (kind === "fig") {
      const prev = parent.children[i - 1];
      if (prev?.type === "paragraph" && (prev as Parent).children[0]?.type === "image")
        target = (prev as Parent).children[0] as Image;
    } else if (kind === "code") target = parent.children[i - 1] as Code;

    if (target) {
      parent.children.splice(i, 1); // 移除标签段落
      register(target, kind, id);
    }
  });

  /* ---------- 为 figcaption 添加 “Figure n:” / “Table n:” ------- */

  visit(tree, node => {
    if (node.type !== "figure" && node.type !== "table") return;
    const id = (node as any).data?.hProperties?.id as string | undefined;
    if (!id) return;
    const info = map[id];
    if (!info) return;
    const prefix = info.kind === "fig" ? "Figure" : "Table";

    const figure = node as Parent;
    let caption = figure.children.find(c => (c as any).type === "figcaption") as Parent | undefined;
    if (!caption) {
      caption = { type: "figcaption", children: [] };
      figure.children.push(caption as any);
    }
    let firstTxt = caption.children.find(c => c.type === "text") as Text | undefined;
    if (!firstTxt) {
      firstTxt = { type: "text", value: "" };
      caption.children.unshift(firstTxt);
    }
    firstTxt.value = firstTxt.value.replace(/^(Figure|Table)\s+\d+:\s*/i, "").trimStart();
    firstTxt.value = `${prefix} ${info.num}: ` + firstTxt.value;
  });

  /* ---------- Pass-2：替换文本引用 ----------------------------- */

  const refRE = /\[#(sec|fig|table|code|appendix|equation):([\w\-]+)]/g;

  visit(tree, "text", (node, idx, parent) => {
    if (!parent || typeof idx !== "number") return;
    const src = (node as Text).value;
    let last = 0;
    let m: RegExpExecArray | null;
    const out: RootContent[] = [];
    while ((m = refRE.exec(src))) {
      const [raw, tp, ident] = m;
      const pos = m.index;
      if (pos > last) out.push({ type: "text", value: src.slice(last, pos) });
      const key = `${tp}:${ident}`;
      const info = map[key];
      if (info) {
        out.push({
          type: "link",
          url: `#${key}`,
          title: null,
          children: [{ type: "text", value: labelOf(info.kind, info.num) }],
        });
      } else {
        const word =
          tp === "sec"
            ? "Section"
            : tp === "fig"
              ? "Figure"
              : tp === "table"
                ? "Table"
                : tp === "code"
                  ? "Code"
                  : tp === "appendix"
                    ? "Appendix"
                    : "Equation";
        out.push({ type: "text", value: `${word} ?` });
      }
      last = pos + raw.length;
    }
    if (out.length) {
      if (last < src.length) out.push({ type: "text", value: src.slice(last) });
      parent.children.splice(idx, 1, ...out);
      return idx + out.length;
    }
  });
  // console.log(inspect(tree, { color: true, showPositions: false }));
};

export default remarkCrossRef;
