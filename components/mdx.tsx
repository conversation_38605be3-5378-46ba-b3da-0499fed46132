import Image, { ImageProps } from "next/image";
import Link, { LinkProps } from "next/link";
import { MDXRemote } from "next-mdx-remote/rsc";
import React, { DetailedHTMLProps, AnchorHTMLAttributes } from "react";
import { highlight } from "sugar-high";
import AnchorLink from "./AnchorLink";

/* ---------- Table ---------- */

export type TableData = {
  headers: string[];
  rows: (string | number)[][];
};

type TableProps = {
  data: TableData;
};

function Table({ data }: TableProps) {
  return (
    <table>
      <thead>
        <tr>
          {data.headers.map((header, i) => (
            <th key={i}>{header}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data.rows.map((row, i) => (
          <tr key={i}>
            {row.map((cell, j) => (
              <td key={j}>{cell}</td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
}

/* ---------- CustomLink ---------- */

type NextLikeLinkProps = LinkProps &
  Omit<DetailedHTMLProps<AnchorHTMLAttributes<HTMLAnchorElement>, HTMLAnchorElement>, "href"> & {
    children: React.ReactNode;
  };

function CustomLink(props: NextLikeLinkProps) {
  const { href, children, ...rest } = props;

  // 把 string | URL | UrlObject 统一转成字符串
  const hrefStr =
    typeof href === "string"
      ? href // 普通字符串
      : href instanceof URL
        ? href.pathname // URL 实例
        : "pathname" in href && typeof href.pathname === "string"
          ? href.pathname // UrlObject
          : "";

  if (hrefStr.startsWith("/")) {
    return (
      <Link href={href} {...rest}>
        {children}
      </Link>
    );
  }

  if (hrefStr.startsWith("#")) {
    return (
      <a href={hrefStr} {...rest}>
        {children}
      </a>
    );
  }

  return (
    <a href={hrefStr} target="_blank" rel="noopener noreferrer" {...rest}>
      {children}
    </a>
  );
}

/* ---------- RoundedImage ---------- */

function RoundedImage(props: ImageProps) {
  return <Image className="rounded-lg" {...props} />;
}

/* ---------- Code ---------- */

type CodeProps = React.HTMLAttributes<HTMLElement> & { children: string };

function Code({ children, ...rest }: CodeProps) {
  const codeHTML = highlight(children);
  return <code dangerouslySetInnerHTML={{ __html: codeHTML }} {...rest} />;
}

/* ---------- Headings & utils ---------- */

function slugify(str: string): string {
  return str
    .toLowerCase()
    .trim()
    .replace(/\s+/g, "-")
    .replace(/&/g, "-and-")
    .replace(/[^\w\-]+/g, "")
    .replace(/\-\-+/g, "-");
}

function createHeading(level: 1 | 2 | 3 | 4 | 5 | 6) {
  const Heading: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const slug = slugify(String(children));
    return React.createElement(
      `h${level}`,
      { id: slug },
      [
        <AnchorLink key={`link-${slug}`} href={`#${slug}`}>
          #
        </AnchorLink>,
      ],
      children
    );
  };

  Heading.displayName = `Heading${level}`;
  return Heading;
}

/* ---------- MDX component map ---------- */

const components = {
  h1: createHeading(1),
  h2: createHeading(2),
  h3: createHeading(3),
  h4: createHeading(4),
  h5: createHeading(5),
  h6: createHeading(6),
  Image: RoundedImage,
  a: CustomLink,
  code: Code,
  Table,
};

/* ---------- MDX wrapper ---------- */

type CustomMDXProps = {
  source: string;
  components?: Record<string, React.ComponentType<any>>;
};

export function CustomMDX({ source, components: extra = {} }: CustomMDXProps) {
  return (
    <MDXRemote
      source={source}
      components={{
        // 先放默认再放外部，方便覆盖
        ...components,
        ...extra,
      }}
    />
  );
}
