"use client";

import { PaperType } from "@prisma/client";

import { Badge } from "@/components/ui/badge_papers";
import { cn } from "@/lib/utils";

interface TypeBadgeProps {
    type: PaperType;
    className?: string;
}

export function TypeBadge({ type, className }: TypeBadgeProps) {
    const config = getTypeBadgeConfig(type);

    return (
        <Badge
            variant="secondary"
            className={cn(
                "text-xs font-medium text-white border-0",
                config.bgColor,
                config.hoverColor,
                className
            )}
        >
            {config.label}
        </Badge>
    );
}

// 类型配置
type TypeConfig = {
    label: string;
    bgColor: string;
    hoverColor: string;
};

// Paper类型的颜色配置
const paperTypeConfig: Record<PaperType, TypeConfig> = {
    FULL: {
        label: "Full Paper",
        bgColor: "bg-emerald-600", // 绿色
        hoverColor: "hover:bg-emerald-700",
    },
    GALLERY: {
        label: "Gallery",
        bgColor: "bg-purple-600", // 紫色
        hoverColor: "hover:bg-purple-700",
    },
    PREPRINT: {
        label: "Preprint",
        bgColor: "bg-orange-600", // 橙色
        hoverColor: "hover:bg-orange-700",
    },
};

// 获取特定类型的徽章配置
function getTypeBadgeConfig(type: PaperType): TypeConfig {
    return paperTypeConfig[type] || {
        label: "Unknown",
        bgColor: "bg-gray-500",
        hoverColor: "hover:bg-gray-600"
    };
} 