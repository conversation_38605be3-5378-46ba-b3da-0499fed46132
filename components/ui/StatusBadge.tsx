"use client";

import { PaperStatus } from "@prisma/client";

import { Badge } from "@/components/ui/badge_papers";
import { getStatusBadgeConfig } from "@/lib/paperStatusConfig";
import { cn } from "@/lib/utils";

interface StatusBadgeProps {
  status: PaperStatus;
  className?: string;
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  const config = getStatusBadgeConfig(status);

  return (
    <Badge
      variant={config.variant}
      className={className}
      style={
        config.color
          ? {
              backgroundColor: config.color,
              color: "white",
              borderColor: "transparent",
            }
          : undefined
      }
    >
      {config.label || status}
    </Badge>
  );
}
