"use client";

import React, { useState, useCallback, useEffect, useRef } from "react";
import { TerminalMenu } from "./TerminalMenu";
import { TerminalMenuButton } from "./TerminalMenuButton";
import { TerminalMenuConfig, defaultTerminalMenuConfig } from "@/lib/terminal-menu-config";

interface TerminalMenuManagerProps {
    config?: TerminalMenuConfig;
    onCommandExecute?: (command: string, source?: string) => void;
    terminalInstance?: any;
}

// 🔧 常量定义
const INITIALIZATION_DELAY = 1000;
const MIN_MENU_WIDTH = 300;
const MAX_MENU_WIDTH = 400;
const VIEWPORT_WIDTH_RATIO = 0.25;

// 🎯 简化的命令执行策略类型
type CommandExecutionMethod = 'callback' | 'event' | 'queue';

export function TerminalMenuManager({
    config = defaultTerminalMenuConfig,
    onCommandExecute,
    terminalInstance
}: TerminalMenuManagerProps) {
    // 🎛️ 核心状态
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isInitialized, setIsInitialized] = useState(false);
    const [menuWidth, setMenuWidth] = useState(MIN_MENU_WIDTH);

    // 🔄 命令队列管理
    const commandQueueRef = useRef<Array<{ command: string; source?: string }>>([]);
    const isExecutingRef = useRef(false);

    // ⏱️ 初始化延迟
    useEffect(() => {
        const timer = setTimeout(() => setIsInitialized(true), INITIALIZATION_DELAY);
        return () => clearTimeout(timer);
    }, []);

    // 📐 响应式菜单宽度计算
    useEffect(() => {
        const calculateMenuWidth = () => {
            if (typeof window === 'undefined') return;

            const viewportWidth = window.innerWidth;
            const calculatedWidth = Math.min(MAX_MENU_WIDTH, viewportWidth * VIEWPORT_WIDTH_RATIO);
            const finalWidth = Math.max(MIN_MENU_WIDTH, calculatedWidth);

            setMenuWidth(finalWidth);
        };

        calculateMenuWidth();
        window.addEventListener('resize', calculateMenuWidth);
        return () => window.removeEventListener('resize', calculateMenuWidth);
    }, []);

    // 🎯 简化的命令执行逻辑
    const executeCommand = useCallback((command: string, source?: string) => {
        if (isExecutingRef.current) {
            commandQueueRef.current.push({ command, source });
            return;
        }

        isExecutingRef.current = true;

        try {
            // 策略1: 优先使用回调函数
            if (onCommandExecute) {
                onCommandExecute(command, source);
                return;
            }

            // 策略2: 广播事件
            const event = new CustomEvent('terminal-menu-command', {
                detail: { command, source },
                bubbles: true
            });
            document.dispatchEvent(event);

        } catch (error) {
            // 🎯 生产级错误处理
            console.error('[TerminalMenuManager] Command execution failed:', error);

            // 添加到队列重试
            commandQueueRef.current.push({ command, source });
        } finally {
            isExecutingRef.current = false;

            // 处理队列中的命令
            if (commandQueueRef.current.length > 0) {
                const nextCommand = commandQueueRef.current.shift();
                if (nextCommand) {
                    // 异步执行，避免递归
                    setTimeout(() => executeCommand(nextCommand.command, nextCommand.source), 0);
                }
            }
        }
    }, [onCommandExecute]);

    // 🔄 菜单控制
    const toggleMenu = useCallback(() => setIsMenuOpen(prev => !prev), []);
    const closeMenu = useCallback(() => setIsMenuOpen(false), []);

    // 🎧 事件监听器 - 优化依赖管理
    useEffect(() => {
        // 外部命令事件
        const handleExternalCommand = (event: CustomEvent) => {
            const { command, source } = event.detail;
            executeCommand(command, `external:${source || 'unknown'}`);
        };

        // 菜单控制事件
        const handleMenuControl = (event: CustomEvent) => {
            const { action } = event.detail;

            switch (action) {
                case 'open':
                    !isMenuOpen && setIsMenuOpen(true);
                    break;
                case 'close':
                    isMenuOpen && setIsMenuOpen(false);
                    break;
                case 'toggle':
                    toggleMenu();
                    break;
            }
        };

        document.addEventListener('terminal-menu-execute-command', handleExternalCommand as EventListener);
        document.addEventListener('terminal-menu-control', handleMenuControl as EventListener);

        return () => {
            document.removeEventListener('terminal-menu-execute-command', handleExternalCommand as EventListener);
            document.removeEventListener('terminal-menu-control', handleMenuControl as EventListener);
        };
    }, [executeCommand, isMenuOpen, toggleMenu]);



    // 🚫 未初始化时不渲染
    if (!isInitialized) return null;

    return (
        <>
            <TerminalMenuButton
                isMenuOpen={isMenuOpen}
                onToggleMenu={toggleMenu}
                menuWidth={menuWidth}
            />
            <TerminalMenu
                isOpen={isMenuOpen}
                onClose={closeMenu}
                onExecuteCommand={executeCommand}
                config={config}
                menuWidth={menuWidth}
            />
        </>
    );
}



// 🌐 导出的全局函数
export function executeTerminalCommand(command: string, source?: string) {
    const event = new CustomEvent('terminal-menu-execute-command', {
        detail: { command, source: source || 'external' },
        bubbles: true
    });
    document.dispatchEvent(event);
}

export function controlTerminalMenu(action: 'open' | 'close' | 'toggle', source?: string) {
    const event = new CustomEvent('terminal-menu-control', {
        detail: { action, source: source || 'external' },
        bubbles: true
    });
    document.dispatchEvent(event);
} 