import { Terminal } from '@xterm/xterm';

// 预生成的 ASCII JSON 接口定义
export interface AsciiJSON {
    cols: number;
    rows: number;
    fps: number;
    frames: string[]
}

// 剪裁策略接口
interface CropStrategy {
    startRow: number;
    endRow: number;
    startCol: number;
    endCol: number;
    needsPadding: boolean;
    padTop: number;
    padBottom: number;
    padLeft: number;
    padRight: number;
}

/** 
 * 根据终端尺寸选择合适的文件名
 * 逻辑：
 *   • 若列数明显少于行数（竖屏 / 高纵横比）或列数 < 100 ⇒ small
 *   • 否则使用 large（横屏）
 */
function pickAsciiFile(cols: number, rows: number): string {
    const isPortrait = cols < rows;          // 比高度还窄 → 竖屏
    const isNarrow = cols < 100;           // 小终端
    return (isPortrait || isNarrow)
        ? '/intro_small.json'
        : '/intro_large.json';
}

/**
 * 计算居中剪裁策略
 * 根据终端尺寸和ASCII数据尺寸，计算最优的居中剪裁方案
 */
function calculateCropStrategy(
    asciiCols: number,
    asciiRows: number,
    termCols: number,
    termRows: number
): CropStrategy {
    console.log(`📐 Calculating crop strategy: ASCII(${asciiCols}×${asciiRows}) → Terminal(${termCols}×${termRows})`);

    let startRow = 0, endRow = asciiRows, startCol = 0, endCol = asciiCols;
    let needsPadding = false;
    let padTop = 0, padBottom = 0, padLeft = 0, padRight = 0;

    // 计算行剪裁/填充策略
    if (asciiRows > termRows) {
        // ASCII内容比终端高，需要居中剪裁
        const excess = asciiRows - termRows;
        startRow = Math.floor(excess / 2);
        endRow = startRow + termRows;
        console.log(`📏 Row cropping: ${startRow} → ${endRow} (cropped ${excess} rows)`);
    } else if (asciiRows < termRows) {
        // ASCII内容比终端矮，需要居中填充
        needsPadding = true;
        const deficit = termRows - asciiRows;
        padTop = Math.floor(deficit / 2);
        padBottom = deficit - padTop;
        console.log(`📏 Row padding: top=${padTop}, bottom=${padBottom}`);
    }

    // 计算列剪裁/填充策略
    if (asciiCols > termCols) {
        // ASCII内容比终端宽，需要居中剪裁
        const excess = asciiCols - termCols;
        startCol = Math.floor(excess / 2);
        endCol = startCol + termCols;
        console.log(`📏 Column cropping: ${startCol} → ${endCol} (cropped ${excess} cols)`);
    } else if (asciiCols < termCols) {
        // ASCII内容比终端窄，需要居中填充
        needsPadding = true;
        const deficit = termCols - asciiCols;
        padLeft = Math.floor(deficit / 2);
        padRight = deficit - padLeft;
        console.log(`📏 Column padding: left=${padLeft}, right=${padRight}`);
    }

    return {
        startRow, endRow, startCol, endCol,
        needsPadding, padTop, padBottom, padLeft, padRight
    };
}

/** 
 * 根据剪裁策略高效处理单帧
 * 使用预计算的剪裁策略，避免每帧重复计算
 */
function adaptFrameWithStrategy(frame: string, strategy: CropStrategy, termCols: number, termRows: number): string {
    const lines = frame.split('\r\n');

    // 第一步：行剪裁
    let processedLines = lines.slice(strategy.startRow, strategy.endRow);

    // 第二步：列剪裁
    processedLines = processedLines.map(line => {
        if (strategy.startCol > 0 || strategy.endCol < line.length) {
            // 需要居中剪裁列
            return line.slice(strategy.startCol, strategy.endCol);
        }
        return line;
    });

    // 第三步：填充处理
    if (strategy.needsPadding) {
        // 上下填充
        if (strategy.padTop > 0) {
            const topPadding = Array(strategy.padTop).fill(' '.repeat(termCols));
            processedLines = [...topPadding, ...processedLines];
        }
        if (strategy.padBottom > 0) {
            const bottomPadding = Array(strategy.padBottom).fill(' '.repeat(termCols));
            processedLines = [...processedLines, ...bottomPadding];
        }

        // 左右填充
        if (strategy.padLeft > 0 || strategy.padRight > 0) {
            processedLines = processedLines.map(line => {
                const leftPad = ' '.repeat(strategy.padLeft);
                const rightPad = ' '.repeat(strategy.padRight);
                return leftPad + line + rightPad;
            });
        }
    }

    // 确保最终尺寸正确
    while (processedLines.length < termRows) {
        processedLines.push(' '.repeat(termCols));
    }
    processedLines = processedLines.slice(0, termRows);

    processedLines = processedLines.map(line => {
        if (line.length < termCols) {
            return line + ' '.repeat(termCols - line.length);
        }
        return line.slice(0, termCols);
    });

    return processedLines.join('\r\n');
}

/**
 * 播放预生成的 ASCII 帧序列
 * @param term xterm.Terminal 实例
 * @param frames 帧字符串数组
 * @param fps 帧率
 */
async function playAscii(term: Terminal, frames: string[], fps = 15): Promise<void> {
    return new Promise<void>(resolve => {
        let i = 0;
        const total = frames.length;
        const interval = 1000 / fps;

        const loop = () => {
            term.write('\x1b[H');       // 回到左上
            term.write(frames[i]);
            i++;
            if (i < total) {
                setTimeout(loop, interval);
            } else {
                resolve();
            }
        };
        loop();
    });
}

/**
 * ASCII动画播放插件主函数
 * 自动选择合适的动画文件，适配终端尺寸，播放动画
 * @param term xterm.Terminal 实例
 */
export async function playAsciiAnimation(term: Terminal): Promise<void> {
    try {
        // 根据终端尺寸选择合适的ASCII文件
        const src = pickAsciiFile(term.cols, term.rows);
        console.log(`🎬 Loading ASCII animation: ${src} for ${term.cols}×${term.rows} terminal`);

        // 获取ASCII数据
        const res = await fetch(src);
        if (!res.ok) {
            throw new Error(`Failed to load ${src}: ${res.status}`);
        }

        const asciiData: AsciiJSON = await res.json();
        console.log(`📦 Loaded ${asciiData.frames.length} frames at ${asciiData.fps}fps, original size: ${asciiData.cols}×${asciiData.rows}`);

        // 计算最优剪裁策略（只计算一次）
        const cropStrategy = calculateCropStrategy(
            asciiData.cols,
            asciiData.rows,
            term.cols,
            term.rows
        );

        // 高效批量处理所有帧（使用预计算的策略）
        console.log(`⚡ Processing ${asciiData.frames.length} frames with optimized strategy...`);
        const startTime = Date.now();

        const adaptedFrames = asciiData.frames.map(frame =>
            adaptFrameWithStrategy(frame, cropStrategy, term.cols, term.rows)
        );

        const processingTime = Date.now() - startTime;
        console.log(`✅ Frame processing completed in ${processingTime}ms (${(processingTime / asciiData.frames.length).toFixed(2)}ms per frame)`);

        // 播放动画
        console.log(`🎥 Starting playback...`);
        await playAscii(term, adaptedFrames, asciiData.fps);

        console.log(`🎬 Animation completed successfully`);

        // 动画结束后清屏
        // term.write('\x1b[2J\x1b[H');

    } catch (error) {
        console.warn('ASCII animation failed:', error);
        // 如果动画播放失败，显示一个简单的替代信息
        term.writeln('🎬 ASCII Animation unavailable');
    }
} 