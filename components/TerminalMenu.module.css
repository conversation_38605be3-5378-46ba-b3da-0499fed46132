/* 🎨 Terminal Menu CSS Module - Simplified */

/* 背景覆盖层 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
  align-items: stretch;
  transition: background 0.8s ease-in-out, backdrop-filter 0.8s ease-in-out;
}

.overlay.backgroundVisible {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
}

.overlay.backgroundHidden {
  background: rgba(0, 0, 0, 0);
  backdrop-filter: blur(0px);
}

/* 菜单面板 */
.panel {
  height: 100vh;
  background: rgb(255, 255, 255);
  backdrop-filter: blur(16px);
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 菜单头部 */
.header {
  padding: 20px 20px 16px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
  background: rgba(255, 255, 255, 0.5);
}

.headerTop {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title {
  color: rgba(51, 65, 85, 0.95);
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.closeButton {
  background: rgba(148, 163, 184, 0.1);
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: 6px;
  color: rgba(51, 65, 85, 0.8);
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: rgba(148, 163, 184, 0.2);
  color: rgba(51, 65, 85, 1);
}

/* 菜单内容 */
.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0; /* 确保flex子项可以收缩 */
}

.menuList {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 菜单项 */
.menuItem {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menuItem:hover {
  background: var(--color-gray-50);
}

.menuItemLabel {
  color: rgba(51, 65, 85, 0.95);
  font-weight: 500;
  font-size: 14px;
  transition: color 0.2s ease;
}