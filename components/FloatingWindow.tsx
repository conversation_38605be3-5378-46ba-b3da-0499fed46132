'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Rnd } from 'react-rnd';
import { X } from 'lucide-react';
import { FloatingWindow as FloatingWindowType, useFloatingWindows } from '@/lib/floating-windows-store';
import { globalMessageManager, TerminalCommandMessage, WindowStateMessage, WindowOperationMessage, NotificationMessage, CodeMirrorOperationMessage, CodeMirrorAIMessage, CodeMirrorUpdateMessage } from '@/lib/floating-window-messages';

interface FloatingWindowProps {
    window: FloatingWindowType;
    onClose: () => void;
    onUpdate: (updates: Partial<FloatingWindowType>) => void;
    onBringToFront: () => void;
    // 新增：消息通信回调（可选，保持向后兼容）
    onTerminalCommand?: (command: string, params?: Record<string, unknown>) => Promise<{
        success: boolean;
        result?: unknown;
        error?: string;
    }>;
}

/**
 * 浮窗组件 - 支持拖拽、缩放和消息通信
 *
 * @param window - 浮窗数据对象
 * @param onClose - 关闭回调函数
 * @param onUpdate - 更新回调函数
 * @param onBringToFront - 置顶回调函数
 * @param onTerminalCommand - 终端命令处理回调（可选）
 */
export default function FloatingWindow({
    window,
    onClose,
    onUpdate,
    onBringToFront,
    onTerminalCommand
}: FloatingWindowProps) {
    const { removeWindow, toggleMaximize } = useFloatingWindows();
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const [isMobile, setIsMobile] = useState(false);
    const [isDragging, setIsDragging] = useState(false);
    /**
     * 处理标题栏双击事件 - 切换全屏状态
     */
    const handleHeaderDoubleClick = useCallback((e: React.MouseEvent) => {
        // 确保双击的是标题栏区域，不是按钮
        const target = e.target as HTMLElement;
        if (!target.closest('button')) {
            e.preventDefault();
            e.stopPropagation();
            toggleMaximize(window.id);
        }
    }, [window.id, toggleMaximize]);
    // 检测是否为移动设备
    useEffect(() => {
        const checkMobile = () => {
            const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                ('ontouchstart' in globalThis) ||
                (globalThis.innerWidth <= 768);
            setIsMobile(isMobileDevice);
        };

        checkMobile();
        globalThis.addEventListener('resize', checkMobile);
        return () => globalThis.removeEventListener('resize', checkMobile);
    }, []);
    /**
     * 处理iframe容器的鼠标事件 - 简化版本
     * CSS已经处理了拖拽冲突，这里只需要处理窗口置顶
     */
    const handleIframeContainerMouseDown = useCallback((e: React.MouseEvent) => {
        onBringToFront();
    }, [onBringToFront]);

    /**
     * 设置iframe内部的点击监听器（仅适用于同域iframe）
     * 对于跨域iframe，依赖容器的点击事件
     */
    const setupIframeClickListener = useCallback(() => {
        const iframe = iframeRef.current;
        if (!iframe || !iframe.contentWindow) return;

        try {
            // 尝试为iframe内容添加点击监听器
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (iframeDoc) {
                const handleIframeClick = () => {
                    onBringToFront();
                };

                // 添加点击监听器
                iframeDoc.addEventListener('click', handleIframeClick);
                iframeDoc.addEventListener('mousedown', handleIframeClick);
            }
        } catch (error) {
            // 跨域iframe无法访问内容，依赖容器的点击事件
            // console.debug('[FloatingWindow] Cross-origin iframe detected, using container click detection');
        }
    }, [onBringToFront]);





    // 处理关闭动画
    const handleClose = () => {
        // console.log(`[FloatingWindow] 开始关闭窗口: ${window.id}`);

        // 发送窗口关闭消息
        globalMessageManager.dispatchMessage({
            id: `close_${window.id}_${Date.now()}`,
            type: 'WINDOW_STATE',
            timestamp: Date.now(),
            windowId: window.id,
            source: 'system',
            payload: {
                state: 'closed'
            }
        });

        // 先启动关闭动画
        // console.log(`[FloatingWindow] 调用onClose回调: ${window.id}`);
        onClose();

        // 等待动画完成后真正删除窗口
        setTimeout(() => {
            // console.log(`[FloatingWindow] 延迟删除窗口: ${window.id}`);
            try {
                removeWindow(window.id);
                // console.log(`[FloatingWindow] 窗口已删除: ${window.id}`);
            } catch (error) {
                // console.error(`[FloatingWindow] 删除窗口失败: ${window.id}`, error);
                // 如果removeWindow失败，直接强制删除
                const { windows } = useFloatingWindows.getState();
                const filteredWindows = windows.filter(w => w.id !== window.id);
                useFloatingWindows.setState({ windows: filteredWindows });
                // console.log(`[FloatingWindow] 强制删除窗口完成: ${window.id}`);
            }
        }, 500); // 与CSS动画时间匹配
    };

    // 设置消息通信
    useEffect(() => {
        const unsubscribers: Array<() => void> = [];

        // 注册终端命令处理器
        if (onTerminalCommand) {
            const unsubscribeCommand = globalMessageManager.on('TERMINAL_COMMAND', async (message: TerminalCommandMessage) => {
                // 只处理指定给此窗口的消息，或者没有指定窗口ID的消息
                if (message.windowId && message.windowId !== window.id) return;

                try {
                    const { command, params } = message.payload;
                    const result = await onTerminalCommand(command, params);

                    // 发送响应
                    await globalMessageManager.sendTerminalResponse(
                        message.id,
                        result.success,
                        command,
                        result.result,
                        result.error
                    );
                } catch (error) {
                    // console.error('[FloatingWindow] Error executing terminal command:', error);
                    await globalMessageManager.sendTerminalResponse(
                        message.id,
                        false,
                        message.payload.command,
                        undefined,
                        error instanceof Error ? error.message : 'Unknown error'
                    );
                }
            });
            unsubscribers.push(unsubscribeCommand);
        }

        // 注册浮窗操作处理器
        const unsubscribeOperation = globalMessageManager.on('WINDOW_OPERATION', async (message: WindowOperationMessage) => {
            if (message.windowId !== window.id) return;

            try {
                const { operation, data, target } = message.payload;
                let result: any = null;

                switch (operation) {
                    case 'query':
                        result = handleQueryOperation(target);
                        break;
                    case 'update':
                        result = handleUpdateOperation(data);
                        break;
                    case 'move':
                        result = handleMoveOperation(data);
                        break;
                    case 'resize':
                        result = handleResizeOperation(data);
                        break;
                    case 'close':
                        handleClose();
                        result = { success: true };
                        break;
                    case 'focus':
                        onBringToFront();
                        result = { success: true };
                        break;
                    case 'dom_query':
                        result = await handleDOMQueryOperation(message.payload.options);
                        break;
                    case 'dom_update':
                        result = await handleDOMUpdateOperation(data, message.payload.options);
                        break;
                    case 'dom_execute':
                        result = await handleDOMExecuteOperation(message.payload.options);
                        break;
                    default:
                        throw new Error(`Unsupported operation: ${operation}`);
                }

                if (message.payload.expectResponse) {
                    await globalMessageManager.sendWindowOperationResponse(
                        message.id,
                        true,
                        operation,
                        result
                    );
                }
            } catch (error) {
                if (message.payload.expectResponse) {
                    await globalMessageManager.sendWindowOperationResponse(
                        message.id,
                        false,
                        message.payload.operation,
                        undefined,
                        error instanceof Error ? error.message : 'Unknown error'
                    );
                }
            }
        });
        unsubscribers.push(unsubscribeOperation);

        // 注册通知处理器
        const unsubscribeNotification = globalMessageManager.on('NOTIFICATION', async (message: NotificationMessage) => {
            if (message.windowId !== window.id) return;

            // 在浮窗中显示通知（可以扩展为更复杂的通知系统）
            handleNotification(message.payload);
        });
        unsubscribers.push(unsubscribeNotification);

        // 🆕 注册 CodeMirror 操作处理器
        const unsubscribeCodeMirror = globalMessageManager.on('CODEMIRROR_OPERATION', async (message: CodeMirrorOperationMessage) => {
            if (message.windowId !== window.id) return;

            // console.log('[FloatingWindow] 收到 CodeMirror 操作请求:', message.id, message.payload);

            try {
                const result = await handleCodeMirrorOperation(message);
                // console.log('[FloatingWindow] CodeMirror 操作完成:', message.id, result);

                if (message.payload.expectResponse) {
                    // console.log('[FloatingWindow] 发送 CodeMirror 响应:', message.id);
                    await globalMessageManager.sendCodeMirrorResponse(
                        message.id,
                        true,
                        message.payload.operation,
                        result
                    );
                    // console.log('[FloatingWindow] CodeMirror 响应已发送:', message.id);
                }
            } catch (error) {
                // console.error('[FloatingWindow] CodeMirror operation error:', error);

                if (message.payload.expectResponse) {
                    // console.log('[FloatingWindow] 发送 CodeMirror 错误响应:', message.id, error);
                    await globalMessageManager.sendCodeMirrorResponse(
                        message.id,
                        false,
                        message.payload.operation,
                        undefined,
                        error instanceof Error ? error.message : 'Unknown error'
                    );
                }
            }
        });
        unsubscribers.push(unsubscribeCodeMirror);

        // 🆕 注册 CodeMirror AI 处理器
        const unsubscribeCodeMirrorAI = globalMessageManager.on('CODEMIRROR_AI', async (message: CodeMirrorAIMessage) => {
            if (message.windowId !== window.id) return;

            try {
                const result = await handleCodeMirrorAI(message);

                if (message.payload.expectResponse) {
                    await globalMessageManager.sendCodeMirrorAIResponse(
                        message.id,
                        message.payload.action,
                        true,
                        result
                    );
                }
            } catch (error) {
                // console.error('[FloatingWindow] CodeMirror AI operation error:', error);

                if (message.payload.expectResponse) {
                    await globalMessageManager.sendCodeMirrorAIResponse(
                        message.id,
                        message.payload.action,
                        false,
                        undefined,
                        undefined,
                        error instanceof Error ? error.message : 'Unknown error'
                    );
                }
            }
        });
        unsubscribers.push(unsubscribeCodeMirrorAI);

        return () => {
            unsubscribers.forEach(unsubscribe => unsubscribe());
        };
    }, [onTerminalCommand, window.id]);

    // 处理查询操作
    const handleQueryOperation = (target?: string) => {
        const windowInfo = {
            id: window.id,
            title: window.title,
            url: window.url,
            position: { x: window.x, y: window.y },
            size: { width: window.width, height: window.height },
            zIndex: window.zIndex,
            isVisible: window.isVisible,
            isClosing: window.isClosing
        };

        if (target) {
            // 如果指定了目标，尝试查询iframe内容
            try {
                const iframe = iframeRef.current;
                if (iframe && iframe.contentWindow) {
                    // 向iframe发送查询请求
                    iframe.contentWindow.postMessage({
                        type: 'QUERY_REQUEST',
                        target,
                        timestamp: Date.now()
                    }, '*');
                }
            } catch (error) {
                // 跨域限制，返回基本信息
            }
        }

        return windowInfo;
    };

    // 处理更新操作
    const handleUpdateOperation = (data: any) => {
        onUpdate(data);
        return { success: true, updated: data };
    };

    // 🆕 处理移动操作（带缓动）
    const handleMoveOperation = (data: { x?: number; y?: number; duration?: number; easing?: string }) => {
        const updates: Partial<FloatingWindowType> = {};
        if (data.x !== undefined) updates.x = data.x;
        if (data.y !== undefined) updates.y = data.y;

        // 使用缓动更新（只有从命令行操作才启用缓动）
        const { animatedUpdateWindow } = useFloatingWindows.getState();
        animatedUpdateWindow(window.id, updates, {
            duration: data.duration || 300,
            easing: data.easing || 'ease-out',
            type: 'move'
        });

        return { success: true, position: updates };
    };

    // 🆕 处理调整大小操作（带缓动）
    const handleResizeOperation = (data: { width?: number; height?: number; duration?: number; easing?: string }) => {
        const updates: Partial<FloatingWindowType> = {};
        if (data.width !== undefined) updates.width = data.width;
        if (data.height !== undefined) updates.height = data.height;

        // 使用缓动更新（只有从命令行操作才启用缓动）
        const { animatedUpdateWindow } = useFloatingWindows.getState();
        animatedUpdateWindow(window.id, updates, {
            duration: data.duration || 300,
            easing: data.easing || 'ease-out',
            type: 'resize'
        });

        return { success: true, size: updates };
    };

    // 处理通知
    const handleNotification = (payload: any) => {
        // 这里可以实现一个简单的通知显示
        // 暂时使用console.log，实际项目中可以扩展为Toast通知等
        console.log(`[${payload.level.toUpperCase()}] ${payload.title}: ${payload.message}`);

        // 可以向iframe发送通知
        try {
            const iframe = iframeRef.current;
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'NOTIFICATION',
                    ...payload,
                    timestamp: Date.now()
                }, '*');
            }
        } catch (error) {
            // 静默处理跨域错误
        }
    };

    // 🆕 检测页面类型（站内 vs 外部）
    const isInternalPage = (url: string): boolean => {
        if (!url) return false;

        // 相对路径都是站内页面
        if (url.startsWith('/')) return true;

        // 检查是否是当前域名
        try {
            const urlObj = new URL(url);
            const currentHost = globalThis.location.host;
            return urlObj.host === currentHost;
        } catch {
            return false;
        }
    };

    // 🆕 为站内页面注入DOM查询处理脚本
    const injectDOMQueryHandler = (iframe: HTMLIFrameElement): Promise<void> => {
        return new Promise((resolve) => {
            try {
                if (!iframe.contentDocument || !iframe.contentWindow) {
                    resolve();
                    return;
                }

                // 检查是否已经注入过
                if (iframe.contentDocument.querySelector('#dom-query-handler-script')) {
                    resolve();
                    return;
                }

                // 创建DOM查询处理脚本
                const script = iframe.contentDocument.createElement('script');
                script.id = 'dom-query-handler-script';
                script.textContent = `
                    // DOM查询处理器
                    if (!window.domQueryHandlerInitialized) {
                        window.domQueryHandlerInitialized = true;

                        const handleMessage = (event) => {
                            const { type, requestId } = event.data;

                            try {
                                switch (type) {
                                    case 'DOM_QUERY_REQUEST':
                                        handleDOMQuery(event.data, requestId);
                                        break;
                                    case 'DOM_UPDATE_REQUEST':
                                        handleDOMUpdate(event.data, requestId);
                                        break;
                                    case 'DOM_EXECUTE_REQUEST':
                                        handleDOMExecute(event.data, requestId);
                                        break;
                                }
                            } catch (error) {
                                sendResponse(requestId, type.replace('_REQUEST', '_RESPONSE'), null, error.message);
                            }
                        };

                        const handleDOMQuery = (data, requestId) => {
                            const { selector, attribute } = data;
                            console.log('[DOM Handler] Query:', selector, attribute);

                            try {
                                const elements = document.querySelectorAll(selector);
                                if (elements.length === 0) {
                                    throw new Error('No matching element found: ' + selector);
                                }

                                let result;
                                if (elements.length === 1) {
                                    result = getElementProperty(elements[0], attribute);
                                } else {
                                    result = Array.from(elements).map(el => getElementProperty(el, attribute));
                                }

                                sendResponse(requestId, 'DOM_QUERY_RESPONSE', result);
                            } catch (error) {
                                sendResponse(requestId, 'DOM_QUERY_RESPONSE', null, error.message);
                            }
                        };

                        const handleDOMUpdate = (data, requestId) => {
                            const { selector, attribute, content } = data;

                            try {
                                const elements = document.querySelectorAll(selector);
                                if (elements.length === 0) {
                                    throw new Error('No matching element found: ' + selector);
                                }

                                let updateCount = 0;
                                elements.forEach(element => {
                                    setElementProperty(element, attribute, content);
                                    updateCount++;
                                });

                                sendResponse(requestId, 'DOM_UPDATE_RESPONSE', {
                                    updatedElements: updateCount,
                                    selector,
                                    attribute,
                                    newValue: content
                                });
                            } catch (error) {
                                sendResponse(requestId, 'DOM_UPDATE_RESPONSE', null, error.message);
                            }
                        };

                        const handleDOMExecute = (data, requestId) => {
                            const { selector, method, args = [] } = data;

                            try {
                                const elements = document.querySelectorAll(selector);
                                if (elements.length === 0) {
                                    throw new Error('No matching element found: ' + selector);
                                }

                                const results = [];
                                elements.forEach(element => {
                                    if (typeof element[method] === 'function') {
                                        const result = element[method].apply(element, args);
                                        results.push(result);
                                    } else {
                                        throw new Error('Method not found: ' + method);
                                    }
                                });

                                sendResponse(requestId, 'DOM_EXECUTE_RESPONSE', {
                                    executedElements: elements.length,
                                    method,
                                    args,
                                    results: results.length === 1 ? results[0] : results
                                });
                            } catch (error) {
                                sendResponse(requestId, 'DOM_EXECUTE_RESPONSE', null, error.message);
                            }
                        };

                        const getElementProperty = (element, attribute) => {
                            switch (attribute) {
                                case 'textContent': return element.textContent;
                                case 'innerHTML': return element.innerHTML;
                                case 'outerHTML': return element.outerHTML;
                                case 'value': return element.value;
                                case 'className': return element.className;
                                case 'id': return element.id;
                                case 'tagName': return element.tagName;
                                case 'classList': return Array.from(element.classList);
                                case 'attributes':
                                    return Array.from(element.attributes).map(attr => ({
                                        name: attr.name,
                                        value: attr.value
                                    }));
                                default:
                                    return element.hasAttribute(attribute)
                                        ? element.getAttribute(attribute)
                                        : element[attribute];
                            }
                        };

                        const setElementProperty = (element, attribute, value) => {
                            switch (attribute) {
                                case 'textContent': element.textContent = value; break;
                                case 'innerHTML': element.innerHTML = value; break;
                                case 'value': element.value = value; break;
                                case 'className': element.className = value; break;
                                case 'id': element.id = value; break;
                                default:
                                    if (element.hasAttribute(attribute)) {
                                        element.setAttribute(attribute, value);
                                    } else {
                                        element[attribute] = value;
                                    }
                            }
                        };

                        const sendResponse = (requestId, type, result, error = null) => {
                            window.parent.postMessage({
                                type,
                                requestId,
                                result,
                                error,
                                timestamp: Date.now()
                            }, '*');
                        };

                        window.addEventListener('message', handleMessage);
                        // console.log('[DOM Handler] DOM query handler initialized');
                    }
                `;

                iframe.contentDocument.head.appendChild(script);
                // console.log('[FloatingWindow] 已为站内页面注入DOM查询处理器');
                resolve();

            } catch (error) {
                // console.warn('[FloatingWindow] 注入DOM查询处理器失败:', error);
                resolve();
            }
        });
    };

    // 🆕 处理DOM查询操作（改进版）
    const handleDOMQueryOperation = async (options: any) => {
        const iframe = iframeRef.current;
        if (!iframe || !iframe.contentWindow) {
            throw new Error('iframe not available');
        }

        const { selector, attribute = 'textContent', timeout = 5000 } = options || {};

        if (!selector) {
            throw new Error('Missing CSS selector');
        }

        // 检查是否是外部页面
        if (!window.url || !isInternalPage(window.url)) {
            throw new Error(`Cannot query external website content: ${window.url || 'Unknown URL'}\nDue to browser cross-origin security restrictions, cannot access DOM elements of external websites.\nSuggestion: Use proxy mode to access external websites, or query internal pages.`);
        }

        // 为站内页面注入DOM查询处理器
        await injectDOMQueryHandler(iframe);

        return new Promise((resolve, reject) => {
            const requestId = `dom_query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // 设置超时
            const timeoutId = setTimeout(() => {
                reject(new Error(`DOM query timeout (${timeout}ms)`));
            }, timeout);

            // 监听响应
            const handleResponse = (event: MessageEvent) => {
                if (event.data?.type === 'DOM_QUERY_RESPONSE' && event.data?.requestId === requestId) {
                    globalThis.removeEventListener('message', handleResponse);
                    clearTimeout(timeoutId);

                    if (event.data.error) {
                        reject(new Error(event.data.error));
                    } else {
                        resolve(event.data.result);
                    }
                }
            };

            globalThis.addEventListener('message', handleResponse);

            // 发送查询请求到iframe
            iframe.contentWindow?.postMessage({
                type: 'DOM_QUERY_REQUEST',
                requestId,
                selector,
                attribute,
                timestamp: Date.now()
            }, '*');
        });
    };

    // 🆕 处理DOM更新操作（改进版）
    const handleDOMUpdateOperation = async (content: string, options: any) => {
        const iframe = iframeRef.current;
        if (!iframe || !iframe.contentWindow) {
            throw new Error('iframe not available');
        }

        const { selector, attribute = 'textContent', timeout = 5000 } = options || {};

        if (!selector) {
            throw new Error('Missing CSS selector');
        }

        // 检查是否是外部页面
        if (!window.url || !isInternalPage(window.url)) {
            throw new Error(`Cannot update external website content: ${window.url || 'Unknown URL'}\nDue to browser cross-origin security restrictions, cannot modify DOM elements of external websites.`);
        }

        // 为站内页面注入DOM查询处理器
        await injectDOMQueryHandler(iframe);

        return new Promise((resolve, reject) => {
            const requestId = `dom_update_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // 设置超时
            const timeoutId = setTimeout(() => {
                reject(new Error(`DOM update timeout (${timeout}ms)`));
            }, timeout);

            // 监听响应
            const handleResponse = (event: MessageEvent) => {
                if (event.data?.type === 'DOM_UPDATE_RESPONSE' && event.data?.requestId === requestId) {
                    globalThis.removeEventListener('message', handleResponse);
                    clearTimeout(timeoutId);

                    if (event.data.error) {
                        reject(new Error(event.data.error));
                    } else {
                        resolve(event.data.result);
                    }
                }
            };

            globalThis.addEventListener('message', handleResponse);

            // 发送更新请求到iframe
            iframe.contentWindow?.postMessage({
                type: 'DOM_UPDATE_REQUEST',
                requestId,
                selector,
                attribute,
                content,
                timestamp: Date.now()
            }, '*');
        });
    };

    // 🆕 处理DOM方法执行操作（改进版）
    const handleDOMExecuteOperation = async (options: any) => {
        const iframe = iframeRef.current;
        if (!iframe || !iframe.contentWindow) {
            throw new Error('iframe not available');
        }

        const { selector, method, args = [], timeout = 5000 } = options || {};

        if (!selector) {
            throw new Error('Missing CSS selector');
        }

        if (!method) {
            throw new Error('Missing method name');
        }

        // 检查是否是外部页面
        if (!window.url || !isInternalPage(window.url)) {
            throw new Error(`Cannot execute method on external website: ${window.url || 'Unknown URL'}\nDue to browser cross-origin security restrictions, cannot execute DOM methods on external websites.`);
        }

        // 为站内页面注入DOM查询处理器
        await injectDOMQueryHandler(iframe);

        return new Promise((resolve, reject) => {
            const requestId = `dom_execute_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // 设置超时
            const timeoutId = setTimeout(() => {
                reject(new Error(`DOM method execution timeout (${timeout}ms)`));
            }, timeout);

            // 监听响应
            const handleResponse = (event: MessageEvent) => {
                if (event.data?.type === 'DOM_EXECUTE_RESPONSE' && event.data?.requestId === requestId) {
                    globalThis.removeEventListener('message', handleResponse);
                    clearTimeout(timeoutId);

                    if (event.data.error) {
                        reject(new Error(event.data.error));
                    } else {
                        resolve(event.data.result);
                    }
                }
            };

            globalThis.addEventListener('message', handleResponse);

            // 发送执行请求到iframe
            iframe.contentWindow?.postMessage({
                type: 'DOM_EXECUTE_REQUEST',
                requestId,
                selector,
                method,
                args,
                timestamp: Date.now()
            }, '*');
        });
    };

    // 处理iframe加载和通信
    useEffect(() => {
        const iframe = iframeRef.current;
        if (!iframe || !window.url) return;

        const handleIframeLoad = () => {
            // iframe加载完成，发送窗口就绪消息
            globalMessageManager.dispatchMessage({
                id: `ready_${window.id}_${Date.now()}`,
                type: 'WINDOW_STATE',
                timestamp: Date.now(),
                windowId: window.id,
                source: 'system',
                payload: {
                    state: 'ready',
                    data: {
                        windowId: window.id,
                        title: window.title,
                        url: window.url
                    }
                }
            });

            // 向iframe发送初始化信息（如果iframe支持）
            try {
                iframe.contentWindow?.postMessage({
                    type: 'WINDOW_INIT',
                    windowId: window.id,
                    timestamp: Date.now(),
                    payload: {
                        windowInfo: {
                            id: window.id,
                            title: window.title,
                            width: window.width,
                            height: window.height
                        }
                    }
                }, '*');
            } catch (error) {
                // 忽略跨域错误
                // console.debug('[FloatingWindow] Could not send init message to iframe (likely cross-origin)');
            }
        };

        iframe.addEventListener('load', handleIframeLoad);
        return () => iframe.removeEventListener('load', handleIframeLoad);
    }, [window.id, window.title, window.url, window.width, window.height]);

    // 计算动画状态
    const animationClass = window.isClosing
        ? 'floating-window-exit'
        : window.isVisible
            ? 'floating-window-enter-active'
            : 'floating-window-enter';

    // 🆕 计算缓动样式
    const getTransitionStyle = () => {
        if (!window.isAnimating) return {};

        const duration = window.animationDuration || 300;
        const easing = window.animationEasing || 'ease-out';
        const type = window.animationType || 'both';

        let properties: string[] = [];
        if (type === 'move' || type === 'both') {
            properties.push('transform');
        }
        if (type === 'resize' || type === 'both') {
            properties.push('width', 'height');
        }

        return {
            transition: properties.map(prop => `${prop} ${duration}ms ${easing}`).join(', ')
        };
    };

    // 🆕 处理 CodeMirror 操作
    const handleCodeMirrorOperation = async (message: CodeMirrorOperationMessage) => {
        const iframe = iframeRef.current;
        if (!iframe || !iframe.contentWindow) {
            throw new Error('iframe not available');
        }

        const { operation, target, data, options } = message.payload;
        const timeout = options?.timeout || 5000;

        return new Promise((resolve, reject) => {
            const requestId = `codemirror_op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // 设置超时
            const timeoutId = setTimeout(() => {
                reject(new Error(`CodeMirror operation timeout (${timeout}ms)`));
            }, timeout);

            // 监听响应
            const handleResponse = (event: MessageEvent) => {
                // console.log('[FloatingWindow] CodeMirror 收到消息:', event.data?.type, 'requestId:', event.data?.requestId, 'expected:', requestId);
                if (event.data?.type === 'CODEMIRROR_OPERATION_RESPONSE' && event.data?.requestId === requestId) {
                    globalThis.removeEventListener('message', handleResponse);
                    clearTimeout(timeoutId);

                    if (event.data.error) {
                        // console.log('[FloatingWindow] CodeMirror 操作错误:', event.data.error);
                        reject(new Error(event.data.error));
                    } else {
                        // console.log('[FloatingWindow] CodeMirror 操作成功:', event.data.result);
                        resolve(event.data.result);
                    }
                }
            };

            globalThis.addEventListener('message', handleResponse);

            // 发送 CodeMirror 操作请求到 iframe
            iframe.contentWindow?.postMessage({
                type: 'CODEMIRROR_OPERATION_REQUEST',
                requestId,
                operation,
                target,
                data,
                options,
                timestamp: Date.now()
            }, '*');
        });
    };

    // 🆕 处理 CodeMirror AI 操作
    const handleCodeMirrorAI = async (message: CodeMirrorAIMessage) => {
        const iframe = iframeRef.current;
        if (!iframe || !iframe.contentWindow) {
            throw new Error('iframe not available');
        }

        const { action, context, prompt, options } = message.payload;
        const timeout = options?.maxTokens || 10000; // 使用 maxTokens 作为超时时间

        return new Promise((resolve, reject) => {
            const requestId = `codemirror_ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // 设置超时
            const timeoutId = setTimeout(() => {
                reject(new Error(`CodeMirror AI operation timeout (${timeout}ms)`));
            }, timeout);

            // 监听响应
            const handleResponse = (event: MessageEvent) => {
                if (event.data?.type === 'CODEMIRROR_AI_RESPONSE' && event.data?.requestId === requestId) {
                    globalThis.removeEventListener('message', handleResponse);
                    clearTimeout(timeoutId);

                    if (event.data.error) {
                        reject(new Error(event.data.error));
                    } else {
                        resolve(event.data.result);
                    }
                }
            };

            globalThis.addEventListener('message', handleResponse);

            // 发送 CodeMirror AI 请求到 iframe
            iframe.contentWindow?.postMessage({
                type: 'CODEMIRROR_AI_REQUEST',
                requestId,
                action,
                context,
                prompt,
                options,
                timestamp: Date.now()
            }, '*');
        });
    };

    return (
        <Rnd
            size={{ width: window.width, height: window.height }}
            position={{ x: window.x, y: window.y }}
            onDragStart={(e) => {
                // 标记开始拖拽
                setIsDragging(true);
            }}
            onDrag={(e, d) => {
                // 拖拽过程中保持拖拽状态
                if (!isDragging) {
                    setIsDragging(true);
                }
            }}
            onDragStop={(e, d) => {
                try {
                    // 确保位置值有效，使用全局window对象获取屏幕尺寸
                    const x = Math.max(0, Math.min(globalThis.innerWidth - window.width, d.x || 0));
                    const y = Math.max(0, Math.min(globalThis.innerHeight - window.height, d.y || 0));
                    onUpdate({ x, y });
                } catch (error) {
                    // console.error('[FloatingWindow] Error updating position:', error);
                } finally {
                    // 拖拽结束，清除拖拽状态
                    setIsDragging(false);
                }
            }}

            onResizeStop={(e, direction, ref, delta, position) => {
                try {
                    const width = Math.max(300, parseInt(ref.style.width) || 300);
                    const height = Math.max(200, parseInt(ref.style.height) || 200);
                    const x = Math.max(0, Math.min(globalThis.innerWidth - width, position.x || 0));
                    const y = Math.max(0, Math.min(globalThis.innerHeight - height, position.y || 0));

                    onUpdate({ width, height, x, y });
                } catch (error) {
                    // console.error('[FloatingWindow] Error updating size:', error);
                }
            }}

            minWidth={300}
            minHeight={200}
            dragHandleClassName="floating-window-header"
            style={{
                zIndex: window.zIndex,
                ...getTransitionStyle()
            }}
        >
            <div className={`flex flex-col h-full bg-white backdrop-blur-sm border-2 border-brand-500 shadow-2xl shadow-black/70 overflow-hidden overscroll-none ${animationClass} ${window.isMaximized ? 'floating-window-maximized' : ''} ${isDragging ? 'floating-window-dragging' : ''}`}>
                {/* 标题栏 */}
                <div
                    className="floating-window-header flex items-center justify-between p-2 border-b-2 border-brand-500 cursor-move"
                    onMouseDown={(e) => {
                        // 只在点击标题栏空白区域时置顶，避免影响按钮点击
                        const target = e.target as HTMLElement;
                        if (target === e.currentTarget || (!target.closest('button') && target.closest('.floating-window-header') === e.currentTarget)) {
                            onBringToFront();
                        }
                    }}
                    onTouchStart={(e) => {
                        // 移动端触摸开始时置顶
                        const target = e.target as HTMLElement;
                        if (target === e.currentTarget || (!target.closest('button') && target.closest('.floating-window-header') === e.currentTarget)) {
                            onBringToFront();
                        }
                    }}
                    onDoubleClick={handleHeaderDoubleClick}
                >
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                        {/* 代理模式指示器 */}
                        {window.originalUrl && (
                            <div className="flex items-center space-x-1 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full shrink-0">
                                <span>proxy</span>
                            </div>
                        )}
                        <div className="text-sm font-medium text-brand-600 truncate">
                            {window.title}
                        </div>
                    </div>

                    <div className="flex items-center space-x-1 shrink-0">
                        {/* 原始URL显示按钮 */}
                        {window.originalUrl && (
                            <button
                                onClick={() => {
                                    navigator.clipboard?.writeText(window.originalUrl!);
                                    // 简单的提示反馈
                                    const btn = document.activeElement as HTMLButtonElement;
                                    const originalTitle = btn.title;
                                    btn.title = 'Copied!';
                                    setTimeout(() => {
                                        btn.title = originalTitle;
                                    }, 2000);
                                }}
                                onTouchStart={(e) => e.stopPropagation()}
                                onTouchEnd={(e) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                }}
                                className={`hover:bg-gray-100 hover:text-gray-700 text-blue-600 text-xs touch-manipulation flex items-center justify-center ${isMobile ? 'w-[14px] h-[14px]' : 'w-[20px] h-[20px]'
                                    }`}
                                title={`Original URL: ${window.originalUrl}\nClick to copy to clipboard`}
                            >
                                <svg className={isMobile ? "w-3 h-3" : "w-4 h-4"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                </svg>
                            </button>
                        )}
                        <button
                            onClick={handleClose}
                            onTouchStart={(e) => {
                                // 阻止触摸事件冒泡，避免与拖拽冲突
                                e.stopPropagation();
                            }}
                            onTouchEnd={(e) => {
                                // 处理触摸结束事件
                                e.stopPropagation();
                                e.preventDefault();
                                handleClose();
                            }}
                            className={`hover:bg-gray-100 hover:text-gray-700 text-brand-600 touch-manipulation flex items-center justify-center ${isMobile ? 'w-[14px] h-[14px]' : 'w-[20px] h-[20px]'
                                }`}
                            title="Close"
                        >
                            <X size="14" />
                        </button>
                    </div>
                </div>

                {/* 内容区域 */}
                <div className="floating-window-content flex-1 overflow-hidden relative">
                    {window.url ? (
                        <>
                            <div
                                className="relative w-full h-full"
                                onMouseDown={handleIframeContainerMouseDown}
                            >
                                <iframe
                                    ref={iframeRef}
                                    src={window.url}
                                    className="w-full h-full border-0"
                                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-presentation allow-downloads allow-downloads-without-user-activation allow-modals"
                                    title={window.title}
                                    onLoad={setupIframeClickListener}
                                />
                            </div>
                        </>
                    ) : (
                        <div
                            className="w-full h-full p-2 overflow-auto overscroll-contain"
                            onMouseDown={onBringToFront}
                        >
                            <div className="text-brand-600 whitespace-pre-wrap">
                                {window.content || 'Hello World'}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </Rnd>
    );
}