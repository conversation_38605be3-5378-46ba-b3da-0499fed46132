// remark-embed-media.ts
import type { Root, <PERSON>, Text, Paragraph, RootContent } from "mdast";
import QRCode from "qrcode";
import type { Plugin } from "unified";
import { visit } from "unist-util-visit";

/* —— 1. 媒体识别 —— */
interface Match {
  service: "youtube" | "vimeo";
  id: string;
}

function matchURL(raw: string): Match | null {
  let url: URL;
  try {
    url = new URL(raw);
  } catch {
    return null;
  }

  if (url.hostname === "www.youtube.com" && url.pathname.startsWith("/embed/")) {
    const m = url.pathname.match(/^\/embed\/([\w-]{11})(?:$|[/?#])/);
    return m ? { service: "youtube", id: m[1] } : null;
  }

  if (url.hostname === "player.vimeo.com" && url.pathname.startsWith("/video/")) {
    const m = url.pathname.match(/^\/video\/(\d{6,12})(?:$|[/?#])/);
    return m ? { service: "vimeo", id: m[1] } : null;
  }

  return null;
}

/* —— 2. remark 插件 —— */
export const remarkEmbedMedia: Plugin<[], Root> = () => async tree => {
  const promises: Promise<void>[] = [];

  visit(tree, "paragraph", para => {
    const p = para as Paragraph;
    const children = p.children;

    for (let i = 0; i < children.length; i++) {
      const ch = children[i] as RootContent;

      // 仅在 link/text 节点尝试解析 URL
      let url: string | null = null;
      if (ch.type === "link") url = (ch as Link).url;
      else if (ch.type === "text") {
        const v = (ch as Text).value.trim();
        if (/^\S+$/.test(v)) url = v;
      }

      const info = url && matchURL(url);
      if (!info) continue;

      /* 生成 iframe 源 */
      const src =
        info.service === "youtube"
          ? `https://www.youtube.com/embed/${info.id}`
          : `https://player.vimeo.com/video/${info.id}`;

      /* ⚠️ async：生成二维码 Data URI */
      const promise = (async () => {
        const dataUrl = await QRCode.toDataURL(src, { width: 360, margin: 2 });

        const html = `
                <iframe class="embed-media" src="${src}" width="640" height="360" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen loading="lazy"></iframe>
                <a class="qr-print-link" href="${src}" target="_blank" rel="noopener noreferrer">
                    <div class="qr-frame">
                        <img src="${dataUrl}" alt="Scan or click to watch the video" class="qr-print" width="200" height="200" />
                    </div>
                </a>
                `.trim();

        // 用 HTML 节点替换原节点
        children.splice(i, 1, { type: "html", value: html });
      })();

      promises.push(promise);
    }
  });

  /* 等待所有二维码生成完毕 */
  if (promises.length) await Promise.all(promises);
};

export default remarkEmbedMedia;
