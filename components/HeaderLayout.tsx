"use client";

import { Menu } from "lucide-react";
import Link from "next/link";
import { usePathname, useSearchParams, useRouter } from "next/navigation";
import { ReactNode, useEffect, useState } from "react";

import { useSupabase } from "@/components/SupabaseProvider";
import { CustomSheetContent } from "@/components/ui/custom-sheet";
import NotificationBell from "@/components/NotificationBell";

/* --- ShadCN UI 抽屉组件 --- */
import { Sheet, SheetTrigger, SheetClose } from "@/components/ui/sheet";

/* ---------- props 类型 ---------- */
interface NavItem {
  label: string;
  href: string;
  target?: string;
}

interface HeaderLayoutProps {
  children: ReactNode;
  navItems?: NavItem[];
}

export default function HeaderLayout({ children, navItems = [] }: HeaderLayoutProps) {
  const { session, user, supabase, isLoading } = useSupabase();
  const router = useRouter();
  const [open, setOpen] = useState(false);

  // 定期检查会话状态是否需要更新（可能被其他页面标记为无效）
  useEffect(() => {
    if (user) {
      // 设置一个较长时间间隔的检查（例如5秒），仅用于更新UI状态
      // 这不是安全检查，只是为了确保UI反映最新状态
      const checkInterval = setInterval(async () => {
        // TODO: 可在此调用后端接口验证 supabase session
      }, 5000);

      return () => clearInterval(checkInterval);
    }
  }, [user]);

  /* ---------- 当前路径（含查询串） ---------- */
  const pathname = usePathname(); // e.g. "/papers"
  const searchParams = useSearchParams(); // URLSearchParams
  const currentUrl = searchParams.size > 0 ? `${pathname}?${searchParams.toString()}` : pathname;

  /* 共用的导航列表（桌面横向 / 抽屉纵向） */
  const NavLinks = ({ vertical = false }: { vertical?: boolean }) => (
    <ul
      className={
        vertical
          ? "flex flex-col space-y-10"
          : "flex items-center space-x-4 lg:space-x-6 flex-shrink-0"
      }
    >
      {navItems.map(({ label, href, target }) => {
        const active = pathname === href;
        // 特殊处理：如果是docs链接，强制在新窗口打开
        const linkTarget = label.toLowerCase() === "docs" ? "_blank" : target;
        const linkRel = linkTarget === "_blank" ? "noopener noreferrer" : undefined;

        return (
          <li key={href} className={vertical ? "" : "flex-shrink-0"}>
            {vertical ? (
              <SheetClose asChild>
                <Link
                  href={href}
                  target={linkTarget}
                  rel={linkRel}
                  className={`font-menu transition-colors text-2xl px-1 py-1.5 ${active ? "text-brand-700" : "text-brand-500 hover:text-brand-700"
                    }`}
                >
                  {label}
                </Link>
              </SheetClose>
            ) : (
              <Link
                href={href}
                target={linkTarget}
                rel={linkRel}
                className={`font-menu text-sm transition-colors px-2 py-1 whitespace-nowrap ${active ? "text-brand-700" : "text-brand-500 hover:text-brand-700"
                  }`}
              >
                {label}
              </Link>
            )}
          </li>
        );
      })}
    </ul>
  );

  /* 用户区（桌面 & 抽屉复用） */
  const UserSection = ({ vertical = false, current }: { vertical?: boolean; current: string }) => {
    // 使用验证过的用户对象
    const isValidSession = !!user;

    // 登出函数
    const handleLogout = async (e: React.MouseEvent) => {
      e.preventDefault();
      console.log("Logout 按钮被点击");

      try {
        // 1. 客户端登出（清除本地会话）
        const { error } = await supabase.auth.signOut();
        if (error && error.name !== "AuthSessionMissingError") {
          console.error("客户端登出错误", error);
        }

        // 2. 服务器端登出（清除服务器端会话）
        try {
          await fetch("/api/auth/logout", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            keepalive: true,
          });
        } catch (err) {
          console.warn("服务器端登出请求失败", err);
          // 服务器端登出失败不影响客户端登出
        }

        // 3. 跳转到登录页
        window.location.href = "/account";
      } catch (error) {
        console.error("登出过程中发生错误:", error);
        // 即使出错也跳转到登录页
        window.location.href = "/account";
      }
    };

    return (
      <div
        className={
          vertical
            ? "flex flex-col space-y-6 pt-8 border-t border-gray-200"
            : "flex items-center space-x-2 lg:space-x-4 flex-shrink-0"
        }
      >
        {isValidSession ? (
          <>
            {/* 纵向菜单中的用户信息和通知区域 */}
            {vertical ? (
              <div className="space-y-6">
                {/* 用户信息卡片 */}
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-lg font-medium text-gray-900 truncate">
                        {user?.user_metadata.name ?? "User"}
                      </p>
                      <p className="text-sm text-gray-500">
                        {user?.email}
                      </p>
                    </div>
                    {/* 头像占位符 */}
                    <div className="w-12 h-12 bg-brand-100 rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                      <span className="text-brand-600 text-lg font-semibold">
                        {(user?.user_metadata.name ?? "U").charAt(0).toUpperCase()}
                      </span>
                    </div>
                  </div>
                </div>

                {/* 通知和登出按钮区域 */}
                <div className="space-y-4">
                  {/* 通知按钮 */}
                  <div className="bg-white rounded-lg p-3 shadow-sm border border-gray-100">
                    <div className="flex items-center justify-between">
                      <span className="text-base text-gray-700">通知</span>
                      <NotificationBell onMobileNavigate={() => setOpen(false)} />
                    </div>
                  </div>

                  {/* 登出按钮 */}
                  <SheetClose asChild>
                    <button
                      onClick={handleLogout}
                      className="w-full bg-gray-50 hover:bg-gray-100 text-gray-700 hover:text-gray-900 py-3 px-4 rounded-lg text-base font-medium transition-colors border border-gray-200"
                    >
                      退出登录
                    </button>
                  </SheetClose>
                </div>
              </div>
            ) : (
              <>
                {/* 桌面端保持原有样式 */}
                <NotificationBell />
                <span className="text-gray-700 truncate text-xs lg:text-sm max-w-24 lg:max-w-none">
                  {user?.user_metadata.name ?? "User"}
                </span>
                <button onClick={handleLogout} className="text-xs lg:text-sm text-gray-600 hover:text-black whitespace-nowrap">
                  Logout
                </button>
              </>
            )}
          </>
        ) : (
          <>
            {vertical ? (
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 text-center">
                  <p className="text-gray-600 mb-4">登录以获得完整功能</p>
                  <SheetClose asChild>
                    <button
                      onClick={() => router.push(`/account?callbackUrl=${encodeURIComponent(current)}`)}
                      className="w-full bg-brand-500 hover:bg-brand-600 text-white py-3 px-4 rounded-lg text-base font-medium transition-colors"
                    >
                      立即登录
                    </button>
                  </SheetClose>
                </div>
              </div>
            ) : (
              <button
                onClick={() => router.push(`/account?callbackUrl=${encodeURIComponent(current)}`)}
                className="text-xs lg:text-sm text-gray-600 hover:text-black whitespace-nowrap"
              >
                Login
              </button>
            )}
          </>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen flex flex-col overflow-x-hidden">
      {/* ---------- 顶部栏 ---------- */}
      <header className="fixed top-0 left-0 w-full z-50 bg-background overflow-hidden">
        <nav className="w-full flex items-center px-4 py-2 h-16 max-w-full overflow-hidden">
          {/* Logo */}
          <div className="flex-shrink-0 mr-4">
            <Link
              href="/"
              aria-label="Journal of Interactives"
              className="inline-block"
            >
              <svg
                className="h-8 lg:h-8 w-auto fill-brand-600 hover:fill-brand-700 transition-colors"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 156.7 20.11"
              >
                <path d="M12.53,19.74h-4.97V6.06h4.97v2.05c.74-1.5,2.36-2.42,4.42-2.42,3.74,0,4.94,2.61,4.94,5.46v8.59h-4.97v-7.27c0-1.87-.52-2.85-2.02-2.85-1.69,0-2.36,1.35-2.36,3.4v6.72Z" />
                <path d="M24.92,9.49h-1.84v-3.07l1.01-.15c1.38-.21,1.81-.95,2.15-2.21l.46-1.78h3.13v3.77h3.56v3.43h-3.56v5c0,1.17.55,1.63,1.56,1.63.61,0,1.29-.12,2.12-.46v3.22c-1.04.77-2.24,1.23-4.11,1.23-2.3,0-4.48-.95-4.48-4.17v-6.44Z" />
                <path d="M49.03,15.23c-.71,3.13-3.22,4.88-6.84,4.88-4.39,0-7.51-2.64-7.51-6.99s3.19-7.42,7.45-7.42c4.57,0,6.99,3.13,6.99,6.84v1.38h-9.6c.12,1.69,1.35,2.48,2.85,2.48,1.35,0,2.21-.46,2.67-1.53l3.99.37ZM44.4,11.52c-.06-1.23-.86-2.27-2.33-2.27-1.59,0-2.3.98-2.48,2.27h4.82Z" />
                <path d="M61.45,10.91c-.67-.4-1.41-.58-2.36-.58-1.84,0-2.97.86-2.97,3.16v6.26h-4.97V6.06h4.97v2.42c.49-1.53,1.93-2.79,3.74-2.79.83,0,1.56.25,1.9.52l-.31,4.69Z" />
                <path d="M75.78,15.72c0,.71.31,1.01.83,1.01.4,0,.71-.06,1.1-.21v2.64c-.71.49-1.69.89-3.1.89-1.69,0-3.01-.86-3.47-2.42-.8,1.59-2.36,2.42-4.54,2.42-2.67,0-4.42-1.38-4.42-3.71,0-2.64,1.9-3.77,4.88-4.29l3.83-.64v-.22c0-1.13-.52-1.81-1.72-1.81-1.1,0-1.66.71-1.87,1.72l-4.6-.31c.43-3.01,2.67-5.09,6.78-5.09,3.62,0,6.29,1.56,6.29,5.37v4.66ZM70.9,13.82l-2.02.37c-1.2.25-1.9.61-1.9,1.5,0,.71.55,1.17,1.38,1.17,1.41,0,2.55-1.04,2.55-2.82v-.21Z" />
                <path d="M85.75,5.69c4.11,0,6.41,2.67,6.75,5.95l-4.08.28c-.34-1.69-1.17-2.48-2.61-2.48s-2.55,1.01-2.55,3.47,1.04,3.47,2.55,3.47,2.27-.83,2.61-2.51l4.08.31c-.34,3.31-2.64,5.95-6.75,5.95-4.32,0-7.48-2.85-7.48-7.21s3.16-7.21,7.48-7.21Z" />
                <path d="M94.89,9.49h-1.84v-3.07l1.01-.15c1.38-.21,1.81-.95,2.15-2.21l.46-1.78h3.13v3.77h3.56v3.43h-3.56v5c0,1.17.55,1.63,1.56,1.63.61,0,1.29-.12,2.12-.46v3.22c-1.04.77-2.24,1.23-4.11,1.23-2.3,0-4.48-.95-4.48-4.17v-6.44Z" />
                <path d="M105.35.42h5.03v4.36h-5.03V.42ZM110.35,6.06v13.68h-4.97V6.06h4.97Z" />
                <path d="M116.73,19.74l-5.09-13.68h5.21l2.97,8.5h.12l2.98-8.5h4.82l-5.09,13.68h-5.92Z" />
                <path d="M142.34,15.23c-.71,3.13-3.22,4.88-6.84,4.88-4.39,0-7.51-2.64-7.51-6.99s3.19-7.42,7.45-7.42c4.57,0,6.99,3.13,6.99,6.84v1.38h-9.6c.12,1.69,1.35,2.48,2.85,2.48,1.35,0,2.21-.46,2.67-1.53l3.99.37ZM137.71,11.52c-.06-1.23-.86-2.27-2.33-2.27-1.59,0-2.3.98-2.48,2.27h4.82Z" />
                <path d="M147.81,15.23c.18,1.13,1.1,1.78,2.61,1.78,1.1,0,1.69-.4,1.69-.95,0-.52-.31-.89-1.26-1.04l-2.02-.31c-3.31-.52-5.06-1.59-5.06-4.39s2.36-4.63,6.2-4.63,6.29,1.75,6.56,4.6l-4.08.12c-.18-.98-1.04-1.59-2.58-1.59-.95,0-1.5.37-1.5.98,0,.46.37.71.83.77l2.67.46c3.07.49,4.85,1.87,4.85,4.54,0,2.88-2.45,4.54-6.35,4.54s-6.59-1.5-6.87-4.82l4.32-.06Z" />
                <polygon points=".03 0 5 0 5 5.31 3.95 13.72 1.11 13.72 .03 5.31 .03 0" />
                <rect y="15.17" width="5" height="4.94" />
              </svg>
            </Link>
          </div>

          {/* 桌面导航（居中，在可用空间内自适应） */}
          <div className="hidden md:flex items-center justify-center flex-1 min-w-0 px-4">
            <NavLinks />
          </div>

          {/* 桌面用户区（靠右显示） */}
          <div className="hidden md:flex items-center flex-shrink-0 ml-4">
            <UserSection current={currentUrl} />
          </div>

          {/* 移动端汉堡 + 抽屉 */}
          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <button
                className="md:hidden ml-auto p-2 rounded hover:bg-brand-100 flex-shrink-0"
                aria-label="Open Menu"
              >
                <Menu className="h-6 w-6" />
              </button>
            </SheetTrigger>

            <CustomSheetContent
              side="left"
              className="w-64 px-6 py-12 bg-brand-50 border-r-0"
              hideCloseButton={true}
              title="导航菜单"
            >
              {/* 抽屉导航 */}
              <div className="flex flex-col justify-between h-full">
                <div>
                  <NavLinks vertical />
                </div>

                {/* 抽屉用户区 - 移到底部 */}
                <div className="mt-auto">
                  <UserSection vertical current={currentUrl} />
                </div>
              </div>
            </CustomSheetContent>
          </Sheet>
        </nav>
      </header>

      {/* ---------- 主内容 ---------- */}
      <main className="flex-1 pt-16">{children}</main>
    </div>
  );
}
