# 🎨 骰子自定义纹理指南

## 📁 文件结构

```
public/
  textures/
    dice1/
      face1.png  # 骰子1的第1面
      face2.png  # 骰子1的第2面
      face3.png  # 骰子1的第3面
      face4.png  # 骰子1的第4面
      face5.png  # 骰子1的第5面
      face6.png  # 骰子1的第6面
    dice2/
      face1.png  # 骰子2的第1面
      face2.png  # 骰子2的第2面
      ...
    dice3/
      face1.png  # 骰子3的第1面
      face2.png  # 骰子3的第2面
      ...
```

## 🖼️ 纹理要求

### 图片格式
- **推荐**: PNG (支持透明度)
- **支持**: JPG, WEBP, GIF
- **尺寸**: 建议 256x256 或 512x512 像素
- **比例**: 正方形 (1:1)

### 性能建议
- PNG: 最佳质量，支持透明度
- WEBP: 最佳压缩比
- JPG: 适合照片类纹理

## 🎮 使用方法

### 方法1: 修改配置文件

在 `components/DiceSimulation/utils.ts` 中修改 `createExampleTextureConfigs()`:

```typescript
export function createExampleTextureConfigs(): DiceTextureConfig[] {
  return [
    {
      diceId: 'dice-1',
      faces: [
        { imageUrl: '/textures/dice1/face1.png', color: 0xffffff },
        { imageUrl: '/textures/dice1/face2.png', color: 0xffffff },
        { imageUrl: '/textures/dice1/face3.png', color: 0xffffff },
        { imageUrl: '/textures/dice1/face4.png', color: 0xffffff },
        { imageUrl: '/textures/dice1/face5.png', color: 0xffffff },
        { imageUrl: '/textures/dice1/face6.png', color: 0xffffff },
      ]
    },
    // 更多骰子配置...
  ];
}
```

### 方法2: 动态加载

```typescript
import { textureManager, type DiceTextureConfig } from './TextureManager';
import { createCustomTextureDiceConfigs } from './utils';

// 创建自定义纹理配置
const customTextures: DiceTextureConfig[] = [
  {
    diceId: 'my-dice-1',
    faces: [
      { imageUrl: '/my-textures/face1.png' },
      { imageUrl: '/my-textures/face2.png' },
      { imageUrl: '/my-textures/face3.png' },
      { imageUrl: '/my-textures/face4.png' },
      { imageUrl: '/my-textures/face5.png' },
      { imageUrl: '/my-textures/face6.png' },
    ]
  }
];

// 创建骰子配置
const diceConfigs = await createCustomTextureDiceConfigs(customTextures);
```

## 🎨 纹理类型示例

### 1. 纯图片纹理
```typescript
{
  imageUrl: '/textures/dice1/face1.png',
  color: 0xffffff  // 白色基底
}
```

### 2. 纯色纹理
```typescript
{
  color: 0xff4444  // 红色
}
```

### 3. 高级材质
```typescript
{
  imageUrl: '/textures/dice1/face1.png',
  normalMapUrl: '/textures/dice1/face1_normal.png',
  roughnessMapUrl: '/textures/dice1/face1_roughness.png',
  color: 0xffffff,
  material: {
    roughness: 0.7,
    metalness: 0.1,
    transparent: true,
    opacity: 0.9
  }
}
```

## 🔧 集成到项目

### 1. 修改默认配置

在 `components/DiceSimulation/index.tsx` 中:

```typescript
// 替换默认骰子创建
const customTextures = createExampleTextureConfigs();
const customDiceConfigs = await createCustomTextureDiceConfigs(customTextures);

// 使用自定义配置
config.dices = customDiceConfigs;
```

### 2. 预加载纹理

```typescript
// 预加载所有纹理
const textureUrls = [
  '/textures/dice1/face1.png',
  '/textures/dice1/face2.png',
  // ... 更多纹理
];

await textureManager.preloadTextures(textureUrls);
```

## 📝 注意事项

1. **路径**: 图片文件放在 `public/` 目录下
2. **命名**: 使用清晰的命名规则
3. **大小**: 控制文件大小以提高加载速度
4. **缓存**: 纹理会自动缓存，避免重复加载
5. **错误处理**: 如果纹理加载失败，会使用默认颜色

## 🎯 快速开始

1. 在 `public/textures/` 创建文件夹
2. 放入你的PNG图片 (face1.png 到 face6.png)
3. 修改 `createExampleTextureConfigs()` 中的路径
4. 重启开发服务器
5. 享受你的自定义骰子！
