import * as THREE from 'three';
import type { World, RigidBody, Collider } from '@dimforge/rapier3d-compat';
import type { 
  PhysicsConfig, 
  ContainerConfig, 
  DiceConfig, 
  DiceInstance,
  ForceConfig 
} from './types';

/**
 * 骰子物理引擎类
 * 负责管理Rapier.js物理世界和骰子的物理行为
 */
export class DicePhysicsEngine {
  private world: World | null = null;
  private rapier: any = null;
  private isInitialized = false;
  private config: PhysicsConfig;
  private containerBodies: RigidBody[] = [];
  private diceInstances: Map<string, DiceInstance> = new Map();
  private diceRestStates: Map<string, boolean> = new Map(); // 跟踪骰子静止状态

  constructor(config: PhysicsConfig) {
    this.config = config;
  }

  /**
   * 异步初始化物理引擎
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 动态导入Rapier.js
      this.rapier = await import('@dimforge/rapier3d-compat');
      await this.rapier.init();

      // 创建物理世界 - 使用新的API格式
      const gravity = {
        x: this.config.gravity.x,
        y: this.config.gravity.y,
        z: this.config.gravity.z
      };
      this.world = new this.rapier.World(gravity);
      
      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 创建容器边界
   */
  createContainer(config: ContainerConfig): void {
    if (!this.world || !this.rapier) {
      throw new Error('物理引擎未初始化');
    }

    const { size, position, physics } = config;
    const halfSize = size.clone().multiplyScalar(0.5);

    // 创建六个面的刚体（地面、天花板、四面墙）
    const walls = [
      // 地面
      { pos: [0, -halfSize.y, 0], size: [halfSize.x, 0.1, halfSize.z] },
      // 天花板
      { pos: [0, halfSize.y, 0], size: [halfSize.x, 0.1, halfSize.z] },
      // 前墙
      { pos: [0, 0, -halfSize.z], size: [halfSize.x, halfSize.y, 0.1] },
      // 后墙
      { pos: [0, 0, halfSize.z], size: [halfSize.x, halfSize.y, 0.1] },
      // 左墙
      { pos: [-halfSize.x, 0, 0], size: [0.1, halfSize.y, halfSize.z] },
      // 右墙
      { pos: [halfSize.x, 0, 0], size: [0.1, halfSize.y, halfSize.z] },
    ];

    walls.forEach(wall => {
      const rigidBodyDesc = this.rapier.RigidBodyDesc.fixed()
        .setTranslation(
          position.x + wall.pos[0],
          position.y + wall.pos[1],
          position.z + wall.pos[2]
        );

      const rigidBody = this.world!.createRigidBody(rigidBodyDesc);

      const colliderDesc = this.rapier.ColliderDesc.cuboid(
        wall.size[0],
        wall.size[1],
        wall.size[2]
      )
        .setFriction(physics.friction)
        .setRestitution(physics.restitution);

      this.world!.createCollider(colliderDesc, rigidBody);
      this.containerBodies.push(rigidBody);
    });
  }

  /**
   * 创建骰子刚体
   */
  createDice(config: DiceConfig, mesh: THREE.Mesh): DiceInstance {
    if (!this.world || !this.rapier) {
      throw new Error('物理引擎未初始化');
    }

    const { position, rotation, size, physics } = config;
    const halfSize = size * 0.5;

    // 创建刚体描述
    const rigidBodyDesc = this.rapier.RigidBodyDesc.dynamic()
      .setTranslation(position.x, position.y, position.z);

    // 设置初始旋转（使用四元数）
    const quaternion = new THREE.Quaternion().setFromEuler(rotation);
    rigidBodyDesc.setRotation({
      x: quaternion.x,
      y: quaternion.y,
      z: quaternion.z,
      w: quaternion.w
    });

    const rigidBody = this.world.createRigidBody(rigidBodyDesc);

    // 创建碰撞体描述
    const colliderDesc = this.rapier.ColliderDesc.cuboid(halfSize, halfSize, halfSize)
      .setMass(physics.mass)
      .setFriction(physics.friction)
      .setRestitution(physics.restitution);

    const collider = this.world.createCollider(colliderDesc, rigidBody);

    // 创建骰子实例
    const diceInstance: DiceInstance = {
      config,
      mesh,
      rigidBody,
      materials: Array.isArray(mesh.material) ? mesh.material : [mesh.material],
    };

    this.diceInstances.set(config.id, diceInstance);
    return diceInstance;
  }

  /**
   * 对指定骰子施加力
   */
  applyForce(diceId: string, forceConfig: ForceConfig): void {
    const diceInstance = this.diceInstances.get(diceId);
    if (!diceInstance) {
      return;
    }

    const { force, point, impulse = false } = forceConfig;
    const { rigidBody } = diceInstance;

    if (impulse) {
      if (point) {
        rigidBody.applyImpulseAtPoint(force, point, true);
      } else {
        rigidBody.applyImpulse(force, true);
      }
    } else {
      if (point) {
        rigidBody.addForceAtPoint(force, point, true);
      } else {
        rigidBody.addForce(force, true);
      }
    }
  }

  /**
   * 对所有骰子施加随机力
   */
  applyRandomForces(baseStrength: number = 5): void {
    this.diceInstances.forEach((diceInstance, index) => {
      // 获取骰子当前位置
      const translation = diceInstance.rigidBody.translation();
      const currentHeight = translation.y;

      // 高度限制：如果骰子已经很高，减少向上的力
      let heightFactor = 1.0;
      if (currentHeight > 3) { // 如果高度超过3（提高阈值）
        heightFactor = Math.max(0.1, 1 - (currentHeight - 3) * 0.2); // 更温和的衰减
      }

      // 为每个骰子生成不同强度的随机力
      const strengthVariation = 0.8 + Math.random() * 0.4; // 0.8-1.2倍变化
      const actualStrength = baseStrength * strengthVariation * heightFactor;

      // 生成随机方向的力，根据高度调整向上力
      const upwardForce = currentHeight > 3
        ? 0 // 如果太高，不再施加向上力
        : Math.random() * actualStrength * 0.4 + actualStrength * 0.2; // 增强向上力

      const force = new THREE.Vector3(
        (Math.random() - 0.4) * actualStrength * 0.2, // 水平力保持不变
        upwardForce, // 增强的向上力
        (Math.random() - 0.4) * actualStrength * 0.2  // 水平力保持不变
      );

      // 生成随机的施力点（更靠近中心，减少旋转）
      const point = new THREE.Vector3(
        (Math.random() - 0.5) * diceInstance.config.size * 0.5,
        (Math.random() - 0.5) * diceInstance.config.size * 0.5,
        (Math.random() - 0.5) * diceInstance.config.size * 0.5
      );

      this.applyForce(diceInstance.config.id, {
        force,
        point,
        impulse: true
      });
    });
  }

  /**
   * 更新物理世界
   */
  step(fixedTimeStep: number): void {
    if (!this.world) return;

    // 使用固定时间步长，确保物理稳定性和一致性
    this.world.timestep = fixedTimeStep;
    this.world.step();

    // 同步Three.js网格位置和旋转
    this.diceInstances.forEach((diceInstance) => {
      const { mesh, rigidBody } = diceInstance;
      const translation = rigidBody.translation();
      const rotation = rigidBody.rotation();

      mesh.position.set(translation.x, translation.y, translation.z);
      mesh.quaternion.set(rotation.x, rotation.y, rotation.z, rotation.w);
    });

    // 简单的碰撞检测（检查骰子之间的距离）
    this.detectCollisions();
  }

  /**
   * 简单的碰撞检测 - 优化版本
   */
  private detectCollisions(): void {
    // 在生产环境中禁用碰撞检测以提高性能
    if (process.env.NODE_ENV === 'production') {
      return;
    }

    const diceArray = Array.from(this.diceInstances.values());
    if (diceArray.length < 2) return;

    for (let i = 0; i < diceArray.length; i++) {
      for (let j = i + 1; j < diceArray.length; j++) {
        const dice1 = diceArray[i];
        const dice2 = diceArray[j];

        const pos1 = dice1.rigidBody.translation();
        const pos2 = dice2.rigidBody.translation();

        // 使用更高效的距离计算
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        const distanceSquared = dx * dx + dy * dy + dz * dz;

        // 如果距离小于两个骰子的大小之和，认为发生碰撞
        const collisionDistance = (dice1.config.size + dice2.config.size) * 0.6;
        const collisionDistanceSquared = collisionDistance * collisionDistance;

        if (distanceSquared < collisionDistanceSquared) {
          // 简化的碰撞处理
          const vel1 = dice1.rigidBody.linvel();
          const vel2 = dice2.rigidBody.linvel();
          const relativeSpeedSquared =
            Math.pow(vel1.x - vel2.x, 2) +
            Math.pow(vel1.y - vel2.y, 2) +
            Math.pow(vel1.z - vel2.z, 2);

          if (relativeSpeedSquared > 1.0) {
            // 碰撞处理已简化
          }
        }
      }
    }
  }

  /**
   * 检查骰子是否静止
   */
  isDiceAtRest(diceId: string, threshold: number = 0.1): boolean {
    const diceInstance = this.diceInstances.get(diceId);
    if (!diceInstance) return false;

    const { rigidBody } = diceInstance;
    const linvel = rigidBody.linvel();
    const angvel = rigidBody.angvel();

    const linearSpeed = Math.sqrt(linvel.x ** 2 + linvel.y ** 2 + linvel.z ** 2);
    const angularSpeed = Math.sqrt(angvel.x ** 2 + angvel.y ** 2 + angvel.z ** 2);

    const isAtRest = linearSpeed < threshold && angularSpeed < threshold;
    const wasAtRest = this.diceRestStates.get(diceId) || false;

    // 只在状态改变时报告
    if (isAtRest && !wasAtRest) {
      // 计算哪一面朝上
      const topFace = this.getTopFace(diceInstance);
      // 静止日志已移除
    }

    this.diceRestStates.set(diceId, isAtRest);
    return isAtRest;
  }

  /**
   * 计算骰子哪一面朝上
   */
  private getTopFace(diceInstance: DiceInstance): number {
    const { mesh } = diceInstance;

    // 获取骰子的世界变换矩阵
    mesh.updateMatrixWorld();

    // 定义六个面的法向量（本地坐标系）
    const faceNormals = [
      new THREE.Vector3(1, 0, 0),   // 右面 (面1)
      new THREE.Vector3(-1, 0, 0),  // 左面 (面2)
      new THREE.Vector3(0, 1, 0),   // 上面 (面3)
      new THREE.Vector3(0, -1, 0),  // 下面 (面4)
      new THREE.Vector3(0, 0, 1),   // 前面 (面5)
      new THREE.Vector3(0, 0, -1),  // 后面 (面6)
    ];

    // 世界坐标系的上方向
    const worldUp = new THREE.Vector3(0, 1, 0);

    let maxDot = -1;
    let topFaceIndex = 0;

    // 找到与世界上方向最接近的面
    faceNormals.forEach((normal, index) => {
      // 将法向量转换到世界坐标系
      const worldNormal = normal.clone().transformDirection(mesh.matrixWorld);
      const dot = worldNormal.dot(worldUp);

      if (dot > maxDot) {
        maxDot = dot;
        topFaceIndex = index;
      }
    });

    // 返回面的编号（1-6）
    return topFaceIndex + 1;
  }

  /**
   * 获取所有骰子的状态
   */
  getAllDiceStates(): Array<{ id: string; position: THREE.Vector3; rotation: THREE.Quaternion; atRest: boolean }> {
    const states: Array<{ id: string; position: THREE.Vector3; rotation: THREE.Quaternion; atRest: boolean }> = [];

    this.diceInstances.forEach((diceInstance, id) => {
      const { rigidBody } = diceInstance;
      const translation = rigidBody.translation();
      const rotation = rigidBody.rotation();

      states.push({
        id,
        position: new THREE.Vector3(translation.x, translation.y, translation.z),
        rotation: new THREE.Quaternion(rotation.x, rotation.y, rotation.z, rotation.w),
        atRest: this.isDiceAtRest(id)
      });
    });

    return states;
  }

  /**
   * 重置骰子位置
   */
  resetDice(diceId: string): void {
    const diceInstance = this.diceInstances.get(diceId);
    if (!diceInstance) return;

    const { rigidBody, config } = diceInstance;
    
    // 重置位置和旋转
    rigidBody.setTranslation(config.position, true);
    rigidBody.setRotation(
      {
        x: config.rotation.x,
        y: config.rotation.y,
        z: config.rotation.z,
        w: 1
      },
      true
    );

    // 清除速度
    rigidBody.setLinvel({ x: 0, y: 0, z: 0 }, true);
    rigidBody.setAngvel({ x: 0, y: 0, z: 0 }, true);
  }

  /**
   * 重置所有骰子
   */
  resetAllDice(): void {
    this.diceInstances.forEach((_, diceId) => {
      this.resetDice(diceId);
    });
  }

  /**
   * 销毁物理引擎
   */
  dispose(): void {
    if (this.world) {
      this.world.free();
      this.world = null;
    }

    this.diceInstances.clear();
    this.diceRestStates.clear();
    this.containerBodies = [];
    this.isInitialized = false;
  }

  /**
   * 获取物理世界实例（用于调试）
   */
  getWorld(): World | null {
    return this.world;
  }

  /**
   * 获取骰子实例
   */
  getDiceInstance(diceId: string): DiceInstance | undefined {
    return this.diceInstances.get(diceId);
  }

  /**
   * 获取所有骰子实例
   */
  getAllDiceInstances(): Map<string, DiceInstance> {
    return new Map(this.diceInstances);
  }
}
