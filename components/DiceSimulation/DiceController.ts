import * as THREE from 'three';
import { DicePhysicsEngine } from './DicePhysicsEngine';
import { Dice<PERSON>enderer } from './DiceRenderer';
import type {
  DiceSimulationConfig,
  KeyboardInputEvent,
  ForceConfig,
  DiceSimulationEvents
} from './types';

/**
 * 骰子控制器类
 * 负责协调物理引擎和渲染器，处理用户输入和动画逻辑
 */
export class DiceController {
  private physicsEngine: DicePhysicsEngine;
  private renderer: DiceRenderer;
  private config: DiceSimulationConfig;
  private isRunning = false;
  private animationId: number | null = null;
  private lastTime = 0;
  
  // 事件系统
  private eventListeners: Map<keyof DiceSimulationEvents, Array<(data: any) => void>> = new Map();
  
  // 键盘输入处理
  private keyboardBuffer: KeyboardInputEvent[] = [];
  private lastKeyboardTime = 0;
  private keyboardCooldown = 50; // 50ms冷却时间，更快响应

  // 智能力度控制
  private forceDecayFactor = 1.0; // 力度衰减因子
  private consecutiveInputCount = 0; // 连续输入计数
  private lastInputTime = 0; // 上次输入时间
  private inputResetThreshold = 1000; // 1秒后重置连续输入计数
  
  // 性能监控和自适应帧率
  private performanceMonitor = {
    systemFPS: 60, // 系统帧率，将自动检测
    activeFPS: 60, // 激活时的帧率（匹配系统）
    idleFPS: 5,    // 静止时的帧率（极致节能）
    frameTimeHistory: [] as number[],
    maxHistoryLength: 30,
    lastWarningTime: 0,
    warningCooldown: 5000, // 5秒警告冷却
    isIdle: false,
    lastActivityTime: 0,
    idleThreshold: 3000, // 3秒无活动后进入idle模式
    frameCount: 0,
    lastFPSTime: 0,
    currentFPS: 60,
    isDetectingSystemFPS: true,
    systemFPSDetectionFrames: 0,
    systemFPSDetectionStart: 0
  };

  constructor(
    container: HTMLElement,
    config: DiceSimulationConfig
  ) {
    this.config = config;
    
    // 初始化渲染器
    this.renderer = new DiceRenderer(
      container,
      config.renderer,
      config.lighting
    );
    
    // 初始化物理引擎
    this.physicsEngine = new DicePhysicsEngine(config.physics);
  }

  /**
   * 异步初始化控制器
   */
  async initialize(): Promise<void> {
    try {
      // 初始化物理引擎
      await this.physicsEngine.initialize();

      // 创建容器
      this.physicsEngine.createContainer(this.config.container);

      // 创建容器可视化（如果启用）
      const containerMesh = this.renderer.createContainerMesh(this.config.container);
      if (containerMesh) {
        this.renderer.addToScene(containerMesh);
      }

      // 创建地面平面（用于接收阴影）
      const groundPlane = this.renderer.createGroundPlane(this.config.container);
      this.renderer.addToScene(groundPlane);

      // 创建骰子
      this.config.dices.forEach((diceConfig, index) => {
        const mesh = this.renderer.createDiceMesh(diceConfig);
        this.renderer.addToScene(mesh);
        this.physicsEngine.createDice(diceConfig, mesh);
      });

      // 测试立方体已移除，骰子应该可见
      
      // 设置性能监控
      this.performanceMonitor.lastActivityTime = performance.now();
      this.performanceMonitor.idleFPS = this.config.performance.idleFPS;
      
    } catch (error) {
      throw error;
    }
  }

  /**
   * 开始动画循环
   */
  start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    this.lastTime = performance.now();
    this.performanceMonitor.lastFPSTime = performance.now();
    this.performanceMonitor.frameCount = 0;

    // 开始系统帧率检测
    this.startSystemFPSDetection();
  }

  /**
   * 开始系统帧率检测
   */
  private startSystemFPSDetection(): void {
    this.performanceMonitor.isDetectingSystemFPS = true;
    this.performanceMonitor.systemFPSDetectionFrames = 0;
    this.performanceMonitor.systemFPSDetectionStart = performance.now();

    // 使用原生requestAnimationFrame检测系统帧率
    this.detectSystemFPS();
  }

  /**
   * 检测系统帧率
   */
  private detectSystemFPS = (): void => {
    if (!this.performanceMonitor.isDetectingSystemFPS || !this.isRunning) return;

    this.performanceMonitor.systemFPSDetectionFrames++;

    // 检测60帧后计算系统帧率
    if (this.performanceMonitor.systemFPSDetectionFrames >= 60) {
      const elapsed = performance.now() - this.performanceMonitor.systemFPSDetectionStart;
      const detectedFPS = Math.round((this.performanceMonitor.systemFPSDetectionFrames * 1000) / elapsed);

      this.performanceMonitor.systemFPS = detectedFPS;
      this.performanceMonitor.activeFPS = detectedFPS;
      this.performanceMonitor.isDetectingSystemFPS = false;

      // 开始正常的动画循环
      this.scheduleNextFrame();
    } else {
      // 继续检测
      requestAnimationFrame(this.detectSystemFPS);
    }
  };

  /**
   * 停止动画循环
   */
  stop(): void {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 动画循环 - 使用setTimeout + requestAnimationFrame控制帧率
   */
  private animate = (): void => {
    if (!this.isRunning) return;

    const currentTime = performance.now();

    // 计算实际的deltaTime，但限制最大值避免物理不稳定
    const deltaTime = Math.min((currentTime - this.lastTime) / 1000, 1/30);
    this.lastTime = currentTime;

    // 处理键盘输入缓冲
    this.processKeyboardBuffer();

    // 更新物理世界 - 使用固定时间步长确保稳定性
    this.physicsEngine.step(1/60); // 固定60Hz物理更新

    // 材质透明度检查已移除

    // 渲染场景
    this.renderer.render();

    // 性能监控
    this.monitorPerformance(deltaTime);

    // 检查骰子静止状态和idle状态
    this.checkDiceRestState();
    this.updateIdleState();

    // 使用setTimeout + requestAnimationFrame控制帧率
    this.scheduleNextFrame();
  };

  /**
   * 调度下一帧 - 使用setTimeout控制帧率
   */
  private scheduleNextFrame(): void {
    if (!this.isRunning || this.performanceMonitor.isDetectingSystemFPS) return;

    // 根据idle状态选择目标FPS
    const targetFPS = this.performanceMonitor.isIdle
      ? this.performanceMonitor.idleFPS
      : this.performanceMonitor.activeFPS;

    const frameDelay = 1000 / targetFPS;

    // 使用setTimeout + requestAnimationFrame的组合来精确控制帧率
    setTimeout(() => {
      if (this.isRunning && !this.performanceMonitor.isDetectingSystemFPS) {
        this.animationId = requestAnimationFrame(this.animate);
      }
    }, frameDelay);
  };

  /**
   * 处理键盘输入缓冲
   */
  private processKeyboardBuffer(): void {
    const now = performance.now();
    
    if (this.keyboardBuffer.length > 0 && now - this.lastKeyboardTime > this.keyboardCooldown) {
      // 计算力的强度，基于输入频率和按键类型
      const inputCount = this.keyboardBuffer.length;

      // 计算平均力度倍数
      const avgForceMultiplier = this.keyboardBuffer.reduce((sum, event) => {
        return sum + ((event as any).forceMultiplier || 1);
      }, 0) / inputCount;

      // 调整力度，保持柔和但确保有足够的向上力
      const baseStrength = Math.min(inputCount * 1.2 + 2.5, 10) * avgForceMultiplier;

      // 施加随机力
      this.physicsEngine.applyRandomForces(baseStrength);

      // 清空缓冲区
      this.keyboardBuffer = [];
      this.lastKeyboardTime = now;
    }
  }

  /**
   * 性能监控 - 优化版本
   */
  private monitorPerformance(deltaTime: number): void {
    if (this.performanceMonitor.isDetectingSystemFPS) return;

    const now = performance.now();
    this.performanceMonitor.frameCount++;

    // 每秒更新一次FPS
    if (now - this.performanceMonitor.lastFPSTime >= 1000) {
      this.performanceMonitor.currentFPS = this.performanceMonitor.frameCount;
      this.performanceMonitor.frameCount = 0;
      this.performanceMonitor.lastFPSTime = now;
    }

    // 简化帧时间记录，只在开发模式下启用
    if (process.env.NODE_ENV === 'development') {
      const frameTime = deltaTime * 1000;
      const { frameTimeHistory, maxHistoryLength } = this.performanceMonitor;

      frameTimeHistory.push(frameTime);
      if (frameTimeHistory.length > maxHistoryLength) {
        frameTimeHistory.shift();
      }
    }
  }

  /**
   * 检查骰子静止状态
   */
  private checkDiceRestState(): void {
    this.config.dices.forEach(diceConfig => {
      if (this.physicsEngine.isDiceAtRest(diceConfig.id)) {
        const diceInstance = this.physicsEngine.getDiceInstance(diceConfig.id);
        if (diceInstance) {
          const rotation = new THREE.Euler().setFromQuaternion(diceInstance.mesh.quaternion);
          this.emit('rest', {
            diceId: diceConfig.id,
            finalRotation: rotation
          });
        }
      }
    });
  }

  /**
   * 更新idle状态
   */
  private updateIdleState(): void {
    const now = performance.now();

    // 检查所有骰子是否都静止
    const allAtRest = this.config.dices.every(diceConfig =>
      this.physicsEngine.isDiceAtRest(diceConfig.id, 0.05)
    );

    if (allAtRest) {
      // 如果所有骰子都静止，且超过阈值时间，进入idle模式
      if (now - this.performanceMonitor.lastActivityTime > this.performanceMonitor.idleThreshold) {
        if (!this.performanceMonitor.isIdle) {
          this.performanceMonitor.isIdle = true;
        }
      }
    } else {
      // 有骰子在运动，退出idle模式
      this.performanceMonitor.lastActivityTime = now;
      if (this.performanceMonitor.isIdle) {
        this.performanceMonitor.isIdle = false;
      }
    }
  }

  /**
   * 处理键盘输入
   */
  handleKeyboardInput(key: string): void {
    const now = performance.now();

    // 重置活动时间，退出idle模式
    this.performanceMonitor.lastActivityTime = now;
    if (this.performanceMonitor.isIdle) {
      this.performanceMonitor.isIdle = false;
    }

    // 检查是否是重置命令
    if (key === 'r' || key === 'R') {
      this.resetAllDice();
      return;
    }

    // 智能力度控制：检测连续输入
    if (now - this.lastInputTime < this.inputResetThreshold) {
      this.consecutiveInputCount++;
    } else {
      this.consecutiveInputCount = 1; // 重置计数
    }
    this.lastInputTime = now;

    // 根据连续输入次数计算力度衰减
    // 前几次输入保持正常力度，之后逐渐衰减
    if (this.consecutiveInputCount <= 3) {
      this.forceDecayFactor = 1.0; // 前3次保持正常
    } else if (this.consecutiveInputCount <= 8) {
      this.forceDecayFactor = 0.6; // 4-8次减弱到60%
    } else {
      this.forceDecayFactor = 0.3; // 8次以上减弱到30%
    }

    const event: KeyboardInputEvent = {
      key,
      timestamp: now
    };

    // 根据不同的按键类型产生不同的效果
    let forceMultiplier = 1;

    // 特殊按键产生稍强的效果，但整体减弱
    if (key === '\r' || key === '\n') { // 回车键
      forceMultiplier = 1.4;
    } else if (key === ' ') { // 空格键
      forceMultiplier = 1.2;
    } else if (key.length === 1 && /[A-Z]/.test(key)) { // 大写字母
      forceMultiplier = 1.1;
    }

    // 应用智能衰减
    forceMultiplier *= this.forceDecayFactor;

    // 将力度倍数存储在事件中
    (event as any).forceMultiplier = forceMultiplier;

    this.keyboardBuffer.push(event);

    // 限制缓冲区大小
    if (this.keyboardBuffer.length > 50) {
      this.keyboardBuffer.shift();
    }
  }

  /**
   * 手动施加力到指定骰子
   */
  applyForce(diceId: string, forceConfig: ForceConfig): void {
    this.physicsEngine.applyForce(diceId, forceConfig);
  }

  /**
   * 施加随机力到所有骰子
   */
  shakeAllDice(strength: number = 10): void {
    this.physicsEngine.applyRandomForces(strength);
  }

  /**
   * 重置指定骰子
   */
  resetDice(diceId: string): void {
    this.physicsEngine.resetDice(diceId);
  }

  /**
   * 重置所有骰子
   */
  resetAllDice(): void {
    this.physicsEngine.resetAllDice();
  }

  /**
   * 获取骰子状态
   */
  getDiceStates() {
    return this.physicsEngine.getAllDiceStates();
  }

  /**
   * 获取当前FPS
   */
  getFPS(): number {
    return this.performanceMonitor.currentFPS;
  }

  /**
   * 更新光照配置
   */
  updateLighting(config: Partial<typeof this.config.lighting>): void {
    this.renderer.updateLighting(config);
  }

  /**
   * 更新相机位置
   */
  updateCamera(position: THREE.Vector3, target: THREE.Vector3): void {
    this.renderer.updateCamera(position, target);
  }

  // 像素化效果控制方法已移除

  /**
   * 事件监听器
   */
  on<K extends keyof DiceSimulationEvents>(
    event: K, 
    listener: (data: DiceSimulationEvents[K]) => void
  ): () => void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    
    const listeners = this.eventListeners.get(event)!;
    listeners.push(listener);
    
    // 返回取消监听的函数
    return () => {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    };
  }

  /**
   * 触发事件
   */
  private emit<K extends keyof DiceSimulationEvents>(
    event: K, 
    data: DiceSimulationEvents[K]
  ): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          // 静默处理事件监听器错误
        }
      });
    }
  }

  /**
   * 销毁控制器
   */
  dispose(): void {
    this.stop();
    this.physicsEngine.dispose();
    this.renderer.dispose();
    this.eventListeners.clear();
    this.keyboardBuffer = [];
  }

  /**
   * 获取配置
   */
  getConfig(): DiceSimulationConfig {
    return { ...this.config };
  }

  /**
   * 获取渲染器实例（用于调试）
   */
  getRenderer(): DiceRenderer {
    return this.renderer;
  }

  /**
   * 获取物理引擎实例（用于调试）
   */
  getPhysicsEngine(): DicePhysicsEngine {
    return this.physicsEngine;
  }
}
