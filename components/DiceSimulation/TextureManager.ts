import * as THREE from 'three';
import type { DiceFaceTexture } from './types';

/**
 * 骰子纹理配置
 */
export interface DiceTextureConfig {
  /** 骰子ID */
  diceId: string;
  /** 六个面的纹理配置 */
  faces: [
    FaceTextureConfig, // 面1
    FaceTextureConfig, // 面2
    FaceTextureConfig, // 面3
    FaceTextureConfig, // 面4
    FaceTextureConfig, // 面5
    FaceTextureConfig  // 面6
  ];
}

/**
 * 单个面的纹理配置
 */
export interface FaceTextureConfig {
  /** 纹理图片URL */
  imageUrl?: string;
  /** 基础颜色 */
  color?: string | number;
  /** 法线贴图URL */
  normalMapUrl?: string;
  /** 粗糙度贴图URL */
  roughnessMapUrl?: string;
  /** 金属度贴图URL */
  metalnessMapUrl?: string;
  /** 材质属性 */
  material?: {
    roughness?: number;
    metalness?: number;
    transparent?: boolean;
    opacity?: number;
  };
}

/**
 * 纹理管理器
 * 负责加载和管理骰子纹理
 */
export class TextureManager {
  private textureLoader: THREE.TextureLoader;
  private loadedTextures: Map<string, THREE.Texture> = new Map();
  private loadingPromises: Map<string, Promise<THREE.Texture>> = new Map();

  constructor() {
    this.textureLoader = new THREE.TextureLoader();
  }

  /**
   * 加载纹理
   */
  async loadTexture(url: string): Promise<THREE.Texture> {
    // 如果已经加载过，直接返回
    if (this.loadedTextures.has(url)) {
      return this.loadedTextures.get(url)!;
    }

    // 如果正在加载，返回加载Promise
    if (this.loadingPromises.has(url)) {
      return this.loadingPromises.get(url)!;
    }

    // 开始加载
    const loadPromise = new Promise<THREE.Texture>((resolve, reject) => {
      this.textureLoader.load(
        url,
        (texture) => {
          // 设置纹理参数以优化抗锯齿效果
          texture.wrapS = THREE.RepeatWrapping;
          texture.wrapT = THREE.RepeatWrapping;

          // 为透明PNG纹理优化过滤设置
          // 检查是否为透明纹理（通常PNG文件包含透明度）
          const isTransparentTexture = url.toLowerCase().includes('.png');

          if (isTransparentTexture) {
            // 对于透明纹理，使用线性过滤以保持抗锯齿效果
            texture.minFilter = THREE.LinearFilter;
            texture.magFilter = THREE.LinearFilter;
            // 禁用mipmap以避免在小尺寸时丢失抗锯齿细节
            texture.generateMipmaps = false;
            // 确保支持透明度
            texture.format = THREE.RGBAFormat;
          } else {
            // 对于不透明纹理，使用标准设置
            texture.minFilter = THREE.LinearMipmapLinearFilter;
            texture.magFilter = THREE.LinearFilter;
            texture.generateMipmaps = true;
          }

          // 设置正确的颜色空间 - 这是关键！
          texture.colorSpace = THREE.SRGBColorSpace;

          this.loadedTextures.set(url, texture);
          this.loadingPromises.delete(url);
          resolve(texture);
        },
        undefined,
        (error) => {
          this.loadingPromises.delete(url);
          reject(error);
        }
      );
    });

    this.loadingPromises.set(url, loadPromise);
    return loadPromise;
  }

  /**
   * 创建骰子面纹理
   */
  async createDiceFaceTexture(config: FaceTextureConfig): Promise<DiceFaceTexture> {
    const faceTexture: DiceFaceTexture = {
      color: config.color ? new THREE.Color(config.color) : new THREE.Color(0xffffff),
    };

    try {
      // 加载主纹理
      if (config.imageUrl) {
        faceTexture.map = await this.loadTexture(config.imageUrl);
      }

      // 加载法线贴图
      if (config.normalMapUrl) {
        faceTexture.normalMap = await this.loadTexture(config.normalMapUrl);
      }

      // 加载粗糙度贴图
      if (config.roughnessMapUrl) {
        faceTexture.roughnessMap = await this.loadTexture(config.roughnessMapUrl);
      }

      // 加载金属度贴图
      if (config.metalnessMapUrl) {
        faceTexture.metalnessMap = await this.loadTexture(config.metalnessMapUrl);
      }
    } catch (error) {
      // 静默降级到默认颜色
    }

    return faceTexture;
  }

  /**
   * 创建完整的骰子纹理配置
   */
  async createDiceTextures(config: DiceTextureConfig): Promise<[DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture]> {
    const faces = await Promise.all(
      config.faces.map(faceConfig => this.createDiceFaceTexture(faceConfig))
    );

    return faces as [DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture, DiceFaceTexture];
  }

  /**
   * 预加载纹理
   */
  async preloadTextures(urls: string[]): Promise<void> {
    await Promise.all(urls.map(url => this.loadTexture(url)));
  }

  /**
   * 清理纹理缓存
   */
  dispose(): void {
    this.loadedTextures.forEach(texture => texture.dispose());
    this.loadedTextures.clear();
    this.loadingPromises.clear();
  }

  /**
   * 获取已加载的纹理
   */
  getTexture(url: string): THREE.Texture | null {
    return this.loadedTextures.get(url) || null;
  }
}

/**
 * 预设纹理配置示例
 */
export const PRESET_DICE_TEXTURES = {
  // 经典点数骰子
  classic: (diceId: string): DiceTextureConfig => ({
    diceId,
    faces: [
      { color: 0xffffff }, // 面1 - 纯白色
      { color: 0xffffff }, // 面2 - 纯白色
      { color: 0xffffff }, // 面3 - 纯白色
      { color: 0xffffff }, // 面4 - 纯白色
      { color: 0xffffff }, // 面5 - 纯白色
      { color: 0xffffff }, // 面6 - 纯白色
    ]
  }),

  // 彩色骰子
  colorful: (diceId: string): DiceTextureConfig => ({
    diceId,
    faces: [
      { color: 0xff4444 }, // 面1 - 红色
      { color: 0x44ff44 }, // 面2 - 绿色
      { color: 0x4444ff }, // 面3 - 蓝色
      { color: 0xffff44 }, // 面4 - 黄色
      { color: 0xff44ff }, // 面5 - 紫色
      { color: 0x44ffff }, // 面6 - 青色
    ]
  }),

  // 自定义图片纹理示例
  custom: (diceId: string, baseUrl: string): DiceTextureConfig => ({
    diceId,
    faces: [
      { imageUrl: `${baseUrl}/face1.png`, color: 0xffffff },
      { imageUrl: `${baseUrl}/face2.png`, color: 0xffffff },
      { imageUrl: `${baseUrl}/face3.png`, color: 0xffffff },
      { imageUrl: `${baseUrl}/face4.png`, color: 0xffffff },
      { imageUrl: `${baseUrl}/face5.png`, color: 0xffffff },
      { imageUrl: `${baseUrl}/face6.png`, color: 0xffffff },
    ]
  }),
};

/**
 * 全局纹理管理器实例
 */
export const textureManager = new TextureManager();
