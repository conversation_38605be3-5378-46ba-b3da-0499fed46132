import * as THREE from 'three';
import { textureManager } from './TextureManager';
import type { DiceTextureConfig } from './TextureManager';
import type {
  DiceSimulationConfig,
  DiceConfig,
  DiceFaceTexture
} from './types';
import {
  DEFAULT_DICE_CONFIG,
  DEFAULT_CONTAINER_CONFIG,
  DEFAULT_LIGHTING_CONFIG,
  DEFAULT_RENDERER_CONFIG,
  DEFAULT_PHYSICS_CONFIG
} from './types';

/**
 * 创建默认的骰子模拟配置
 */
export function createDefaultDiceSimulationConfig(): DiceSimulationConfig {
  return {
    dices: [], // 将在初始化时动态创建自定义纹理骰子
    container: { ...DEFAULT_CONTAINER_CONFIG },
    lighting: { ...DEFAULT_LIGHTING_CONFIG },
    renderer: { ...DEFAULT_RENDERER_CONFIG },
    physics: { ...DEFAULT_PHYSICS_CONFIG },
    performance: {
      targetFPS: 60, // 默认60帧，将动态检测系统帧率
      idleFPS: 5,    // 静止时降到5帧，极致节能
      adaptiveQuality: true,
      minQuality: 0.5,
      motionBlur: true, // 启用运动模糊
    },
  };
}

/**
 * 创建自定义纹理的骰子配置
 */
export async function createCustomTextureDiceConfigs(
  textureConfigs: DiceTextureConfig[]
): Promise<DiceConfig[]> {
  const diceConfigs: DiceConfig[] = [];

  for (let i = 0; i < textureConfigs.length; i++) {
    const textureConfig = textureConfigs[i];

    // 为每个骰子设置不同的固定位置
    const positions = [
      new THREE.Vector3(-1.5, 3, 0),   // 骰子1：左侧
      new THREE.Vector3(0, 3, 1.5),    // 骰子2：中间偏后
      new THREE.Vector3(1.5, 3, -1.5), // 骰子3：右侧偏前
    ];

    // 加载自定义纹理
    const faces = await textureManager.createDiceTextures(textureConfig);

    const diceConfig: DiceConfig = {
      id: textureConfig.diceId,
      position: positions[i] || new THREE.Vector3(i * 2 - 2, 3, 0),
      rotation: new THREE.Euler(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
      ),
      size: 1.2,
      faces,
      physics: {
        mass: 1,
        friction: 0.4,
        restitution: 0.3,
      },
    };

    diceConfigs.push(diceConfig);
  }

  return diceConfigs;
}

/**
 * 检查WebGL支持
 */
export function checkWebGLSupport(): boolean {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!gl;
  } catch (e) {
    return false;
  }
}

/**
 * 获取设备性能等级
 */
export function getDevicePerformanceLevel(): 'low' | 'medium' | 'high' {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl');
  
  if (!gl) return 'low';
  
  const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
  if (debugInfo) {
    const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
    
    // 简单的GPU性能检测
    if (renderer.includes('Intel') || renderer.includes('Mali')) {
      return 'low';
    } else if (renderer.includes('GTX') || renderer.includes('RTX') || renderer.includes('Radeon')) {
      return 'high';
    }
  }
  
  // 基于内存和CPU核心数的简单检测
  const memory = (navigator as any).deviceMemory || 4;
  const cores = navigator.hardwareConcurrency || 4;
  
  if (memory >= 8 && cores >= 8) {
    return 'high';
  } else if (memory >= 4 && cores >= 4) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * 根据设备性能调整配置
 */
export function adjustConfigForPerformance(
  config: DiceSimulationConfig, 
  performanceLevel: 'low' | 'medium' | 'high'
): DiceSimulationConfig {
  const adjustedConfig = { ...config };
  
  switch (performanceLevel) {
    case 'low':
      adjustedConfig.renderer.antialias = false;
      adjustedConfig.renderer.pixelRatio = 1;
      adjustedConfig.lighting.shadows = false;
      adjustedConfig.performance.targetFPS = 30;
      break;
    case 'medium':
      adjustedConfig.renderer.antialias = true;
      adjustedConfig.renderer.pixelRatio = Math.min((typeof window !== 'undefined' ? window.devicePixelRatio : 1) || 1, 1.5);
      adjustedConfig.lighting.shadows = true;
      adjustedConfig.performance.targetFPS = 45;
      break;
    case 'high':
      adjustedConfig.renderer.antialias = true;
      adjustedConfig.renderer.pixelRatio = Math.min((typeof window !== 'undefined' ? window.devicePixelRatio : 1) || 1, 2);
      adjustedConfig.lighting.shadows = true;
      adjustedConfig.performance.targetFPS = 60;
      break;
  }
  
  return adjustedConfig;
}

/**
 * 创建示例自定义纹理配置
 * 展示如何使用PNG图片作为骰子纹理
 */
export function createExampleTextureConfigs(): DiceTextureConfig[] {
  return [
    // 骰子1 - 尝试加载自定义图片，失败时使用彩色方案
    {
      diceId: 'dice-1',
      faces: [
        { imageUrl: '/textures/dice1/face1.png', color: 0xa42423 },
        { imageUrl: '/textures/dice1/inter.png', color: 0xa42423 },
        { imageUrl: '/textures/dice1/face3.png', color: 0xa42423 },
        { imageUrl: '/textures/dice1/face.png', color: 0xa42423 },
        { imageUrl: '/textures/dice1/face.png', color: 0xa42423 },
        { imageUrl: '/textures/dice1/face.png', color: 0xa42423 },
      ]
    },
    // 骰子2 - 尝试加载自定义图片，失败时使用冷色调
    {
      diceId: 'dice-2',
      faces: [
        { imageUrl: '/textures/dice1/face1.png', color: 0x013E75 },
        { imageUrl: '/textures/dice2/hci.png', color: 0x013E75 },
        { imageUrl: '/textures/dice2/games.png', color: 0x013E75 },
        { imageUrl: '/textures/dice2/new_media.png', color: 0x013E75 },
        { color: 0x013E75 },
        { color: 0x013E75 },
      ]
    },
    // 骰子3 - 混合纯色和纹理
    {
      diceId: 'dice-3',
      faces: [
        { imageUrl: '/textures/dice3/face1.png', color: 0xf8f2e1 },
        { imageUrl: '/textures/dice3/full.png', color: 0xf8f2e1 },
        { imageUrl: '/textures/dice3/gallery.png', color: 0xf8f2e1 },
        { imageUrl: '/textures/dice3/preprints.png', color: 0xf8f2e1 },
        { color: 0xf8f2e1 },
        { color: 0xf8f2e1 },
      ]
    }
  ];
}