'use client';

import React from 'react';
import { useFloatingWindows } from '@/lib/floating-windows-store';
import FloatingWindow from './FloatingWindow';
import ImageFloatingWindow from './ImageFloatingWindow';

interface FloatingWindowManagerProps {
    // 终端命令处理器（可选，保持向后兼容）
    onTerminalCommand?: (command: string, params?: Record<string, any>) => Promise<{ success: boolean; result?: any; error?: string }>;
}

export default function FloatingWindowManager({ onTerminalCommand }: FloatingWindowManagerProps = {}) {
    const { windows, startClosing, updateWindow, bringToFront } = useFloatingWindows();

    return (
        <>
            {windows.map((window) => {
                // 根据窗口类型渲染不同的组件
                if (window.type === 'image') {
                    return (
                        <ImageFloatingWindow
                            key={window.id}
                            window={window}
                            onClose={() => startClosing(window.id)}
                            onUpdate={(updates) => updateWindow(window.id, updates)}
                            onBringToFront={() => bringToFront(window.id)}
                        />
                    );
                }

                // 默认使用普通浮动窗口（web类型）
                return (
                    <FloatingWindow
                        key={window.id}
                        window={window}
                        onClose={() => startClosing(window.id)}
                        onUpdate={(updates) => updateWindow(window.id, updates)}
                        onBringToFront={() => bringToFront(window.id)}
                        onTerminalCommand={onTerminalCommand}
                    />
                );
            })}
        </>
    );
} 