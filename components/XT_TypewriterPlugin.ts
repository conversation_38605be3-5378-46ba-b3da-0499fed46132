import { Terminal } from '@xterm/xterm';

/**
 * 打字机效果插件
 * 逐字符显示文本，模拟打字机打字效果
 * @param term xterm.Terminal 实例
 * @param text 要显示的文本
 * @param speed 打字速度（0=直接打印，1-100=每秒字符数，默认0）
 * @returns Promise<void>
 */
export function typeText(term: Terminal, text: string, speed = 0): Promise<void> {
    return new Promise(resolve => {
        // 如果速度为0，直接全部打印
        if (speed === 0) {
            // 处理换行符，确保正确显示
            const processedText = text.replace(/\n/g, '\r\n');
            term.write(processedText);
            resolve();
            return;
        }

        // 计算每个字符的间隔时间（毫秒）
        // speed表示每秒字符数，所以间隔 = 1000ms / speed
        const interval = 1000 / speed;

        let i = 0;
        const timer = setInterval(() => {
            if (i >= text.length) {
                clearInterval(timer);
                resolve();
            } else {
                const ch = text[i++];
                // 保证换行同时回到行首（\r\n），避免光标留在缩进位置
                if (ch === '\n') {
                    term.write('\r\n');
                } else {
                    term.write(ch);
                }
            }
        }, interval);
    });
}

/**
 * 快速打字机效果
 * 适用于长文本的快速显示（每秒60个字符）
 * @param term xterm.Terminal 实例
 * @param text 要显示的文本
 * @returns Promise<void>
 */
export function typeTextFast(term: Terminal, text: string): Promise<void> {
    return typeText(term, text, 60);
}

/**
 * 中速打字机效果
 * 标准打字速度（每秒30个字符）
 * @param term xterm.Terminal 实例
 * @param text 要显示的文本
 * @returns Promise<void>
 */
export function typeTextMedium(term: Terminal, text: string): Promise<void> {
    return typeText(term, text, 30);
}

/**
 * 慢速打字机效果
 * 适用于强调重要内容（每秒10个字符）
 * @param term xterm.Terminal 实例
 * @param text 要显示的文本
 * @returns Promise<void>
 */
export function typeTextSlow(term: Terminal, text: string): Promise<void> {
    return typeText(term, text, 10);
}

/**
 * 瞬间显示
 * 直接打印所有内容，无打字效果
 * @param term xterm.Terminal 实例
 * @param text 要显示的文本
 * @returns Promise<void>
 */
export function typeTextInstant(term: Terminal, text: string): Promise<void> {
    return typeText(term, text, 0);
}

/**
 * 带暂停的打字机效果
 * 在标点符号处添加额外暂停
 * @param term xterm.Terminal 实例
 * @param text 要显示的文本
 * @param baseSpeed 基础速度（每秒字符数，默认20）
 * @param pauseChars 暂停字符数组
 * @param pauseDuration 暂停时长（毫秒）
 * @returns Promise<void>
 */
export function typeTextWithPauses(
    term: Terminal,
    text: string,
    baseSpeed = 20,
    pauseChars = ['.', '!', '?', ','],
    pauseDuration = 500
): Promise<void> {
    return new Promise(resolve => {
        // 如果速度为0，直接打印
        if (baseSpeed === 0) {
            const processedText = text.replace(/\n/g, '\r\n');
            term.write(processedText);
            resolve();
            return;
        }

        // 计算基础间隔时间
        const baseInterval = 1000 / baseSpeed;
        let i = 0;

        const typeNext = () => {
            if (i >= text.length) {
                resolve();
                return;
            }

            const ch = text[i++];

            // 显示字符
            if (ch === '\n') {
                term.write('\r\n');
            } else {
                term.write(ch);
            }

            // 检查是否需要暂停
            const shouldPause = pauseChars.includes(ch);
            const delay = shouldPause ? pauseDuration : baseInterval;

            setTimeout(typeNext, delay);
        };

        typeNext();
    });
}

/**
 * 自定义速度打字机效果
 * 提供更灵活的速度控制
 * @param term xterm.Terminal 实例
 * @param text 要显示的文本
 * @param speed 打字速度（0=瞬间，1-100=每秒字符数）
 * @returns Promise<void>
 */
export function typeTextCustom(term: Terminal, text: string, speed: number): Promise<void> {
    // 限制速度范围
    const clampedSpeed = Math.max(0, Math.min(100, speed));
    return typeText(term, text, clampedSpeed);
} 