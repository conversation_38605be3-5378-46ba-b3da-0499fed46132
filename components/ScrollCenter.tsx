"use client";
import { useEffect } from "react";

export default function ScrollCenter() {
  useEffect(() => {
    const onClick = (e: MouseEvent) => {
      const anchor = (e.target as HTMLElement).closest('a[href^="#"]');
      if (!anchor) return;
      const hash = anchor.getAttribute("href")!.slice(1);
      console.log("[ScrollCenter] 点击锚点", { hash, anchor });
      // 仅在分页可见区域内查找目标元素，避免命中隐藏源内容
      const container = document.querySelector(".pages-container");
      const target = container
        ? (container.querySelector(`#${CSS.escape(hash)}`) as HTMLElement | null)
        : null;
      console.log("[ScrollCenter] 查找目标", { container, target });
      if (!target) return;

      e.preventDefault(); // 阻止浏览器默认锚点滚动
      // 延时让 URL hash 正常更新
      history.pushState(null, "", `#${hash}`);

      // 简化：让浏览器自身负责锚点滚动，解决缩放矩阵复杂误差
      target.scrollIntoView({ behavior: "smooth", block: "center" });
      // 调试：打印滚动后位置
      setTimeout(() => {
        const preview = document.querySelector(".paged-preview") as HTMLElement | null;
        console.log("[ScrollCenter] scrollIntoView 后 scrollTop", preview?.scrollTop);
      }, 600);
    };
    document.addEventListener("click", onClick);
    return () => document.removeEventListener("click", onClick);
  }, []);
  return null; // 组件无可视输出
}
