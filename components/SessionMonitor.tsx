"use client";

import { usePathname, useSearchParams } from "next/navigation";
import { useEffect, useRef } from "react";

import { useSupabase } from "@/components/SupabaseProvider";

export function SessionMonitor() {
  const { session, user, supabase } = useSupabase();
  const originalFetchRef = useRef<typeof window.fetch | null>(null);
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // 检查会话是否无效 - 已禁用，因为这个逻辑有问题
  // useEffect(() => {
  //   if (!user) return;

  //   // 目前 Supabase SDK 在 token 失效时会自动刷新；如仍失效则登出
  //   // 可以根据业务逻辑自行添加 isValid 标记
  //   console.log("会话被标记为无效，执行登出");
  //   // 添加一个短暂延迟，确保状态传播
  //   setTimeout(() => {
  //     supabase.auth.signOut();
  //     window.location.href = "/account";
  //   }, 100);
  // }, [user, supabase.auth]);

  useEffect(() => {
    if (!user) return;

    // 保存原始fetch
    if (!originalFetchRef.current) {
      originalFetchRef.current = window.fetch;
    }

    // 保存原始fetch的引用，确保后续使用一致
    const originalFetch = originalFetchRef.current;

    // 添加全局请求拦截器，捕获401响应
    window.fetch = async function (input, init) {
      try {
        // 确保绑定原始上下文
        const response = await originalFetch.apply(window, [input, init]);

        // 如果响应状态码为401且不是验证接口本身，执行登出操作
        if (response.status === 401) {
          let url = "";
          if (typeof input === "string") {
            url = input;
          } else if (input instanceof Request) {
            url = input.url;
          } else if (input instanceof URL) {
            url = input.toString();
          }

          // 避免处理API验证端点本身
          if (!url.includes("/api/auth/validate")) {
            console.log("API请求被拒绝(401)，执行登出操作");
            // 首先强制刷新会话状态，然后执行登出
            try {
              // 先获取用户信息
              await supabase.auth.getUser();
              supabase.auth.signOut();
              window.location.href = "/account";
            } catch (e) {
              // 如果刷新失败，直接登出
              supabase.auth.signOut();
              window.location.href = "/account";
            }
          }
        }

        return response;
      } catch (error) {
        console.error("请求拦截器错误:", error);
        // 出错时仍然尝试使用原始fetch
        return originalFetch.apply(window, [input, init]);
      }
    };

    return () => {
      // 恢复原始fetch
      if (originalFetchRef.current) {
        window.fetch = originalFetchRef.current;
      }
    };
  }, [user, supabase.auth]);

  // 路由变化时验证会话
  useEffect(() => {
    if (!user) return;

    // 当路径或查询参数变化时，验证会话状态
    const checkSession = async () => {
      try {
        const originalFetch = originalFetchRef.current;
        if (!originalFetch) return;

        // 使用apply确保正确的this上下文
        const res = await originalFetch.apply(window, ["/api/auth/validate", {}]);
        if (!res.ok) {
          console.log("路由变化检查：会话已失效，执行登出操作");
          // 首先尝试刷新会话状态
          try {
            // 先验证用户身份
            const { data } = await supabase.auth.getUser();
            // 如果用户无效，执行登出
            if (!data.user) {
              supabase.auth.signOut();
              window.location.href = "/account";
            }
          } catch (e) {
            // 如果刷新失败，直接登出
            supabase.auth.signOut();
            window.location.href = "/account";
          }
        }
      } catch (error) {
        console.error("会话验证出错:", error);
      }
    };

    // 执行会话检查
    checkSession();
  }, [pathname, searchParams, user, supabase.auth]);

  return null; // 这是一个无UI组件
}
