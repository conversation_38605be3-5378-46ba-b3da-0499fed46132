"use client";

import React, { useState, useEffect } from 'react';
import { useSupabase } from '@/components/SupabaseProvider';
import { logoutManager } from '@/lib/auth/logout-manager';

/**
 * Logout测试面板
 * 用于测试和验证logout功能的鲁棒性
 */
export function LogoutTestPanel() {
    const { user, supabase } = useSupabase();
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const [lastLogoutResult, setLastLogoutResult] = useState<string>('');
    const [authEvents, setAuthEvents] = useState<string[]>([]);

    // 监听认证事件
    useEffect(() => {
        const handleForceAuthRefresh = () => {
            const timestamp = new Date().toLocaleTimeString();
            setAuthEvents(prev => [...prev.slice(-9), `${timestamp}: force-auth-refresh`]);
        };

        const handleAuthStateChanged = (event: CustomEvent) => {
            const timestamp = new Date().toLocaleTimeString();
            const { event: authEvent, user: eventUser } = event.detail;
            setAuthEvents(prev => [...prev.slice(-9), `${timestamp}: auth-state-changed (${authEvent})`]);
        };

        window.addEventListener('force-auth-refresh', handleForceAuthRefresh);
        window.addEventListener('auth-state-changed', handleAuthStateChanged as EventListener);

        return () => {
            window.removeEventListener('force-auth-refresh', handleForceAuthRefresh);
            window.removeEventListener('auth-state-changed', handleAuthStateChanged as EventListener);
        };
    }, []);

    const handleNormalLogout = async () => {
        setIsLoggingOut(true);
        try {
            const result = await logoutManager.logout(supabase, {
                timeout: 3000,
                clearStorage: true
            });
            setLastLogoutResult(`Normal logout: ${result.success ? 'Success' : 'Failed'} ${result.error ? `(${result.error})` : ''}`);
        } catch (error) {
            setLastLogoutResult(`Normal logout error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
            setTimeout(() => setIsLoggingOut(false), 500);
        }
    };

    const handleForceLogout = async () => {
        setIsLoggingOut(true);
        try {
            const result = await logoutManager.logout(supabase, {
                force: true,
                clearStorage: true
            });
            setLastLogoutResult(`Force logout: ${result.success ? 'Success' : 'Failed'} ${result.forced ? '(Forced)' : ''}`);
        } catch (error) {
            setLastLogoutResult(`Force logout error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
            setTimeout(() => setIsLoggingOut(false), 500);
        }
    };

    const handleQuickLogout = async () => {
        setIsLoggingOut(true);
        try {
            const result = await logoutManager.logout(supabase, {
                timeout: 1000, // 快速超时
                clearStorage: true
            });
            setLastLogoutResult(`Quick logout: ${result.success ? 'Success' : 'Failed'} ${result.forced ? '(Forced due to timeout)' : ''}`);
        } catch (error) {
            setLastLogoutResult(`Quick logout error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
            setTimeout(() => setIsLoggingOut(false), 500);
        }
    };

    const clearEvents = () => {
        setAuthEvents([]);
        setLastLogoutResult('');
    };

    if (!user) {
        return (
            <div className="p-4 bg-gray-100 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Logout Test Panel</h3>
                <p className="text-gray-600">Please login first to test logout functionality.</p>
            </div>
        );
    }

    return (
        <div className="p-4 bg-white border rounded-lg shadow-sm">
            <h3 className="text-lg font-semibold mb-4">Logout Test Panel</h3>
            
            <div className="mb-4">
                <p className="text-sm text-gray-600">Current user: <span className="font-medium">{user.email}</span></p>
                <p className="text-sm text-gray-600">Logout manager status: {logoutManager.isLoggingOutNow() ? 'Logging out...' : 'Ready'}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                <button
                    onClick={handleNormalLogout}
                    disabled={isLoggingOut}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                    {isLoggingOut ? 'Logging out...' : 'Normal Logout'}
                </button>
                
                <button
                    onClick={handleForceLogout}
                    disabled={isLoggingOut}
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                    {isLoggingOut ? 'Logging out...' : 'Force Logout'}
                </button>
                
                <button
                    onClick={handleQuickLogout}
                    disabled={isLoggingOut}
                    className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                    {isLoggingOut ? 'Logging out...' : 'Quick Logout'}
                </button>
            </div>

            {lastLogoutResult && (
                <div className="mb-4 p-3 bg-gray-50 rounded border">
                    <h4 className="text-sm font-medium mb-1">Last Result:</h4>
                    <p className="text-sm text-gray-700">{lastLogoutResult}</p>
                </div>
            )}

            <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                    <h4 className="text-sm font-medium">Auth Events (Last 10):</h4>
                    <button
                        onClick={clearEvents}
                        className="text-xs px-2 py-1 bg-gray-200 rounded hover:bg-gray-300"
                    >
                        Clear
                    </button>
                </div>
                <div className="bg-gray-50 rounded border p-2 max-h-32 overflow-y-auto">
                    {authEvents.length === 0 ? (
                        <p className="text-xs text-gray-500">No events yet</p>
                    ) : (
                        authEvents.map((event, index) => (
                            <p key={index} className="text-xs text-gray-700 font-mono">{event}</p>
                        ))
                    )}
                </div>
            </div>

            <div className="text-xs text-gray-500">
                <p>• Normal Logout: Standard logout with 3s timeout</p>
                <p>• Force Logout: Immediate logout without waiting for network</p>
                <p>• Quick Logout: Fast logout with 1s timeout (may force if slow)</p>
            </div>
        </div>
    );
}
