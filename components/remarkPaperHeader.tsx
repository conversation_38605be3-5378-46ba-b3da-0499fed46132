// remarkPaperHeader.ts
/* -----------------------------------------------------------------------
 * 将 Markdown 文件 YAML front-matter 渲染为论文信息区（HTML 片段）
 * ---------------------------------------------------------------------*/
import matter from "gray-matter";
import type { Root, RootContent, Yaml } from "mdast";
import type { Plugin } from "unified";
import { shouldShowLineNumbers } from "@/lib/line-numbers";

/* ---------- 运行时判定节点是否为 YAML ----------------------------- */
function isYaml(node: RootContent): node is Yaml {
  return node.type === "yaml";
}

/* ---------- HTML 生成工具 ----------------------------------------- */
function buildHeaderHTML(data: Record<string, unknown>, className: string): string {
  const esc = (v: unknown) =>
    String(v ?? "").replaceAll(/["&<>]/g, c =>
      c === "&" ? "&amp;" : c === "<" ? "&lt;" : c === ">" ? "&gt;" : "&quot;"
    );

  const out: string[] = [`<div class="${className}">`];

  /* 1) 标题 -------------------------------------------------------- */
  if (data.title) out.push(`  <h1>${esc(data.title)}</h1>`);

  /* 2) 作者 -------------------------------------------------------- */
  if (Array.isArray(data.authors)) {
    const authors = data.authors
      .map(a => {
        if (typeof a === "string") return esc(a);
        if (typeof a === "object" && a) {
          const name = esc((a as Record<string, unknown>).name);
          const aff =
            (a as Record<string, unknown>).affiliations ??
            (a as Record<string, unknown>).affiliation;
          if (aff) {
            const sup = Array.isArray(aff) ? aff.join(", ") : aff;
            return `${name}<sup>${esc(sup)}</sup>`;
          }
          return name;
        }
        return "";
      })
      .filter(Boolean)
      .join(", ");
    if (authors) out.push(`  <p class="authors">${authors}</p>`);
  }

  /* 3) 机构 -------------------------------------------------------- */
  if (data.affiliations) {
    const items: string[] = [];

    if (Array.isArray(data.affiliations)) {
      data.affiliations.forEach(aff => {
        if (typeof aff === "string") {
          items.push(esc(aff));
        } else if (typeof aff === "object" && aff) {
          const obj = aff as Record<string, unknown>;
          const id = obj.id ?? obj.index ?? obj.no;
          const name = obj.name ?? obj.organization ?? obj.affiliation ?? obj.institute;
          if (name) {
            const head = id !== undefined ? `<sup>${esc(id)}</sup> ` : "";
            items.push(`${head}${esc(name)}`);
          }
        }
      });
    } else if (typeof data.affiliations === "object") {
      Object.entries(data.affiliations as Record<string, unknown>).forEach(([id, name]) =>
        items.push(`<sup>${esc(id)}</sup> ${esc(name)}`)
      );
    }

    if (items.length) out.push(`  <p class="affiliations">${items.join("; ")}</p>`);
  }

  /* 4) 日期 / 摘要 / 标签 ----------------------------------------- */
  // if (data.date) out.push(`  <p class="date">${esc(data.date)}</p>`);
  if (data.abstract)
    out.push(`  <p class="abstract"><strong>Abstract:</strong> ${esc(data.abstract)}</p>`);
  if (data.tags) {
    const tagStr = Array.isArray(data.tags) ? (data.tags as unknown[]).join(", ") : data.tags;
    out.push(`  <p class="tags"><strong>Tags:</strong> ${esc(tagStr)}</p>`);
  }

  out.push("</div>");
  return out.join("\n");
}

/* ---------- Remark 插件主体 --------------------------------------- */
export interface PaperHeaderOptions {
  className?: string;
}

export const remarkPaperHeader: Plugin<[PaperHeaderOptions?], Root> = opts => {
  const className = opts?.className ?? "paper-header";

  return (tree, file) => {
    /* 1) 查找 YAML front-matter 并移除 */
    let yamlNode: Yaml | null = null;

    for (let i = 0; i < tree.children.length; i += 1) {
      const child = tree.children[i];
      if (isYaml(child)) {
        yamlNode = child;
        tree.children.splice(i, 1); // 删除 YAML 节点
        break;
      }
    }
    if (!yamlNode) return; // 没有 front-matter，直接返回

    /* 2) 解析 YAML 文本 ------------------------------------------ */
    const { data } = matter(`---\n${yamlNode.value}\n---`);

    /* 3) 检查 status 字段并设置行号显示标记 ---------------------- */
    const showLineNumbers = shouldShowLineNumbers(data.status);

    // 将行号显示状态存储到 file.data 中，供后续插件使用
    if (!file.data) file.data = {};
    file.data.showLineNumbers = showLineNumbers;

    /* 4) 生成 HTML 并插入 AST 顶部 ------------------------------- */
    const htmlNode: RootContent = {
      type: "html",
      value: buildHeaderHTML(data as Record<string, unknown>, className),
    };
    tree.children.unshift(htmlNode);
  };
};

export default remarkPaperHeader;
