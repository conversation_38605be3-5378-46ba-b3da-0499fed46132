"use client";

import { Bell } from "lucide-react";
import { useEffect, useState, useCallback, useRef } from "react";
import { formatDistanceToNow } from "date-fns";
import { enUS } from "date-fns/locale";

import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useSupabase } from "@/components/SupabaseProvider";
import { generateNotificationUrl, generateMobileNotificationUrl, type NotificationType } from "@/lib/notifications/url-generator";
import { executeTerminalCommand } from "@/components/TerminalMenuManager";

interface NotificationActor {
    id: string;
    name: string | null;
    image: string | null;
}

interface NotificationData {
    id: string;
    type: string;
    paperId: string | null;
    commentId: string | null;
    preview: string | null;
    paperTitle: string | null;
    actorName: string | null;
    createdAt: string;
    actor: NotificationActor;
}

interface NotificationItem {
    id: string;
    notificationId: string;
    readAt: string | null;
    archivedAt: string | null;
    muted: boolean;
    notification: NotificationData;
}

interface NotificationsResponse {
    notifications: NotificationItem[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
    };
}

interface NotificationBellProps {
    onMobileNavigate?: () => void;
}

export default function NotificationBell({ onMobileNavigate }: NotificationBellProps) {
    const { user } = useSupabase();
    const [unreadCount, setUnreadCount] = useState(0);
    const [notifications, setNotifications] = useState<NotificationItem[]>([]);
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [isOpen, setIsOpen] = useState(false);
    const [isMobile, setIsMobile] = useState(false);

    // 检测是否有浮窗管理能力（通过检测Terminal菜单管理器是否存在）
    const hasFloatingWindowCapability = useCallback(() => {
        // 检查是否存在Terminal菜单管理器的DOM元素或全局事件监听器
        return typeof window !== 'undefined' && (
            document.querySelector('[data-terminal-menu-manager]') !== null ||
            // 或者检查是否在Terminal页面
            window.location.pathname === '/terminal' ||
            window.location.pathname.startsWith('/terminal/')
        );
    }, []);

    // 使用useRef追踪状态，避免useEffect依赖问题
    const hasLoadedNotifications = useRef(false);
    const hasInitialized = useRef(false);

    // 检测是否为移动设备
    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    // 获取未读通知数量
    const fetchUnreadCount = useCallback(async () => {
        if (!user) return;

        try {
            const response = await fetch("/api/notifications", { method: "HEAD" });
            if (response.ok) {
                const count = response.headers.get("X-Unread-Count");
                setUnreadCount(parseInt(count || "0", 10));
            }
        } catch (error) {
            console.error("Failed to fetch unread notification count:", error);
        }
    }, [user]);

    // 获取通知列表
    const fetchNotifications = useCallback(async (pageNum: number = 1, append: boolean = false) => {
        if (!user || loading) return;

        setLoading(true);
        try {
            const response = await fetch(`/api/notifications?page=${pageNum}&limit=10`);
            if (response.ok) {
                const data: NotificationsResponse = await response.json();

                if (append) {
                    setNotifications(prev => [...prev, ...data.notifications]);
                } else {
                    setNotifications(data.notifications);
                    hasLoadedNotifications.current = true;
                }

                setHasMore(data.pagination.hasNext);
                setPage(pageNum);
            }
        } catch (error) {
            console.error("Failed to fetch notification list:", error);
        } finally {
            setLoading(false);
        }
    }, [user, loading]);

    // 标记通知为已读
    const markAsRead = useCallback(async (notificationId: string) => {
        try {
            const response = await fetch(`/api/notifications/${notificationId}/read`, {
                method: "PATCH",
            });

            if (response.ok) {
                // 更新本地状态
                setNotifications(prev =>
                    prev.map(notif =>
                        notif.id === notificationId
                            ? { ...notif, readAt: new Date().toISOString() }
                            : notif
                    )
                );

                // 更新未读数量
                setUnreadCount(prev => Math.max(0, prev - 1));
            }
        } catch (error) {
            console.error("Failed to mark notification as read:", error);
        }
    }, []);

    // 处理通知点击
    const handleNotificationClick = useCallback((notification: NotificationItem) => {
        // 如果未读，标记为已读
        if (!notification.readAt) {
            markAsRead(notification.id);
        }

        // 关闭popover
        setIsOpen(false);

        // 根据通知类型跳转到相应页面
        if (notification.notification.paperId) {
            let url = '';

            try {
                // 使用统一的URL生成工具
                const notificationType = notification.notification.type as NotificationType;

                if (isMobile) {
                    // 移动端使用查询参数而不是锚点
                    url = generateMobileNotificationUrl(notificationType, {
                        paperId: notification.notification.paperId,
                        commentId: notification.notification.commentId || undefined,
                    });
                } else {
                    // 桌面端使用锚点
                    url = generateNotificationUrl(notificationType, {
                        paperId: notification.notification.paperId,
                        commentId: notification.notification.commentId || undefined,
                    });
                }
            } catch (error) {
                console.error('Failed to generate notification URL:', error);
                // 回退到默认URL
                url = `/paper/${notification.notification.paperId}`;
                if (notification.notification.commentId) {
                    url += `#comment-${notification.notification.commentId}`;
                }
            }

            // 检测是否有浮窗管理能力
            const canUseFloatingWindow = hasFloatingWindowCapability();

            if (isMobile) {
                // 移动端：在当前窗口跳转
                onMobileNavigate?.();
                setTimeout(() => {
                    window.location.href = url;
                }, 150);
            } else if (canUseFloatingWindow) {
                // 桌面端有浮窗能力：使用Terminal命令系统创建浮窗
                try {
                    // 生成窗口标题
                    const notificationType = notification.notification.type as NotificationType;
                    let title = 'Notification';
                    if (notificationType === 'REVIEW_UPDATE' || notificationType === 'PAPER_STATUS_CHANGE') {
                        title = 'Review';
                    } else if (notificationType === 'COMMENT_ON_PAPER' || notificationType === 'REPLY_ON_COMMENT') {
                        title = 'Paper';
                    }

                    // 构建create_window命令
                    const createWindowCommand = `create_window type=path target=${url} width=0.8 height=0.8 x=0.1 y=0.1 title=${title}`;

                    // 使用Terminal命令系统执行命令
                    executeTerminalCommand(createWindowCommand, 'notification');
                } catch (error) {
                    console.error('Failed to execute terminal command:', error);
                    // 回退到新窗口
                    window.open(url, '_blank');
                }
            } else {
                // 桌面端无浮窗能力：使用新窗口
                window.open(url, '_blank');
            }
        }
    }, [markAsRead, isMobile, onMobileNavigate, hasFloatingWindowCapability]);

    // 加载更多通知
    const loadMore = useCallback(() => {
        if (hasMore && !loading) {
            fetchNotifications(page + 1, true);
        }
    }, [hasMore, loading, page, fetchNotifications]);

    // 初始化加载
    useEffect(() => {
        if (user && !hasInitialized.current) {
            fetchUnreadCount();
            hasInitialized.current = true;
        }
    }, [user, fetchUnreadCount]);

    // 当popover打开时加载通知
    useEffect(() => {
        if (isOpen && user && !hasLoadedNotifications.current && !loading) {
            fetchNotifications(1, false);
        }

        // 当popover关闭时，重置加载状态以便下次打开时可以重新加载
        if (!isOpen) {
            hasLoadedNotifications.current = false;
        }
    }, [isOpen, user, fetchNotifications]);

    // 格式化通知类型
    const getNotificationText = (notification: NotificationData) => {
        switch (notification.type) {
            case 'COMMENT_ON_PAPER':
                return `${notification.actorName} commented on your paper "${notification.paperTitle}"`;
            case 'REPLY_ON_COMMENT':
                return `${notification.actorName} replied to your comment`;
            case 'REVIEW_UPDATE':
                return `${notification.actorName} updated the paper "${notification.paperTitle}" in the review process`;
            case 'PAPER_STATUS_CHANGE':
                return `${notification.actorName} updated the status of the paper "${notification.paperTitle}"`;
            case 'MENTION':
                return `${notification.actorName} mentioned you in "${notification.paperTitle}"`;
            case 'SYSTEM':
                return `System notification`;
            default:
                return notification.preview || 'New notification';
        }
    };

    // 格式化时间
    const formatTime = (dateString: string) => {
        try {
            const date = new Date(dateString);
            return formatDistanceToNow(date, { addSuffix: true, locale: enUS });
        } catch (error) {
            return 'Unknown time';
        }
    };

    if (!user) return null;

    return (
        <Popover open={isOpen} onOpenChange={setIsOpen} modal={true}>
            <PopoverTrigger asChild>
                <button
                    className="relative p-2 rounded-full hover:bg-gray-100 transition-colors"
                    aria-label={`Notification${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
                >
                    <Bell className="h-5 w-5 text-gray-600" />
                    {unreadCount > 0 && (
                        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center min-w-[20px]">
                            {unreadCount > 99 ? '99+' : unreadCount}
                        </span>
                    )}
                </button>
            </PopoverTrigger>

            <PopoverContent
                className={`${isMobile ? 'w-72' : 'w-80 md:w-96'} p-0 z-[60]`}
                align="end"
                side="bottom"
                sideOffset={8}
                // 确保z-index生效，覆盖Sheet的z-50
                style={{
                    zIndex: 60,
                    ...(isMobile ? {
                        touchAction: 'pan-y',
                        WebkitOverflowScrolling: 'touch'
                    } : {})
                }}
            >
                {/* 移动端使用原生滚动，桌面端使用ScrollArea */}
                {isMobile ? (
                    <div
                        className="h-96 max-h-[70vh] overflow-y-auto overscroll-contain"
                        style={{
                            WebkitOverflowScrolling: 'touch',
                            touchAction: 'pan-y'
                        }}
                        // 阻止事件冒泡，确保Sheet不会拦截滚动事件
                        onTouchStart={(e) => e.stopPropagation()}
                        onTouchMove={(e) => e.stopPropagation()}
                        onTouchEnd={(e) => e.stopPropagation()}
                        onClick={(e) => e.stopPropagation()}
                    >
                        <div className="p-2">
                            {notifications.length === 0 ? (
                                <div className="text-center py-8 text-gray-500 text-sm">
                                    {loading ? (
                                        <div className="flex items-center justify-center space-x-2">
                                            <div className="w-4 h-4 bg-gray-300 rounded-full animate-pulse"></div>
                                            <span>Loading...</span>
                                        </div>
                                    ) : (
                                        <div className="space-y-2">
                                            <Bell className="h-8 w-8 text-gray-300 mx-auto" />
                                            <p>No notifications</p>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <>
                                    {notifications.map((item) => (
                                        <div
                                            key={item.id}
                                            className={`p-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors border-l-2 active:bg-gray-100 ${!item.readAt
                                                ? 'bg-blue-50 border-l-blue-400'
                                                : 'bg-white border-l-transparent hover:border-l-gray-200'
                                                }`}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleNotificationClick(item);
                                            }}
                                            // 添加触摸反馈和事件处理
                                            onTouchStart={(e) => {
                                                e.stopPropagation();
                                                e.currentTarget.style.backgroundColor = '#f3f4f6';
                                            }}
                                            onTouchEnd={(e) => {
                                                e.stopPropagation();
                                                e.currentTarget.style.backgroundColor = '';
                                            }}
                                        >
                                            <div className="flex items-start gap-3">
                                                {/* 未读指示器 */}
                                                <div className="flex-shrink-0 mt-1">
                                                    {!item.readAt && (
                                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                    )}
                                                </div>

                                                <div className="flex-1 min-w-0">
                                                    {/* 通知内容 */}
                                                    <p className="text-sm text-gray-900 leading-5 font-medium">
                                                        {getNotificationText(item.notification)}
                                                    </p>

                                                    {/* 预览内容 */}
                                                    {item.notification.preview && (
                                                        <p className="text-xs text-gray-600 mt-1 line-clamp-2 bg-gray-50 p-2 rounded border-l-2 border-l-gray-200">
                                                            "{item.notification.preview}"
                                                        </p>
                                                    )}

                                                    {/* 时间 */}
                                                    <p className="text-xs text-gray-500 mt-2 flex items-center">
                                                        <span>{formatTime(item.notification.createdAt)}</span>
                                                        {!item.readAt && (
                                                            <span className="ml-2 text-blue-500 text-xs">• Unread</span>
                                                        )}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    ))}

                                    {/* 加载更多按钮 */}
                                    {hasMore && (
                                        <div className="text-center py-4 border-t border-gray-100 mt-2">
                                            <button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    loadMore();
                                                }}
                                                disabled={loading}
                                                className="text-sm text-blue-500 hover:text-blue-700 disabled:opacity-50 px-4 py-2 rounded-md hover:bg-blue-50 transition-colors active:bg-blue-100"
                                            >
                                                {loading ? (
                                                    <div className="flex items-center space-x-2">
                                                        <div className="w-3 h-3 bg-blue-300 rounded-full animate-pulse"></div>
                                                        <span>Loading...</span>
                                                    </div>
                                                ) : (
                                                    "Load more"
                                                )}
                                            </button>
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    </div>
                ) : (
                    <ScrollArea className="h-96 max-h-[70vh]">
                        <div className="p-2">
                            {notifications.length === 0 ? (
                                <div className="text-center py-8 text-gray-500 text-sm">
                                    {loading ? (
                                        <div className="flex items-center justify-center space-x-2">
                                            <div className="w-4 h-4 bg-gray-300 rounded-full animate-pulse"></div>
                                            <span>Loading...</span>
                                        </div>
                                    ) : (
                                        <div className="space-y-2">
                                            <Bell className="h-8 w-8 text-gray-300 mx-auto" />
                                            <p>No notifications</p>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <>
                                    {notifications.map((item) => (
                                        <div
                                            key={item.id}
                                            className={`p-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors border-l-2 ${!item.readAt
                                                ? 'bg-blue-50 border-l-blue-400'
                                                : 'bg-white border-l-transparent hover:border-l-gray-200'
                                                }`}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleNotificationClick(item);
                                            }}
                                        >
                                            <div className="flex items-start gap-3">
                                                {/* 未读指示器 */}
                                                <div className="flex-shrink-0 mt-1">
                                                    {!item.readAt && (
                                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                    )}
                                                </div>

                                                <div className="flex-1 min-w-0">
                                                    {/* 通知内容 */}
                                                    <p className="text-sm text-gray-900 leading-5 font-medium">
                                                        {getNotificationText(item.notification)}
                                                    </p>

                                                    {/* 预览内容 */}
                                                    {item.notification.preview && (
                                                        <p className="text-xs text-gray-600 mt-1 line-clamp-2 bg-gray-50 p-2 rounded border-l-2 border-l-gray-200">
                                                            "{item.notification.preview}"
                                                        </p>
                                                    )}

                                                    {/* 时间 */}
                                                    <p className="text-xs text-gray-500 mt-2 flex items-center">
                                                        <span>{formatTime(item.notification.createdAt)}</span>
                                                        {!item.readAt && (
                                                            <span className="ml-2 text-blue-500 text-xs">• Unread</span>
                                                        )}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    ))}

                                    {/* 加载更多按钮 */}
                                    {hasMore && (
                                        <div className="text-center py-4 border-t border-gray-100 mt-2">
                                            <button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    loadMore();
                                                }}
                                                disabled={loading}
                                                className="text-sm text-blue-500 hover:text-blue-700 disabled:opacity-50 px-4 py-2 rounded-md hover:bg-blue-50 transition-colors"
                                            >
                                                {loading ? (
                                                    <div className="flex items-center space-x-2">
                                                        <div className="w-3 h-3 bg-blue-300 rounded-full animate-pulse"></div>
                                                        <span>Loading...</span>
                                                    </div>
                                                ) : (
                                                    "Load more"
                                                )}
                                            </button>
                                        </div>
                                    )}
                                </>
                            )}
                        </div>
                    </ScrollArea>
                )}
            </PopoverContent>
        </Popover>
    );
} 