"use client";
import { ZoomIn, ZoomOut, Maximize, Fullscreen, Printer } from "lucide-react";
import React, { useEffect, useRef, useState, useCallback } from "react";

import { Button } from "@/components/ui/button";
import { LINE_NUMBER_CONFIG } from "@/lib/line-numbers";

// 导入共享的页面样式
import "../app/shared/page-layout.css";

interface StaticPagedPreviewProps {
    content: string; // 已分页的HTML内容
    debug?: boolean;
}

/**
 * 静态分页预览组件
 * 用于显示已经分页处理的HTML内容，不再进行分页计算
 * 主要用于review页面等需要显示预处理HTML的场景
 */
const StaticPagedPreview: React.FC<StaticPagedPreviewProps> = ({ content, debug = false }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const contentContainerRef = useRef<HTMLDivElement>(null);

    // 添加缩放状态
    const [scale, setScale] = useState<number>(1);
    const [autoScale, setAutoScale] = useState<boolean>(true);

    // 检查内容是否超出容器宽度
    const checkContentOverflow = useCallback((currentScale: number) => {
        const previewContainer = containerRef.current;
        if (!previewContainer || !contentContainerRef.current) {
            return false;
        }

        // 获取内容容器的原始宽度
        const contentElement = contentContainerRef.current.querySelector('.pages-container') as HTMLElement;
        if (!contentElement || contentElement.children.length === 0) return false;

        // 获取第一个页面元素的原始宽度
        const pageElement = contentElement.children[0] as HTMLElement;
        if (!pageElement) return false;

        // 计算缩放后的实际宽度
        const pageOriginalWidth = pageElement.offsetWidth;
        const scaledPageWidth = pageOriginalWidth * currentScale;

        // 获取容器可用宽度（减去padding）
        const containerWidth = previewContainer.clientWidth - 40; // 40px为左右padding

        return scaledPageWidth > containerWidth;
    }, []);

    // 应用布局模式
    const applyLayoutMode = useCallback((isOverflow: boolean, newScale: number) => {
        const previewContainer = containerRef.current;
        const previewWrapper = previewContainer?.querySelector(".preview-wrapper") as HTMLElement;

        if (!previewContainer || !previewWrapper || !contentContainerRef.current) return;

        if (isOverflow) {
            // 内容超出容器：左对齐模式
            previewWrapper.style.justifyContent = 'flex-start';
            contentContainerRef.current.style.transformOrigin = "top left";
        } else {
            // 内容未超出容器：居中模式
            previewWrapper.style.justifyContent = 'center';
            contentContainerRef.current.style.transformOrigin = "top center";
        }

        contentContainerRef.current.style.transform = `scale(${newScale})`;
    }, []);

    // 智能缩放函数：根据内容实际宽度动态选择布局模式
    const zoomWithFocus = useCallback(
        (newScale: number) => {
            if (newScale === scale) return;

            const previewContainer = containerRef.current;
            if (!previewContainer || !contentContainerRef.current) return;

            // 保存当前滚动位置和视口信息
            const viewportWidth = previewContainer.clientWidth;
            const viewportHeight = previewContainer.clientHeight;
            const scrollTop = previewContainer.scrollTop;
            const scrollLeft = previewContainer.scrollLeft;

            // 检查新缩放级别下内容是否会超出容器
            const willOverflow = checkContentOverflow(newScale);

            // 应用新的布局模式
            applyLayoutMode(willOverflow, newScale);

            // 根据布局模式调整滚动位置
            if (!willOverflow) {
                // 居中模式：保持垂直滚动，重置水平滚动
                const centerY = (scrollTop + viewportHeight / 2) / scale;
                const newScrollTop = centerY * newScale - viewportHeight / 2;
                previewContainer.scrollTop = Math.max(0, newScrollTop);
                previewContainer.scrollLeft = 0;
            } else {
                // 左对齐模式：保持视觉中心点
                const centerX = (scrollLeft + viewportWidth / 2) / scale;
                const centerY = (scrollTop + viewportHeight / 2) / scale;

                const newScrollLeft = centerX * newScale - viewportWidth / 2;
                const newScrollTop = centerY * newScale - viewportHeight / 2;

                previewContainer.scrollLeft = Math.max(0, newScrollLeft);
                previewContainer.scrollTop = Math.max(0, newScrollTop);
            }

            // 异步更新React状态
            setTimeout(() => {
                setScale(newScale);
            }, 0);
        },
        [scale, checkContentOverflow, applyLayoutMode]
    );

    // 自动计算适合当前窗口大小的缩放比例
    const calculateOptimalScale = useCallback(() => {
        if (!contentContainerRef.current || !containerRef.current) return 1;

        // 查找第一个页面元素来计算缩放比例
        const pageElement = contentContainerRef.current.querySelector('.page') as HTMLElement;
        if (!pageElement) return 1;

        const containerWidth = containerRef.current.clientWidth - 40; // 40px作为边距
        const containerHeight = containerRef.current.clientHeight - 40;
        const pageWidth = pageElement.offsetWidth;
        const pageHeight = pageElement.offsetHeight;

        // 确保计算值合理
        if (containerWidth <= 0 || containerHeight <= 0 || pageWidth <= 0 || pageHeight <= 0) {
            return 0.9; // 默认值
        }

        // 取水平和垂直方向的最小缩放比例，确保页面完全可见
        const widthScale = containerWidth / pageWidth;
        const heightScale = containerHeight / pageHeight;
        const scale = Math.min(widthScale, heightScale, 1); // 最大不超过原始大小

        return scale > 0 ? scale : 0.9;
    }, []);

    // 窗口大小变化时调整缩放
    useEffect(() => {
        if (!autoScale) return;

        let resizeTimeout: NodeJS.Timeout | null = null;
        let lastWidth = 0;
        let lastHeight = 0;

        const handleResize = () => {
            const container = containerRef.current;
            if (!container) return;

            const currentWidth = container.clientWidth;
            const currentHeight = container.clientHeight;

            // 大小变化足够显著时才触发更新
            if (
                Math.abs(currentWidth - lastWidth) > 5 ||
                Math.abs(currentHeight - lastHeight) > 5
            ) {
                if (resizeTimeout) clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    const newScale = calculateOptimalScale();
                    if (newScale > 0) {
                        zoomWithFocus(newScale);
                    } else {
                        // 即使没有缩放变化，也要检查布局模式
                        const isOverflow = checkContentOverflow(scale);
                        applyLayoutMode(isOverflow, scale);
                    }
                    lastWidth = currentWidth;
                    lastHeight = currentHeight;
                }, 100);
            }
        };

        const container = containerRef.current;
        let resizeObserver: ResizeObserver | null = null;

        if (container && window.ResizeObserver) {
            resizeObserver = new ResizeObserver(handleResize);
            resizeObserver.observe(container);
        }

        if (!resizeObserver) {
            window.addEventListener("resize", handleResize);
        }

        // 初始化时立即调整一次
        handleResize();

        return () => {
            if (resizeTimeout) clearTimeout(resizeTimeout);
            window.removeEventListener("resize", handleResize);
            if (resizeObserver) {
                resizeObserver.disconnect();
            }
        };
    }, [calculateOptimalScale, autoScale, zoomWithFocus]);

    // 添加缩放功能 - Ctrl/Cmd + 滚轮
    useEffect(() => {
        const handleWheel = (e: WheelEvent) => {
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                const delta = e.deltaY < 0 ? 0.05 : -0.05;
                const newScale = Math.max(0.5, Math.min(2, scale + delta));
                zoomWithFocus(newScale);
                setAutoScale(false); // 手动缩放时禁用自动缩放
            }
        };

        window.addEventListener("wheel", handleWheel, { passive: false });
        return () => window.removeEventListener("wheel", handleWheel);
    }, [scale, zoomWithFocus]);

    // 处理内容更新
    useEffect(() => {
        if (!content || !contentContainerRef.current) return;

        // 清空容器
        contentContainerRef.current.innerHTML = "";

        try {
            // 解析HTML内容
            const tempDiv = document.createElement("div");
            tempDiv.innerHTML = content;

            // 查找页面容器或页面元素
            let pagesToAdd: Element[] = [];

            // 首先尝试查找pages-container
            const pagesContainer = tempDiv.querySelector('.pages-container');
            if (pagesContainer) {
                pagesToAdd = Array.from(pagesContainer.children);
            } else {
                // 直接查找page元素
                const pages = tempDiv.querySelectorAll('.page');
                if (pages.length > 0) {
                    pagesToAdd = Array.from(pages);
                } else {
                    // If no paged structure found, add content directly
                    contentContainerRef.current.innerHTML = content;
                    return;
                }
            }

            // 创建pages容器并添加页面
            const pagesWrapper = document.createElement('div');
            pagesWrapper.className = 'pages-container';

            pagesToAdd.forEach(page => {
                pagesWrapper.appendChild(page.cloneNode(true));
            });

            contentContainerRef.current.appendChild(pagesWrapper);

            // 内容加载完成后，如果启用了自动缩放，计算并应用最佳缩放
            if (autoScale) {
                setTimeout(() => {
                    const newScale = calculateOptimalScale();
                    if (newScale > 0) zoomWithFocus(newScale);
                }, 100);
            }
        } catch (error) {
            // Fallback: set content directly if parsing fails
            contentContainerRef.current.innerHTML = content;
        }
    }, [content, debug, autoScale, calculateOptimalScale, zoomWithFocus]);

    // 处理锚点跳转
    useEffect(() => {
        const handleAnchorClick = (e: MouseEvent) => {
            const anchor = (e.target as HTMLElement).closest('a[href^="#"]');
            if (!anchor) return;

            const hash = anchor.getAttribute("href")!.slice(1);
            const target = contentContainerRef.current?.querySelector(
                `#${CSS.escape(hash)}`
            ) as HTMLElement | null;

            if (!target || !containerRef.current) return;

            e.preventDefault();
            history.pushState(null, "", `#${hash}`);

            // 滚动到目标位置
            const containerRect = containerRef.current.getBoundingClientRect();
            const targetRect = target.getBoundingClientRect();
            const delta = targetRect.top - containerRect.top - containerRect.height / 2 + targetRect.height / 2;

            containerRef.current.scrollTop += delta;
        };

        const container = contentContainerRef.current;
        container?.addEventListener("click", handleAnchorClick);
        return () => container?.removeEventListener("click", handleAnchorClick);
    }, []);

    return (
        <div ref={containerRef} className="paged-preview">
            {/* 缩放控制按钮 */}
            <div className="fixed bottom-5 right-5 z-50 flex items-center gap-1 bg-background p-2 rounded-md shadow-md">
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                        const newScale = Math.min(2, scale + 0.1);
                        zoomWithFocus(newScale);
                        setAutoScale(false);
                    }}
                >
                    <ZoomIn />
                </Button>

                <span className="text-xs font-medium px-2">{Math.round(scale * 100)}%</span>

                <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                        const newScale = Math.max(0.5, scale - 0.1);
                        zoomWithFocus(newScale);
                        setAutoScale(false);
                    }}
                >
                    <ZoomOut />
                </Button>

                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                        zoomWithFocus(1);
                        setAutoScale(false);
                    }}
                >
                    <Maximize />
                </Button>

                <Button
                    variant={autoScale ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setAutoScale(!autoScale)}
                >
                    <Fullscreen />
                </Button>

                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                        try {
                            window.print();
                        } catch (err) {
                            // Silently handle print errors
                        }
                    }}
                    className="ml-1"
                >
                    <Printer />
                </Button>
            </div>

            {/* 内容容器 */}
            <div className="preview-wrapper">
                <div
                    ref={contentContainerRef}
                    className="scaled-container"
                    style={{
                        transform: `scale(${scale})`,
                        transformOrigin: "top center",
                    }}
                />
            </div>

            {/* 添加CSS样式 */}
            <style jsx>{`
        .paged-preview {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          overflow: auto;
          padding: 0;
          background: #f0f0f0;
        }
        .preview-wrapper {
          width: 100%;
          display: flex;
          justify-content: center;
          padding: 0;
          min-height: 100%;
          overflow: visible;
        }
        .scaled-container {
          display: inline-block;
        }
        .pages-container {
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        .page {
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          margin-bottom: 20px;
        }
        .page:last-child {
          margin-bottom: 0;
        }
        /* 行号列样式 */
        .line-numbers {
          user-select: none;
        }
        .line-numbers span {
          display: block;
          margin: 2px 0;
          opacity: ${LINE_NUMBER_CONFIG.OPACITY};
          text-align: center;
          font-size: ${LINE_NUMBER_CONFIG.FONT_SIZE};
        }
      `}</style>
        </div>
    );
};

export default StaticPagedPreview; 