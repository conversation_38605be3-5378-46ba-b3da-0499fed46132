"use client";

import React from "react";

interface AnchorLinkProps {
  href: string;
  children: React.ReactNode;
}

export default function AnchorLink({ href, children }: AnchorLinkProps) {
  return (
    <a
      href={href}
      className="heading-anchor"
      aria-hidden="true"
      onClick={e => {
        // 防止页面跳转太突然
        e.preventDefault();
        const id = href.replace("#", "");
        const element = document.getElementById(id);
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
          // 更新URL但不重新加载页面
          window.history.pushState({}, "", href);
        }
      }}
    >
      {children}
    </a>
  );
}
