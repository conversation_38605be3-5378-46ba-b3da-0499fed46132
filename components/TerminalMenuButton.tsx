"use client";

import React from "react";
import { EllipsisVertical } from "lucide-react";

interface TerminalMenuButtonProps {
    isMenuOpen: boolean;
    onToggleMenu: () => void;
    menuWidth: number;
}

export function TerminalMenuButton({
    isMenuOpen,
    onToggleMenu,
    menuWidth,
}: TerminalMenuButtonProps) {
    const buttonBg = isMenuOpen ? "#ffffff" : "var(--color-brand-600)";
    const iconColor = isMenuOpen ? "var(--color-brand-600)" : "#ffffff";

    return (
        <div
            style={{
                position: "fixed",
                top: "70%",
                transform: "translateY(-50%)",
                right: isMenuOpen ? `${menuWidth}px` : "0px",
                zIndex: 1001,
            }}
        >
            <button
                className="terminal-menu-button"
                style={{
                    position: "relative",
                    background: buttonBg,
                    borderRadius: "12px 0 0 12px",
                    borderRight: "none",
                    padding: "10px 4px",
                    cursor: "pointer",
                    transition: "none",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    color: iconColor,
                    userSelect: "none",
                    minWidth: "24px",
                    minHeight: "48px",
                    border: "none",
                    outline: "none",
                    // 强制硬件加速，改善移动端渲染
                    willChange: "background-color",
                    backfaceVisibility: "hidden",
                    WebkitBackfaceVisibility: "hidden",
                }}
                onClick={onToggleMenu}
                title={isMenuOpen ? "close menu" : "open menu"}
            >
                {/* 上方反向圆角 - 使用 SVG */}
                <svg
                    style={{
                        position: "absolute",
                        top: "-12px",
                        right: "-0.5px", // 微调位置消除间隙
                        width: "13px", // 稍微增加宽度确保覆盖
                        height: "13px", // 稍微增加高度确保覆盖
                        pointerEvents: "none",
                        zIndex: 1,
                        // 强制硬件加速，改善移动端渲染
                        willChange: "auto",
                        backfaceVisibility: "hidden",
                        WebkitBackfaceVisibility: "hidden",
                    }}
                    viewBox="0 0 12 12"
                    preserveAspectRatio="none" // 允许拉伸以确保完全覆盖
                >
                    <path
                        d="M 12,0 L 12,12 L 0,12 Q 12,12 12,0 Z"
                        fill={buttonBg}
                        style={{
                            // 确保在移动端正确渲染
                            vectorEffect: "non-scaling-stroke",
                        }}
                    />
                </svg>

                {/* 下方反向圆角 - 使用 SVG */}
                <svg
                    style={{
                        position: "absolute",
                        bottom: "-12px",
                        right: "-0.5px", // 微调位置消除间隙
                        width: "13px", // 稍微增加宽度确保覆盖
                        height: "13px", // 稍微增加高度确保覆盖
                        pointerEvents: "none",
                        zIndex: 1,
                        // 强制硬件加速，改善移动端渲染
                        willChange: "auto",
                        backfaceVisibility: "hidden",
                        WebkitBackfaceVisibility: "hidden",
                    }}
                    viewBox="0 0 12 12"
                    preserveAspectRatio="none" // 允许拉伸以确保完全覆盖
                >
                    <path
                        d="M 12,12 L 12,0 L 0,0 Q 12,0 12,12 Z"
                        fill={buttonBg}
                        style={{
                            // 确保在移动端正确渲染
                            vectorEffect: "non-scaling-stroke",
                        }}
                    />
                </svg>

                <div
                    style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                    }}
                >
                    <EllipsisVertical size={25} />
                </div>
            </button>

            {/* 添加移动端特定的样式 */}
            <style jsx>{`
                @media (max-width: 768px) {
                    .terminal-menu-button {
                        /* 移动端优化：确保像素对齐 */
                        transform: translateZ(0);
                        -webkit-transform: translateZ(0);
                        /* 改善移动端触摸响应 */
                        touch-action: manipulation;
                        -webkit-tap-highlight-color: transparent;
                    }
                }

                /* 确保 SVG 在所有设备上正确渲染 */
                .terminal-menu-button svg {
                    shape-rendering: geometricPrecision;
                    image-rendering: crisp-edges;
                }
            `}</style>
        </div>
    );
}