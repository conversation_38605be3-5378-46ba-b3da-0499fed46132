"use client";

import type { Session, User } from "@supabase/supabase-js";
import { SupabaseClient } from "@supabase/supabase-js";
import { ReactNode, useState, useEffect, createContext, useContext, useCallback } from "react";

import { monitorWebSocketErrors } from "@/utils/fixWebSocketPattern";
import { createClient } from "@/utils/supabase/client";
import { getSessionCompat } from "@/utils/supabase/compat";

// 创建我们自己的上下文，避免使用旧的 SessionContext
interface SupabaseContextType {
  supabase: SupabaseClient;
  session: Session | null;
  user: User | null;
  isLoading: boolean;
}

const SupabaseContext = createContext<SupabaseContextType | undefined>(undefined);

// 自定义钩子，使用我们的上下文
export function useSupabase() {
  const context = useContext(SupabaseContext);
  if (context === undefined) {
    throw new Error("useSupabase must be used within a SupabaseProvider");
  }
  return context;
}

interface Props {
  initialSession: Session | null;
  children: ReactNode;
}

export default function SupabaseProvider({ initialSession, children }: Props) {
  // 使用新的 SSR createClient
  const [supabase] = useState(() => createClient());
  const [isLoading, setIsLoading] = useState(true);
  const [session, setSession] = useState<Session | null>(initialSession);
  const [user, setUser] = useState<User | null>(initialSession?.user || null);

  // 监视WebSocket错误
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      try {
        monitorWebSocketErrors();
      } catch (e) {
        console.warn("监视WebSocket错误失败", e);
      }
    }
  }, []);

  // 监听强制认证刷新事件 - 增强鲁棒性
  const handleForceAuthRefresh = useCallback(async () => {
    try {
      // 强制清除本地状态
      setUser(null);
      setSession(null);

      // 重新获取用户状态
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError || !userData.user) {
        // 确保状态为null
        setUser(null);
        setSession(null);
      } else {
        setUser(userData.user);
        // 如果有用户，获取会话
        const { data: sessionData } = await getSessionCompat(supabase);
        setSession(sessionData.session as unknown as Session);
      }
    } catch (error) {
      // 发生错误时确保清除状态
      setUser(null);
      setSession(null);
    }
  }, [supabase]);

  // 添加强制刷新事件监听器
  useEffect(() => {
    window.addEventListener('force-auth-refresh', handleForceAuthRefresh);
    return () => {
      window.removeEventListener('force-auth-refresh', handleForceAuthRefresh);
    };
  }, [handleForceAuthRefresh]);

  // 管理会话状态
  useEffect(() => {
    // 初始化时安全地获取用户和会话
    const initializeAuth = async () => {
      try {
        // 先获取验证过的用户信息
        const { data: userData, error: userError } = await supabase.auth.getUser();
        if (userError) {
          // 当未登录或会话不存在时，Supabase 会抛出 AuthSessionMissingError，这是正常情况
          if (
            userError.name !== "AuthSessionMissingError" &&
            !/Auth session missing/i.test(userError.message || "")
          ) {
            console.error("获取用户信息失败:", userError);
          } // 否则忽略该错误
          setUser(null);
        } else {
          setUser(userData.user);

          // 如果有用户登录，触发同步
          if (userData.user) {
            await syncUserToPrismaIfNeeded(userData.user);
          }
        }

        // 获取会话信息
        const { data: sessionData } = await getSessionCompat(supabase);
        setSession(sessionData.session as unknown as Session);
      } catch (error) {
        console.error("初始化认证失败:", error);
      } finally {
        setIsLoading(false);
      }
    };

    // 尝试同步用户到Prisma，只在需要时执行
    const syncUserToPrismaIfNeeded = async (authUser: User) => {
      try {
        // 只在客户端执行，使用API端点而不是直接导入syncUser库（避免服务端组件问题）
        const res = await fetch("/api/auth/sync-roles");
        if (!res.ok) {
          console.warn("同步用户失败，状态码:", res.status);
        }
      } catch (error) {
        console.error("同步用户失败:", error);
      }
    };

    initializeAuth();

    // 订阅认证状态变化 - 增强鲁棒性
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      // 处理登出事件 - 确保状态清除
      if (event === 'SIGNED_OUT') {
        setUser(null);
        setSession(null);

        // 额外触发一次状态更新事件，确保所有组件都能收到通知
        setTimeout(() => {
          const refreshEvent = new CustomEvent('auth-state-changed', {
            detail: { event: 'SIGNED_OUT', user: null, session: null }
          });
          window.dispatchEvent(refreshEvent);
        }, 50);

        return;
      }

      // 验证用户身份
      if (session?.user) {
        const { data } = await supabase.auth.getUser();
        setUser(data.user);

        // 登录/注册事件时同步用户
        if (["SIGNED_IN", "USER_UPDATED", "TOKEN_REFRESHED"].includes(event)) {
          if (data.user) {
            await syncUserToPrismaIfNeeded(data.user);
          }
        }

        // 触发登录状态更新事件
        setTimeout(() => {
          const refreshEvent = new CustomEvent('auth-state-changed', {
            detail: { event, user: data.user, session }
          });
          window.dispatchEvent(refreshEvent);
        }, 50);
      } else {
        setUser(null);
      }
      setSession(session);
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [supabase, initialSession]);

  return (
    <SupabaseContext.Provider value={{ supabase, session, user, isLoading }}>
      {children}
    </SupabaseContext.Provider>
  );
}
