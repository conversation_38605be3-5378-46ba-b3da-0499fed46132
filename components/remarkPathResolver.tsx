// remarkPathResolver.tsx - 处理Markdown中所有相对路径资源
import path from "path";

import matter from "gray-matter";
import type { Root, Image, Link } from "mdast";
import type { Plugin } from "unified";
import { visit } from "unist-util-visit";

// 构造完整的Supabase URL
const generateSupabaseUrl = (userId: string, projectName: string, relativePath: string) => {
  // 移除可能的前导./
  const cleanPath = relativePath.replace(/^\.\//, "");

  // 使用download-tree API代替直接访问Supabase存储
  // 这样可以利用现有的API权限处理和缓存机制
  const url = `/api/editor/projects/${encodeURIComponent(projectName)}/download-tree?path=${encodeURIComponent(cleanPath)}`;
  console.log(`[remarkPathResolver] 转换相对路径: ${relativePath} => ${url}`);
  return url;
};

// 检查URL是否为相对路径
const isRelativePath = (url: string): boolean => {
  return (
    !url.startsWith("http://") &&
    !url.startsWith("https://") &&
    !url.startsWith("//") &&
    !url.startsWith("#") &&
    !url.startsWith("data:")
  );
};

interface RemarkPathResolverOptions {
  userId: string;
  projectName: string;
  // 发布API的基础路径（默认为download-tree）
  apiBasePath?: string;
}

export const remarkPathResolver: Plugin<[RemarkPathResolverOptions], Root> = options => {
  const { userId, projectName, apiBasePath = "/api/editor/projects" } = options;

  return (tree, file) => {
    // 从文件内容中解析YAML front matter
    const content = String(file);

    // 使用gray-matter解析YAML front matter
    const { data: frontMatter } = matter(content);
    let mode: "draft" | "publish" = "draft"; // 默认为草稿模式

    // 从frontMatter中的status字段获取模式
    if (frontMatter && frontMatter.status) {
      const status = String(frontMatter.status).toLowerCase();
      if (status === "publish") {
        mode = "publish";
      } else if (status === "draft") {
        mode = "draft";
      }
      // 如果status不是publish或draft，默认使用draft模式
    }

    console.log(`[remarkPathResolver] 检测到文档模式(从YAML): ${mode}, frontMatter:`, frontMatter);

    // 处理所有图片节点
    visit(tree, "image", (node: Image) => {
      if (isRelativePath(node.url)) {
        if (mode === "draft") {
          // 草稿模式: 转换为Supabase存储URL
          node.url = generateSupabaseUrl(userId, projectName, node.url);
        } else if (mode === "publish") {
          // 发布模式: 使用下载API路径
          // 移除可能的前导./
          const cleanPath = node.url.replace(/^\.\//, "");
          node.url = `${apiBasePath}/${encodeURIComponent(projectName)}/download-tree?path=${encodeURIComponent(cleanPath)}`;
        }
      }
    });

    // 处理所有链接节点
    visit(tree, "link", (node: Link) => {
      // 只处理可能指向本地资源的链接
      if (
        isRelativePath(node.url) &&
        !node.url.startsWith("#") && // 跳过锚点链接
        !node.url.endsWith("/")
      ) {
        // 跳过目录链接

        if (mode === "draft") {
          // 草稿模式: 转换为Supabase存储URL
          node.url = generateSupabaseUrl(userId, projectName, node.url);
        } else if (mode === "publish") {
          // 发布模式: 使用下载API路径
          // 移除可能的前导./
          const cleanPath = node.url.replace(/^\.\//, "");
          node.url = `${apiBasePath}/${encodeURIComponent(projectName)}/download-tree?path=${encodeURIComponent(cleanPath)}`;
        }
      }
    });
  };
};

export default remarkPathResolver;
