"use client";
import { ZoomIn, ZoomOut, Maximize, Fullscreen, Printer } from "lucide-react";
import React, { useEffect, useRef, useState, useCallback } from "react";

import { Button } from "@/components/ui/button";
import { LINE_NUMBER_CONFIG, calculateLinesPerPage, calculateStartLineNumber } from "@/lib/line-numbers";

interface PagedPreviewProps {
  content: React.ReactNode | string;
  debug?: boolean;
}
// 精确的分页容差和配置
const BOTTOM_TOLERANCE = 8; // 增加容差，提高分页稳定性
const MEASUREMENT_PRECISION = 2; // 高度测量精度
const MAX_SPLIT_ATTEMPTS = 50; // 文本分割最大尝试次数

const PagedPreview: React.FC<PagedPreviewProps> = ({ content, debug = false }) => {
  const sourceRef = useRef<HTMLDivElement>(null);
  const pagesContainerRef = useRef<HTMLDivElement>(null);
  const previewContainerRef = useRef<HTMLDivElement>(null);
  const pageContentHeightRef = useRef<number>(0);
  const contentWidthRef = useRef<number>(0);

  // 添加缩放状态
  const [scale, setScale] = useState<number>(1);
  const [autoScale, setAutoScale] = useState<boolean>(true); // 是否允许自动缩放

  // 检查内容是否超出容器宽度
  const checkContentOverflow = useCallback((currentScale: number, containerElement?: HTMLElement) => {
    // 优先使用ref，避免DOM查询
    const previewContainer = containerElement || previewContainerRef.current;
    if (!previewContainer || !pagesContainerRef.current || pagesContainerRef.current.children.length === 0) {
      return false;
    }

    // 获取第一个页面元素的原始宽度
    const pageElement = pagesContainerRef.current.children[0] as HTMLElement;
    if (!pageElement) return false;

    // 计算缩放后的实际宽度
    const pageOriginalWidth = pageElement.offsetWidth;
    const scaledPageWidth = pageOriginalWidth * currentScale;

    // 获取容器可用宽度（减去padding）
    const containerWidth = previewContainer.clientWidth - 40; // 40px为左右padding

    return scaledPageWidth > containerWidth;
  }, []);

  // 应用布局模式
  const applyLayoutMode = useCallback((isOverflow: boolean, newScale: number, containerElement?: HTMLElement) => {
    // 优先使用ref，避免DOM查询
    const previewContainer = containerElement || previewContainerRef.current;
    const previewWrapper = previewContainer?.querySelector(".preview-wrapper") as HTMLElement;
    const scaledContainer = previewContainer?.querySelector(".scaled-container") as HTMLElement;

    if (!previewContainer || !previewWrapper || !scaledContainer) return;

    if (isOverflow) {
      // 内容超出容器：左对齐模式
      previewWrapper.style.justifyContent = 'flex-start';
      scaledContainer.style.transformOrigin = 'top left';
    } else {
      // 内容未超出容器：居中模式
      previewWrapper.style.justifyContent = 'center';
      scaledContainer.style.transformOrigin = 'top center';
    }

    scaledContainer.style.transform = `scale(${newScale})`;
  }, []);

  // 智能缩放函数：根据内容实际宽度动态选择布局模式
  const zoomWithFocus = useCallback(
    (newScale: number) => {
      // 如果缩放比例没有变化，不需要处理
      if (newScale === scale) return;

      // 获取预览容器
      const previewContainer = previewContainerRef.current;
      if (!previewContainer || !pagesContainerRef.current) return;

      // 保存当前滚动位置和视口信息
      const viewportWidth = previewContainer.clientWidth;
      const viewportHeight = previewContainer.clientHeight;
      const scrollTop = previewContainer.scrollTop;
      const scrollLeft = previewContainer.scrollLeft;

      // 检查新缩放级别下内容是否会超出容器
      const willOverflow = checkContentOverflow(newScale, previewContainer);

      // 应用新的布局模式
      applyLayoutMode(willOverflow, newScale, previewContainer);

      // 根据布局模式调整滚动位置
      if (!willOverflow) {
        // 居中模式：保持垂直滚动，重置水平滚动
        const centerY = (scrollTop + viewportHeight / 2) / scale;
        const newScrollTop = centerY * newScale - viewportHeight / 2;
        previewContainer.scrollTop = Math.max(0, newScrollTop);
        previewContainer.scrollLeft = 0;
      } else {
        // 左对齐模式：保持视觉中心点
        const centerX = (scrollLeft + viewportWidth / 2) / scale;
        const centerY = (scrollTop + viewportHeight / 2) / scale;

        const newScrollLeft = centerX * newScale - viewportWidth / 2;
        const newScrollTop = centerY * newScale - viewportHeight / 2;

        previewContainer.scrollLeft = Math.max(0, newScrollLeft);
        previewContainer.scrollTop = Math.max(0, newScrollTop);
      }

      // 异步更新React状态
      setTimeout(() => {
        setScale(newScale);
      }, 0);
    },
    [scale, checkContentOverflow, applyLayoutMode]
  );

  // 自动计算适合当前窗口大小的缩放比例
  const calculateOptimalScale = useCallback(() => {
    if (!pagesContainerRef.current || pagesContainerRef.current.children.length === 0) return 1;

    const container = previewContainerRef.current;
    if (!container) return 1;

    // 使用第一个页面元素计算缩放比例
    const pageElement = pagesContainerRef.current.children[0] as HTMLElement;

    // 如果页面元素未渲染完成或尺寸为0，返回默认值
    if (!pageElement || pageElement.offsetWidth === 0 || pageElement.offsetHeight === 0) {
      return 0.9; // 默认缩放比例
    }

    const pageWidth = pageElement.offsetWidth;
    const pageHeight = pageElement.offsetHeight;

    const containerWidth = container.clientWidth - 40; // 40px作为边距
    const containerHeight = container.clientHeight - 40;

    // 确保计算值合理
    if (containerWidth <= 0 || containerHeight <= 0 || pageWidth <= 0 || pageHeight <= 0) {
      return 0.9; // 默认值
    }

    // 取水平和垂直方向的最小缩放比例，确保页面完全可见
    const widthScale = containerWidth / pageWidth;
    const heightScale = containerHeight / pageHeight;
    const scale = Math.min(widthScale, heightScale, 1); // 最大不超过原始大小

    // 缩放计算完成
    return scale > 0 ? scale : 0.9; // 确保返回正值
  }, []);

  // 窗口大小变化时调整缩放和布局
  useEffect(() => {
    // 防止重复计算的节流机制
    let resizeTimeout: NodeJS.Timeout | null = null;
    let lastWidth = 0;
    let lastHeight = 0;
    let isFirstResize = true;

    const handleResize = () => {
      if (!autoScale) return;

      const container = previewContainerRef.current;
      if (!container) return;

      const currentWidth = container.clientWidth;
      const currentHeight = container.clientHeight;

      // 第一次调整或大小变化足够显著时才触发更新
      if (
        isFirstResize ||
        Math.abs(currentWidth - lastWidth) > 5 ||
        Math.abs(currentHeight - lastHeight) > 5
      ) {
        // 使用节流避免频繁计算
        if (resizeTimeout) clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          const newScale = calculateOptimalScale();
          if (newScale > 0) {
            zoomWithFocus(newScale);
          } else {
            // 即使没有缩放变化，也要检查布局模式
            const isOverflow = checkContentOverflow(scale, container);
            applyLayoutMode(isOverflow, scale, container);
          }

          // 更新记录的尺寸
          lastWidth = currentWidth;
          lastHeight = currentHeight;
          isFirstResize = false;
        }, 100); // 节流间隔
      }
    };

    // 使用ResizeObserver监测容器大小变化
    const container = previewContainerRef.current;
    let resizeObserver: ResizeObserver | null = null;

    if (container && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        // 记录有大小变化，但不重复输出日志
        handleResize();
      });
      resizeObserver.observe(container);
    }

    // 仅在没有ResizeObserver时使用window resize
    if (!resizeObserver) {
      window.addEventListener("resize", handleResize);
    }

    // 初始化时立即调整一次
    handleResize();

    return () => {
      if (resizeTimeout) clearTimeout(resizeTimeout);
      window.removeEventListener("resize", handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [calculateOptimalScale, autoScale, zoomWithFocus]);

  // 添加缩放功能 - Ctrl/Cmd + 滚轮
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
        const delta = e.deltaY < 0 ? 0.05 : -0.05;
        const newScale = Math.max(0.5, Math.min(2, scale + delta));
        zoomWithFocus(newScale);
        setAutoScale(false); // 手动缩放时禁用自动缩放
      }
    };

    window.addEventListener("wheel", handleWheel, { passive: false });
    return () => window.removeEventListener("wheel", handleWheel);
  }, [scale, zoomWithFocus]);

  // 处理父窗口发来的消息和外部缩放重置事件
  useEffect(() => {
    // 处理消息事件
    const handleMessage = (e: MessageEvent) => {
      if (e.data?.type === "reset-scale") {
        // 重置缩放并启用自动缩放
        setAutoScale(true);
        const optimalScale = calculateOptimalScale();
        if (optimalScale > 0) zoomWithFocus(optimalScale);
      }
    };

    // 处理自定义的重置缩放事件
    const handleResetScale = () => {
      // 重置缩放并启用自动缩放
      setAutoScale(true);
      const optimalScale = calculateOptimalScale();
      if (optimalScale > 0) zoomWithFocus(optimalScale);
    };

    window.addEventListener("message", handleMessage);
    window.addEventListener("reset-preview-scale", handleResetScale);

    return () => {
      window.removeEventListener("message", handleMessage);
      window.removeEventListener("reset-preview-scale", handleResetScale);
    };
  }, [calculateOptimalScale, zoomWithFocus]);

  // 移除缓存机制，确保每次内容变化都重新处理

  useEffect(() => {
    // 预加载所有图片并等待它们完成加载后再执行分页
    const preloadImages = async () => {
      // 首先确保sourceRef已经渲染完成且包含内容
      if (!sourceRef.current) return;

      // 创建一个临时容器用于解析HTML内容（如果是字符串）
      const tempDiv = document.createElement("div");
      if (typeof content === "string") {
        tempDiv.innerHTML = content;
      } else {
        // 如果不是字符串，需要确俞sourceRef中已有内容
        if (sourceRef.current.childNodes.length === 0) return;
      }

      // 从临时div或sourceRef中获取所有图片
      const imgElements = (
        typeof content === "string"
          ? tempDiv.querySelectorAll("img")
          : sourceRef.current.querySelectorAll("img")
      ) as NodeListOf<HTMLImageElement>;

      // 移除预加载状态缓存，每次都重新处理图片

      // 为所有图片设置默认样式，确保即使加载失败也有合理的尺寸和占位符
      imgElements.forEach(img => {
        // 确保每个图片元素都有默认尺寸，防止加载失败时布局塌陷
        if (!img.style.width) {
          img.style.width = "300px"; // 默认宽度
        }
        if (!img.style.height) {
          img.style.height = "auto"; // 默认高度自适应
        }
        // Set default alt text if none exists
        if (!img.alt) {
          img.alt = "Image failed to load";
        }
        // 添加加载失败时的处理函数，显示占位符
        img.onerror = function () {
          // 设置样式显示占位符背景和边框
          this.style.border = "1px dashed #ccc";
          this.style.background = "#f5f5f5";
          this.style.display = "flex";
          this.style.alignItems = "center";
          this.style.justifyContent = "center";
          // 显示错误文本作为替代
          this.style.position = "relative";
          // 设置一个最小高度，确保占位符始终可见
          if (parseInt(this.style.height) < 50) {
            this.style.height = "50px";
          }
          // 添加伪元素显示失败图标和文本
          const textNode = document.createElement("span");
          textNode.textContent = "Image failed to load";
          textNode.style.color = "#888";
          textNode.style.fontSize = "12px";
          textNode.style.textAlign = "center";
          textNode.style.padding = "10px";
          textNode.style.display = "block";
          this.parentNode?.insertBefore(textNode, this.nextSibling);
        };
      });

      if (imgElements.length === 0) {
        // 没有图片需要预加载，直接执行分页
        performPagination();
        return;
      }

      // 创建加载所有图片的Promise数组
      const imageLoadPromises = Array.from(imgElements).map(img => {
        // 跳过已经加载完成的图片
        if (img.complete) return Promise.resolve();

        return new Promise<void>((resolve, reject) => {
          const actualSrc = img.src;

          // 设置超时，避免某张图片长时间加载阻塞整个过程
          const timeoutId = setTimeout(() => {
            // 应用失败样式
            applyErrorStyle(img);
            resolve(); // 超时后仍然算作"完成"
          }, 5000); // 5秒超时

          img.onload = () => {
            clearTimeout(timeoutId);
            resolve();
          };

          img.onerror = () => {
            clearTimeout(timeoutId);
            // 应用失败样式
            applyErrorStyle(img);
            resolve(); // 错误后也算作"完成"，避免整体阻塞
          };
        });
      });

      // Styles applied when image loading fails (standalone function for reuse)
      function applyErrorStyle(img: HTMLImageElement) {
        // 设置样式显示占位符背景和边框
        img.style.border = "1px dashed #ccc";
        img.style.background = "#f5f5f5";
        // 确保有合理的尺寸
        if (!img.style.width || img.style.width === "auto") {
          img.style.width = "300px";
        }
        if (!img.style.height || parseInt(img.style.height) < 50) {
          img.style.height = "150px";
        }
      }

      // 等待所有图片加载，然后执行分页
      try {
        await Promise.all(imageLoadPromises);
        // 所有图片预加载完成
      } catch (err) {
        // 图片预加载过程中出错，继续执行分页
      }

      // 执行分页处理
      performPagination();
    };

    // 实际的分页计算逻辑，从原始useEffect中提取
    const performPagination = () => {
      // 1. 测量页面高度和内容区域高度（减去上下 padding 共20mm）
      const measurePage = document.createElement("div");
      measurePage.className = "page";
      measurePage.style.visibility = "hidden";
      measurePage.style.position = "absolute";
      measurePage.style.top = "0";
      measurePage.style.left = "0";
      document.body.appendChild(measurePage);
      const style = window.getComputedStyle(measurePage);
      const paddingTop = parseFloat(style.paddingTop) || 0;
      const paddingBottom = parseFloat(style.paddingBottom) || 0;
      const paddingLeft = parseFloat(style.paddingLeft) || 0;
      const paddingRight = parseFloat(style.paddingRight) || 0;
      const fullHeight = measurePage.clientHeight;
      const fullWidth = measurePage.clientWidth;
      // 内容区域高度 = page.clientHeight - 上下内边距
      const contentHeight = fullHeight - (paddingTop + paddingBottom);
      const contentWidth = fullWidth - (paddingLeft + paddingRight);
      pageContentHeightRef.current = contentHeight;
      contentWidthRef.current = contentWidth;
      document.body.removeChild(measurePage);

      // 设置隐藏源内容容器的宽度以匹配页面宽度，确保换行方式一致
      if (sourceRef.current) {
        sourceRef.current.style.width = contentWidthRef.current + "px";
      }

      if (pagesContainerRef.current && sourceRef.current) {
        // 清空旧的分页内容
        pagesContainerRef.current.innerHTML = "";

        // 检查是否需要显示行号
        const shouldShowLineNumbers = sourceRef.current?.querySelector('[data-show-line-numbers="true"]') !== null;

        // 创建数组保存页面元素，并提供创建新页面的工具函数
        const pages: HTMLDivElement[] = [];
        const createPage = (): HTMLDivElement => {
          const page = document.createElement("div");
          page.className = "page";

          /* —— 页眉：左对齐SVG logo + 右对齐期刊号 —— */
          const header = document.createElement("div");
          header.className = "page-header";
          Object.assign(header.style, {
            position: "absolute",
            left: "0",
            right: "0",
            top: "0", // 以 page 的 padding 顶为参考
            height: "10mm",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between", // 左右分布
            fontSize: "10pt",
            pointerEvents: "none", // 不拦截鼠标，纯展示
            paddingLeft: "5mm",
            paddingRight: "5mm",
          } as CSSStyleDeclaration);

          // 创建左侧logo容器
          const logoContainer = document.createElement("div");
          logoContainer.style.display = "flex";
          logoContainer.style.alignItems = "center";
          logoContainer.innerHTML = `
            <svg
              style="height: 4mm; width: auto; fill: #4F46E5;"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 156.7 20.11"
            >
              <path d="M12.53,19.74h-4.97V6.06h4.97v2.05c.74-1.5,2.36-2.42,4.42-2.42,3.74,0,4.94,2.61,4.94,5.46v8.59h-4.97v-7.27c0-1.87-.52-2.85-2.02-2.85-1.69,0-2.36,1.35-2.36,3.4v6.72Z" />
              <path d="M24.92,9.49h-1.84v-3.07l1.01-.15c1.38-.21,1.81-.95,2.15-2.21l.46-1.78h3.13v3.77h3.56v3.43h-3.56v5c0,1.17.55,1.63,1.56,1.63.61,0,1.29-.12,2.12-.46v3.22c-1.04.77-2.24,1.23-4.11,1.23-2.3,0-4.48-.95-4.48-4.17v-6.44Z" />
              <path d="M49.03,15.23c-.71,3.13-3.22,4.88-6.84,4.88-4.39,0-7.51-2.64-7.51-6.99s3.19-7.42,7.45-7.42c4.57,0,6.99,3.13,6.99,6.84v1.38h-9.6c.12,1.69,1.35,2.48,2.85,2.48,1.35,0,2.21-.46,2.67-1.53l3.99.37ZM44.4,11.52c-.06-1.23-.86-2.27-2.33-2.27-1.59,0-2.3.98-2.48,2.27h4.82Z" />
              <path d="M61.45,10.91c-.67-.4-1.41-.58-2.36-.58-1.84,0-2.97.86-2.97,3.16v6.26h-4.97V6.06h4.97v2.42c.49-1.53,1.93-2.79,3.74-2.79.83,0,1.56.25,1.9.52l-.31,4.69Z" />
              <path d="M75.78,15.72c0,.71.31,1.01.83,1.01.4,0,.71-.06,1.1-.21v2.64c-.71.49-1.69.89-3.1.89-1.69,0-3.01-.86-3.47-2.42-.8,1.59-2.36,2.42-4.54,2.42-2.67,0-4.42-1.38-4.42-3.71,0-2.64,1.9-3.77,4.88-4.29l3.83-.64v-.22c0-1.13-.52-1.81-1.72-1.81-1.1,0-1.66.71-1.87,1.72l-4.6-.31c.43-3.01,2.67-5.09,6.78-5.09,3.62,0,6.29,1.56,6.29,5.37v4.66ZM70.9,13.82l-2.02.37c-1.2.25-1.9.61-1.9,1.5,0,.71.55,1.17,1.38,1.17,1.41,0,2.55-1.04,2.55-2.82v-.21Z" />
              <path d="M85.75,5.69c4.11,0,6.41,2.67,6.75,5.95l-4.08.28c-.34-1.69-1.17-2.48-2.61-2.48s-2.55,1.01-2.55,3.47,1.04,3.47,2.55,3.47,2.27-.83,2.61-2.51l4.08.31c-.34,3.31-2.64,5.95-6.75,5.95-4.32,0-7.48-2.85-7.48-7.21s3.16-7.21,7.48-7.21Z" />
              <path d="M94.89,9.49h-1.84v-3.07l1.01-.15c1.38-.21,1.81-.95,2.15-2.21l.46-1.78h3.13v3.77h3.56v3.43h-3.56v5c0,1.17.55,1.63,1.56,1.63.61,0,1.29-.12,2.12-.46v3.22c-1.04.77-2.24,1.23-4.11,1.23-2.3,0-4.48-.95-4.48-4.17v-6.44Z" />
              <path d="M105.35.42h5.03v4.36h-5.03V.42ZM110.35,6.06v13.68h-4.97V6.06h4.97Z" />
              <path d="M116.73,19.74l-5.09-13.68h5.21l2.97,8.5h.12l2.98-8.5h4.82l-5.09,13.68h-5.92Z" />
              <path d="M142.34,15.23c-.71,3.13-3.22,4.88-6.84,4.88-4.39,0-7.51-2.64-7.51-6.99s3.19-7.42,7.45-7.42c4.57,0,6.99,3.13,6.99,6.84v1.38h-9.6c.12,1.69,1.35,2.48,2.85,2.48,1.35,0,2.21-.46,2.67-1.53l3.99.37ZM137.71,11.52c-.06-1.23-.86-2.27-2.33-2.27-1.59,0-2.3.98-2.48,2.27h4.82Z" />
              <path d="M147.81,15.23c.18,1.13,1.1,1.78,2.61,1.78,1.1,0,1.69-.4,1.69-.95,0-.52-.31-.89-1.26-1.04l-2.02-.31c-3.31-.52-5.06-1.59-5.06-4.39s2.36-4.63,6.2-4.63,6.29,1.75,6.56,4.6l-4.08.12c-.18-.98-1.04-1.59-2.58-1.59-.95,0-1.5.37-1.5.98,0,.46.37.71.83.77l2.67.46c3.07.49,4.85,1.87,4.85,4.54,0,2.88-2.45,4.54-6.35,4.54s-6.59-1.5-6.87-4.82l4.32-.06Z" />
              <polygon points=".03 0 5 0 5 5.31 3.95 13.72 1.11 13.72 .03 5.31 .03 0" />
              <rect y="15.17" width="5" height="4.94" />
            </svg>
          `;

          // 创建右侧期刊号
          const journalInfo = document.createElement("div");
          journalInfo.style.fontSize = "10pt";
          journalInfo.style.color = "#2b2b2b";
          journalInfo.style.fontWeight = "400";
          journalInfo.textContent = "Vol.0 No.0 (2025)";

          header.appendChild(logoContainer);
          header.appendChild(journalInfo);
          page.appendChild(header);

          /* —— 页脚：右对齐页码 —— */
          const footer = document.createElement("div");
          footer.className = "page-footer";
          Object.assign(footer.style, {
            position: "absolute",
            left: "0",
            right: "0",
            bottom: "0", // 参考 page 的 padding 底
            height: "10mm",
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-end", // 右对齐
            fontSize: "10pt",
            fontWeight: "400",
            pointerEvents: "none",
            paddingRight: "5mm",
            color: "#2b2b2b",
          } as CSSStyleDeclaration);

          // 页码将在页面创建后设置
          footer.textContent = String(pages.length + 1).padStart(2, '0');
          page.appendChild(footer);

          /* —— 行号列（如果需要显示） —— */
          if (shouldShowLineNumbers) {
            // 左侧行号列
            const leftLineNumbers = document.createElement("div");
            leftLineNumbers.className = "line-numbers line-numbers-left";
            Object.assign(leftLineNumbers.style, {
              position: "absolute",
              left: LINE_NUMBER_CONFIG.COLUMN_OFFSET,
              top: "15mm",
              bottom: "15mm",
              width: LINE_NUMBER_CONFIG.COLUMN_WIDTH,
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
              alignItems: "center",
              fontSize: LINE_NUMBER_CONFIG.FONT_SIZE,
              color: "var(--color-gray-400)",
              pointerEvents: "none",
              fontFamily: "monospace",
              lineHeight: "1",
              zIndex: "10",
            } as CSSStyleDeclaration);

            // 右侧行号列
            const rightLineNumbers = document.createElement("div");
            rightLineNumbers.className = "line-numbers line-numbers-right";
            Object.assign(rightLineNumbers.style, {
              position: "absolute",
              right: LINE_NUMBER_CONFIG.COLUMN_OFFSET,
              top: "15mm",
              bottom: "15mm",
              width: LINE_NUMBER_CONFIG.COLUMN_WIDTH,
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
              alignItems: "center",
              fontSize: LINE_NUMBER_CONFIG.FONT_SIZE,
              color: "var(--color-gray-400)",
              pointerEvents: "none",
              fontFamily: "monospace",
              lineHeight: "1",
              zIndex: "10",
            } as CSSStyleDeclaration);

            // 生成行号（基于页面内容高度和行高估算）
            const contentHeight = pageContentHeightRef.current;
            const linesPerPage = calculateLinesPerPage(contentHeight);
            const startLineNumber = calculateStartLineNumber(pages.length, linesPerPage);

            // 创建均匀分布的行号
            for (let i = 0; i < linesPerPage; i++) {
              const lineNumber = startLineNumber + i;

              // 左侧行号
              const leftLineSpan = document.createElement("span");
              leftLineSpan.textContent = String(lineNumber);
              leftLineNumbers.appendChild(leftLineSpan);

              // 右侧行号
              const rightLineSpan = document.createElement("span");
              rightLineSpan.textContent = String(lineNumber);
              rightLineNumbers.appendChild(rightLineSpan);
            }

            page.appendChild(leftLineNumbers);
            page.appendChild(rightLineNumbers);
          }

          /* —— 调试描边（可选） —— */
          if (debug) page.style.outline = "1px solid #0f0";

          pages.push(page);
          pagesContainerRef.current!.appendChild(page);
          return page;
        };

        // 初始化第一页
        let currentPage = createPage();
        let remainingSpace = pageContentHeightRef.current; // 当前页剩余可用高度

        // 辅助函数：识别不可分割的特殊元素（数学公式和代码块）
        const isIndivisibleElement = (elem: HTMLElement): boolean => {
          // 排除一些明显不应该被识别为不可分割的元素
          const excludedTags = ['DIV', 'SPAN', 'P', 'SECTION', 'ARTICLE', 'MAIN', 'BODY', 'HTML'];
          const excludedClasses = ['jsx-', 'page', 'pages-container', 'preview-wrapper', 'scaled-container'];

          // 如果是通用容器标签，进行额外检查
          if (excludedTags.includes(elem.tagName)) {
            // 检查是否有styled-jsx类名（通常以jsx-开头）
            if (elem.className && excludedClasses.some(cls => elem.className.includes(cls))) {
              return false; // 明确排除这些容器元素
            }
          }

          // 1. 代码块
          if (elem.tagName === "PRE") return true;
          if (elem.id && elem.id.startsWith("code:")) return true;

          // 2. 数学公式相关元素 - 只识别块级数学公式
          if (elem.tagName === "MATH") {
            return true;
          }
          if (elem.classList.contains("math") && !elem.classList.contains("math-inline")) {
            return true;
          }
          if (elem.classList.contains("math-display")) {
            return true;
          }

          // MathJax渲染的块级数学公式容器
          if (elem.tagName === "MJX-CONTAINER" && elem.getAttribute("display") === "true") {
            return true;
          }
          if (elem.classList.contains("MathJax") && elem.getAttribute("display") === "true") {
            return true;
          }

          // 兼容其他可能的数学公式标识
          if (elem.id && elem.id.startsWith("equation:")) {
            return true;
          }

          // 3. 图片和表格（已有的逻辑）
          if (elem.tagName === "IMG") return true;
          if (elem.tagName === "FIGURE") return true;
          if (elem.tagName === "TABLE") return true;

          return false;
        };

        // 优化的高度测量函数 - 提高精度和性能
        const measureElementHeight = (elem: HTMLElement): number => {
          // 创建精确的测量容器
          const tempContainer = document.createElement("div");
          tempContainer.className = "page page-measure";
          Object.assign(tempContainer.style, {
            padding: "0",
            margin: "0",
            border: "none",
            height: "auto",
            boxSizing: "content-box",
            visibility: "hidden",
            position: "absolute",
            top: "-9999px", // 移到屏幕外，避免影响布局
            left: "-9999px",
            width: contentWidthRef.current + "px",
            fontSize: "inherit", // 继承字体设置
            lineHeight: "inherit",
            fontFamily: "inherit",
          });
          document.body.appendChild(tempContainer);

          const clone = elem.cloneNode(true) as HTMLElement;
          tempContainer.appendChild(clone);

          // 精确处理图片尺寸和布局
          const imgElements = clone.querySelectorAll("img");
          imgElements.forEach(img => {
            // 强制应用图片的实际尺寸或合理默认值
            const hasNaturalSize = img.naturalWidth > 0 && img.naturalHeight > 0;
            const hasStyleSize = img.style.width && img.style.height &&
              img.style.width !== "auto" && img.style.height !== "auto";

            if (hasNaturalSize && !hasStyleSize) {
              const aspectRatio = img.naturalWidth / img.naturalHeight;
              const maxWidthPerc = 0.85; // 图片最大宽度为页面宽度的85%
              const maxWidth = contentWidthRef.current * maxWidthPerc;

              if (img.naturalWidth > maxWidth) {
                img.style.width = `${Math.floor(maxWidth)}px`;
                img.style.height = `${Math.floor(maxWidth / aspectRatio)}px`;
              } else {
                img.style.width = `${img.naturalWidth}px`;
                img.style.height = `${img.naturalHeight}px`;
              }
            } else if (!hasNaturalSize && !hasStyleSize) {
              // Image loading failed or incomplete, set reasonable default values
              img.style.width = "280px";
              img.style.height = "140px";
              img.style.border = "1px dashed #ccc";
              img.style.background = "#f5f5f5";
              img.style.display = "block";
            }

            // 确保图片不会导致水平滚动
            img.style.maxWidth = "100%";
            img.style.height = "auto";
            img.style.objectFit = "contain";
          });

          // 精确计算元素高度，特别处理特殊元素
          let h = 0;

          // 强制触发布局计算
          clone.style.display = clone.style.display || "block";
          tempContainer.offsetHeight; // 强制重排

          if (isIndivisibleElement(clone)) {
            // 数学公式特殊处理
            const mathElements = clone.querySelectorAll("mjx-container, .MathJax, .math, .math-display");
            if (mathElements.length > 0) {
              // 等待 MathJax 渲染完成
              clone.style.position = "static";
              clone.style.display = "block";

              // 多次测量取稳定值
              let measurements = [];
              for (let i = 0; i < 3; i++) {
                tempContainer.offsetHeight; // 强制重排
                measurements.push(clone.offsetHeight);
              }
              h = Math.max(...measurements);

              // 为数学公式添加安全边距
              h += 8;
            }

            // 代码块特殊处理
            else if (clone.tagName === "PRE" || clone.id?.startsWith("code:")) {
              clone.style.whiteSpace = "pre-wrap";
              clone.style.wordBreak = "break-word";
              clone.style.overflowWrap = "break-word";

              // 确保代码块宽度不超出页面
              clone.style.maxWidth = "100%";
              clone.style.overflow = "hidden";

              tempContainer.offsetHeight; // 强制重排
              h = clone.offsetHeight;

              // 为代码块添加安全边距
              h += 6;
            }

            // 其他特殊元素
            else {
              tempContainer.offsetHeight; // 强制重排
              h = clone.offsetHeight;
              h += 4; // 通用安全边距
            }
          } else {
            // 普通元素
            tempContainer.offsetHeight; // 强制重排
            h = clone.offsetHeight;
          }

          // 确保高度为正数且合理
          h = Math.max(h, 1);

          document.body.removeChild(tempContainer);
          return h;
        };

        // 辅助函数：将元素 elem 拆分为两部分：在允许高度 allowedHeight 内能放下的部分 和 剩余部分
        const splitElementToHeight = (
          elem: HTMLElement,
          allowedHeight: number
        ): [HTMLElement | null, HTMLElement | null] => {
          const totalHeight = measureElementHeight(elem.cloneNode(true) as HTMLElement);
          if (totalHeight <= allowedHeight) {
            // 元素整体在允许高度内，直接返回自身（完全适Fits合，无剩余）
            return [elem.cloneNode(true) as HTMLElement, null];
          }

          // 对于不可分割的特殊元素（数学公式、代码块、图片、表格），直接返回整体作为剩余
          if (isIndivisibleElement(elem)) {
            // 安全检查：如果不可分割元素高度超过两个页面高度，强制分割以避免死循环
            if (totalHeight > pageContentHeightRef.current * 2) {
              // 对于超大元素，当作普通元素处理，允许分割
            } else {
              return [null, elem.cloneNode(true) as HTMLElement];
            }
          }

          // 如果元素不可再拆分（没有子节点）
          if (!elem.hasChildNodes() || elem.childNodes.length === 0) {
            return [null, elem.cloneNode(true) as HTMLElement]; // 无法部分容纳，整块作为剩余
          }

          // 创建元素的克隆用于"当前页部分"和"剩余部分"
          const partElem = elem.cloneNode(false) as HTMLElement; // 当前页容纳的部分
          const remainElem = elem.cloneNode(false) as HTMLElement; // 剩余内容部分
          if (debug) {
            partElem.style.outline = "1px solid red"; // 调试模式下标出拆分边界
          }
          // 在隐藏容器中实时构建 partElem 来测量高度
          const measureContainer = document.createElement("div");
          measureContainer.className = "page page-measure";
          Object.assign(measureContainer.style, {
            padding: "0",
            height: "auto",
            boxSizing: "content-box",
            visibility: "hidden",
            position: "absolute",
            top: "0",
            left: "0",
            width: contentWidthRef.current + "px",
          });
          document.body.appendChild(measureContainer);
          measureContainer.appendChild(partElem);

          const children = Array.from(elem.childNodes);
          for (let i = 0; i < children.length; i++) {
            const child = children[i];
            if (child.nodeType === Node.TEXT_NODE) {
              // 文本节点处理
              const text = child.textContent || "";
              if (text.trim() === "") {
                // 空白文本直接添加（不影响高度）
                partElem.appendChild(document.createTextNode(text));
                continue;
              }
              const textNode = document.createTextNode(text);
              partElem.appendChild(textNode);
              // 检查添加整个文本节点后高度是否超出 allowedHeight
              if (partElem.offsetHeight > allowedHeight) {
                // 超出，需要拆分此文本节点
                partElem.removeChild(textNode);
                let low = 0;
                let high = text.length;
                let fitText = "";
                let attempts = 0;

                // 优化的二分查找，考虑单词边界
                while (low < high && attempts < MAX_SPLIT_ATTEMPTS) {
                  const mid = Math.floor((low + high) / 2) + 1;
                  let testText = text.slice(0, mid);

                  // 尝试在单词边界分割（避免单词被截断）
                  if (mid < text.length && mid > 0) {
                    const char = text[mid];
                    const prevChar = text[mid - 1];

                    // 如果不在单词边界，向前查找最近的空格或标点
                    if (!/\s/.test(char) && !/\s/.test(prevChar) && !/[.,;:!?]/.test(prevChar)) {
                      const spaceIndex = testText.lastIndexOf(' ');
                      const punctIndex = Math.max(
                        testText.lastIndexOf('.'),
                        testText.lastIndexOf(','),
                        testText.lastIndexOf(';'),
                        testText.lastIndexOf(':')
                      );
                      const breakIndex = Math.max(spaceIndex, punctIndex);

                      if (breakIndex > low) {
                        testText = text.slice(0, breakIndex + 1);
                      }
                    }
                  }

                  const testNode = document.createTextNode(testText);
                  partElem.appendChild(testNode);
                  measureContainer.offsetHeight; // 强制重排
                  const testHeight = partElem.offsetHeight;
                  partElem.removeChild(testNode);

                  if (testHeight <= allowedHeight) {
                    low = testText.length;
                    fitText = testText;
                  } else {
                    high = testText.length - 1;
                  }
                  attempts++;
                }
                // 将找到的适配文本添加到当前页部分
                if (low > 0) {
                  partElem.appendChild(document.createTextNode(fitText));
                }
                // 剩余文本放入 remainElem
                const remainingText = text.slice(low);
                if (remainingText.length > 0) {
                  remainElem.appendChild(document.createTextNode(remainingText));
                }
                // 剩余的兄弟节点全部加入 remainElem，不再尝试放入当前页
                for (let j = i + 1; j < children.length; j++) {
                  remainElem.appendChild(children[j].cloneNode(true));
                }
                document.body.removeChild(measureContainer);
                return [partElem, remainElem];
              }
              // 文本节点完整放入当前部分（未超出），继续处理下一个子节点
              continue;
            } else if (child.nodeType === Node.ELEMENT_NODE) {
              // 子元素节点处理
              const childElem = child as HTMLElement;

              // 检查子元素是否为不可分割元素
              if (isIndivisibleElement(childElem)) {
                const childHeight = measureElementHeight(childElem);
                const currentPartHeight = partElem.offsetHeight;

                // 安全检查：如果子元素过大，当作普通元素处理
                if (childHeight > pageContentHeightRef.current * 2) {
                  // 继续按普通元素处理
                } else {
                  // 对于不可分割元素，需要更严格的检查：
                  // 1. 检查是否会超出允许高度
                  // 2. 检查是否有足够的剩余空间来完整显示（参考文字分页逻辑）
                  const wouldExceedHeight = currentPartHeight + childHeight > allowedHeight;
                  const remainingSpaceForChild = allowedHeight - currentPartHeight;
                  const needsMinimumSpace = childHeight > remainingSpaceForChild - BOTTOM_TOLERANCE;

                  if (wouldExceedHeight || needsMinimumSpace) {
                    // 当前子元素和后续所有元素都移到剩余部分
                    for (let j = i; j < children.length; j++) {
                      remainElem.appendChild(children[j].cloneNode(true));
                    }
                    document.body.removeChild(measureContainer);
                    return [partElem, remainElem];
                  } else {
                    // 可以放入当前页
                    partElem.appendChild(childElem.cloneNode(true));
                    continue;
                  }
                }
              }

              const childClone = childElem.cloneNode(true) as HTMLElement;
              partElem.appendChild(childClone);
              if (partElem.offsetHeight > allowedHeight) {
                // 当前子元素放不下，需要拆分
                partElem.removeChild(childClone);
                // 递归拆分子元素（计算在剩余高度中子元素的可放部分）
                const remainingHeight = allowedHeight - partElem.offsetHeight;
                const [childPart, childRemain] = splitElementToHeight(
                  childElem,
                  remainingHeight - BOTTOM_TOLERANCE
                );
                if (childPart) {
                  partElem.appendChild(childPart);
                }
                if (childRemain) {
                  remainElem.appendChild(childRemain);
                }
                // 其余兄弟节点直接加入剩余部分
                for (let j = i + 1; j < children.length; j++) {
                  remainElem.appendChild(children[j].cloneNode(true));
                }
                document.body.removeChild(measureContainer);
                return [partElem, remainElem];
              }
              // 子元素完整放入当前部分，继续迭代
              continue;
            }
          }
          // 移除测量容器
          document.body.removeChild(measureContainer);
          // 如果代码执行到这里，说明所有子节点都放入当前部分（即元素总高度 <= allowedHeight，理论上已在上面处理）
          return [partElem, null];
        };

        // 从隐藏源容器获取所有内容节点，依次进行分页布局
        const contentNodes = Array.from(sourceRef.current!.childNodes); // ← 非空断言 sourceRef
        for (const node of contentNodes) {
          /* ---------- 1️⃣ 统一把 node 转成 HTMLElement ---------- */
          let element: HTMLElement;
          if (node.nodeType === Node.TEXT_NODE) {
            const text = node.textContent || "";
            if (text.trim() === "") continue; // 跳过纯空白
            element = document.createElement("p");
            element.textContent = text;
          } else if (node.nodeType === Node.ELEMENT_NODE) {
            element = node as HTMLElement;
          } else {
            continue; // 其他类型跳过
          }

          /* ---------- 2️⃣ 当前页还能完全放得下？ ---------- */
          const isSpecialElement = isIndivisibleElement(element);
          const elementHeight = measureElementHeight(element.cloneNode(true) as HTMLElement);

          if (elementHeight <= remainingSpace - BOTTOM_TOLERANCE) {
            currentPage.appendChild(element.cloneNode(true));
            remainingSpace -= elementHeight;
            continue;
          }

          /* =========================================================
           * 3️⃣ 放不下：先看看还能塞"部分"进去（remainingSpace > 0）
           * ======================================================= */
          if (remainingSpace > 0) {
            const [partElem, remainElem] = splitElementToHeight(
              element,
              remainingSpace - BOTTOM_TOLERANCE
            );

            if (partElem) currentPage.appendChild(partElem); // 填满当前页
            remainingSpace = 0; // 当前页空间用尽

            /* ---------- 处理剩余部分 ---------- */
            let remainderElem: HTMLElement | null = remainElem;
            if (!remainderElem) {
              // 元素恰好卡在页末时，remainElem 为 null，
              // 为了后续逻辑统一，造个空占位元素
              remainderElem = document.createElement(element.tagName);
            }

            /* 只要 remainderElem 仍有内容，就循环分页 */
            while (remainderElem) {
              const newPage = createPage();

              const [fitElem, remElem] = splitElementToHeight(
                remainderElem,
                pageContentHeightRef.current - BOTTOM_TOLERANCE
              );

              if (fitElem) newPage.appendChild(fitElem);

              // 下一轮处理
              remainderElem = remElem;
              currentPage = newPage;
              remainingSpace = remainderElem
                ? 0
                : pageContentHeightRef.current -
                measureElementHeight((newPage.lastElementChild as HTMLElement) ?? newPage);
            }

            continue; // 当前原始 node 已完全处理，进入下一个循环
          }

          /* =========================================================
           * 4️⃣ 当前页已经 0 空间：看元素本身是否超过一整页
           * ======================================================= */

          // 如果剩余空间 < 1px，直接换新页
          if (remainingSpace < 1) {
            currentPage = createPage();
            remainingSpace = pageContentHeightRef.current;
          }

          if (elementHeight > pageContentHeightRef.current) {
            /* ---------- 元素本身比一整页还高，需要拆成多页 ---------- */
            let remainderElem: HTMLElement | null = element.cloneNode(true) as HTMLElement;

            while (remainderElem) {
              const [fitElem, remElem] = splitElementToHeight(
                remainderElem,
                pageContentHeightRef.current - BOTTOM_TOLERANCE
              );

              currentPage.appendChild(fitElem!); // fitElem 一定非空
              remainderElem = remElem; // 可能为 null

              if (remainderElem) {
                // 这页已满，再开新页
                currentPage = createPage();
                remainingSpace = pageContentHeightRef.current;
              } else {
                // 最后页：算一下还剩多少空间
                remainingSpace =
                  pageContentHeightRef.current -
                  measureElementHeight(currentPage.lastElementChild as HTMLElement);
              }
            }
          } else {
            /* ---------- 元素可以整块放进新页 ---------- */
            currentPage.appendChild(element.cloneNode(true));
            remainingSpace = pageContentHeightRef.current - elementHeight;
          }
        } // end for contentNodes

        // 设置页脚页码（页码和总页数）
        pages.forEach((page, index) => {
          const footer = page.querySelector(".page-footer");
          if (footer) {
            // footer.textContent = `Page ${index + 1} / ${pages.length}`;
            footer.textContent = String(index + 1).padStart(2, '0');
          }
        });

        // === 处理重复 ID，保持页面内唯一性 ===
        if (pagesContainerRef.current) {
          const seen = new Set<string>();
          pagesContainerRef.current.querySelectorAll("[id]").forEach(el => {
            const id = el.getAttribute("id");
            if (!id) return;
            if (seen.has(id)) {
              // 若已存在同ID，则移除重复ID防止锚点混淆
              el.removeAttribute("id");
            } else {
              seen.add(id);
            }
          });
        }

        // 分页完成
      }
    };

    // 启动预加载流程
    preloadImages();
  }, [content, debug]);

  useEffect(() => {
    // —— 调试：锚点点击滚动 ——
    const handleAnchorClick = (e: MouseEvent) => {
      const anchor = (e.target as HTMLElement).closest('a[href^="#"]');
      if (!anchor) return;
      const hash = anchor.getAttribute("href")!.slice(1);

      // 在分页区域查找目标
      const target = pagesContainerRef.current?.querySelector(
        `#${CSS.escape(hash)}`
      ) as HTMLElement | null;
      if (!target) return; // 找不到目标则交给浏览器默认行为

      // 阻止浏览器默认锚点处理
      e.preventDefault();
      history.pushState(null, "", `#${hash}`);

      const preview = previewContainerRef.current;
      if (!preview) {
        return;
      }

      // 当前缩放倍率（state 中维护）
      const currentScale = scale || 1;

      /* ---------- 迭代式精确滚动 ---------- */
      let attempts = 0;
      const maxAttempts = 6;

      const adjust = () => {
        const preRect = preview.getBoundingClientRect();
        const tarRect = target.getBoundingClientRect();
        const delta = tarRect.top - preRect.top - preRect.height / 2 + tarRect.height / 2;

        if (Math.abs(delta) < 2 || attempts >= maxAttempts) {
          return;
        }

        preview.scrollTop += delta;
        attempts++;
        setTimeout(adjust, 40); // 等待布局刷新
      };

      adjust();
    };

    // 监听容器内部点击即可，减少全局绑定
    const containerEl = pagesContainerRef.current;
    containerEl?.addEventListener("click", handleAnchorClick);
    return () => containerEl?.removeEventListener("click", handleAnchorClick);
  }, [scale]);

  // 处理文档内容变化，重新计算缩放
  useEffect(() => {
    if (autoScale) {
      // 给页面渲染留出时间后再计算缩放
      const timer = setTimeout(() => {
        const newScale = calculateOptimalScale();
        if (newScale > 0) zoomWithFocus(newScale);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [content, autoScale, calculateOptimalScale, zoomWithFocus]);

  // 处理空容器情况，避免无内容时预览区域不可见
  useEffect(() => {
    const handleEmptyContainer = () => {
      // 检查分页容器是否为空
      if (pagesContainerRef.current && pagesContainerRef.current.children.length === 0) {
        const container = pagesContainerRef.current;

        // 创建一个空白占位页，确保容器有默认尺寸
        const emptyPage = document.createElement("div");
        emptyPage.className = "page";
        emptyPage.style.minHeight = "297mm"; // A4高度
        emptyPage.style.width = "210mm"; // A4宽度

        // 添加提示信息
        const message = document.createElement("div");
        message.textContent = "Preparing paper preview...";
        message.style.color = "#888";
        message.style.fontStyle = "italic";
        message.style.display = "flex";
        message.style.height = "100%";
        message.style.justifyContent = "center";
        message.style.alignItems = "center";

        emptyPage.appendChild(message);
        container.appendChild(emptyPage);
      }
    };

    // 初始加载和内容变化时检查
    setTimeout(handleEmptyContainer, 200);
  }, [content]);

  return (
    <div ref={previewContainerRef} className="paged-preview">
      {/* 缩放控制按钮 (shadcn UI) */}
      <div className="fixed bottom-5 right-5 z-50 flex items-center gap-1 bg-background p-2 rounded-md shadow-md">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            const newScale = Math.min(2, scale + 0.1);
            zoomWithFocus(newScale);
            setAutoScale(false);
          }}
        >
          <ZoomIn />
        </Button>

        <span className="text-xs font-medium px-2">{Math.round(scale * 100)}%</span>

        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            const newScale = Math.max(0.5, scale - 0.1);
            zoomWithFocus(newScale);
            setAutoScale(false);
          }}
        >
          <ZoomOut />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            zoomWithFocus(1);
            setAutoScale(false);
          }}
        >
          <Maximize />
        </Button>

        <Button
          variant={autoScale ? "default" : "ghost"}
          size="sm"
          onClick={() => setAutoScale(!autoScale)}
        >
          <Fullscreen />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            try {
              window.print();
            } catch (err) {
              // 静默处理打印错误
            }
          }}
          className="ml-1"
        >
          <Printer />
        </Button>
      </div>

      {/* 隐藏的源内容容器 */}
      <div ref={sourceRef} style={{ visibility: "hidden", position: "absolute", top: 0, left: 0 }}>
        {typeof content === "string" ? (
          <div dangerouslySetInnerHTML={{ __html: content }} />
        ) : (
          content
        )}
      </div>

      {/* 分页结果容器 */}
      <div className="preview-wrapper">
        <div className="scaled-container">
          <div ref={pagesContainerRef} className="pages-container" />
        </div>
      </div>

      {/* 添加CSS样式 */}
      <style jsx>{`
        .paged-preview {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          overflow: auto;
          padding: 0;
        }
        .preview-wrapper {
          width: 100%;
          display: flex;
          justify-content: center;
          padding: 0;
          min-height: 100px;
          overflow: visible;
        }
        .scaled-container {
          display: inline-block;
          transform: scale(${scale});
          transform-origin: top center;
        }
        .pages-container {
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        .page {
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          margin-bottom: 20px;
        }
        .page:last-child {
          margin-bottom: 0;
        }
        /* 行号列样式 */
        .line-numbers {
          user-select: none;
        }
        .line-numbers span {
          display: block;
          margin: 2px 0;
          opacity: ${LINE_NUMBER_CONFIG.OPACITY};
          text-align: center;
          font-size: ${LINE_NUMBER_CONFIG.FONT_SIZE};
        }
      `}</style>
    </div>
  );
};

export default PagedPreview;
