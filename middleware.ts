/* ─────────────────────────────── middleware.ts ───────────────────────────────
 * ❶ 只在「需要保护」的 URL 下执行（通过 matcher 控制）
 * ❷ 从 JWT cookie 里取出用户信息（无需访问数据库 ⇒ 中间件可保持 Edge Runtime）
 * ❸ 判断是否具备访问当前路径所需的 RoleBinding
 *    - 若缺少 -> 302 跳转到 /account 或 403
 *    - 若满足 -> NextResponse.next()
 * ----------------------------------------------------------------------------*/

import type { Role, ScopeType } from "@prisma/client";
import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { NextRequest, NextResponse } from "next/server";
import { nanoid } from "nanoid";

import { getUserRoles } from "./utils/supabase/compat";
import { updateSession } from "./utils/supabase/middleware";

import { setCurrentRequestIP } from "@/lib/requestIP";

/* -------- matcher (must be static for Next.js) ---------------------------- */
export const config = {
  matcher: ["/submit/new", "/api/papers/submit", "/editor/:path*", "/api/ai/chat"],
};

/* -------- 1. 路径 → 权限映射表 ------------------------------------------ */
/* 可以按需再追加行；同一路径可以写多条（例如 GET & POST 权限不同）          */
const ACL: {
  pattern: RegExp; // Path Match
  need: { role: Role; scope: ScopeType }[];
  redirect?: string; // 不满足时去哪
}[] = [
    // —— 投稿 —— -----------------------------------------------------------------
    {
      pattern: /^\/submit\/new\/?$/, // 新建投稿页
      need: [{ role: "AUTHOR", scope: "GLOBAL" }],
      redirect: "/submit", // 不是作者就回列表页
    },
    {
      pattern: /^\/api\/papers\/submit\/?$/, // 上传接口
      need: [{ role: "AUTHOR", scope: "GLOBAL" }],
    },

    // —— 编辑（期刊级）—— --------------------------------------------------------
    {
      pattern: /^\/editor(\/.*)?$/, // 编辑器
      need: [
        { role: "ADMIN", scope: "GLOBAL" },
        { role: "AUTHOR", scope: "GLOBAL" },
      ],
      redirect: "/",
    },
  ];

/* -------- 2. 中间件主体 ----------------------------------------------------- */
export async function middleware(req: NextRequest) {
  // 首先更新和验证会话
  const res = await updateSession(req);

  // 获取并设置当前请求IP地址
  const forwarded = req.headers.get("x-forwarded-for");
  let ipAddress: string | undefined;

  if (forwarded) {
    ipAddress = forwarded.split(",")[0].trim();
  } else {
    ipAddress = req.headers.get("x-real-ip") || req.headers.get("x-client-ip") || "unknown";
  }

  // 设置全局IP变量
  setCurrentRequestIP(ipAddress);
  console.log("中间件捕获到的请求IP:", ipAddress);

  // 检查是否是WebSocket请求
  const isWebSocketRequest = req.headers.get("upgrade") === "websocket";
  const pathname = req.nextUrl.pathname;

  if (isWebSocketRequest) {
    console.log(`WebSocket连接请求: ${pathname}`);
    // 允许WebSocket连接通过，不做权限检查
    return res;
  }

  // 🆕 访客识别逻辑 - 为AI API访问者生成visitor_id
  if (pathname === "/api/ai/chat") {
    const visitorId = req.cookies.get("visitor_id")?.value;

    if (!visitorId) {
      // 生成新的访客ID并设置Cookie（30天过期，httpOnly）
      const newVisitorId = nanoid(21);
      res.cookies.set("visitor_id", newVisitorId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 30 * 24 * 60 * 60 // 30天
      });
      console.log("为AI访问者生成新的visitor_id:", newVisitorId);
    }
  }

  // 2-1 只处理匹配到 ACL 的路径
  try {
    const rule = ACL.find(r => {
      try {
        return r.pattern.test(pathname);
      } catch (error) {
        console.error(`正则匹配错误，路径: ${pathname}`, error);
        return false;
      }
    });
    if (!rule) return res;

    // 2-2 创建 Supabase 客户端并获取用户状态
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return req.cookies.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            res.cookies.set({
              name,
              value,
              ...options,
            });
          },
          remove(name: string, options: CookieOptions) {
            res.cookies.set({
              name,
              value: "",
              ...options,
            });
          },
        },
      }
    );

    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      // 未登录：跳去 /account?callbackUrl=<原始路径> （仅站内）
      const login = new URL("/account", req.url);
      const dest = req.nextUrl.pathname + req.nextUrl.search;
      login.searchParams.set("callbackUrl", encodeURIComponent(dest));
      return NextResponse.redirect(login);
    }

    // 2-3 从 supabase user 的 metadata 里获取 roles
    console.log("用户元数据:", JSON.stringify(user.user_metadata));
    // 兼容两种可能的数据结构
    const roles = getUserRoles(user) as {
      role: Role;
      scope: ScopeType;
      scopeId?: string | null;
    }[];
    console.log("解析出的角色:", JSON.stringify(roles));

    // 2-4 判定是否满足其一
    const pass = rule.need.some(({ role, scope }) =>
      // 使用更宽松的匹配，允许roles中存在其他属性和字段名差异
      roles.some(r => {
        // 检查角色名称匹配
        const roleMatches = r.role === role;

        // 检查作用域匹配（考虑到字段名可能不同）
        const scopeMatches = (r as any).scope === scope || (r as any).scopeType === scope;

        return roleMatches && scopeMatches;
      })
    );
    console.log(
      `权限检查: 路径=${pathname}, 需要角色=${JSON.stringify(rule.need)}, 拥有角色=${JSON.stringify(roles)}, 结果=${pass}`
    );

    if (pass) return res;

    // 2-5 无权：302 / 403
    if (rule.redirect) return NextResponse.redirect(new URL(rule.redirect, req.url));
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  } catch (error) {
    console.error(`中间件处理异常:`, error);
    // 出现异常时，允许请求通过，避免阻塞正常访问
    return res;
  }
}
