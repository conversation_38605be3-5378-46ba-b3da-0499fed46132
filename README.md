# Journal Next.js

基于 **Next.js 15** 与 **TypeScript** 的学术期刊平台，集成 Supabase、Prisma、Tailwind CSS 以及 PWA 功能。项目包含离线编辑器、管理后台和基于 MDX 的文档系统。

## 功能特性

- **离线可用的编辑器**：在 `app/editor` 中实现，利用 `idb-keyval` 缓存草稿，支持文件树浏览、预览和编译，配合 `next-pwa` 提供离线体验。
- **Supabase 认证与存储**：`utils/supabase` 封装了浏览器与服务端客户端，用于用户登录、会话管理和文件存储。
- **Prisma ORM**：`prisma/schema.prisma` 定义了用户、角色及稿件等模型，并实现角色绑定 (RBAC)。
- **文档系统**：文档位于 `content/docs`，通过 [Fumadocs](https://github.com/xiejiea/fumadocs) 渲染 MDX 文件。
- **管理后台**：例如 `/admin/sessions` 页面用于查看和结束活跃会话。
- **3D 演示主页**：首页 (`app/(site)/page.tsx`) 使用 Three.js 渲染海面效果。

## 目录结构

- `app/` – Next.js 页面与布局（包括 `(auth)`、`(site)`、`(docs)` 等子应用）。
- `components/` – 可复用的 UI 组件与 MDX 相关组件。
- `content/` – MDX 格式的文档内容。
- `lib/` – Supabase 客户端、Prisma 实例等工具库。
- `prisma/` – 数据库 schema 与迁移脚本。
- `public/` – 静态资源和 PWA 所需文件（`sw.js`、`offline.html` 等）。
- `scripts/` – 项目维护脚本。
- `utils/` – 通用工具函数和 Supabase 辅助方法。

## 快速开始

1. 安装依赖并启动开发服务器：

   ```bash
   npm install
   npm run dev
   ```

2. 创建 `.env` 文件，至少包含：

   ```
   NEXT_PUBLIC_SUPABASE_URL=<your-supabase-url>
   NEXT_PUBLIC_SUPABASE_ANON_KEY=<your-supabase-anon-key>
   ```

3. 浏览器访问 `http://localhost:3000`。

## 常用脚本

- `npm run build` – 生产环境构建
- `npm run lint` – ESLint 检查
- `npm run format` – Prettier 格式化
- `npm run check` – 执行 `lint` 与 `format:check`

## 编辑器界面重构与优化

### 架构重构

1. **顶层结构**
   - `ProjectEditor` 负责整体状态管理
   - `FileTreePanel` 文件树面板
   - `EditorPanel` 编辑区
   - `PreviewPanel` 预览区
   - `ContextMenu` 右键菜单
2. **UI 细分组件**
   - `PersistenceStatus` 保存与在线状态
   - `ActionButtons` 保存 / 编译按钮
   - `FilePath` 文件路径显示与复制

### 自定义 Hooks

- `useOfflineBuffer` 管理离线存储与同步
- `useFileOperations` 封装文件操作
- `useIframeMessaging` 处理 iframe 通信

### 性能优化

- 使用 `React.memo`、`useMemo`、`useCallback` 减少渲染
- 添加并发保护与错误重试
- 优化 iframe 加载和消息确认机制

### 用户体验

- 颜色区分保存状态并显示上次保存时间
- 文件路径快速复制和文件名验证
- 快捷键：`Ctrl+S` 保存、`Ctrl+B` 编译

### 稳定性

- 加强网络错误与离线场景处理
- 提供顺序化保存和冲突解决策略
