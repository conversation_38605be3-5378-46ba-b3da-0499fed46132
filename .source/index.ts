// @ts-nocheck -- skip type checking
import * as docs_7 from "../content/docs/submitting-a-paper.mdx?collection=docs&hash=1747567650436"
import * as docs_6 from "../content/docs/reviewing-for-joss.mdx?collection=docs&hash=1747567650436"
import * as docs_5 from "../content/docs/review-criteria.mdx?collection=docs&hash=1747567650436"
import * as docs_4 from "../content/docs/review-checklist.mdx?collection=docs&hash=1747567650436"
import * as docs_3 from "../content/docs/policies.mdx?collection=docs&hash=1747567650436"
import * as docs_2 from "../content/docs/paper-format.mdx?collection=docs&hash=1747567650436"
import * as docs_1 from "../content/docs/index.mdx?collection=docs&hash=1747567650436"
import * as docs_0 from "../content/docs/example-paper.mdx?collection=docs&hash=1747567650436"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"example-paper.mdx","absolutePath":"/Users/<USER>/Documents/GitHub/journal_nextjs/content/docs/example-paper.mdx"}, data: docs_0 }, { info: {"path":"index.mdx","absolutePath":"/Users/<USER>/Documents/GitHub/journal_nextjs/content/docs/index.mdx"}, data: docs_1 }, { info: {"path":"paper-format.mdx","absolutePath":"/Users/<USER>/Documents/GitHub/journal_nextjs/content/docs/paper-format.mdx"}, data: docs_2 }, { info: {"path":"policies.mdx","absolutePath":"/Users/<USER>/Documents/GitHub/journal_nextjs/content/docs/policies.mdx"}, data: docs_3 }, { info: {"path":"review-checklist.mdx","absolutePath":"/Users/<USER>/Documents/GitHub/journal_nextjs/content/docs/review-checklist.mdx"}, data: docs_4 }, { info: {"path":"review-criteria.mdx","absolutePath":"/Users/<USER>/Documents/GitHub/journal_nextjs/content/docs/review-criteria.mdx"}, data: docs_5 }, { info: {"path":"reviewing-for-joss.mdx","absolutePath":"/Users/<USER>/Documents/GitHub/journal_nextjs/content/docs/reviewing-for-joss.mdx"}, data: docs_6 }, { info: {"path":"submitting-a-paper.mdx","absolutePath":"/Users/<USER>/Documents/GitHub/journal_nextjs/content/docs/submitting-a-paper.mdx"}, data: docs_7 }], [{"info":{"path":"meta.json","absolutePath":"/Users/<USER>/Documents/GitHub/journal_nextjs/content/docs/meta.json"},"data":{"title":"Documentation","pages":["index","---Author Guides---","submitting-a-paper","paper-format","example-paper","policies","---Reviewer Guides---","reviewing-for-joss","review-criteria","review-checklist"]}}])